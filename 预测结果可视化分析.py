#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测结果可视化分析
生成图表展示预测方案的性能对比
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Rectangle

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')

def create_performance_comparison_chart():
    """创建预测方案性能对比图"""
    
    # 数据
    schemes = ['预测数字1', '预测数字2', '基础预测1', '基础预测2']
    hit_rates = [0.600, 0.600, 0.200, 0.200]
    hit_counts = [3, 3, 1, 1]
    total_counts = [5, 5, 5, 5]
    
    # 基线数据
    baseline_rate = 0.292
    random_rate = 0.082
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 子图1: 命中率对比
    colors = ['#2E8B57', '#2E8B57', '#CD5C5C', '#CD5C5C']  # 绿色表示最终预测，红色表示基础预测
    bars1 = ax1.bar(schemes, hit_rates, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    
    # 添加基线
    ax1.axhline(y=baseline_rate, color='blue', linestyle='--', linewidth=2, label='29.2%基线')
    ax1.axhline(y=random_rate, color='gray', linestyle=':', linewidth=2, label='8.2%随机基线')
    
    # 添加数值标签
    for i, (bar, rate, count, total) in enumerate(zip(bars1, hit_rates, hit_counts, total_counts)):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{rate:.1%}\n({count}/{total})',
                ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    ax1.set_title('预测方案命中率对比', fontsize=14, fontweight='bold')
    ax1.set_ylabel('命中率', fontsize=12)
    ax1.set_ylim(0, 0.7)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 子图2: 与基线对比
    baseline_comparison = [rate - baseline_rate for rate in hit_rates]
    colors2 = ['green' if x > 0 else 'red' for x in baseline_comparison]
    
    bars2 = ax2.bar(schemes, baseline_comparison, color=colors2, alpha=0.8, edgecolor='black', linewidth=1)
    ax2.axhline(y=0, color='black', linestyle='-', linewidth=1)
    
    # 添加数值标签
    for bar, diff in zip(bars2, baseline_comparison):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., 
                height + (0.01 if height > 0 else -0.03),
                f'{diff:+.1%}',
                ha='center', va='bottom' if height > 0 else 'top', 
                fontweight='bold', fontsize=10)
    
    ax2.set_title('相对29.2%基线的性能差异', fontsize=14, fontweight='bold')
    ax2.set_ylabel('性能差异', fontsize=12)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('预测方案性能对比.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_period_by_period_analysis():
    """创建逐期分析图"""
    
    # 逐期数据
    periods = [181, 182, 183, 184, 185]
    final_pred_hits = [1, 1, 0, 0, 1]  # 最终预测命中情况
    base_pred_hits = [1, 0, 0, 0, 0]   # 基础预测命中情况
    
    # 预测数字和实际开奖
    predictions = {
        181: {'final': [7, 2], 'base': [2, 3], 'actual': [10, 21, 40, 37, 2, 39]},
        182: {'final': [1, 2], 'base': [2, 3], 'actual': [1, 49, 21, 39, 6, 34]},
        183: {'final': [1, 2], 'base': [3, 2], 'actual': [12, 39, 38, 7, 28, 45]},
        184: {'final': [3, 5], 'base': [3, 1], 'actual': [8, 32, 20, 30, 18, 35]},
        185: {'final': [1, 2], 'base': [3, 5], 'actual': [22, 40, 29, 45, 11, 1]}
    }
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # 子图1: 逐期命中情况
    x = np.arange(len(periods))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, final_pred_hits, width, label='最终预测', color='#2E8B57', alpha=0.8)
    bars2 = ax1.bar(x + width/2, base_pred_hits, width, label='基础预测', color='#CD5C5C', alpha=0.8)
    
    ax1.set_title('逐期命中情况对比', fontsize=14, fontweight='bold')
    ax1.set_xlabel('期号', fontsize=12)
    ax1.set_ylabel('命中数量', fontsize=12)
    ax1.set_xticks(x)
    ax1.set_xticklabels([f'{p}期' for p in periods])
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 1.5)
    
    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            if height > 0:
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                        f'{int(height)}',
                        ha='center', va='bottom', fontweight='bold')
    
    # 子图2: 累积命中率趋势
    final_cumulative = np.cumsum(final_pred_hits) / np.arange(1, len(final_pred_hits) + 1)
    base_cumulative = np.cumsum(base_pred_hits) / np.arange(1, len(base_pred_hits) + 1)
    
    ax2.plot(periods, final_cumulative, marker='o', linewidth=3, markersize=8, 
             label='最终预测累积命中率', color='#2E8B57')
    ax2.plot(periods, base_cumulative, marker='s', linewidth=3, markersize=8, 
             label='基础预测累积命中率', color='#CD5C5C')
    
    # 添加基线
    ax2.axhline(y=0.292, color='blue', linestyle='--', linewidth=2, label='29.2%基线')
    ax2.axhline(y=0.082, color='gray', linestyle=':', linewidth=2, label='8.2%随机基线')
    
    ax2.set_title('累积命中率趋势', fontsize=14, fontweight='bold')
    ax2.set_xlabel('期号', fontsize=12)
    ax2.set_ylabel('累积命中率', fontsize=12)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 0.8)
    
    # 添加最终数值标签
    ax2.text(periods[-1], final_cumulative[-1] + 0.03, f'{final_cumulative[-1]:.1%}', 
             ha='center', va='bottom', fontweight='bold', color='#2E8B57')
    ax2.text(periods[-1], base_cumulative[-1] + 0.03, f'{base_cumulative[-1]:.1%}', 
             ha='center', va='bottom', fontweight='bold', color='#CD5C5C')
    
    plt.tight_layout()
    plt.savefig('逐期分析图.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_confidence_analysis():
    """创建置信度分析图"""
    
    # 置信度数据
    periods = [181, 182, 183, 184, 185]
    confidences = [0.676, 0.534, 0.601, 0.770, 0.703]
    hit_results = [1, 1, 0, 0, 1]  # 1表示命中，0表示未命中
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 子图1: 置信度与命中结果
    colors = ['green' if hit else 'red' for hit in hit_results]
    bars = ax1.bar(range(len(periods)), confidences, color=colors, alpha=0.7, edgecolor='black')
    
    # 添加命中标记
    for i, (bar, hit, conf) in enumerate(zip(bars, hit_results, confidences)):
        symbol = '✓' if hit else '✗'
        ax1.text(bar.get_x() + bar.get_width()/2., conf + 0.02,
                symbol, ha='center', va='bottom', fontsize=16, fontweight='bold')
        ax1.text(bar.get_x() + bar.get_width()/2., conf/2,
                f'{conf:.3f}', ha='center', va='center', fontweight='bold', color='white')
    
    ax1.set_title('置信度与命中结果', fontsize=14, fontweight='bold')
    ax1.set_xlabel('期号', fontsize=12)
    ax1.set_ylabel('置信度', fontsize=12)
    ax1.set_xticks(range(len(periods)))
    ax1.set_xticklabels([f'{p}期' for p in periods])
    ax1.set_ylim(0, 0.9)
    ax1.grid(True, alpha=0.3)
    
    # 添加图例
    from matplotlib.patches import Patch
    legend_elements = [Patch(facecolor='green', alpha=0.7, label='命中'),
                      Patch(facecolor='red', alpha=0.7, label='未命中')]
    ax1.legend(handles=legend_elements)
    
    # 子图2: 置信度分布与命中率
    # 将置信度分组
    conf_bins = [0.5, 0.6, 0.7, 0.8]
    bin_labels = ['0.5-0.6', '0.6-0.7', '0.7-0.8']
    
    # 统计每个区间的数据
    bin_data = {label: {'count': 0, 'hits': 0} for label in bin_labels}
    
    for conf, hit in zip(confidences, hit_results):
        if 0.5 <= conf < 0.6:
            bin_data['0.5-0.6']['count'] += 1
            bin_data['0.5-0.6']['hits'] += hit
        elif 0.6 <= conf < 0.7:
            bin_data['0.6-0.7']['count'] += 1
            bin_data['0.6-0.7']['hits'] += hit
        elif 0.7 <= conf < 0.8:
            bin_data['0.7-0.8']['count'] += 1
            bin_data['0.7-0.8']['hits'] += hit
    
    # 计算命中率
    hit_rates = []
    counts = []
    for label in bin_labels:
        count = bin_data[label]['count']
        hits = bin_data[label]['hits']
        hit_rate = hits / count if count > 0 else 0
        hit_rates.append(hit_rate)
        counts.append(count)
    
    bars2 = ax2.bar(bin_labels, hit_rates, color='skyblue', alpha=0.8, edgecolor='black')
    
    # 添加数值标签
    for bar, rate, count in zip(bars2, hit_rates, counts):
        if count > 0:
            ax2.text(bar.get_x() + bar.get_width()/2., rate + 0.05,
                    f'{rate:.1%}\n({count}期)',
                    ha='center', va='bottom', fontweight='bold')
    
    ax2.set_title('置信度区间与命中率', fontsize=14, fontweight='bold')
    ax2.set_xlabel('置信度区间', fontsize=12)
    ax2.set_ylabel('命中率', fontsize=12)
    ax2.set_ylim(0, 1.2)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('置信度分析图.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_comprehensive_summary():
    """创建综合总结图"""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 方案对比雷达图
    categories = ['命中率', '稳定性', '超越基线', '实用价值']
    
    # 数据 (0-1标准化)
    final_pred = [1.0, 0.6, 1.0, 1.0]  # 最终预测
    base_pred = [0.33, 0.2, 0.0, 0.2]  # 基础预测
    
    # 雷达图
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]  # 闭合
    
    final_pred += final_pred[:1]
    base_pred += base_pred[:1]
    
    ax1.plot(angles, final_pred, 'o-', linewidth=2, label='最终预测', color='green')
    ax1.fill(angles, final_pred, alpha=0.25, color='green')
    ax1.plot(angles, base_pred, 'o-', linewidth=2, label='基础预测', color='red')
    ax1.fill(angles, base_pred, alpha=0.25, color='red')
    
    ax1.set_xticks(angles[:-1])
    ax1.set_xticklabels(categories)
    ax1.set_ylim(0, 1)
    ax1.set_title('预测方案综合评价', fontsize=14, fontweight='bold')
    ax1.legend()
    ax1.grid(True)
    
    # 2. 关键指标对比
    metrics = ['命中率', '命中期数', '总期数']
    final_values = [60, 3, 5]
    base_values = [20, 1, 5]
    
    x = np.arange(len(metrics))
    width = 0.35
    
    ax2.bar(x - width/2, final_values, width, label='最终预测', color='green', alpha=0.8)
    ax2.bar(x + width/2, base_values, width, label='基础预测', color='red', alpha=0.8)
    
    ax2.set_title('关键指标对比', fontsize=14, fontweight='bold')
    ax2.set_xticks(x)
    ax2.set_xticklabels(metrics)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 基线对比
    baselines = ['随机基线\n8.2%', '连续预测\n20.8%', '单期预测\n29.2%', '最终预测\n60.0%']
    baseline_values = [8.2, 20.8, 29.2, 60.0]
    colors = ['gray', 'orange', 'blue', 'green']
    
    bars = ax3.bar(baselines, baseline_values, color=colors, alpha=0.8, edgecolor='black')
    
    for bar, value in zip(bars, baseline_values):
        ax3.text(bar.get_x() + bar.get_width()/2., value + 1,
                f'{value}%', ha='center', va='bottom', fontweight='bold')
    
    ax3.set_title('与各基线对比', fontsize=14, fontweight='bold')
    ax3.set_ylabel('命中率 (%)', fontsize=12)
    ax3.set_ylim(0, 70)
    ax3.grid(True, alpha=0.3)
    
    # 4. 改进效果
    improvement_data = {
        '基础→最终': 40.0,  # 20% → 60%
        'vs随机基线': 51.8,  # 60% - 8.2%
        'vs连续预测': 39.2,  # 60% - 20.8%
        'vs单期预测': 30.8   # 60% - 29.2%
    }
    
    bars = ax4.bar(improvement_data.keys(), improvement_data.values(), 
                   color=['purple', 'gray', 'orange', 'blue'], alpha=0.8, edgecolor='black')
    
    for bar, value in zip(bars, improvement_data.values()):
        ax4.text(bar.get_x() + bar.get_width()/2., value + 1,
                f'+{value}%', ha='center', va='bottom', fontweight='bold')
    
    ax4.set_title('性能提升幅度', fontsize=14, fontweight='bold')
    ax4.set_ylabel('提升幅度 (%)', fontsize=12)
    ax4.set_ylim(0, 60)
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('综合总结图.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("🎨 生成预测结果可视化分析图表")
    print("=" * 50)
    
    try:
        print("1. 创建预测方案性能对比图...")
        create_performance_comparison_chart()
        
        print("2. 创建逐期分析图...")
        create_period_by_period_analysis()
        
        print("3. 创建置信度分析图...")
        create_confidence_analysis()
        
        print("4. 创建综合总结图...")
        create_comprehensive_summary()
        
        print("\n✅ 所有图表生成完成!")
        print("生成的图表文件:")
        print("- 预测方案性能对比.png")
        print("- 逐期分析图.png") 
        print("- 置信度分析图.png")
        print("- 综合总结图.png")
        
    except Exception as e:
        print(f"❌ 图表生成失败: {e}")

if __name__ == "__main__":
    main()
