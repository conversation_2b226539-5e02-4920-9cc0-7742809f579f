#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强动态置信度调整器
Enhanced Dynamic Confidence Adjuster

集成到生产环境的高级动态置信度调整系统
支持实时监控、自动校准和智能优化

作者: AI Assistant
创建时间: 2025-07-15
版本: 2.0 Production
"""

import numpy as np
import pandas as pd
from collections import defaultdict, deque
from datetime import datetime, timedelta
import json
import logging
import threading
import time
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class EnhancedDynamicConfidenceAdjuster:
    """增强动态置信度调整器"""
    
    def __init__(self, config=None):
        """初始化调整器"""
        self.config = config or self._get_default_config()
        
        # 历史记录
        self.prediction_history = deque(maxlen=self.config.get('history_window', 200))
        self.confidence_history = deque(maxlen=self.config.get('history_window', 200))
        self.performance_metrics = deque(maxlen=self.config.get('metrics_window', 50))
        
        # 校准因子
        self.calibration_factors = {
            'accuracy_factor': 1.0,
            'consistency_factor': 1.0,
            'stability_factor': 1.0,
            'trend_factor': 1.0,
            'quality_factor': 1.0
        }
        
        # 实时监控
        self.monitoring_enabled = self.config.get('real_time_monitoring', {}).get('enabled', True)
        self.monitoring_thread = None
        self.monitoring_stop_event = threading.Event()

        # 自动校准
        self.auto_calibration_enabled = self.config.get('auto_calibration', {}).get('enabled', True)
        self.last_calibration = datetime.now()
        
        # 性能统计
        self.performance_stats = {
            'total_adjustments': 0,
            'successful_calibrations': 0,
            'failed_calibrations': 0,
            'start_time': datetime.now()
        }
        
        logger.info("🔧 增强动态置信度调整器初始化完成")
        logger.info(f"  实时监控: {'启用' if self.monitoring_enabled else '禁用'}")
        logger.info(f"  自动校准: {'启用' if self.auto_calibration_enabled else '禁用'}")
        
        # 启动实时监控
        if self.monitoring_enabled:
            self.start_monitoring()
    
    def _get_default_config(self):
        """获取默认配置"""
        return {
            'history_window': 200,
            'metrics_window': 50,
            'calibration_params': {
                'min_adjustment': 0.3,
                'max_adjustment': 3.0,
                'learning_rate': 0.08,
                'stability_threshold': 0.15,
                'trend_sensitivity': 0.06,
                'quality_weight': 0.2
            },
            'confidence_bounds': {
                'min_confidence': 0.05,
                'max_confidence': 0.95,
                'target_range': (0.2, 0.8)
            },
            'real_time_monitoring': {
                'enabled': True,
                'check_interval': 30,  # 秒
                'alert_thresholds': {
                    'low_accuracy': 0.15,
                    'poor_calibration': 0.1,
                    'high_variance': 0.3
                }
            },
            'auto_calibration': {
                'enabled': True,
                'interval_minutes': 60,
                'min_predictions': 10,
                'trigger_conditions': {
                    'accuracy_drop': 0.1,
                    'calibration_drift': 0.2,
                    'variance_spike': 0.25
                }
            },
            'adaptive_strategies': [
                'accuracy_based',
                'consistency_based',
                'stability_based',
                'trend_based',
                'quality_based',
                'ensemble_based'
            ]
        }
    
    def adjust_confidence(self, base_confidence, prediction_context=None):
        """动态调整置信度"""
        try:
            # 记录调整请求
            self.performance_stats['total_adjustments'] += 1
            
            # 如果历史数据不足，返回轻微调整的置信度
            if len(self.performance_metrics) < 3:
                adjusted_confidence = base_confidence * 1.1
                return self._apply_bounds(adjusted_confidence)
            
            # 计算各维度调整因子
            factors = self._calculate_adjustment_factors(prediction_context)
            
            # 应用集成调整
            adjusted_confidence = self._apply_ensemble_adjustment(
                base_confidence, factors, prediction_context
            )
            
            # 应用边界约束
            final_confidence = self._apply_bounds(adjusted_confidence)
            
            # 记录调整结果
            self._record_adjustment(base_confidence, final_confidence, factors)
            
            return final_confidence
            
        except Exception as e:
            logger.error(f"置信度调整失败: {e}")
            return base_confidence
    
    def _calculate_adjustment_factors(self, prediction_context):
        """计算调整因子"""
        factors = {}
        
        # 准确率因子
        factors['accuracy'] = self._calculate_accuracy_factor()
        
        # 一致性因子
        factors['consistency'] = self._calculate_consistency_factor()
        
        # 稳定性因子
        factors['stability'] = self._calculate_stability_factor()
        
        # 趋势因子
        factors['trend'] = self._calculate_trend_factor()
        
        # 质量因子
        factors['quality'] = self._calculate_quality_factor(prediction_context)
        
        # 集成因子
        factors['ensemble'] = self._calculate_ensemble_factor(prediction_context)
        
        return factors
    
    def _calculate_accuracy_factor(self):
        """计算准确率调整因子"""
        if len(self.performance_metrics) < 5:
            return 1.0
        
        recent_metrics = list(self.performance_metrics)[-5:]
        avg_accuracy = np.mean([m['accuracy'] for m in recent_metrics])
        
        # 目标准确率
        target_accuracy = 0.3
        
        # 计算调整因子
        if avg_accuracy > target_accuracy:
            # 准确率高，适当提高置信度
            factor = 1.0 + (avg_accuracy - target_accuracy) * 1.5
        else:
            # 准确率低，降低置信度
            factor = 1.0 - (target_accuracy - avg_accuracy) * 1.2
        
        # 应用学习率平滑
        learning_rate = self.config.get('calibration_params', {}).get('learning_rate', 0.08)
        self.calibration_factors['accuracy_factor'] = (
            (1 - learning_rate) * self.calibration_factors['accuracy_factor'] +
            learning_rate * factor
        )
        
        return self.calibration_factors['accuracy_factor']
    
    def _calculate_consistency_factor(self):
        """计算一致性调整因子"""
        if len(self.performance_metrics) < 3:
            return 1.0
        
        recent_metrics = list(self.performance_metrics)[-5:]
        calibrations = [m.get('calibration', 0) for m in recent_metrics]
        
        # 计算校准一致性
        if len(calibrations) > 1:
            avg_calibration = np.mean(calibrations)
            calibration_std = np.std(calibrations)
        else:
            avg_calibration = 0.5
            calibration_std = 0.1
        
        # 目标校准值和稳定性
        target_calibration = 0.5
        target_stability = 0.1
        
        # 计算因子
        calibration_factor = 1.0 + (avg_calibration - target_calibration) * 0.4
        stability_factor = 1.0 - min(0.3, calibration_std / target_stability - 1) * 0.2
        
        factor = (calibration_factor + stability_factor) / 2
        
        # 平滑更新
        learning_rate = self.config.get('calibration_params', {}).get('learning_rate', 0.08)
        self.calibration_factors['consistency_factor'] = (
            (1 - learning_rate) * self.calibration_factors['consistency_factor'] +
            learning_rate * factor
        )
        
        return self.calibration_factors['consistency_factor']
    
    def _calculate_stability_factor(self):
        """计算稳定性调整因子"""
        if len(self.confidence_history) < 5:
            return 1.0
        
        recent_confidences = [
            conf.get('final_confidence', 0.5) 
            for conf in list(self.confidence_history)[-10:]
        ]
        
        if len(recent_confidences) > 1:
            confidence_std = np.std(recent_confidences)
            confidence_mean = np.mean(recent_confidences)
        else:
            confidence_std = 0.1
            confidence_mean = 0.5
        
        # 计算稳定性评分
        stability_threshold = self.config.get('calibration_params', {}).get('stability_threshold', 0.15)
        if confidence_std < stability_threshold:
            # 稳定性好，轻微提升
            factor = 1.0 + (stability_threshold - confidence_std) * 0.5
        else:
            # 稳定性差，适当降低
            factor = 1.0 - (confidence_std - stability_threshold) * 0.3
        
        # 考虑置信度水平
        if confidence_mean < 0.3:
            factor *= 1.1  # 低置信度时稍微提升稳定性因子
        elif confidence_mean > 0.7:
            factor *= 0.95  # 高置信度时稍微降低稳定性因子
        
        # 平滑更新
        learning_rate = self.config.get('calibration_params', {}).get('learning_rate', 0.08)
        self.calibration_factors['stability_factor'] = (
            (1 - learning_rate) * self.calibration_factors['stability_factor'] +
            learning_rate * factor
        )
        
        return self.calibration_factors['stability_factor']
    
    def _calculate_trend_factor(self):
        """计算趋势调整因子"""
        if len(self.performance_metrics) < 5:
            return 1.0
        
        recent_metrics = list(self.performance_metrics)[-5:]
        accuracies = [m['accuracy'] for m in recent_metrics]
        
        # 计算趋势
        if len(accuracies) >= 3:
            # 使用线性回归计算趋势
            x = np.arange(len(accuracies))
            trend_slope = np.polyfit(x, accuracies, 1)[0]
        else:
            trend_slope = 0
        
        # 趋势敏感度
        sensitivity = self.config.get('calibration_params', {}).get('trend_sensitivity', 0.06)
        
        # 计算趋势因子
        factor = 1.0 + trend_slope * sensitivity * 10
        
        # 限制调整幅度
        factor = max(0.7, min(1.4, factor))
        
        # 平滑更新
        learning_rate = self.config.get('calibration_params', {}).get('learning_rate', 0.08)
        self.calibration_factors['trend_factor'] = (
            (1 - learning_rate) * self.calibration_factors['trend_factor'] +
            learning_rate * factor
        )
        
        return self.calibration_factors['trend_factor']
    
    def _calculate_quality_factor(self, prediction_context):
        """计算数据质量调整因子"""
        if not prediction_context:
            return 1.0
        
        # 数据来源质量评分
        data_source = prediction_context.get('data_source', '未知')
        source_quality = {
            '真实数据': 1.2,
            '用户验证': 1.15,
            '自动验证': 1.1,
            '历史数据': 1.05,
            '用户输入': 1.1,
            '预测数据': 0.85,
            '测试数据': 0.9,
            '未知': 0.95
        }
        
        base_quality = source_quality.get(data_source, 0.95)
        
        # 数据完整性评分
        previous_numbers = prediction_context.get('previous_numbers', [])
        completeness = len(previous_numbers) / 6 if previous_numbers else 0.5
        
        # 数据一致性评分
        consistency = self._check_data_consistency(previous_numbers)
        
        # 综合质量因子
        quality_weight = self.config.get('calibration_params', {}).get('quality_weight', 0.2)
        factor = (
            0.5 * base_quality +
            0.3 * (1 + completeness * quality_weight) +
            0.2 * (1 + consistency * quality_weight)
        )
        
        # 平滑更新
        learning_rate = self.config.get('calibration_params', {}).get('learning_rate', 0.08)
        self.calibration_factors['quality_factor'] = (
            (1 - learning_rate) * self.calibration_factors['quality_factor'] +
            learning_rate * factor
        )
        
        return self.calibration_factors['quality_factor']
    
    def _calculate_ensemble_factor(self, prediction_context):
        """计算集成调整因子"""
        if not prediction_context:
            return 1.0
        
        # 检查是否有算法集成信息
        algorithm_predictions = prediction_context.get('algorithm_predictions', {})
        algorithm_weights = prediction_context.get('algorithm_weights', {})
        
        if not algorithm_predictions:
            return 1.0
        
        # 计算算法一致性
        if len(algorithm_predictions) > 1:
            all_predictions = list(algorithm_predictions.values())
            # 计算预测的相似度
            similarity_scores = []
            for i in range(len(all_predictions)):
                for j in range(i + 1, len(all_predictions)):
                    pred1 = set(all_predictions[i])
                    pred2 = set(all_predictions[j])
                    similarity = len(pred1 & pred2) / len(pred1 | pred2) if pred1 | pred2 else 0
                    similarity_scores.append(similarity)
            
            avg_similarity = np.mean(similarity_scores) if similarity_scores else 0
            
            # 一致性高的预测获得更高的置信度
            ensemble_factor = 1.0 + avg_similarity * 0.3
        else:
            ensemble_factor = 1.0
        
        # 考虑权重分布
        if algorithm_weights:
            weight_entropy = -sum(w * np.log(w + 1e-10) for w in algorithm_weights.values())
            max_entropy = np.log(len(algorithm_weights))
            normalized_entropy = weight_entropy / max_entropy if max_entropy > 0 else 0
            
            # 权重分布越均匀，集成效果越好
            weight_factor = 1.0 + normalized_entropy * 0.2
            ensemble_factor = (ensemble_factor + weight_factor) / 2
        
        return min(1.5, max(0.8, ensemble_factor))
    
    def _check_data_consistency(self, numbers):
        """检查数据一致性"""
        if not numbers:
            return 0.5
        
        try:
            # 检查数字范围
            range_check = all(1 <= n <= 49 for n in numbers if isinstance(n, (int, float)))
            
            # 检查重复
            unique_check = len(numbers) == len(set(numbers))
            
            # 检查数量
            count_check = len(numbers) <= 6
            
            # 检查数据类型
            type_check = all(isinstance(n, (int, float)) for n in numbers)
            
            consistency = (range_check + unique_check + count_check + type_check) / 4
            return consistency
            
        except Exception:
            return 0.0
    
    def _apply_ensemble_adjustment(self, base_confidence, factors, prediction_context):
        """应用集成调整"""
        # 权重配置
        weights = {
            'accuracy': 0.25,
            'consistency': 0.20,
            'stability': 0.20,
            'trend': 0.15,
            'quality': 0.10,
            'ensemble': 0.10
        }
        
        # 计算加权调整因子
        weighted_factor = sum(
            weights[key] * factors.get(key, 1.0) 
            for key in weights
        )
        
        # 应用调整
        adjusted_confidence = base_confidence * weighted_factor
        
        # 应用调整边界
        min_adj = self.config.get('calibration_params', {}).get('min_adjustment', 0.3)
        max_adj = self.config.get('calibration_params', {}).get('max_adjustment', 3.0)
        
        adjustment_ratio = adjusted_confidence / base_confidence if base_confidence > 0 else 1.0
        adjustment_ratio = max(min_adj, min(max_adj, adjustment_ratio))
        
        return base_confidence * adjustment_ratio
    
    def _apply_bounds(self, confidence):
        """应用置信度边界"""
        min_conf = self.config.get('confidence_bounds', {}).get('min_confidence', 0.05)
        max_conf = self.config.get('confidence_bounds', {}).get('max_confidence', 0.95)
        return max(min_conf, min(max_conf, confidence))
    
    def _record_adjustment(self, original, adjusted, factors):
        """记录调整结果"""
        adjustment_record = {
            'timestamp': datetime.now(),
            'original_confidence': original,
            'adjusted_confidence': adjusted,
            'adjustment_ratio': adjusted / original if original > 0 else 1.0,
            'factors': factors.copy()
        }
        
        # 可以添加到历史记录或日志中
        if hasattr(self, 'adjustment_history'):
            self.adjustment_history.append(adjustment_record)
    
    def update_prediction_result(self, prediction_result):
        """更新预测结果"""
        try:
            # 添加到历史记录
            self.prediction_history.append(prediction_result)
            
            if 'confidence_details' in prediction_result:
                self.confidence_history.append(prediction_result['confidence_details'])
            
            # 更新性能指标
            self._update_performance_metrics()
            
            # 检查是否需要自动校准
            if self.auto_calibration_enabled:
                self._check_auto_calibration_trigger()
            
        except Exception as e:
            logger.error(f"更新预测结果失败: {e}")
    
    def _update_performance_metrics(self):
        """更新性能指标"""
        if len(self.prediction_history) < 5:
            return
        
        recent_predictions = list(self.prediction_history)[-self.config['metrics_window']:]
        
        # 计算准确率
        hits = sum(1 for p in recent_predictions if p.get('is_hit', False))
        accuracy = hits / len(recent_predictions)
        
        # 计算置信度校准
        confidences = [p.get('confidence', 0.5) for p in recent_predictions]
        hit_flags = [1 if p.get('is_hit', False) else 0 for p in recent_predictions]
        
        if len(confidences) > 1:
            calibration = np.corrcoef(confidences, hit_flags)[0, 1]
            calibration = max(-1, min(1, calibration))
        else:
            calibration = 0
        
        # 计算置信度稳定性
        if len(confidences) > 1:
            stability = 1 - np.std(confidences)
        else:
            stability = 0.5
        
        # 计算趋势
        if len(self.performance_metrics) > 0:
            prev_accuracy = self.performance_metrics[-1]['accuracy']
            trend = accuracy - prev_accuracy
        else:
            trend = 0
        
        metrics = {
            'timestamp': datetime.now(),
            'accuracy': accuracy,
            'calibration': calibration,
            'stability': stability,
            'trend': trend,
            'sample_size': len(recent_predictions)
        }
        
        self.performance_metrics.append(metrics)
    
    def _check_auto_calibration_trigger(self):
        """检查自动校准触发条件"""
        # 检查时间间隔
        time_since_last = datetime.now() - self.last_calibration
        interval_minutes = self.config.get('auto_calibration', {}).get('interval_minutes', 60)
        
        if time_since_last < timedelta(minutes=interval_minutes):
            return
        
        # 检查最小预测数量
        min_predictions = self.config.get('auto_calibration', {}).get('min_predictions', 10)
        if len(self.prediction_history) < min_predictions:
            return
        
        # 检查触发条件
        triggers = self.config.get('auto_calibration', {}).get('trigger_conditions', {
            'accuracy_drop': 0.1,
            'calibration_drift': 0.2,
            'variance_spike': 0.25
        })
        should_calibrate = False
        
        if len(self.performance_metrics) >= 2:
            current_metrics = self.performance_metrics[-1]
            prev_metrics = self.performance_metrics[-2]
            
            # 准确率下降
            accuracy_drop = prev_metrics['accuracy'] - current_metrics['accuracy']
            if accuracy_drop > triggers['accuracy_drop']:
                should_calibrate = True
                logger.info(f"触发自动校准: 准确率下降 {accuracy_drop:.3f}")
            
            # 校准漂移
            calibration_drift = abs(current_metrics['calibration'] - prev_metrics['calibration'])
            if calibration_drift > triggers['calibration_drift']:
                should_calibrate = True
                logger.info(f"触发自动校准: 校准漂移 {calibration_drift:.3f}")
        
        if should_calibrate:
            self.perform_auto_calibration()
    
    def perform_auto_calibration(self):
        """执行自动校准"""
        try:
            logger.info("🔧 执行自动校准")
            
            # 重新计算所有校准因子
            self._recalibrate_all_factors()
            
            # 更新校准时间
            self.last_calibration = datetime.now()
            
            # 更新统计
            self.performance_stats['successful_calibrations'] += 1
            
            logger.info("✅ 自动校准完成")
            
        except Exception as e:
            logger.error(f"❌ 自动校准失败: {e}")
            self.performance_stats['failed_calibrations'] += 1
    
    def _recalibrate_all_factors(self):
        """重新校准所有因子"""
        # 重新计算所有调整因子
        self._calculate_accuracy_factor()
        self._calculate_consistency_factor()
        self._calculate_stability_factor()
        self._calculate_trend_factor()
        
        logger.info(f"校准因子已更新: {self.calibration_factors}")
    
    def start_monitoring(self):
        """启动实时监控"""
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            return
        
        self.monitoring_stop_event.clear()
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        
        logger.info("🔍 实时监控已启动")
    
    def stop_monitoring(self):
        """停止实时监控"""
        if self.monitoring_thread:
            self.monitoring_stop_event.set()
            self.monitoring_thread.join(timeout=5)
            logger.info("🔍 实时监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        check_interval = self.config.get('real_time_monitoring', {}).get('check_interval', 30)
        
        while not self.monitoring_stop_event.wait(check_interval):
            try:
                self._perform_monitoring_check()
            except Exception as e:
                logger.error(f"监控检查失败: {e}")
    
    def _perform_monitoring_check(self):
        """执行监控检查"""
        if len(self.performance_metrics) < 3:
            return
        
        current_metrics = self.performance_metrics[-1]
        thresholds = self.config.get('real_time_monitoring', {}).get('alert_thresholds', {
            'low_accuracy': 0.15,
            'poor_calibration': 0.1,
            'high_variance': 0.3
        })
        
        # 检查低准确率
        if current_metrics['accuracy'] < thresholds['low_accuracy']:
            logger.warning(f"⚠️ 准确率过低: {current_metrics['accuracy']:.3f}")
        
        # 检查校准质量
        if abs(current_metrics['calibration']) < thresholds['poor_calibration']:
            logger.warning(f"⚠️ 校准质量差: {current_metrics['calibration']:.3f}")
        
        # 检查方差
        recent_confidences = [
            conf.get('final_confidence', 0.5) 
            for conf in list(self.confidence_history)[-10:]
        ]
        if len(recent_confidences) > 1:
            variance = np.var(recent_confidences)
            if variance > thresholds['high_variance']:
                logger.warning(f"⚠️ 置信度方差过高: {variance:.3f}")
    
    def get_status_report(self):
        """获取状态报告"""
        return {
            'timestamp': datetime.now().isoformat(),
            'calibration_factors': self.calibration_factors.copy(),
            'performance_stats': self.performance_stats.copy(),
            'monitoring_enabled': self.monitoring_enabled,
            'auto_calibration_enabled': self.auto_calibration_enabled,
            'history_size': len(self.prediction_history),
            'metrics_size': len(self.performance_metrics),
            'last_calibration': self.last_calibration.isoformat(),
            'config': self.config
        }
    
    def export_calibration_data(self, filename):
        """导出校准数据"""
        try:
            export_data = {
                'status_report': self.get_status_report(),
                'prediction_history': [
                    {
                        'timestamp': p.get('timestamp', datetime.now()).isoformat() if hasattr(p.get('timestamp', datetime.now()), 'isoformat') else str(p.get('timestamp', datetime.now())),
                        'confidence': p.get('confidence', 0),
                        'is_hit': p.get('is_hit', False),
                        'predicted_numbers': p.get('predicted_numbers', []),
                        'actual_numbers': p.get('actual_numbers', [])
                    }
                    for p in list(self.prediction_history)
                ],
                'performance_metrics': [
                    {
                        'timestamp': m['timestamp'].isoformat(),
                        'accuracy': m['accuracy'],
                        'calibration': m['calibration'],
                        'stability': m['stability'],
                        'trend': m['trend']
                    }
                    for m in list(self.performance_metrics)
                ],
                'export_timestamp': datetime.now().isoformat()
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 校准数据已导出到: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 导出校准数据失败: {e}")
            return False
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, 'monitoring_thread') and self.monitoring_thread:
            self.stop_monitoring()

# 便捷函数
def create_enhanced_confidence_adjuster(config=None):
    """创建增强置信度调整器实例"""
    return EnhancedDynamicConfidenceAdjuster(config)

if __name__ == "__main__":
    # 示例使用
    adjuster = create_enhanced_confidence_adjuster()
    
    # 模拟预测结果
    prediction_result = {
        'predicted_numbers': [5, 40],
        'confidence': 0.65,
        'actual_numbers': [5, 15, 25, 35, 40, 45],
        'is_hit': True,
        'timestamp': datetime.now()
    }
    
    # 更新预测结果
    adjuster.update_prediction_result(prediction_result)
    
    # 调整置信度
    context = {
        'data_source': '真实数据',
        'previous_numbers': [1, 15, 23, 30, 35, 42]
    }
    
    adjusted_confidence = adjuster.adjust_confidence(0.5, context)
    print(f"调整后置信度: {adjusted_confidence:.3f}")
    
    # 获取状态报告
    status = adjuster.get_status_report()
    print(f"系统状态: {status['performance_stats']}")
    
    # 停止监控
    adjuster.stop_monitoring()
