#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试集成评分系统是否已应用优化
Test if the integrated scoring system has applied optimizations
"""

import sys
import os

# 添加当前目录到路径
sys.path.append('.')

def test_optimization_parameters():
    """测试优化参数是否已应用"""
    print("🔍 测试集成评分系统优化参数")
    print("="*50)
    
    try:
        # 导入集成系统
        from 集成评分系统的预测系统 import IntegratedPredictionSystem
        
        # 创建系统实例
        system = IntegratedPredictionSystem()
        
        # 检查优化参数
        print("📊 当前系统参数:")
        print(f"  high_freq_boost: {system.optimal_params['high_freq_boost']}")
        print(f"  rising_trend_boost: {system.optimal_params['rising_trend_boost']}")
        print(f"  perturbation: {system.optimal_params['perturbation']}")
        print(f"  max_single_number_freq: {system.optimal_params.get('max_single_number_freq', '未设置')}")
        
        # 检查数字分类
        print(f"\n📊 数字分类:")
        print(f"  high_freq_numbers: {system.high_freq_numbers}")
        print(f"  rising_numbers: {system.rising_numbers}")
        
        # 验证是否为优化参数
        is_optimized = (
            system.optimal_params['high_freq_boost'] == 1.08 and
            system.optimal_params['rising_trend_boost'] == 1.05 and
            system.optimal_params['perturbation'] == 0.12 and
            30 not in system.high_freq_numbers and
            40 not in system.high_freq_numbers and
            30 not in system.rising_numbers
        )
        
        print(f"\n🎯 优化状态检查:")
        print(f"  high_freq_boost = 1.08: {'✅' if system.optimal_params['high_freq_boost'] == 1.08 else '❌'}")
        print(f"  rising_trend_boost = 1.05: {'✅' if system.optimal_params['rising_trend_boost'] == 1.05 else '❌'}")
        print(f"  perturbation = 0.12: {'✅' if system.optimal_params['perturbation'] == 0.12 else '❌'}")
        print(f"  数字30不在high_freq_numbers: {'✅' if 30 not in system.high_freq_numbers else '❌'}")
        print(f"  数字40不在high_freq_numbers: {'✅' if 40 not in system.high_freq_numbers else '❌'}")
        print(f"  数字30不在rising_numbers: {'✅' if 30 not in system.rising_numbers else '❌'}")
        print(f"  多样性约束参数存在: {'✅' if 'max_single_number_freq' in system.optimal_params else '❌'}")
        
        if is_optimized:
            print(f"\n✅ 系统已成功应用优化！")
            return True
        else:
            print(f"\n❌ 系统尚未应用优化或应用不完整")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prediction_diversity():
    """测试预测多样性"""
    print(f"\n🎨 测试预测多样性")
    print("="*30)
    
    try:
        from 集成评分系统的预测系统 import IntegratedPredictionSystem
        
        system = IntegratedPredictionSystem()
        
        # 初始化系统
        if not system.initialize_system():
            print("❌ 系统初始化失败")
            return False
        
        # 进行多次预测测试
        test_numbers = [3, 7, 11, 30, 34, 46]  # 使用一个固定的测试数据
        predictions = []
        
        print("🔮 进行10次预测测试:")
        for i in range(10):
            pred, confidence = system.predict_next_period(test_numbers)
            predictions.append(pred)
            print(f"  第{i+1}次: {pred} (置信度: {confidence:.4f})")
        
        # 分析预测多样性
        all_predicted_numbers = []
        for pred in predictions:
            all_predicted_numbers.extend(pred)
        
        from collections import Counter
        freq = Counter(all_predicted_numbers)
        
        print(f"\n📊 预测数字频率分析:")
        for num, count in freq.most_common():
            percentage = count / len(predictions) * 100
            print(f"  数字{num}: {count}次 ({percentage:.1f}%)")
        
        # 检查30和40的频率
        freq_30 = freq.get(30, 0) / len(predictions) * 100
        freq_40 = freq.get(40, 0) / len(predictions) * 100
        
        print(f"\n🎯 关键数字频率:")
        print(f"  数字30: {freq_30:.1f}%")
        print(f"  数字40: {freq_40:.1f}%")
        
        # 评估多样性
        unique_combinations = len(set(tuple(pred) for pred in predictions))
        diversity_ratio = unique_combinations / len(predictions)
        
        print(f"\n🎨 多样性分析:")
        print(f"  独特组合数: {unique_combinations}/{len(predictions)}")
        print(f"  多样性比例: {diversity_ratio:.1%}")
        
        # 判断是否改善
        if freq_30 < 50 and freq_40 < 50 and diversity_ratio > 0.5:
            print(f"✅ 预测多样性良好！")
            return True
        else:
            print(f"⚠️ 预测多样性仍需改善")
            return False
            
    except Exception as e:
        print(f"❌ 多样性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 集成评分系统优化验证测试")
    print("="*60)
    
    # 1. 测试优化参数
    param_test = test_optimization_parameters()
    
    # 2. 测试预测多样性
    diversity_test = test_prediction_diversity()
    
    # 3. 总结
    print(f"\n🎯 测试结果总结:")
    print("="*30)
    print(f"参数优化测试: {'✅ 通过' if param_test else '❌ 失败'}")
    print(f"多样性测试: {'✅ 通过' if diversity_test else '❌ 失败'}")
    
    if param_test and diversity_test:
        print(f"\n🎉 集成评分系统已成功应用优化！")
        print(f"✅ 系统参数已更新")
        print(f"✅ 预测多样性已改善")
        print(f"✅ 可以正常使用优化后的系统")
    elif param_test:
        print(f"\n⚠️ 参数已优化，但多样性效果需要更多测试")
        print(f"✅ 系统参数已更新")
        print(f"⚠️ 建议进行更多预测测试验证多样性")
    else:
        print(f"\n❌ 优化未完全生效，需要检查")
        print(f"❌ 请检查参数设置和代码修改")

if __name__ == "__main__":
    main()
