
命中率评分系统设计报告
============================================================

系统信息:
==============================
创建日期: 2025-07-15T22:27:46
模型类型: RandomForestClassifier
训练样本: 153
测试样本: 39
特征数量: 20
基础命中率: 35.4%

模型性能:
==============================
训练集AUC: 0.999
测试集AUC: 0.481
整体AUC: 0.958

评分系统设计:
==============================
0-20分: 极低命中概率
  预期命中率: 10-20%
  建议: 不建议
21-40分: 低命中概率
  预期命中率: 20-30%
  建议: 谨慎考虑
41-60分: 中等命中概率
  预期命中率: 30-40%
  建议: 可以考虑
61-80分: 较高命中概率
  预期命中率: 40-50%
  建议: 值得关注
81-100分: 高命中概率
  预期命中率: 50%+
  建议: 重点关注

不同评分阈值分析:
==============================
评分≥50分:
  样本数量: 50 (26.0%)
  实际命中率: 94.0%
  平均预测概率: 64.4%
评分≥60分:
  样本数量: 30 (15.6%)
  实际命中率: 100.0%
  平均预测概率: 70.6%
评分≥70分:
  样本数量: 13 (6.8%)
  实际命中率: 100.0%
  平均预测概率: 75.6%
评分≥80分:
  样本数量: 2 (1.0%)
  实际命中率: 100.0%
  平均预测概率: 81.8%

重要特征 (Top 10):
==============================
confidence_improvement: 0.086
period_normalized: 0.083
composite_factor: 0.082
number_historical_performance: 0.082
original_confidence: 0.071
recent_confidence_trend: 0.071
stability_factor: 0.066
recent_avg_confidence: 0.060
calibration_factor: 0.060
trend_factor: 0.059

关键发现:
==============================
✅ 评分≥70分的预测:
  - 占总预测的 6.8%
  - 实际命中率达到 100.0%
  - 相比基础命中率提升 +64.6个百分点
✅ 模型性能良好 (AUC=0.958)，评分系统可靠

使用建议:
==============================
1. 评分≥60分: 值得重点关注的预测
2. 评分40-60分: 中等风险，可适度关注
3. 评分<40分: 低成功概率，谨慎对待

注意事项:
- 评分基于历史数据训练，未来表现可能有差异
- 建议结合其他分析方法综合判断
- 定期重新训练模型以保持准确性

---
报告生成时间: 2025-07-15 22:27:46
评分系统版本: v1.0
