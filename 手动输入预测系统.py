#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动输入预测系统 - 真实使用场景
用户手动输入当期真实开奖数据，系统预测下期，并保存预测vs实际对比记录
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class ManualPredictionSystem:
    """手动输入预测系统"""
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.prediction_history_file = "prediction_history.json"
        self.comparison_file = "prediction_vs_actual.csv"
        
        # 34.3%方法的最佳参数
        self.optimal_params = {
            'high_freq_boost': 1.15,
            'low_freq_penalty': 0.85,
            'rising_trend_boost': 1.10,
            'falling_trend_penalty': 0.90,
            'perturbation': 0.05
        }
        
        # 数字分类
        self.high_freq_numbers = [5, 15, 3, 40, 30]
        self.low_freq_numbers = [41, 1, 8, 48, 47]
        self.rising_numbers = [30, 39, 4, 8, 22]
        self.falling_numbers = [5, 26, 44, 36, 15]
        
        # 预测历史
        self.prediction_history = []
        self.load_prediction_history()
        
        # 模型组件
        self.enhanced_markov_prob = None
        
    def load_data_and_build_model(self):
        """加载数据并构建模型"""
        print("🔧 加载数据并构建34.3%预测模型...")
        
        try:
            # 加载数据
            self.full_data = pd.read_csv(self.data_file)
            self.full_data = self.full_data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            # 使用2023-2024年作为训练数据
            self.train_data = self.full_data[
                (self.full_data['年份'] >= 2023) & 
                (self.full_data['年份'] <= 2024)
            ].copy()
            
            print(f"✅ 数据加载完成: 训练集{len(self.train_data)}期")
            
            # 构建增强马尔可夫模型
            self.build_enhanced_markov_model()
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def build_enhanced_markov_model(self):
        """构建增强马尔可夫模型"""
        # 基础马尔可夫转移概率
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 计算基础转移概率
        base_markov_prob = {}
        for curr_num in range(1, 50):
            if curr_num in transition_count:
                total = sum(transition_count[curr_num].values())
                smoothed_total = total + 49
                base_markov_prob[curr_num] = {}
                
                for next_num in range(1, 50):
                    count = transition_count[curr_num].get(next_num, 0)
                    base_markov_prob[curr_num][next_num] = (count + 1) / smoothed_total
            else:
                base_markov_prob[curr_num] = {
                    next_num: 1/49 for next_num in range(1, 50)
                }
        
        # 应用增强策略
        self.apply_enhancements(base_markov_prob)
        
        print("✅ 34.3%增强马尔可夫模型构建完成")
    
    def apply_enhancements(self, base_markov_prob):
        """应用增强策略"""
        # 计算权重
        frequency_weights = {}
        trend_weights = {}
        
        for num in range(1, 50):
            if num in self.high_freq_numbers:
                frequency_weights[num] = self.optimal_params['high_freq_boost']
            elif num in self.low_freq_numbers:
                frequency_weights[num] = self.optimal_params['low_freq_penalty']
            else:
                frequency_weights[num] = 1.0
            
            if num in self.rising_numbers:
                trend_weights[num] = self.optimal_params['rising_trend_boost']
            elif num in self.falling_numbers:
                trend_weights[num] = self.optimal_params['falling_trend_penalty']
            else:
                trend_weights[num] = 1.0
        
        # 构建增强马尔可夫概率
        self.enhanced_markov_prob = {}
        for curr_num in base_markov_prob:
            self.enhanced_markov_prob[curr_num] = {}
            total_weight = 0
            
            for next_num, base_prob in base_markov_prob[curr_num].items():
                freq_weight = frequency_weights[next_num]
                trend_weight = trend_weights[next_num]
                combined_weight = freq_weight * trend_weight
                weighted_prob = base_prob * combined_weight
                
                self.enhanced_markov_prob[curr_num][next_num] = weighted_prob
                total_weight += weighted_prob
            
            # 归一化
            for next_num in self.enhanced_markov_prob[curr_num]:
                self.enhanced_markov_prob[curr_num][next_num] /= total_weight
    
    def predict_next_period(self, current_numbers):
        """预测下期数字"""
        if self.enhanced_markov_prob is None:
            raise RuntimeError("模型未初始化")
        
        # 使用固定随机种子确保可重复性
        np.random.seed(42)
        
        number_probs = defaultdict(float)
        total_prob = 0.0
        
        current_set = set(current_numbers)
        
        for curr_num in current_set:
            if curr_num in self.enhanced_markov_prob:
                for next_num, prob in self.enhanced_markov_prob[curr_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        # 添加随机扰动
        perturbation = self.optimal_params['perturbation']
        for num in number_probs:
            noise = np.random.normal(0, perturbation * number_probs[num])
            number_probs[num] = max(0, number_probs[num] + noise)
        
        # 选择前2个数字
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            prediction = [num for num, prob in sorted_numbers[:2]]
            confidence = (sorted_numbers[0][1] + sorted_numbers[1][1]) / 2
        else:
            prediction = [1, 2]
            confidence = 0.02
        
        return prediction, confidence
    
    def load_prediction_history(self):
        """加载预测历史"""
        if os.path.exists(self.prediction_history_file):
            try:
                with open(self.prediction_history_file, 'r', encoding='utf-8') as f:
                    self.prediction_history = json.load(f)
                print(f"✅ 加载预测历史: {len(self.prediction_history)}条记录")
            except:
                self.prediction_history = []
                print("⚠️ 预测历史文件损坏，重新开始")
        else:
            self.prediction_history = []
            print("📝 开始新的预测历史记录")
    
    def save_prediction_history(self):
        """保存预测历史"""
        try:
            with open(self.prediction_history_file, 'w', encoding='utf-8') as f:
                json.dump(self.prediction_history, f, ensure_ascii=False, indent=2)
            print(f"✅ 预测历史已保存: {len(self.prediction_history)}条记录")
        except Exception as e:
            print(f"⚠️ 预测历史保存失败: {e}")
    
    def update_comparison_csv(self):
        """更新预测对比CSV文件"""
        try:
            # 准备数据
            comparison_data = []
            for record in self.prediction_history:
                if record.get('actual_numbers'):  # 只包含有实际结果的记录
                    comparison_data.append({
                        '预测日期': record['prediction_date'],
                        '预测期号': record.get('predicted_period', ''),
                        '当期数字': str(record['current_numbers']),
                        '预测数字': str(record['predicted_numbers']),
                        '实际数字': str(record['actual_numbers']),
                        '命中数量': record['hit_count'],
                        '是否命中': '是' if record['is_hit'] else '否',
                        '置信度': f"{record['confidence']:.3f}",
                        '备注': record.get('notes', '')
                    })
            
            if comparison_data:
                df = pd.DataFrame(comparison_data)
                df.to_csv(self.comparison_file, index=False, encoding='utf-8-sig')
                print(f"✅ 预测对比表已更新: {len(comparison_data)}条记录")
            
        except Exception as e:
            print(f"⚠️ 预测对比表更新失败: {e}")
    
    def input_current_period_data(self):
        """输入当期数据"""
        print("\n📝 请输入当期开奖数据")
        print("=" * 40)
        
        while True:
            try:
                # 输入期号信息
                year = input("请输入年份 (如: 2025): ").strip()
                period = input("请输入期号 (如: 180): ").strip()
                
                if not year.isdigit() or not period.isdigit():
                    print("❌ 年份和期号必须是数字，请重新输入")
                    continue
                
                year = int(year)
                period = int(period)
                
                # 输入开奖数字
                print("请输入6个开奖数字:")
                print("支持格式: 空格分隔(5 12 23 31 40 45) 或 逗号分隔(5,12,23,31,40,45)")
                numbers_input = input("开奖数字: ").strip()

                # 解析数字 - 支持多种分隔符
                if ',' in numbers_input:
                    # 逗号分隔
                    numbers_str = numbers_input.replace(' ', '').split(',')
                else:
                    # 空格分隔
                    numbers_str = numbers_input.split()

                # 转换为整数并处理前导零
                numbers = []
                for num_str in numbers_str:
                    num_str = num_str.strip()
                    if num_str:  # 非空字符串
                        numbers.append(int(num_str))
                
                # 验证输入
                if len(numbers) != 6:
                    print("❌ 必须输入6个数字，请重新输入")
                    continue
                
                if not all(1 <= num <= 49 for num in numbers):
                    print("❌ 数字必须在1-49范围内，请重新输入")
                    continue
                
                if len(set(numbers)) != 6:
                    print("❌ 数字不能重复，请重新输入")
                    continue
                
                return {
                    'year': year,
                    'period': period,
                    'numbers': sorted(numbers)
                }
                
            except ValueError:
                print("❌ 输入格式错误，请重新输入")
            except KeyboardInterrupt:
                print("\n👋 用户取消输入")
                return None
    
    def make_prediction(self, current_data):
        """进行预测"""
        print(f"\n🎯 基于{current_data['year']}年{current_data['period']}期数据进行预测")
        print("=" * 50)
        
        current_numbers = current_data['numbers']
        print(f"当期开奖: {current_numbers}")
        
        # 执行预测
        predicted_numbers, confidence = self.predict_next_period(current_numbers)
        
        print(f"预测下期: {predicted_numbers}")
        print(f"预测置信度: {confidence:.3f}")
        
        # 保存预测记录
        prediction_record = {
            'prediction_date': datetime.now().isoformat(),
            'current_year': current_data['year'],
            'current_period': current_data['period'],
            'predicted_period': f"{current_data['year']}年{current_data['period']+1}期",
            'current_numbers': current_numbers,
            'predicted_numbers': predicted_numbers,
            'confidence': confidence,
            'method': '34.3%增强马尔可夫',
            'actual_numbers': None,  # 待填入
            'hit_count': None,
            'is_hit': None,
            'notes': ''
        }
        
        self.prediction_history.append(prediction_record)
        self.save_prediction_history()
        
        return prediction_record
    
    def input_actual_result(self):
        """输入实际开奖结果"""
        if not self.prediction_history:
            print("⚠️ 没有待验证的预测记录")
            return
        
        # 找到最近的未验证预测
        unverified_predictions = [p for p in self.prediction_history if p['actual_numbers'] is None]
        
        if not unverified_predictions:
            print("✅ 所有预测都已验证")
            return
        
        print(f"\n📊 有{len(unverified_predictions)}个预测待验证")
        print("=" * 50)
        
        for i, pred in enumerate(unverified_predictions):
            print(f"\n预测{i+1}: {pred['predicted_period']}")
            print(f"预测数字: {pred['predicted_numbers']}")
            print(f"预测时间: {pred['prediction_date'][:19]}")
            
            while True:
                choice = input("是否输入此期的实际开奖结果？(y/n/s=跳过): ").strip().lower()
                
                if choice == 'n':
                    return
                elif choice == 's':
                    break
                elif choice == 'y':
                    # 输入实际结果
                    try:
                        print("请输入实际开奖数字 (用空格分隔):")
                        actual_input = input("实际数字: ").strip()
                        actual_numbers = [int(x) for x in actual_input.split()]
                        
                        if len(actual_numbers) != 6:
                            print("❌ 必须输入6个数字")
                            continue
                        
                        if not all(1 <= num <= 49 for num in actual_numbers):
                            print("❌ 数字必须在1-49范围内")
                            continue
                        
                        if len(set(actual_numbers)) != 6:
                            print("❌ 数字不能重复")
                            continue
                        
                        # 计算命中情况
                        predicted_set = set(pred['predicted_numbers'])
                        actual_set = set(actual_numbers)
                        hit_numbers = predicted_set & actual_set
                        hit_count = len(hit_numbers)
                        is_hit = hit_count >= 1
                        
                        # 更新记录
                        pred['actual_numbers'] = sorted(actual_numbers)
                        pred['hit_count'] = hit_count
                        pred['is_hit'] = is_hit
                        pred['hit_numbers'] = list(hit_numbers)
                        
                        # 显示结果
                        print(f"\n🎯 验证结果:")
                        print(f"预测数字: {pred['predicted_numbers']}")
                        print(f"实际数字: {actual_numbers}")
                        print(f"命中数字: {list(hit_numbers) if hit_numbers else '无'}")
                        print(f"命中数量: {hit_count}")
                        print(f"命中状态: {'✅ 命中' if is_hit else '❌ 未命中'}")
                        
                        break
                        
                    except ValueError:
                        print("❌ 输入格式错误，请重新输入")
                    except KeyboardInterrupt:
                        print("\n👋 用户取消输入")
                        return
                else:
                    print("请输入 y/n/s")
        
        # 保存更新的历史
        self.save_prediction_history()
        self.update_comparison_csv()
    
    def show_statistics(self):
        """显示统计信息"""
        if not self.prediction_history:
            print("📊 暂无预测记录")
            return
        
        print(f"\n📊 预测统计信息")
        print("=" * 50)
        
        total_predictions = len(self.prediction_history)
        verified_predictions = [p for p in self.prediction_history if p['actual_numbers'] is not None]
        unverified_predictions = total_predictions - len(verified_predictions)
        
        print(f"总预测次数: {total_predictions}")
        print(f"已验证: {len(verified_predictions)}")
        print(f"待验证: {unverified_predictions}")
        
        if verified_predictions:
            hits = sum(1 for p in verified_predictions if p['is_hit'])
            hit_rate = hits / len(verified_predictions)
            
            print(f"\n命中统计:")
            print(f"命中次数: {hits}")
            print(f"命中率: {hit_rate:.3f} ({hit_rate:.1%})")
            
            # 最近5次预测
            recent_predictions = verified_predictions[-5:]
            print(f"\n最近{len(recent_predictions)}次预测:")
            for i, pred in enumerate(recent_predictions, 1):
                status = "✅" if pred['is_hit'] else "❌"
                print(f"  {i}. {pred['predicted_period']}: 预测{pred['predicted_numbers']} "
                      f"实际{pred['actual_numbers']} {status}")
    
    def main_menu(self):
        """主菜单"""
        while True:
            print(f"\n🎯 手动输入预测系统")
            print("=" * 40)
            print("1. 输入当期数据并预测下期")
            print("2. 输入实际开奖结果验证预测")
            print("3. 查看预测统计")
            print("4. 退出系统")
            
            choice = input("\n请选择操作 (1-4): ").strip()
            
            if choice == '1':
                current_data = self.input_current_period_data()
                if current_data:
                    self.make_prediction(current_data)
            
            elif choice == '2':
                self.input_actual_result()
            
            elif choice == '3':
                self.show_statistics()
            
            elif choice == '4':
                print("👋 感谢使用，再见！")
                break
            
            else:
                print("❌ 无效选择，请重新输入")

def main():
    """主函数"""
    print("🎯 手动输入预测系统 - 真实使用场景")
    print("基于34.3%增强马尔可夫方法")
    print("=" * 60)
    
    system = ManualPredictionSystem()
    
    # 初始化系统
    if not system.load_data_and_build_model():
        print("❌ 系统初始化失败")
        return
    
    print("✅ 系统初始化完成")
    print("\n💡 使用说明:")
    print("1. 输入当期真实开奖数据")
    print("2. 系统预测下期数字")
    print("3. 下期开奖后输入实际结果")
    print("4. 系统自动计算命中率和保存对比记录")
    
    # 启动主菜单
    system.main_menu()

if __name__ == "__main__":
    main()
