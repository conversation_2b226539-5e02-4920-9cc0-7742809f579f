#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化预测系统演示
清楚展示系统如何等待用户输入并进行预测
"""

import pandas as pd
import numpy as np
from collections import defaultdict

def load_data():
    """加载数据"""
    try:
        data = pd.read_csv("data/processed/lottery_data_clean_no_special.csv")
        data = data.sort_values(['年份', '期号']).reset_index(drop=True)
        print(f"✅ 数据加载成功，共{len(data)}期")
        return data
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None

def build_markov_model(data):
    """构建马尔可夫模型"""
    transition_count = defaultdict(lambda: defaultdict(int))
    
    for i in range(len(data) - 1):
        current_numbers = set([data.iloc[i][f'数字{j}'] for j in range(1, 7)])
        next_numbers = set([data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
        
        for curr_num in current_numbers:
            for next_num in next_numbers:
                transition_count[curr_num][next_num] += 1
    
    transition_prob = {}
    for curr_num in transition_count:
        total = sum(transition_count[curr_num].values())
        if total > 0:
            transition_prob[curr_num] = {
                next_num: count / total 
                for next_num, count in transition_count[curr_num].items()
            }
    
    print(f"✅ 马尔可夫模型构建完成，{len(transition_prob)}个状态")
    return transition_prob

def predict_next_period(previous_numbers, transition_prob):
    """预测下一期"""
    print(f"\n🔮 基于数字 {previous_numbers} 进行预测")
    print("-" * 50)
    
    # 计算转移概率
    number_probs = defaultdict(float)
    total_prob = 0.0
    coverage_count = 0
    
    print(f"分析转移概率:")
    for prev_num in previous_numbers:
        if prev_num in transition_prob:
            coverage_count += 1
            print(f"  数字{prev_num}: 找到{len(transition_prob[prev_num])}个转移")
            for next_num, prob in transition_prob[prev_num].items():
                number_probs[next_num] += prob
                total_prob += prob
        else:
            print(f"  数字{prev_num}: 无历史转移数据")
    
    if total_prob > 0:
        for num in number_probs:
            number_probs[num] /= total_prob
    
    print(f"数据覆盖率: {coverage_count}/{len(previous_numbers)} = {coverage_count/len(previous_numbers):.3f}")
    
    # 选择前两个概率最高的数字
    if len(number_probs) >= 2:
        sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
        predicted_numbers = [num for num, prob in sorted_numbers[:2]]
        
        print(f"\n候选数字概率排序:")
        for i, (num, prob) in enumerate(sorted_numbers[:10], 1):
            print(f"  {i:2d}. 数字{num:2d}: {prob:.4f}")
        
        confidence = np.mean([prob for num, prob in sorted_numbers[:2]])
        
        print(f"\n预测结果:")
        print(f"  预测数字: {predicted_numbers}")
        print(f"  基础置信度: {confidence:.4f}")
        
        return predicted_numbers, confidence
    else:
        print(f"⚠️ 概率数据不足，使用默认预测")
        return [1, 2], 0.1

def get_user_input():
    """获取用户输入"""
    print(f"\n📝 请输入开奖数据")
    print("=" * 50)
    
    try:
        # 输入年份和期号
        year_period = input("请输入年份和期号 (如: 2025年181期): ").strip()
        
        if '年' in year_period and '期' in year_period:
            year_str = year_period.split('年')[0]
            period_str = year_period.split('年')[1].replace('期', '')
        else:
            raise ValueError("格式错误")
        
        year = int(year_str)
        period = int(period_str)
        
        # 输入开奖数字
        numbers_input = input("请输入6个开奖数字 (用逗号或空格分隔): ").strip()
        
        # 处理中文逗号
        numbers_input = numbers_input.replace('，', ',').replace('、', ',')
        
        if ',' in numbers_input:
            numbers_str = numbers_input.split(',')
        else:
            numbers_str = numbers_input.split()
        
        numbers = []
        for num_str in numbers_str:
            num_str = num_str.strip()
            if num_str:
                numbers.append(int(num_str))
        
        # 验证输入
        if len(numbers) != 6:
            raise ValueError(f"需要6个数字，您输入了{len(numbers)}个")
        
        if not all(1 <= num <= 49 for num in numbers):
            raise ValueError("数字必须在1-49范围内")
        
        if len(set(numbers)) != 6:
            raise ValueError("数字不能重复")
        
        print(f"✅ 输入验证通过")
        print(f"  期号: {year}年{period}期")
        print(f"  数字: {numbers}")
        
        return numbers, year, period
    
    except Exception as e:
        print(f"❌ 输入错误: {e}")
        return None, None, None

def main():
    """主函数"""
    print("🎯 简化预测系统演示")
    print("清楚展示系统如何等待用户输入")
    print("=" * 80)
    
    # 1. 加载数据
    data = load_data()
    if data is None:
        return
    
    # 2. 构建模型
    transition_prob = build_markov_model(data)
    
    while True:
        print(f"\n" + "="*80)
        
        # 3. 获取用户输入
        numbers, year, period = get_user_input()
        if numbers is None:
            continue
        
        # 4. 进行预测
        predicted_numbers, confidence = predict_next_period(numbers, transition_prob)
        
        # 5. 显示最终结果
        print(f"\n🎯 最终预测结果")
        print("=" * 50)
        print(f"输入期号: {year}年{period}期")
        print(f"输入数字: {numbers}")
        print(f"预测下期: {predicted_numbers}")
        print(f"预测置信度: {confidence:.4f}")
        
        if confidence >= 0.4:
            print(f"投注建议: 建议投注 (置信度较高)")
        else:
            print(f"投注建议: 建议跳过 (置信度较低)")
        
        # 6. 询问是否继续
        choice = input(f"\n是否继续预测? (y/n): ").strip().lower()
        if choice not in ['y', 'yes', '是']:
            break
    
    print(f"\n👋 感谢使用预测系统！")

if __name__ == "__main__":
    main()
