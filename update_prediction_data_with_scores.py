#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新prediction_data.csv文件，添加测试集验证的评分信息
Update prediction_data.csv with test set validation and scoring information

将comprehensive_validation_results中的评分信息更新到prediction_data.csv
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

def load_validation_results():
    """加载验证结果数据"""
    print("📊 加载测试集验证结果...")
    
    validation_file = 'comprehensive_validation_results_20250715_203038.csv'
    if not os.path.exists(validation_file):
        print(f"❌ 验证结果文件不存在: {validation_file}")
        return None
    
    df = pd.read_csv(validation_file, encoding='utf-8-sig')
    print(f"✅ 验证结果加载完成: {len(df)}期数据")
    
    return df

def load_current_prediction_data():
    """加载当前预测数据"""
    print("📊 加载当前预测数据...")
    
    if not os.path.exists('prediction_data.csv'):
        print("❌ prediction_data.csv文件不存在")
        return None
    
    df = pd.read_csv('prediction_data.csv', encoding='utf-8-sig')
    print(f"✅ 当前预测数据加载完成: {len(df)}期数据")
    
    return df

def create_enhanced_prediction_data(validation_df):
    """基于验证数据创建增强的预测数据"""
    print("🔧 创建增强预测数据...")
    
    enhanced_data = []
    
    for _, row in validation_df.iterrows():
        # 解析命中数字
        hit_numbers_str = row['命中数字']
        if hit_numbers_str == '[]':
            hit_numbers = []
        else:
            hit_numbers_str = hit_numbers_str.strip('[]')
            if hit_numbers_str:
                hit_numbers = [int(x.strip()) for x in hit_numbers_str.split(',') if x.strip()]
            else:
                hit_numbers = []
        
        # 构建增强记录
        enhanced_record = {
            '预测日期': '2025-07-15',  # 测试集验证日期
            '预测时间': '20:30:00',
            '当期年份': 2025,
            '当期期号': row['期号'] - 1,  # 当期期号是预测期号-1
            '预测期号': f"2025年{row['期号']}期",
            
            # 当期数字（前一期的实际数字，这里用占位符）
            '当期数字1': '',
            '当期数字2': '',
            '当期数字3': '',
            '当期数字4': '',
            '当期数字5': '',
            '当期数字6': '',
            
            # 预测数字
            '预测数字1': row['预测数字1'],
            '预测数字2': row['预测数字2'],
            '预测置信度': f"{row['原始置信度']:.6f}",
            '预测方法': '34.3%增强马尔可夫',
            
            # 评分信息（新增）
            '预测评分': f"{row['优化置信度'] * 100:.1f}",  # 将置信度转换为评分
            '评分等级': get_score_grade(row['优化置信度'] * 100),
            '评分建议': get_score_recommendation(row['优化置信度'] * 100),
            '评分概率': f"{row['优化置信度']:.3f}",
            
            # 实际结果
            '实际数字1': row['实际数字1'],
            '实际数字2': row['实际数字2'],
            '实际数字3': row['实际数字3'],
            '实际数字4': row['实际数字4'],
            '实际数字5': row['实际数字5'],
            '实际数字6': row['实际数字6'],
            
            # 命中情况
            '命中数量': row['命中数量'],
            '是否命中': row['是否命中'],
            '命中数字': ','.join(map(str, hit_numbers)) if hit_numbers else '',
            '备注': '测试集验证数据'
        }
        
        enhanced_data.append(enhanced_record)
    
    print(f"✅ 增强预测数据创建完成: {len(enhanced_data)}期")
    return enhanced_data

def get_score_grade(score):
    """获取评分等级"""
    if score >= 81:
        return "A+ (高命中概率)"
    elif score >= 61:
        return "A (较高命中概率)"
    elif score >= 41:
        return "B (中等命中概率)"
    elif score >= 21:
        return "C (低命中概率)"
    else:
        return "D (极低命中概率)"

def get_score_recommendation(score):
    """获取评分建议"""
    if score >= 81:
        return "重点关注"
    elif score >= 61:
        return "值得关注"
    elif score >= 41:
        return "可以考虑"
    elif score >= 21:
        return "谨慎考虑"
    else:
        return "不建议"

def update_prediction_data_csv(enhanced_data):
    """更新prediction_data.csv文件"""
    print("💾 更新prediction_data.csv文件...")
    
    # 创建DataFrame
    enhanced_df = pd.DataFrame(enhanced_data)
    
    # 确保列顺序正确
    columns_order = [
        '预测日期', '预测时间', '当期年份', '当期期号', '预测期号',
        '当期数字1', '当期数字2', '当期数字3', '当期数字4', '当期数字5', '当期数字6',
        '预测数字1', '预测数字2', '预测置信度', '预测方法',
        '预测评分', '评分等级', '评分建议', '评分概率',  # 新增评分列
        '实际数字1', '实际数字2', '实际数字3', '实际数字4', '实际数字5', '实际数字6',
        '命中数量', '是否命中', '命中数字', '备注'
    ]
    
    # 重新排列列顺序
    enhanced_df = enhanced_df[columns_order]
    
    # 备份原文件
    if os.path.exists('prediction_data.csv'):
        backup_filename = f'prediction_data_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        os.rename('prediction_data.csv', backup_filename)
        print(f"📁 原文件已备份为: {backup_filename}")
    
    # 保存新文件
    enhanced_df.to_csv('prediction_data.csv', index=False, encoding='utf-8-sig')
    
    print(f"✅ prediction_data.csv文件更新完成")
    print(f"   总记录数: {len(enhanced_df)}")
    print(f"   期号范围: 第{enhanced_df['当期期号'].min()}-{enhanced_df['当期期号'].max()}期")
    
    return enhanced_df

def generate_update_summary(enhanced_df):
    """生成更新摘要"""
    print("\n📊 更新摘要统计")
    print("="*50)
    
    # 基本统计
    total_records = len(enhanced_df)
    hit_records = len(enhanced_df[enhanced_df['是否命中'] == '是'])
    hit_rate = hit_records / total_records if total_records > 0 else 0
    
    print(f"总记录数: {total_records}")
    print(f"命中记录: {hit_records}")
    print(f"命中率: {hit_rate:.1%}")
    
    # 评分统计
    scores = pd.to_numeric(enhanced_df['预测评分'], errors='coerce')
    print(f"\n评分统计:")
    print(f"  平均评分: {scores.mean():.1f}分")
    print(f"  最高评分: {scores.max():.1f}分")
    print(f"  最低评分: {scores.min():.1f}分")
    
    # 评分分布
    high_scores = len(scores[scores >= 70])
    medium_scores = len(scores[(scores >= 50) & (scores < 70)])
    low_scores = len(scores[scores < 50])
    
    print(f"\n评分分布:")
    print(f"  高评分(≥70分): {high_scores}期 ({high_scores/total_records:.1%})")
    print(f"  中评分(50-69分): {medium_scores}期 ({medium_scores/total_records:.1%})")
    print(f"  低评分(<50分): {low_scores}期 ({low_scores/total_records:.1%})")
    
    # 评分与命中率关系
    if high_scores > 0:
        high_score_df = enhanced_df[pd.to_numeric(enhanced_df['预测评分'], errors='coerce') >= 70]
        high_score_hits = len(high_score_df[high_score_df['是否命中'] == '是'])
        high_score_hit_rate = high_score_hits / len(high_score_df)
        print(f"\n高评分命中率: {high_score_hit_rate:.1%} ({high_score_hits}/{len(high_score_df)})")
    
    if medium_scores > 0:
        medium_score_df = enhanced_df[
            (pd.to_numeric(enhanced_df['预测评分'], errors='coerce') >= 50) & 
            (pd.to_numeric(enhanced_df['预测评分'], errors='coerce') < 70)
        ]
        medium_score_hits = len(medium_score_df[medium_score_df['是否命中'] == '是'])
        medium_score_hit_rate = medium_score_hits / len(medium_score_df)
        print(f"中评分命中率: {medium_score_hit_rate:.1%} ({medium_score_hits}/{len(medium_score_df)})")
    
    if low_scores > 0:
        low_score_df = enhanced_df[pd.to_numeric(enhanced_df['预测评分'], errors='coerce') < 50]
        low_score_hits = len(low_score_df[low_score_df['是否命中'] == '是'])
        low_score_hit_rate = low_score_hits / len(low_score_df)
        print(f"低评分命中率: {low_score_hit_rate:.1%} ({low_score_hits}/{len(low_score_df)})")

def main():
    """主函数"""
    print("🔄 更新prediction_data.csv文件（添加评分信息）")
    print("="*60)
    
    try:
        # 1. 加载验证结果
        validation_df = load_validation_results()
        if validation_df is None:
            return
        
        # 2. 创建增强预测数据
        enhanced_data = create_enhanced_prediction_data(validation_df)
        
        # 3. 更新CSV文件
        enhanced_df = update_prediction_data_csv(enhanced_data)
        
        # 4. 生成更新摘要
        generate_update_summary(enhanced_df)
        
        print(f"\n✅ 更新完成！")
        print(f"prediction_data.csv文件已更新，包含:")
        print(f"  - 第2-194期的完整预测数据")
        print(f"  - 每期预测的评分信息")
        print(f"  - 完整的命中验证结果")
        print(f"  - 测试集验证标记")
        
    except Exception as e:
        print(f"❌ 更新过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
