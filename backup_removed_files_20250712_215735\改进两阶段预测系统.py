#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的两阶段数字选择策略分析系统
基于第一次实验的结果，改进第二阶段的过滤机制
重点优化：更智能的12→2数字选择策略
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class ImprovedTwoStagePredictor:
    """
    改进的两阶段预测系统
    阶段1：马尔可夫方法预测12个候选数字
    阶段2：改进的智能过滤机制选择最终2个数字
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.data = None
        self.train_data = None
        self.test_data = None
        self.transition_prob = {}
        self.number_patterns = {}  # 数字模式分析
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            print(f"✅ 数据加载成功: {len(self.data)}期")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def prepare_data_split(self):
        """准备训练和测试数据"""
        print("\n📊 准备数据分割")
        print("=" * 50)
        
        self.train_data = self.data[(self.data['年份'] >= 2023) & (self.data['年份'] < 2025)].copy()
        self.test_data = self.data[(self.data['年份'] == 2025) & (self.data['期号'] <= 182)].copy()
        
        print(f"训练数据: {len(self.train_data)}期 (2023-2024年)")
        print(f"测试数据: {len(self.test_data)}期 (2025年1-182期)")
        
        return len(self.train_data) > 0 and len(self.test_data) > 0
    
    def build_enhanced_models(self):
        """构建增强的预测模型"""
        print("\n🔬 构建增强预测模型")
        
        # 1. 马尔可夫转移矩阵
        transition_count = defaultdict(lambda: defaultdict(int))
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        self.transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                self.transition_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        # 2. 数字模式分析
        self.analyze_number_patterns()
        
        print(f"✅ 模型构建完成")
        print(f"  马尔可夫状态: {len(self.transition_prob)}")
        print(f"  模式分析完成")
        
        return True
    
    def analyze_number_patterns(self):
        """分析数字出现模式"""
        self.number_patterns = {}
        
        for num in range(1, 50):
            # 分析每个数字的出现模式
            appearances = []
            for _, row in self.train_data.iterrows():
                if num in [row[f'数字{j}'] for j in range(1, 7)]:
                    appearances.append(row['期号'])
            
            if len(appearances) >= 2:
                # 计算间隔模式
                intervals = [appearances[i+1] - appearances[i] for i in range(len(appearances)-1)]
                self.number_patterns[num] = {
                    'frequency': len(appearances) / len(self.train_data),
                    'avg_interval': np.mean(intervals) if intervals else 0,
                    'last_appearance': max(appearances) if appearances else 0,
                    'recent_frequency': sum(1 for app in appearances if app > max(appearances) - 20) / 20 if appearances else 0
                }
            else:
                self.number_patterns[num] = {
                    'frequency': 0,
                    'avg_interval': 0,
                    'last_appearance': 0,
                    'recent_frequency': 0
                }
    
    def stage1_predict_12_candidates(self, previous_numbers, current_period):
        """阶段1：预测12个候选数字（改进版）"""
        number_scores = defaultdict(float)
        
        # 评分1：马尔可夫转移概率 (40%)
        for prev_num in previous_numbers:
            if prev_num in self.transition_prob:
                for next_num, prob in self.transition_prob[prev_num].items():
                    number_scores[next_num] += prob * 0.4
        
        # 评分2：基于间隔模式的预测 (30%)
        for num in range(1, 50):
            if num in self.number_patterns:
                pattern = self.number_patterns[num]
                if pattern['last_appearance'] > 0 and pattern['avg_interval'] > 0:
                    expected_next = pattern['last_appearance'] + pattern['avg_interval']
                    interval_score = max(0, 1 - abs(current_period - expected_next) / pattern['avg_interval'])
                    number_scores[num] += interval_score * 0.3
        
        # 评分3：频率权重 (20%)
        for num in range(1, 50):
            if num in self.number_patterns:
                freq_score = self.number_patterns[num]['frequency']
                number_scores[num] += freq_score * 0.2
        
        # 评分4：最近趋势 (10%)
        for num in range(1, 50):
            if num in self.number_patterns:
                trend_score = self.number_patterns[num]['recent_frequency']
                number_scores[num] += trend_score * 0.1
        
        # 选择得分最高的12个数字
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        candidates = [num for num, score in sorted_numbers[:12]]
        
        # 确保有12个候选数字
        if len(candidates) < 12:
            all_numbers = list(range(1, 50))
            for num in all_numbers:
                if len(candidates) >= 12:
                    break
                if num not in candidates:
                    candidates.append(num)
        
        avg_confidence = np.mean([score for num, score in sorted_numbers[:12]]) if sorted_numbers else 0.3
        
        return candidates[:12], avg_confidence
    
    def stage2_intelligent_selection(self, candidates, previous_numbers, current_period):
        """阶段2：智能选择最终2个数字（大幅改进）"""
        if len(candidates) < 2:
            return candidates + [1, 2][:2-len(candidates)], 0.1
        
        final_scores = {}
        
        for num in candidates:
            score = 0.0
            
            # 评分1：马尔可夫强度 (35%)
            markov_strength = 0.0
            for prev_num in previous_numbers:
                if prev_num in self.transition_prob and num in self.transition_prob[prev_num]:
                    markov_strength += self.transition_prob[prev_num][num]
            score += markov_strength * 0.35
            
            # 评分2：时间模式匹配度 (25%)
            if num in self.number_patterns:
                pattern = self.number_patterns[num]
                if pattern['avg_interval'] > 0:
                    time_since_last = current_period - pattern['last_appearance']
                    pattern_match = 1.0 / (1.0 + abs(time_since_last - pattern['avg_interval']) / pattern['avg_interval'])
                    score += pattern_match * 0.25
            
            # 评分3：避免重复（与前期数字的差异性）(20%)
            diversity_score = 1.0
            for prev_num in previous_numbers:
                if abs(num - prev_num) <= 3:  # 相近数字降分
                    diversity_score *= 0.8
            score += diversity_score * 0.2
            
            # 评分4：数字位置平衡 (10%)
            position_score = 0.5  # 基础分
            if 1 <= num <= 16:
                position_score = 0.6  # 小数字区间
            elif 17 <= num <= 33:
                position_score = 0.7  # 中数字区间
            else:
                position_score = 0.5  # 大数字区间
            score += position_score * 0.1
            
            # 评分5：奇偶平衡考虑 (10%)
            odd_even_score = 0.5
            prev_odd_count = sum(1 for pn in previous_numbers if pn % 2 == 1)
            if prev_odd_count <= 2 and num % 2 == 1:  # 需要更多奇数
                odd_even_score = 0.7
            elif prev_odd_count >= 4 and num % 2 == 0:  # 需要更多偶数
                odd_even_score = 0.7
            score += odd_even_score * 0.1
            
            final_scores[num] = score
        
        # 选择得分最高的2个数字，但要确保一定的多样性
        sorted_candidates = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 智能选择：避免选择过于相似的数字
        final_2 = [sorted_candidates[0][0]]  # 选择得分最高的
        
        for num, score in sorted_candidates[1:]:
            if abs(num - final_2[0]) >= 5:  # 确保数字间有一定差距
                final_2.append(num)
                break
        
        # 如果没找到合适的第二个数字，选择得分第二高的
        if len(final_2) < 2:
            final_2.append(sorted_candidates[1][0])
        
        # 计算置信度
        top_scores = [final_scores[num] for num in final_2]
        confidence = np.mean(top_scores) if top_scores else 0.1
        
        return final_2, confidence
    
    def predict_single_period(self, previous_numbers, current_period):
        """预测单期的2个数字"""
        # 阶段1：预测12个候选数字
        candidates, stage1_confidence = self.stage1_predict_12_candidates(previous_numbers, current_period)
        
        # 阶段2：智能选择最终2个数字
        final_2, stage2_confidence = self.stage2_intelligent_selection(candidates, previous_numbers, current_period)
        
        # 综合置信度
        overall_confidence = (stage1_confidence + stage2_confidence) / 2
        
        return {
            'final_prediction': final_2,
            'candidates': candidates,
            'stage1_confidence': stage1_confidence,
            'stage2_confidence': stage2_confidence,
            'overall_confidence': overall_confidence
        }
    
    def validate_improved_method(self):
        """验证改进的两阶段方法"""
        print("\n🔬 验证改进的两阶段预测方法")
        print("=" * 50)
        
        predictions = []
        correct_predictions = 0
        stage1_hits = 0
        
        for idx, test_row in self.test_data.iterrows():
            period_num = test_row['期号']
            actual_numbers = set([test_row[f'数字{j}'] for j in range(1, 7)])
            
            # 获取前一期数字
            if idx == self.test_data.index[0]:
                prev_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                prev_numbers = set([self.test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            # 预测
            result = self.predict_single_period(prev_numbers, period_num)
            
            # 评估
            stage1_hit_count = len(set(result['candidates']) & actual_numbers)
            stage1_hits += stage1_hit_count
            
            final_hit_count = len(set(result['final_prediction']) & actual_numbers)
            is_success = final_hit_count >= 1
            
            if is_success:
                correct_predictions += 1
            
            predictions.append({
                'period': period_num,
                'predicted_2': result['final_prediction'],
                'candidates_12': result['candidates'],
                'actual': list(actual_numbers),
                'stage1_hits': stage1_hit_count,
                'final_hits': final_hit_count,
                'success': is_success,
                'overall_confidence': result['overall_confidence']
            })
        
        # 计算性能指标
        total_periods = len(predictions)
        success_rate = correct_predictions / total_periods
        avg_stage1_hits = stage1_hits / total_periods
        
        print(f"✅ 改进两阶段方法验证完成")
        print(f"  测试期数: {total_periods}")
        print(f"  阶段1平均命中: {avg_stage1_hits:.2f}/6 ({avg_stage1_hits/6*100:.1f}%)")
        print(f"  最终成功预测: {correct_predictions}")
        print(f"  最终成功率: {success_rate:.3f} ({success_rate*100:.1f}%)")
        print(f"  基准性能: 29.2%")
        print(f"  性能提升: {(success_rate - 0.292)*100:+.1f}个百分点")
        
        return {
            'predictions': predictions,
            'success_rate': success_rate,
            'avg_stage1_hits': avg_stage1_hits,
            'improvement_vs_baseline': success_rate - 0.292
        }

def main():
    """主函数"""
    print("🎯 改进的两阶段数字选择策略分析系统")
    print("基于第一次实验结果的改进版本")
    print("=" * 60)
    
    # 初始化系统
    predictor = ImprovedTwoStagePredictor()
    
    # 1. 加载数据
    if not predictor.load_data():
        return
    
    # 2. 准备数据分割
    if not predictor.prepare_data_split():
        return
    
    # 3. 构建增强模型
    if not predictor.build_enhanced_models():
        return
    
    # 4. 验证改进方法
    results = predictor.validate_improved_method()
    
    if results:
        # 5. 统计显著性检验
        baseline_rate = 0.292
        observed_rate = results['success_rate']
        n = len(results['predictions'])
        
        try:
            result = stats.binomtest(int(observed_rate * n), n, baseline_rate, alternative='greater')
            p_value = result.pvalue
        except AttributeError:
            from scipy.stats import binom
            p_value = 1 - binom.cdf(int(observed_rate * n) - 1, n, baseline_rate)
        
        print(f"\n📊 统计显著性检验")
        print(f"  观察成功率: {observed_rate:.3f}")
        print(f"  基准成功率: {baseline_rate:.3f}")
        print(f"  样本数量: {n}")
        print(f"  p值: {p_value:.4f}")
        print(f"  显著性(p<0.05): {'是' if p_value < 0.05 else '否'}")
        
        # 6. 保存结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f"改进两阶段预测验证结果_{timestamp}.csv"
        summary_file = f"改进两阶段预测摘要_{timestamp}.txt"
        
        df_results = pd.DataFrame(results['predictions'])
        df_results.to_csv(results_file, index=False, encoding='utf-8')
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(f"改进两阶段预测方法验证摘要\n")
            f.write(f"=" * 40 + "\n")
            f.write(f"成功率: {results['success_rate']:.3f} ({results['success_rate']*100:.1f}%)\n")
            f.write(f"阶段1平均命中: {results['avg_stage1_hits']:.2f}/6\n")
            f.write(f"相对基准提升: {results['improvement_vs_baseline']*100:+.1f}个百分点\n")
            f.write(f"统计显著性p值: {float(p_value):.4f}\n")
            f.write(f"显著性(p<0.05): {'是' if p_value < 0.05 else '否'}\n")
        
        print(f"\n✅ 结果已保存:")
        print(f"  详细结果: {results_file}")
        print(f"  摘要结果: {summary_file}")
        
        # 7. 结论
        print(f"\n🎯 改进两阶段预测方法分析结论")
        print("=" * 50)
        if results['improvement_vs_baseline'] > 0 and p_value < 0.05:
            print("✅ 改进两阶段方法显著优于基准方法")
        elif results['improvement_vs_baseline'] > 0:
            print("⚠️ 改进两阶段方法略优于基准，但未达到统计显著性")
        else:
            print("❌ 改进两阶段方法仍未能改善基准性能")

if __name__ == "__main__":
    main()
