#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后的预测系统
Optimized prediction system with improved diversity and balanced weights

基于频繁预测分析的改进建议实施优化
"""

import pandas as pd
import numpy as np
from collections import Counter, defaultdict
import random
from datetime import datetime
import pickle
import os

class OptimizedMarkovPredictor:
    """优化后的马尔可夫预测器"""
    
    def __init__(self):
        self.transition_matrix = defaultdict(lambda: defaultdict(int))
        self.number_counts = defaultdict(int)
        self.total_transitions = 0
        
        # 优化后的参数设置
        self.optimized_params = {
            'high_freq_boost': 1.08,      # 从1.15降低到1.08
            'rising_trend_boost': 1.05,   # 从1.10降低到1.05
            'falling_trend_boost': 0.95,  # 从0.90提升到0.95
            'low_freq_boost': 0.92,       # 从0.85提升到0.92
            'perturbation': 0.12,         # 从0.05增加到0.12
            'max_single_number_freq': 0.35  # 新增：单个数字最大预测频率
        }
        
        # 重新评估的数字分类（基于长期历史数据）
        self.optimized_categories = {
            'high_freq_numbers': [3, 15, 5, 2, 43],      # 移除30和40，基于实际频率
            'low_freq_numbers': [41, 1, 8, 48, 47, 46],  # 扩展低频数字
            'rising_numbers': [39, 4, 8, 22, 16],        # 移除30，添加16
            'falling_numbers': [26, 44, 36, 5, 49]       # 重新评估
        }
        
        # 预测历史记录（用于多样性控制）
        self.prediction_history = []
        
    def load_data(self, file_path):
        """加载数据"""
        df = pd.read_csv(file_path)
        return df
    
    def build_transition_matrix(self, df):
        """构建转移矩阵"""
        print("🔧 构建优化的转移矩阵...")
        
        for i in range(len(df) - 1):
            current_numbers = [df.iloc[i][f'数字{j}'] for j in range(1, 7)]
            next_numbers = [df.iloc[i + 1][f'数字{j}'] for j in range(1, 7)]
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    self.transition_matrix[curr_num][next_num] += 1
                    self.total_transitions += 1
                    
            for num in current_numbers:
                self.number_counts[num] += 1
        
        print(f"✅ 转移矩阵构建完成，总转移数: {self.total_transitions}")
    
    def calculate_base_probabilities(self, current_numbers):
        """计算基础马尔可夫概率"""
        probabilities = defaultdict(float)
        
        for curr_num in current_numbers:
            if curr_num in self.transition_matrix:
                total_transitions_from_curr = sum(self.transition_matrix[curr_num].values())
                
                for next_num, count in self.transition_matrix[curr_num].items():
                    prob = count / total_transitions_from_curr if total_transitions_from_curr > 0 else 0
                    probabilities[next_num] += prob
        
        # 归一化
        total_prob = sum(probabilities.values())
        if total_prob > 0:
            for num in probabilities:
                probabilities[num] /= total_prob
        
        return probabilities
    
    def apply_optimized_weights(self, probabilities):
        """应用优化后的权重"""
        weighted_probs = probabilities.copy()
        
        for num in weighted_probs:
            weight = 1.0
            
            # 高频数字权重（降低）
            if num in self.optimized_categories['high_freq_numbers']:
                weight *= self.optimized_params['high_freq_boost']
            
            # 低频数字权重（提升）
            elif num in self.optimized_categories['low_freq_numbers']:
                weight *= self.optimized_params['low_freq_boost']
            
            # 上升趋势权重（降低）
            if num in self.optimized_categories['rising_numbers']:
                weight *= self.optimized_params['rising_trend_boost']
            
            # 下降趋势权重（提升）
            elif num in self.optimized_categories['falling_numbers']:
                weight *= self.optimized_params['falling_trend_boost']
            
            weighted_probs[num] *= weight
        
        return weighted_probs
    
    def apply_diversity_constraint(self, probabilities):
        """应用多样性约束"""
        if len(self.prediction_history) < 10:
            return probabilities
        
        # 统计最近预测的数字频率
        recent_predictions = []
        for pred in self.prediction_history[-20:]:  # 最近20期
            recent_predictions.extend(pred)
        
        recent_freq = Counter(recent_predictions)
        total_recent = len(self.prediction_history[-20:])
        
        # 对频率过高的数字进行惩罚
        adjusted_probs = probabilities.copy()
        max_freq = self.optimized_params['max_single_number_freq']
        
        for num, prob in probabilities.items():
            current_freq = recent_freq.get(num, 0) / total_recent if total_recent > 0 else 0
            
            if current_freq > max_freq:
                # 频率过高，降低概率
                penalty = 1.0 - (current_freq - max_freq) * 2
                penalty = max(0.3, penalty)  # 最低保持30%概率
                adjusted_probs[num] *= penalty
        
        return adjusted_probs
    
    def add_perturbation(self, probabilities):
        """添加随机扰动（增强）"""
        perturbed_probs = probabilities.copy()
        perturbation_strength = self.optimized_params['perturbation']
        
        for num in perturbed_probs:
            # 增强随机扰动
            random_factor = 1 + random.uniform(-perturbation_strength, perturbation_strength)
            perturbed_probs[num] *= random_factor
        
        return perturbed_probs
    
    def predict_next_period(self, current_numbers):
        """预测下期数字"""
        # 1. 计算基础概率
        base_probs = self.calculate_base_probabilities(current_numbers)
        
        # 2. 应用优化权重
        weighted_probs = self.apply_optimized_weights(base_probs)
        
        # 3. 应用多样性约束
        constrained_probs = self.apply_diversity_constraint(weighted_probs)
        
        # 4. 添加随机扰动
        final_probs = self.add_perturbation(constrained_probs)
        
        # 5. 归一化
        total_prob = sum(final_probs.values())
        if total_prob > 0:
            for num in final_probs:
                final_probs[num] /= total_prob
        
        # 6. 选择前两个概率最高的数字
        sorted_probs = sorted(final_probs.items(), key=lambda x: x[1], reverse=True)
        
        if len(sorted_probs) >= 2:
            pred_num1, prob1 = sorted_probs[0]
            pred_num2, prob2 = sorted_probs[1]
            confidence = (prob1 + prob2) / 2
        else:
            pred_num1, pred_num2, confidence = 30, 3, 0.02
        
        # 记录预测历史
        self.prediction_history.append([pred_num1, pred_num2])
        
        return pred_num1, pred_num2, confidence

def run_optimized_predictions():
    """运行优化后的预测"""
    print("🚀 运行优化后的预测系统")
    print("="*50)
    
    # 加载数据
    predictor = OptimizedMarkovPredictor()
    df = predictor.load_data('data/processed/lottery_data_clean_no_special.csv')
    
    # 构建转移矩阵
    predictor.build_transition_matrix(df)
    
    # 筛选2025年数据进行预测验证
    df_2025 = df[df['年份'] == 2025].copy()
    df_2025 = df_2025.sort_values('期号').reset_index(drop=True)
    
    print(f"✅ 2025年数据: {len(df_2025)}期 (第{df_2025['期号'].min()}-{df_2025['期号'].max()}期)")
    
    # 进行预测
    predictions = []
    
    for i in range(len(df_2025) - 1):  # 预测下一期
        current_period = df_2025.iloc[i]
        next_period = df_2025.iloc[i + 1]
        
        # 当期数字
        current_numbers = [current_period[f'数字{j}'] for j in range(1, 7)]
        
        # 预测下期
        pred_num1, pred_num2, confidence = predictor.predict_next_period(current_numbers)
        
        # 实际下期数字
        actual_numbers = [next_period[f'数字{j}'] for j in range(1, 7)]
        
        # 计算命中情况
        predicted_numbers = [pred_num1, pred_num2]
        hit_numbers = list(set(predicted_numbers) & set(actual_numbers))
        hit_count = len(hit_numbers)
        is_hit = hit_count >= 1
        
        # 记录预测结果
        prediction = {
            '当期年份': current_period['年份'],
            '当期期号': current_period['期号'],
            '预测期号': f"{next_period['年份']}年{next_period['期号']}期",
            '当期数字': current_numbers,
            '预测数字1': pred_num1,
            '预测数字2': pred_num2,
            '预测置信度': confidence,
            '实际数字': actual_numbers,
            '命中数量': hit_count,
            '是否命中': '是' if is_hit else '否',
            '命中数字': hit_numbers,
            '预测方法': '优化马尔可夫'
        }
        
        predictions.append(prediction)
        
        # 显示进度
        if (i + 1) % 20 == 0:
            print(f"  预测进度: {i+1}/{len(df_2025)-1}")
    
    print(f"✅ 优化预测完成: {len(predictions)}期")
    
    return predictions

def analyze_optimization_results(optimized_predictions):
    """分析优化结果"""
    print(f"\n📊 分析优化结果")
    print("="*40)
    
    # 基本统计
    total_predictions = len(optimized_predictions)
    hit_predictions = len([p for p in optimized_predictions if p['是否命中'] == '是'])
    hit_rate = hit_predictions / total_predictions
    
    print(f"优化后预测统计:")
    print(f"  总预测期数: {total_predictions}")
    print(f"  命中期数: {hit_predictions}")
    print(f"  命中率: {hit_rate:.1%}")
    
    # 预测数字频率分析
    all_predictions = []
    for pred in optimized_predictions:
        all_predictions.extend([pred['预测数字1'], pred['预测数字2']])
    
    pred_freq = Counter(all_predictions)
    
    print(f"\n优化后预测频率 (Top 10):")
    for num, freq in pred_freq.most_common(10):
        percentage = freq / total_predictions * 100
        print(f"  数字{num:2d}: {freq:3d}次 ({percentage:5.1f}%)")
    
    # 特别关注30和40
    freq_30 = pred_freq.get(30, 0)
    freq_40 = pred_freq.get(40, 0)
    
    print(f"\n🎯 重点数字分析:")
    print(f"  数字30: {freq_30}次 ({freq_30/total_predictions*100:.1f}%)")
    print(f"  数字40: {freq_40}次 ({freq_40/total_predictions*100:.1f}%)")
    print(f"  30+40合计: {freq_30+freq_40}次 ({(freq_30+freq_40)/total_predictions*100:.1f}%)")
    
    # 多样性分析
    unique_predictions = len(set([(p['预测数字1'], p['预测数字2']) for p in optimized_predictions]))
    diversity_ratio = unique_predictions / total_predictions
    
    print(f"\n多样性分析:")
    print(f"  独特预测组合: {unique_predictions}")
    print(f"  多样性比例: {diversity_ratio:.1%}")
    
    return {
        'total_predictions': total_predictions,
        'hit_predictions': hit_predictions,
        'hit_rate': hit_rate,
        'pred_freq': pred_freq,
        'diversity_ratio': diversity_ratio
    }

def compare_with_original():
    """与原始系统对比"""
    print(f"\n📈 与原始系统对比")
    print("="*40)
    
    # 加载原始预测数据
    original_df = pd.read_csv('prediction_data.csv')
    
    # 原始系统统计
    original_total = len(original_df)
    original_hits = len(original_df[original_df['是否命中'] == '是'])
    original_hit_rate = original_hits / original_total
    
    # 原始预测频率
    original_predictions = []
    for _, row in original_df.iterrows():
        original_predictions.extend([row['预测数字1'], row['预测数字2']])
    
    original_freq = Counter(original_predictions)
    original_30_freq = original_freq.get(30, 0) / original_total * 100
    original_40_freq = original_freq.get(40, 0) / original_total * 100
    
    print(f"原始系统:")
    print(f"  总期数: {original_total}")
    print(f"  命中期数: {original_hits}")
    print(f"  命中率: {original_hit_rate:.1%}")
    print(f"  数字30频率: {original_30_freq:.1f}%")
    print(f"  数字40频率: {original_40_freq:.1f}%")
    print(f"  30+40合计: {original_30_freq + original_40_freq:.1f}%")
    
    return {
        'original_hit_rate': original_hit_rate,
        'original_30_freq': original_30_freq,
        'original_40_freq': original_40_freq
    }

def save_optimized_results(predictions):
    """保存优化结果"""
    print(f"\n💾 保存优化结果")
    print("="*40)
    
    # 转换为DataFrame
    results = []
    for pred in predictions:
        result = {
            '预测日期': datetime.now().strftime('%Y-%m-%d'),
            '预测时间': datetime.now().strftime('%H:%M:%S'),
            '当期年份': pred['当期年份'],
            '当期期号': pred['当期期号'],
            '预测期号': pred['预测期号'],
            '当期数字1': pred['当期数字'][0] if len(pred['当期数字']) > 0 else '',
            '当期数字2': pred['当期数字'][1] if len(pred['当期数字']) > 1 else '',
            '当期数字3': pred['当期数字'][2] if len(pred['当期数字']) > 2 else '',
            '当期数字4': pred['当期数字'][3] if len(pred['当期数字']) > 3 else '',
            '当期数字5': pred['当期数字'][4] if len(pred['当期数字']) > 4 else '',
            '当期数字6': pred['当期数字'][5] if len(pred['当期数字']) > 5 else '',
            '预测数字1': pred['预测数字1'],
            '预测数字2': pred['预测数字2'],
            '预测置信度': f"{pred['预测置信度']:.6f}",
            '预测方法': pred['预测方法'],
            '实际数字1': pred['实际数字'][0] if len(pred['实际数字']) > 0 else '',
            '实际数字2': pred['实际数字'][1] if len(pred['实际数字']) > 1 else '',
            '实际数字3': pred['实际数字'][2] if len(pred['实际数字']) > 2 else '',
            '实际数字4': pred['实际数字'][3] if len(pred['实际数字']) > 3 else '',
            '实际数字5': pred['实际数字'][4] if len(pred['实际数字']) > 4 else '',
            '实际数字6': pred['实际数字'][5] if len(pred['实际数字']) > 5 else '',
            '命中数量': pred['命中数量'],
            '是否命中': pred['是否命中'],
            '命中数字': ','.join(map(str, pred['命中数字'])) if pred['命中数字'] else '',
            '备注': '优化算法预测'
        }
        results.append(result)
    
    # 保存到CSV
    df_results = pd.DataFrame(results)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f'optimized_prediction_results_{timestamp}.csv'
    df_results.to_csv(filename, index=False, encoding='utf-8-sig')
    
    print(f"✅ 优化结果已保存: {filename}")
    return filename

def main():
    """主函数"""
    print("🔧 优化预测系统并重新预测1-197期")
    print("="*60)
    
    try:
        # 1. 运行优化预测
        optimized_predictions = run_optimized_predictions()
        
        # 2. 分析优化结果
        opt_stats = analyze_optimization_results(optimized_predictions)
        
        # 3. 与原始系统对比
        original_stats = compare_with_original()
        
        # 4. 保存优化结果
        result_filename = save_optimized_results(optimized_predictions)
        
        # 5. 生成对比报告
        print(f"\n🎯 优化效果总结:")
        print(f"="*40)
        
        hit_rate_change = opt_stats['hit_rate'] - original_stats['original_hit_rate']
        freq_30_change = (opt_stats['pred_freq'].get(30, 0) / opt_stats['total_predictions'] * 100) - original_stats['original_30_freq']
        freq_40_change = (opt_stats['pred_freq'].get(40, 0) / opt_stats['total_predictions'] * 100) - original_stats['original_40_freq']
        
        print(f"命中率变化: {original_stats['original_hit_rate']:.1%} → {opt_stats['hit_rate']:.1%} ({hit_rate_change:+.1%})")
        print(f"数字30频率: {original_stats['original_30_freq']:.1f}% → {opt_stats['pred_freq'].get(30, 0) / opt_stats['total_predictions'] * 100:.1f}% ({freq_30_change:+.1f}%)")
        print(f"数字40频率: {original_stats['original_40_freq']:.1f}% → {opt_stats['pred_freq'].get(40, 0) / opt_stats['total_predictions'] * 100:.1f}% ({freq_40_change:+.1f}%)")
        print(f"预测多样性: {opt_stats['diversity_ratio']:.1%}")
        
        if hit_rate_change >= 0 and abs(freq_30_change) > 10:
            print(f"✅ 优化成功：保持命中率的同时显著提升多样性")
        elif hit_rate_change > 0:
            print(f"✅ 优化成功：命中率提升")
        else:
            print(f"⚠️ 需要进一步调整：命中率有所下降")
        
    except Exception as e:
        print(f"❌ 优化过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
