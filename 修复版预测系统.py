#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版预测系统
修复过度集中在[2,49]组合的问题，增强预测多样性

核心修复措施：
1. 大幅降低频率模型权重：40% → 25%
2. 显著提升探索率：20% → 45%
3. 强化多样性约束：强制每5期必须有不同组合
4. 增加随机性：引入更多随机因素
5. 动态调整机制：基于近期预测调整策略
"""

import pandas as pd
import numpy as np
from datetime import datetime
from collections import defaultdict, Counter
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class FixedPredictionSystem:
    """修复版预测系统"""
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        
        # 基于550期数据的科学发现
        self.scientific_findings = {
            'high_freq_numbers': [5, 15, 3, 40, 30],
            'low_freq_numbers': [41, 1, 8, 48, 47],
            'medium_freq_numbers': [25, 35, 20, 10, 45, 33, 18, 12, 28, 38],
            'freq_difference': 0.47,
            'avg_sum': 149.88,
            'sum_std': 33.22
        }
        
        # 修复后的配置
        self.config = {
            # 大幅降低频率权重，增加多样性
            'model_weights': {
                'frequency': 0.25,      # 从40%降至25%
                'statistical': 0.25,    # 保持25%
                'random': 0.25,         # 新增随机模型25%
                'diversity': 0.25       # 新增多样性模型25%
            },
            
            # 大幅提升多样性
            'diversity': {
                'exploration_rate': 0.45,    # 从20%提升至45%
                'candidate_pool_size': 40,   # 扩大候选池
                'min_distance': 8,           # 增加最小距离
                'force_diversity_every': 3,  # 每3期强制不同组合
                'max_repeat_count': 2        # 最多连续重复2次
            },
            
            # 动态调整机制
            'adaptive': {
                'recent_window': 10,         # 考虑最近10期
                'diversity_penalty': 0.3,    # 重复组合惩罚
                'novelty_bonus': 0.2         # 新颖组合奖励
            }
        }
        
        # 数据存储
        self.data = {}
        self.models = {}
        self.recent_predictions = []  # 记录最近预测
        self.results = {}
        
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("📊 加载和准备数据...")
        
        try:
            # 加载数据
            full_data = pd.read_csv(self.data_file, encoding='utf-8')
            full_data = full_data.dropna().sort_values(['年份', '期号'])
            
            # 数据分割：训练集1-150期，预测151-204期
            train_condition = (
                (full_data['年份'] < 2025) |
                ((full_data['年份'] == 2025) & (full_data['期号'] <= 150))
            )
            
            predict_condition = (
                (full_data['年份'] == 2025) &
                (full_data['期号'] >= 151) &
                (full_data['期号'] <= 204)
            )
            
            self.data['train'] = full_data[train_condition].copy()
            self.data['predict'] = full_data[predict_condition].copy()
            
            print(f"   训练集: {len(self.data['train'])} 期")
            print(f"   预测集: {len(self.data['predict'])} 期")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def extract_features(self, data):
        """提取特征"""
        features = []
        
        for _, row in data.iterrows():
            numbers = [int(row[f'数字{i}']) for i in range(1, 7)]
            
            feature = {
                'sum': sum(numbers),
                'span': max(numbers) - min(numbers),
                'odd_count': sum(1 for n in numbers if n % 2 == 1),
                'mean': np.mean(numbers),
                'std': np.std(numbers),
                
                # 频率特征
                'high_freq_count': sum(1 for n in numbers if n in self.scientific_findings['high_freq_numbers']),
                'low_freq_count': sum(1 for n in numbers if n in self.scientific_findings['low_freq_numbers']),
                'medium_freq_count': sum(1 for n in numbers if n in self.scientific_findings['medium_freq_numbers']),
                
                # 分布特征
                'small_count': sum(1 for n in numbers if n <= 16),
                'large_count': sum(1 for n in numbers if n >= 34),
                
                # 时间特征
                'year': int(row['年份']),
                'period': int(row['期号']),
                'month': self.estimate_month(int(row['年份']), int(row['期号'])),
                
                # 原始数字
                'numbers': numbers
            }
            
            features.append(feature)
        
        return features
    
    def estimate_month(self, year, period):
        """估算月份"""
        if year == 2024:
            month = min(12, max(1, int((period - 1) / 30.5) + 1))
        else:  # 2025年
            month = min(12, max(1, int((period - 1) / 15) + 1))
        return month
    
    def build_enhanced_models(self, train_features):
        """构建增强模型"""
        print("\n🔧 构建增强多样性模型...")
        
        try:
            # 1. 频率模型（权重降低）
            self.models['frequency'] = self.build_frequency_model(train_features)
            
            # 2. 统计模型
            self.models['statistical'] = self.build_statistical_model(train_features)
            
            # 3. 随机模型（新增）
            self.models['random'] = self.build_random_model()
            
            # 4. 多样性模型（新增）
            self.models['diversity'] = self.build_diversity_model(train_features)
            
            print(f"   构建模型: {len(self.models)} 个")
            
            return True
            
        except Exception as e:
            print(f"❌ 模型构建失败: {e}")
            return False
    
    def build_frequency_model(self, train_features):
        """构建频率模型（权重降低）"""
        number_counts = defaultdict(int)
        total_count = 0
        
        for feature in train_features:
            for num in feature['numbers']:
                number_counts[num] += 1
                total_count += 1
        
        frequencies = {}
        for num in range(1, 50):
            base_freq = number_counts[num] / total_count if total_count > 0 else 0
            
            # 大幅减少频率偏差调整
            if num in self.scientific_findings['high_freq_numbers']:
                frequencies[num] = base_freq * 1.05  # 从1.1降至1.05
            elif num in self.scientific_findings['low_freq_numbers']:
                frequencies[num] = base_freq * 0.95  # 从0.9提升至0.95
            else:
                frequencies[num] = base_freq
        
        return {'type': 'frequency', 'frequencies': frequencies}
    
    def build_statistical_model(self, train_features):
        """构建统计模型"""
        recent_features = train_features[-30:]  # 使用最近30期
        
        avg_sum = np.mean([f['sum'] for f in recent_features])
        avg_span = np.mean([f['span'] for f in recent_features])
        avg_odd = np.mean([f['odd_count'] for f in recent_features])
        
        return {
            'type': 'statistical',
            'avg_sum': avg_sum,
            'avg_span': avg_span,
            'avg_odd': avg_odd
        }
    
    def build_random_model(self):
        """构建随机模型（新增）"""
        return {
            'type': 'random',
            'seed': np.random.randint(1000, 9999)
        }
    
    def build_diversity_model(self, train_features):
        """构建多样性模型（新增）"""
        # 分析历史数字分布，找出被忽视的数字
        all_numbers = []
        for feature in train_features:
            all_numbers.extend(feature['numbers'])
        
        number_counts = Counter(all_numbers)
        
        # 找出中低频数字作为多样性候选
        sorted_counts = sorted(number_counts.items(), key=lambda x: x[1])
        diversity_candidates = [num for num, count in sorted_counts[:30]]  # 前30个低频数字
        
        return {
            'type': 'diversity',
            'candidates': diversity_candidates
        }
    
    def enhanced_prediction(self, target_year, target_period, train_features):
        """增强预测方法"""
        try:
            # 1. 检查是否需要强制多样性
            force_diversity = self.should_force_diversity()
            
            # 2. 获取各模型预测
            predictions = {}
            
            for model_name, model in self.models.items():
                pred = self.get_model_prediction(model, target_year, target_period)
                predictions[model_name] = pred
            
            # 3. 集成预测（考虑多样性）
            if force_diversity:
                ensemble_pred = self.force_diverse_prediction(predictions)
            else:
                ensemble_pred = self.ensemble_predictions(predictions)
            
            # 4. 最终多样性增强
            final_pred = self.apply_diversity_enhancement(ensemble_pred, target_year, target_period)
            
            # 5. 记录预测历史
            self.recent_predictions.append(tuple(sorted(final_pred['numbers'])))
            if len(self.recent_predictions) > self.config['adaptive']['recent_window']:
                self.recent_predictions.pop(0)
            
            return final_pred
            
        except Exception as e:
            print(f"⚠️ 预测失败: {e}")
            return self.fallback_prediction()
    
    def should_force_diversity(self):
        """判断是否需要强制多样性"""
        if len(self.recent_predictions) < 2:
            return False
        
        # 检查最近的重复情况
        recent_count = len(self.recent_predictions)
        force_every = self.config['diversity']['force_diversity_every']
        max_repeat = self.config['diversity']['max_repeat_count']
        
        # 如果达到强制多样性周期
        if recent_count % force_every == 0:
            return True
        
        # 如果最近重复次数过多
        if recent_count >= max_repeat:
            last_combo = self.recent_predictions[-1]
            repeat_count = sum(1 for combo in self.recent_predictions[-max_repeat:] if combo == last_combo)
            if repeat_count >= max_repeat:
                return True
        
        return False
    
    def get_model_prediction(self, model, year, period):
        """获取单个模型预测"""
        model_type = model['type']
        
        if model_type == 'frequency':
            return self.frequency_prediction(model)
        elif model_type == 'statistical':
            return self.statistical_prediction(model, year, period)
        elif model_type == 'random':
            return self.random_prediction(model)
        elif model_type == 'diversity':
            return self.diversity_prediction(model)
        else:
            return {'numbers': [25, 30], 'confidence': 0.1}
    
    def frequency_prediction(self, model):
        """频率模型预测"""
        frequencies = model['frequencies']
        sorted_nums = sorted(frequencies.items(), key=lambda x: x[1], reverse=True)
        
        # 从前30个候选中选择，增加随机性
        candidates = [num for num, freq in sorted_nums[:30]]
        
        # 随机选择第一个数字
        first_num = np.random.choice(candidates[:15])
        
        # 选择第二个数字，确保距离
        second_candidates = [num for num in candidates 
                           if abs(num - first_num) >= self.config['diversity']['min_distance']]
        
        if second_candidates:
            second_num = np.random.choice(second_candidates[:10])
        else:
            second_num = np.random.choice(candidates[15:25])
        
        selected = sorted([first_num, second_num])
        confidence = np.mean([frequencies[num] for num in selected])
        
        return {'numbers': selected, 'confidence': confidence}
    
    def statistical_prediction(self, model, year, period):
        """统计模型预测"""
        target_sum = model['avg_sum'] / 3  # 调整为2个数字
        
        # 增加随机性，不再寻找最优组合
        candidates = list(range(1, 50))
        np.random.shuffle(candidates)
        
        # 随机选择满足基本条件的组合
        for _ in range(100):  # 最多尝试100次
            num1 = np.random.choice(candidates[:25])
            num2 = np.random.choice(candidates[25:])
            
            if abs(num1 - num2) >= self.config['diversity']['min_distance']:
                combination_sum = num1 + num2
                if abs(combination_sum - target_sum) <= target_sum * 0.5:  # 放宽条件
                    selected = sorted([num1, num2])
                    confidence = 1.0 / (1.0 + abs(combination_sum - target_sum))
                    return {'numbers': selected, 'confidence': confidence}
        
        # 如果找不到合适组合，随机选择
        selected = sorted(np.random.choice(candidates, 2, replace=False))
        return {'numbers': selected, 'confidence': 0.2}
    
    def random_prediction(self, model):
        """随机模型预测"""
        np.random.seed(model['seed'] + len(self.recent_predictions))
        
        # 完全随机选择
        candidates = list(range(1, 50))
        selected = sorted(np.random.choice(candidates, 2, replace=False))
        
        return {'numbers': selected, 'confidence': 0.15}
    
    def diversity_prediction(self, model):
        """多样性模型预测"""
        candidates = model['candidates']
        
        # 优先选择最近未使用的数字
        recent_used = set()
        for combo in self.recent_predictions[-5:]:  # 最近5期
            recent_used.update(combo)
        
        unused_candidates = [num for num in candidates if num not in recent_used]
        
        if len(unused_candidates) >= 2:
            selected = sorted(np.random.choice(unused_candidates, 2, replace=False))
        else:
            # 如果未使用的不够，从所有候选中选择
            selected = sorted(np.random.choice(candidates, 2, replace=False))
        
        return {'numbers': selected, 'confidence': 0.25}
    
    def force_diverse_prediction(self, predictions):
        """强制多样性预测"""
        print("   🎨 强制多样性预测")
        
        # 获取最近使用的组合
        recent_combos = set(self.recent_predictions[-5:])
        
        # 尝试生成不同的组合
        for _ in range(50):  # 最多尝试50次
            # 随机选择一个模型的预测作为基础
            base_model = np.random.choice(list(predictions.keys()))
            base_pred = predictions[base_model]
            
            # 生成变化
            new_numbers = self.generate_diverse_numbers(base_pred['numbers'])
            new_combo = tuple(sorted(new_numbers))
            
            if new_combo not in recent_combos:
                return {
                    'numbers': new_numbers,
                    'confidence': base_pred['confidence'] * 0.8,  # 降低置信度
                    'method': f'Force_Diverse_{base_model}'
                }
        
        # 如果无法生成不同组合，完全随机
        candidates = list(range(1, 50))
        new_numbers = sorted(np.random.choice(candidates, 2, replace=False))
        
        return {
            'numbers': new_numbers,
            'confidence': 0.1,
            'method': 'Force_Random'
        }
    
    def generate_diverse_numbers(self, base_numbers):
        """基于基础数字生成多样化数字"""
        candidates = list(range(1, 50))
        
        # 移除基础数字附近的数字
        excluded = set()
        for num in base_numbers:
            for i in range(max(1, num-3), min(50, num+4)):
                excluded.add(i)
        
        available = [num for num in candidates if num not in excluded]
        
        if len(available) >= 2:
            return sorted(np.random.choice(available, 2, replace=False))
        else:
            # 如果可选数字不够，随机选择
            return sorted(np.random.choice(candidates, 2, replace=False))
    
    def ensemble_predictions(self, predictions):
        """集成预测"""
        if not predictions:
            return self.fallback_prediction()
        
        # 使用修复后的权重
        weights = self.config['model_weights']
        
        # 加权投票
        number_votes = defaultdict(float)
        total_confidence = 0
        
        for model_name, pred in predictions.items():
            weight = weights.get(model_name, 0.25)
            confidence = pred['confidence']
            
            for num in pred['numbers']:
                number_votes[num] += weight * confidence
            
            total_confidence += confidence
        
        # 应用多样性惩罚
        number_votes = self.apply_diversity_penalty(number_votes)
        
        # 选择得票最高的数字
        sorted_votes = sorted(number_votes.items(), key=lambda x: x[1], reverse=True)
        
        selected = [sorted_votes[0][0]]
        for num, votes in sorted_votes[1:]:
            if abs(num - selected[0]) >= self.config['diversity']['min_distance']:
                selected.append(num)
                break
        
        if len(selected) < 2:
            selected.append(sorted_votes[1][0])
        
        avg_confidence = total_confidence / len(predictions) if predictions else 0.1
        
        return {
            'numbers': sorted(selected),
            'confidence': avg_confidence,
            'method': 'Enhanced_Ensemble'
        }
    
    def apply_diversity_penalty(self, number_votes):
        """应用多样性惩罚"""
        penalty = self.config['adaptive']['diversity_penalty']
        
        # 对最近使用过的数字进行惩罚
        recent_used = set()
        for combo in self.recent_predictions[-3:]:  # 最近3期
            recent_used.update(combo)
        
        for num in recent_used:
            if num in number_votes:
                number_votes[num] *= (1 - penalty)
        
        return number_votes
    
    def apply_diversity_enhancement(self, prediction, year, period):
        """应用多样性增强"""
        diversity_config = self.config['diversity']
        
        # 高概率进行探索性调整
        if np.random.random() < diversity_config['exploration_rate']:
            # 45%概率进行探索性调整
            candidates = list(range(1, 50))
            np.random.shuffle(candidates)
            
            if len(prediction['numbers']) >= 2:
                # 随机替换一个数字
                replace_idx = np.random.randint(0, 2)
                keep_num = prediction['numbers'][1 - replace_idx]
                
                # 寻找满足距离要求的替换数字
                for candidate in candidates:
                    if abs(candidate - keep_num) >= diversity_config['min_distance']:
                        prediction['numbers'][replace_idx] = candidate
                        prediction['numbers'] = sorted(prediction['numbers'])
                        prediction['confidence'] *= 0.9  # 略微降低置信度
                        break
        
        return prediction
    
    def fallback_prediction(self):
        """备用预测"""
        # 随机选择，避免固定组合
        candidates = list(range(1, 50))
        selected = sorted(np.random.choice(candidates, 2, replace=False))
        
        return {
            'numbers': selected,
            'confidence': 0.15,
            'method': 'Fallback_Random'
        }
    
    def predict_periods_151_204_fixed(self):
        """修复版预测151-204期"""
        print("\n🎯 修复版预测151-204期...")
        
        try:
            # 提取训练特征
            train_features = self.extract_features(self.data['train'])
            
            # 构建增强模型
            model_success = self.build_enhanced_models(train_features)
            if not model_success:
                print("❌ 模型构建失败")
                return []
            
            # 预测151-204期
            predictions = []
            
            for _, row in self.data['predict'].iterrows():
                year = int(row['年份'])
                period = int(row['期号'])
                actual_numbers = [int(row[f'数字{i}']) for i in range(1, 7)]
                
                # 预测
                prediction = self.enhanced_prediction(year, period, train_features)
                
                # 计算命中
                pred_set = set(prediction['numbers'])
                actual_set = set(actual_numbers)
                hit_count = len(pred_set & actual_set)
                
                result = {
                    'year': year,
                    'period': period,
                    'period_id': f"{year}年{period}期",
                    'predicted_numbers': prediction['numbers'],
                    'actual_numbers': actual_numbers,
                    'confidence': prediction['confidence'],
                    'hit_count': hit_count,
                    'is_hit': hit_count > 0,
                    'hit_rate': hit_count / 2.0,
                    'pred_sum': sum(prediction['numbers']),
                    'actual_sum': sum(actual_numbers),
                    'method': prediction['method']
                }
                
                predictions.append(result)
                
                # 实时输出
                status = "✅" if result['is_hit'] else "❌"
                print(f"   {result['period_id']}: 预测{prediction['numbers']} → 实际{actual_numbers[:2]} {status}")
            
            return predictions
            
        except Exception as e:
            print(f"❌ 预测失败: {e}")
            return []
    
    def analyze_fixed_performance(self, predictions):
        """分析修复后的性能"""
        print(f"\n📊 修复版性能分析...")
        
        if not predictions:
            print("❌ 无预测结果")
            return
        
        # 基本统计
        total_periods = len(predictions)
        hit_periods = sum(1 for p in predictions if p['is_hit'])
        hit_rate = hit_periods / total_periods if total_periods > 0 else 0
        
        # 多样性分析
        pred_combinations = [tuple(sorted(p['predicted_numbers'])) for p in predictions]
        unique_combinations = len(set(pred_combinations))
        diversity_rate = unique_combinations / total_periods if total_periods > 0 else 0
        
        # 重复组合分析
        combo_counts = Counter(pred_combinations)
        most_common = combo_counts.most_common(3)
        
        print(f"   总预测期数: {total_periods}")
        print(f"   命中期数: {hit_periods}")
        print(f"   命中率: {hit_rate:.1%}")
        print(f"   唯一组合数: {unique_combinations}")
        print(f"   多样性率: {diversity_rate:.1%}")
        print(f"   最常用组合: {most_common}")
        
        # 与原版对比
        print(f"\n📈 与原版对比:")
        print(f"   原版多样性率: 18.9% → 修复版: {diversity_rate:.1%}")
        print(f"   原版[2,49]占比: 83.0% → 修复版最高占比: {most_common[0][1]/total_periods:.1%}")
        
        return {
            'hit_rate': hit_rate,
            'diversity_rate': diversity_rate,
            'unique_combinations': unique_combinations,
            'most_common_combo': most_common[0] if most_common else None
        }
    
    def save_fixed_results_to_csv(self, predictions):
        """保存修复后的结果到CSV文件"""
        print(f"\n💾 保存修复版结果到CSV文件...")
        
        try:
            if not predictions:
                print("❌ 无预测结果可保存")
                return None
            
            # 准备CSV数据
            csv_data = []
            for result in predictions:
                csv_data.append({
                    '年份': result['year'],
                    '期号': result['period'],
                    '期号标识': result['period_id'],
                    '预测数字1': result['predicted_numbers'][0],
                    '预测数字2': result['predicted_numbers'][1],
                    '预测组合': str(result['predicted_numbers']),
                    '预测置信度': round(result['confidence'], 4),
                    '实际数字1': result['actual_numbers'][0],
                    '实际数字2': result['actual_numbers'][1],
                    '实际数字3': result['actual_numbers'][2],
                    '实际数字4': result['actual_numbers'][3],
                    '实际数字5': result['actual_numbers'][4],
                    '实际数字6': result['actual_numbers'][5],
                    '实际组合': str(result['actual_numbers']),
                    '命中数量': result['hit_count'],
                    '是否命中': '是' if result['is_hit'] else '否',
                    '命中率': round(result['hit_rate'], 4),
                    '预测数字和': result['pred_sum'],
                    '实际数字和': result['actual_sum'],
                    '预测方法': result['method'],
                    '生成时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
            
            # 保存CSV文件，覆盖原文件
            csv_file = "执行建议预测结果151-204期_20250725_004620.csv"
            df = pd.DataFrame(csv_data)
            df.to_csv(csv_file, index=False, encoding='utf-8')
            
            print(f"✅ 修复版CSV文件已保存: {csv_file}")
            return csv_file
            
        except Exception as e:
            print(f"❌ CSV保存失败: {e}")
            return None
    
    def run_fixed_system(self):
        """运行修复版系统"""
        print("🚀 修复版预测系统")
        print("修复过度集中在[2,49]组合的问题")
        print("=" * 80)
        
        print("🔧 核心修复措施:")
        print("   ✅ 大幅降低频率模型权重：40% → 25%")
        print("   ✅ 显著提升探索率：20% → 45%")
        print("   ✅ 强化多样性约束：每3期强制不同组合")
        print("   ✅ 增加随机性：新增随机模型和多样性模型")
        print("   ✅ 动态调整机制：基于近期预测调整策略")
        
        # 1. 数据加载
        if not self.load_and_prepare_data():
            return False
        
        # 2. 修复版预测151-204期
        predictions = self.predict_periods_151_204_fixed()
        
        if not predictions:
            print("❌ 预测失败")
            return False
        
        # 3. 性能分析
        performance = self.analyze_fixed_performance(predictions)
        
        # 4. 保存修复版CSV文件
        csv_file = self.save_fixed_results_to_csv(predictions)
        
        # 5. 最终评估
        if performance:
            hit_rate = performance['hit_rate']
            diversity_rate = performance['diversity_rate']
            
            print(f"\n🏆 修复版最终评估:")
            print(f"   命中率: {hit_rate:.1%}")
            print(f"   多样性率: {diversity_rate:.1%}")
            print(f"   唯一组合数: {performance['unique_combinations']}")
            
            if csv_file:
                print(f"   修复版文件: {csv_file}")
            
            # 修复效果评估
            if diversity_rate > 0.5:  # 50%以上多样性
                fix_grade = "修复成功"
            elif diversity_rate > 0.3:  # 30%以上多样性
                fix_grade = "显著改善"
            else:
                fix_grade = "仍需改进"
            
            print(f"   修复效果: {fix_grade}")
            
            return diversity_rate > 0.3
        
        return False

def main():
    """主函数"""
    print("🔬 修复版预测系统")
    print("修复过度集中在[2,49]组合的问题，增强预测多样性")
    print("=" * 80)
    
    # 创建修复版系统
    system = FixedPredictionSystem()
    
    # 运行修复版系统
    success = system.run_fixed_system()
    
    if success:
        print(f"\n🎉 修复版预测系统运行成功！")
        print(f"已成功修复多样性问题并更新CSV文件。")
    else:
        print(f"\n⚠️ 修复版系统仍需进一步调整")

if __name__ == "__main__":
    main()
