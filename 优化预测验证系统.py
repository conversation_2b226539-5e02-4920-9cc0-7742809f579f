#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化预测验证系统
通过分析不同训练集配置和方法权重组合，找到最优预测配置
目标：提升2025年151-203期的预测命中率从19.8%至25%+

优化策略：
1. 训练集权重分析：测试不同时间窗口组合
2. 方法权重优化：优化Best_Ensemble_Method权重配置
3. 交叉验证：使用2025年1-150期作为验证集
4. 最终测试：应用最优配置于2025年151-203期
"""

import pandas as pd
import numpy as np
from datetime import datetime
from collections import defaultdict, Counter
from itertools import product
import warnings
warnings.filterwarnings('ignore')

class OptimizedPredictionValidationSystem:
    """优化预测验证系统"""
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.output_file = "优化预测验证结果.csv"
        self.analysis_file = "优化配置分析报告.csv"
        
        # 数据存储
        self.full_data = None
        self.optimization_results = []
        self.best_config = None
        
        # 训练集配置选项
        self.training_configs = [
            {'name': '仅2024-2025前150期', 'years': [2024, 2025], 'max_period': 150},
            {'name': '2023-2025前150期', 'years': [2023, 2024, 2025], 'max_period': 150},
            {'name': '2022-2025前150期', 'years': [2022, 2023, 2024, 2025], 'max_period': 150},
            {'name': '全部2021-2025前150期', 'years': [2021, 2022, 2023, 2024, 2025], 'max_period': 150}
        ]
        
        # 方法权重配置选项
        self.weight_configs = [
            {'name': '当前配置', 'frequency': 0.4, 'markov': 0.35, 'statistical': 0.25},
            {'name': '频率主导', 'frequency': 0.6, 'markov': 0.25, 'statistical': 0.15},
            {'name': '马尔可夫主导', 'frequency': 0.25, 'markov': 0.6, 'statistical': 0.15},
            {'name': '统计主导', 'frequency': 0.25, 'markov': 0.15, 'statistical': 0.6},
            {'name': '均衡配置', 'frequency': 0.33, 'markov': 0.33, 'statistical': 0.34},
            {'name': '频率+马尔可夫', 'frequency': 0.5, 'markov': 0.4, 'statistical': 0.1},
            {'name': '频率+统计', 'frequency': 0.5, 'markov': 0.1, 'statistical': 0.4},
            {'name': '马尔可夫+统计', 'frequency': 0.2, 'markov': 0.5, 'statistical': 0.3}
        ]
        
    def load_data(self):
        """加载数据"""
        try:
            self.full_data = pd.read_csv(self.data_file, encoding='utf-8')
            self.full_data = self.full_data.dropna()
            print(f"✅ 加载完整数据: {len(self.full_data)} 条记录")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def get_training_data(self, config):
        """根据配置获取训练数据"""
        try:
            # 筛选年份
            year_mask = self.full_data['年份'].isin(config['years'])
            
            # 筛选期号（2025年只取前150期）
            period_mask = (
                (self.full_data['年份'] < 2025) |
                ((self.full_data['年份'] == 2025) & (self.full_data['期号'] <= config['max_period']))
            )
            
            training_data = self.full_data[year_mask & period_mask].copy()
            return training_data
            
        except Exception as e:
            print(f"⚠️ 获取训练数据失败: {e}")
            return None
    
    def build_prediction_model(self, training_data, weight_config):
        """构建预测模型"""
        try:
            # 1. 频率分析模型
            all_numbers = []
            for _, row in training_data.iterrows():
                for i in range(1, 7):
                    num = row[f'数字{i}']
                    if pd.notna(num):
                        all_numbers.append(int(num))
            
            number_freq = Counter(all_numbers)
            total_count = sum(number_freq.values())
            number_probs = {num: count/total_count for num, count in number_freq.items()}
            
            # 2. 马尔可夫转移模型
            transition_probs = defaultdict(lambda: defaultdict(float))
            
            for i in range(1, len(training_data)):
                prev_numbers = []
                curr_numbers = []
                
                for j in range(1, 7):
                    prev_num = training_data.iloc[i-1][f'数字{j}']
                    curr_num = training_data.iloc[i][f'数字{j}']
                    if pd.notna(prev_num):
                        prev_numbers.append(int(prev_num))
                    if pd.notna(curr_num):
                        curr_numbers.append(int(curr_num))
                
                for prev_num in prev_numbers:
                    for curr_num in curr_numbers:
                        transition_probs[prev_num][curr_num] += 1
            
            # 归一化转移概率
            for prev_num in transition_probs:
                total = sum(transition_probs[prev_num].values())
                if total > 0:
                    for curr_num in transition_probs[prev_num]:
                        transition_probs[prev_num][curr_num] /= total
            
            # 3. 统计特征模型
            sums = []
            ranges = []
            
            for _, row in training_data.iterrows():
                numbers = []
                for i in range(1, 7):
                    num = row[f'数字{i}']
                    if pd.notna(num):
                        numbers.append(int(num))
                
                if len(numbers) >= 2:
                    sums.append(sum(numbers))
                    ranges.append(max(numbers) - min(numbers))
            
            statistical_features = {
                'avg_sum': np.mean(sums) if sums else 150,
                'avg_range': np.mean(ranges) if ranges else 40,
                'std_sum': np.std(sums) if sums else 30
            }
            
            return {
                'number_probs': number_probs,
                'transition_probs': transition_probs,
                'statistical_features': statistical_features,
                'weights': weight_config,
                'training_size': len(training_data)
            }
            
        except Exception as e:
            print(f"⚠️ 模型构建失败: {e}")
            return None
    
    def predict_with_model(self, model, recent_numbers):
        """使用模型进行预测"""
        try:
            # 1. 频率分析预测
            freq_candidates = sorted(model['number_probs'].items(), 
                                   key=lambda x: x[1], reverse=True)[:20]
            
            # 2. 马尔可夫预测
            markov_probs = defaultdict(float)
            for curr_num in recent_numbers:
                if curr_num in model['transition_probs']:
                    for next_num, prob in model['transition_probs'][curr_num].items():
                        markov_probs[next_num] += prob
            
            # 3. 统计特征预测
            target_sum = model['statistical_features']['avg_sum']
            
            # 4. 集成预测（使用配置的权重）
            final_scores = defaultdict(float)
            weights = model['weights']
            
            # 频率权重
            for num, prob in freq_candidates:
                final_scores[num] += prob * weights['frequency']
            
            # 马尔可夫权重
            total_markov = sum(markov_probs.values())
            if total_markov > 0:
                for num, prob in markov_probs.items():
                    final_scores[num] += (prob / total_markov) * weights['markov']
            
            # 统计权重
            target_avg = target_sum / 6
            for num in range(1, 50):
                distance_factor = 1.0 / (1.0 + abs(num - target_avg) / 15)
                final_scores[num] += distance_factor * weights['statistical']
            
            # 选择得分最高的2个数字
            sorted_scores = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
            predicted_numbers = [num for num, _ in sorted_scores[:2]]
            
            # 计算置信度
            confidence = np.mean([score for _, score in sorted_scores[:2]])
            
            return {
                'predicted_numbers': predicted_numbers,
                'confidence': confidence
            }
            
        except Exception as e:
            print(f"⚠️ 预测失败: {e}")
            return {
                'predicted_numbers': [25, 30],
                'confidence': 0.15
            }
    
    def evaluate_configuration(self, training_config, weight_config):
        """评估特定配置的性能"""
        try:
            # 获取训练数据
            training_data = self.get_training_data(training_config)
            if training_data is None or len(training_data) == 0:
                return None
            
            # 构建模型
            model = self.build_prediction_model(training_data, weight_config)
            if model is None:
                return None
            
            # 在验证集（2025年1-150期）上测试
            validation_data = self.full_data[
                (self.full_data['年份'] == 2025) & 
                (self.full_data['期号'] >= 1) & 
                (self.full_data['期号'] <= 150)
            ].copy()
            
            if len(validation_data) == 0:
                return None
            
            # 逐期预测验证
            correct_predictions = 0
            total_predictions = 0
            detailed_results = []
            
            for i, val_row in validation_data.iterrows():
                try:
                    # 获取当前期信息
                    current_period = int(val_row['期号'])
                    actual_numbers = [int(val_row[f'数字{j}']) for j in range(1, 7)]
                    
                    # 获取历史数据（包括训练数据 + 验证集中当前期之前的数据）
                    historical_data = training_data.copy()
                    
                    # 添加验证集中当前期之前的数据
                    prev_validation = validation_data[validation_data['期号'] < current_period]
                    if len(prev_validation) > 0:
                        historical_data = pd.concat([historical_data, prev_validation], ignore_index=True)
                    
                    # 获取最近一期数字作为预测输入
                    if len(historical_data) > 0:
                        last_row = historical_data.iloc[-1]
                        recent_numbers = [int(last_row[f'数字{j}']) for j in range(1, 7)]
                    else:
                        recent_numbers = [25, 30, 35, 40, 45, 49]
                    
                    # 重新构建模型（包含最新历史数据）
                    current_model = self.build_prediction_model(historical_data, weight_config)
                    if current_model is None:
                        continue
                    
                    # 进行预测
                    prediction_result = self.predict_with_model(current_model, recent_numbers)
                    
                    # 检查命中情况
                    pred_set = set(prediction_result['predicted_numbers'])
                    actual_set = set(actual_numbers)
                    hit_count = len(pred_set & actual_set)
                    is_hit = hit_count > 0
                    
                    if is_hit:
                        correct_predictions += 1
                    total_predictions += 1
                    
                    detailed_results.append({
                        'period': current_period,
                        'predicted': prediction_result['predicted_numbers'],
                        'actual': actual_numbers,
                        'hit_count': hit_count,
                        'is_hit': is_hit,
                        'confidence': prediction_result['confidence']
                    })
                    
                except Exception as e:
                    print(f"⚠️ 验证第{current_period}期失败: {e}")
                    continue
            
            # 计算命中率
            hit_rate = correct_predictions / total_predictions if total_predictions > 0 else 0
            
            return {
                'training_config': training_config['name'],
                'weight_config': weight_config['name'],
                'training_size': len(training_data),
                'validation_periods': total_predictions,
                'correct_predictions': correct_predictions,
                'hit_rate': hit_rate,
                'detailed_results': detailed_results,
                'weights': weight_config
            }
            
        except Exception as e:
            print(f"⚠️ 配置评估失败: {e}")
            return None
    
    def run_optimization_analysis(self):
        """运行优化分析"""
        print("🚀 开始优化分析...")
        print("=" * 70)
        
        if not self.load_data():
            return False
        
        print(f"📊 优化配置:")
        print(f"   训练集配置: {len(self.training_configs)} 种")
        print(f"   权重配置: {len(self.weight_configs)} 种")
        print(f"   总组合数: {len(self.training_configs) * len(self.weight_configs)} 种")
        print(f"   验证集: 2025年1-150期")
        print(f"   测试集: 2025年151-203期")
        
        # 测试所有配置组合
        self.optimization_results = []
        total_combinations = len(self.training_configs) * len(self.weight_configs)
        current_combination = 0
        
        for training_config in self.training_configs:
            for weight_config in self.weight_configs:
                current_combination += 1
                print(f"\n🔍 测试配置 {current_combination}/{total_combinations}:")
                print(f"   训练集: {training_config['name']}")
                print(f"   权重: {weight_config['name']} (频率:{weight_config['frequency']:.0%}, 马尔可夫:{weight_config['markov']:.0%}, 统计:{weight_config['statistical']:.0%})")
                
                result = self.evaluate_configuration(training_config, weight_config)
                
                if result:
                    self.optimization_results.append(result)
                    print(f"   ✅ 命中率: {result['hit_rate']:.1%} ({result['correct_predictions']}/{result['validation_periods']})")
                else:
                    print(f"   ❌ 评估失败")
        
        # 找到最佳配置
        if self.optimization_results:
            self.best_config = max(self.optimization_results, key=lambda x: x['hit_rate'])
            
            print(f"\n🏆 最佳配置:")
            print(f"   训练集: {self.best_config['training_config']}")
            print(f"   权重: {self.best_config['weight_config']}")
            print(f"   验证集命中率: {self.best_config['hit_rate']:.1%}")
            print(f"   训练数据量: {self.best_config['training_size']} 期")
            
            # 保存优化分析结果
            self.save_optimization_analysis()
            
            # 应用最佳配置进行最终测试
            return self.apply_best_configuration()
        else:
            print("❌ 未找到有效配置")
            return False
    
    def save_optimization_analysis(self):
        """保存优化分析结果"""
        try:
            # 准备分析数据
            analysis_data = []
            for result in self.optimization_results:
                analysis_data.append({
                    '训练集配置': result['training_config'],
                    '权重配置': result['weight_config'],
                    '频率权重': result['weights']['frequency'],
                    '马尔可夫权重': result['weights']['markov'],
                    '统计权重': result['weights']['statistical'],
                    '训练数据量': result['training_size'],
                    '验证期数': result['validation_periods'],
                    '命中次数': result['correct_predictions'],
                    '命中率': result['hit_rate'],
                    '是否最佳': result == self.best_config
                })
            
            # 保存到CSV
            analysis_df = pd.DataFrame(analysis_data)
            analysis_df = analysis_df.sort_values('命中率', ascending=False)
            analysis_df.to_csv(self.analysis_file, index=False, encoding='utf-8')
            
            print(f"✅ 优化分析结果已保存到: {self.analysis_file}")
            
        except Exception as e:
            print(f"❌ 保存优化分析失败: {e}")
    
    def apply_best_configuration(self):
        """应用最佳配置进行最终测试"""
        print(f"\n🎯 应用最佳配置进行最终测试...")
        print("=" * 50)
        
        try:
            # 找到对应的训练和权重配置
            best_training_config = None
            best_weight_config = None
            
            for tc in self.training_configs:
                if tc['name'] == self.best_config['training_config']:
                    best_training_config = tc
                    break
            
            for wc in self.weight_configs:
                if wc['name'] == self.best_config['weight_config']:
                    best_weight_config = wc
                    break
            
            if not best_training_config or not best_weight_config:
                print("❌ 未找到最佳配置参数")
                return False
            
            # 获取完整训练数据（包括2025年1-150期）
            full_training_data = self.get_training_data(best_training_config)
            validation_data = self.full_data[
                (self.full_data['年份'] == 2025) & 
                (self.full_data['期号'] >= 1) & 
                (self.full_data['期号'] <= 150)
            ]
            
            # 合并训练数据和验证数据作为最终训练集
            final_training_data = pd.concat([full_training_data, validation_data], ignore_index=True)
            
            # 获取测试数据（2025年151-203期）
            test_data = self.full_data[
                (self.full_data['年份'] == 2025) & 
                (self.full_data['期号'] >= 151) & 
                (self.full_data['期号'] <= 203)
            ].copy()
            
            print(f"📊 最终测试配置:")
            print(f"   最终训练数据: {len(final_training_data)} 期")
            print(f"   测试数据: {len(test_data)} 期 (2025年151-203期)")
            print(f"   最佳权重: 频率{best_weight_config['frequency']:.0%}, 马尔可夫{best_weight_config['markov']:.0%}, 统计{best_weight_config['statistical']:.0%}")
            
            # 对测试集进行预测
            final_results = []
            
            for i, test_row in test_data.iterrows():
                try:
                    current_period = int(test_row['期号'])
                    actual_numbers = [int(test_row[f'数字{j}']) for j in range(1, 7)]
                    
                    # 获取历史数据（最终训练数据 + 测试集中当前期之前的数据）
                    historical_data = final_training_data.copy()
                    
                    # 添加测试集中当前期之前的数据
                    prev_test_data = test_data[test_data['期号'] < current_period]
                    if len(prev_test_data) > 0:
                        historical_data = pd.concat([historical_data, prev_test_data], ignore_index=True)
                    
                    # 构建模型
                    model = self.build_prediction_model(historical_data, best_weight_config)
                    if model is None:
                        continue
                    
                    # 获取最近一期数字
                    if len(historical_data) > 0:
                        last_row = historical_data.iloc[-1]
                        recent_numbers = [int(last_row[f'数字{j}']) for j in range(1, 7)]
                    else:
                        recent_numbers = [25, 30, 35, 40, 45, 49]
                    
                    # 进行预测
                    prediction_result = self.predict_with_model(model, recent_numbers)
                    
                    # 计算命中情况
                    pred_set = set(prediction_result['predicted_numbers'])
                    actual_set = set(actual_numbers)
                    hit_numbers = pred_set & actual_set
                    hit_count = len(hit_numbers)
                    is_hit = "是" if hit_count > 0 else "否"
                    hit_numbers_str = ",".join(map(str, sorted(hit_numbers))) if hit_numbers else ""
                    hit_rate = hit_count / len(pred_set) if len(pred_set) > 0 else 0
                    
                    # 记录结果
                    result_record = {
                        '预测序号': len(final_results) + 1,
                        '训练数据量': len(historical_data),
                        '预测目标期号': f"2025年{current_period}期",
                        '训练集配置': self.best_config['training_config'],
                        '权重配置': self.best_config['weight_config'],
                        '频率权重': best_weight_config['frequency'],
                        '马尔可夫权重': best_weight_config['markov'],
                        '统计权重': best_weight_config['statistical'],
                        '预测数字1': prediction_result['predicted_numbers'][0],
                        '预测数字2': prediction_result['predicted_numbers'][1],
                        '预测置信度': round(prediction_result['confidence'], 4),
                        '实际数字1': actual_numbers[0],
                        '实际数字2': actual_numbers[1],
                        '实际数字3': actual_numbers[2],
                        '实际数字4': actual_numbers[3],
                        '实际数字5': actual_numbers[4],
                        '实际数字6': actual_numbers[5],
                        '命中数量': hit_count,
                        '是否命中': is_hit,
                        '命中数字': hit_numbers_str,
                        '命中率': round(hit_rate, 4),
                        '预测方法': 'Best_Ensemble_Optimized',
                        '优化状态': '已优化'
                    }
                    
                    final_results.append(result_record)
                    
                except Exception as e:
                    print(f"⚠️ 处理第{current_period}期失败: {e}")
                    continue
            
            # 保存最终结果
            if final_results:
                results_df = pd.DataFrame(final_results)
                results_df.to_csv(self.output_file, index=False, encoding='utf-8')
                
                # 计算最终性能
                total_predictions = len(final_results)
                correct_predictions = len(results_df[results_df['是否命中'] == '是'])
                final_hit_rate = correct_predictions / total_predictions if total_predictions > 0 else 0
                
                print(f"\n🎉 最终测试完成!")
                print(f"   测试期数: {total_predictions}")
                print(f"   命中次数: {correct_predictions}")
                print(f"   最终命中率: {final_hit_rate:.1%}")
                print(f"   相比原始19.8%: {final_hit_rate - 0.198:+.1%}")
                print(f"   目标达成: {'✅ 是' if final_hit_rate >= 0.25 else '❌ 否'}")
                print(f"   结果文件: {self.output_file}")
                
                # 生成详细分析报告
                self.generate_final_analysis_report(results_df, final_hit_rate)
                
                return True
            else:
                print("❌ 未生成有效预测结果")
                return False
                
        except Exception as e:
            print(f"❌ 应用最佳配置失败: {e}")
            return False

    def generate_final_analysis_report(self, results_df, final_hit_rate):
        """生成最终分析报告"""
        print(f"\n📊 优化预测验证分析报告")
        print("=" * 70)

        try:
            # 基本统计
            total_predictions = len(results_df)
            correct_predictions = len(results_df[results_df['是否命中'] == '是'])

            print(f"🎯 优化结果总结:")
            print(f"   原始命中率: 19.8%")
            print(f"   优化后命中率: {final_hit_rate:.1%}")
            print(f"   提升幅度: {final_hit_rate - 0.198:+.1%}")
            print(f"   目标达成: {'✅ 是' if final_hit_rate >= 0.25 else '❌ 否'}")

            # 最佳配置详情
            print(f"\n🏆 最佳配置详情:")
            print(f"   训练集配置: {self.best_config['training_config']}")
            print(f"   权重配置: {self.best_config['weight_config']}")
            print(f"   频率分析权重: {self.best_config['weights']['frequency']:.0%}")
            print(f"   马尔可夫权重: {self.best_config['weights']['markov']:.0%}")
            print(f"   统计方法权重: {self.best_config['weights']['statistical']:.0%}")
            print(f"   验证集表现: {self.best_config['hit_rate']:.1%}")

            # 配置对比分析
            print(f"\n📈 配置对比分析:")

            # 按训练集配置分组
            training_performance = {}
            for result in self.optimization_results:
                config_name = result['training_config']
                if config_name not in training_performance:
                    training_performance[config_name] = []
                training_performance[config_name].append(result['hit_rate'])

            print(f"   训练集配置表现:")
            for config, rates in training_performance.items():
                avg_rate = np.mean(rates)
                max_rate = np.max(rates)
                print(f"     {config}: 平均{avg_rate:.1%}, 最高{max_rate:.1%}")

            # 按权重配置分组
            weight_performance = {}
            for result in self.optimization_results:
                config_name = result['weight_config']
                if config_name not in weight_performance:
                    weight_performance[config_name] = []
                weight_performance[config_name].append(result['hit_rate'])

            print(f"   权重配置表现:")
            for config, rates in weight_performance.items():
                avg_rate = np.mean(rates)
                max_rate = np.max(rates)
                print(f"     {config}: 平均{avg_rate:.1%}, 最高{max_rate:.1%}")

            # 最终测试详细分析
            print(f"\n🔍 最终测试详细分析:")
            print(f"   测试期间: 2025年151-203期")
            print(f"   总预测次数: {total_predictions}")
            print(f"   命中次数: {correct_predictions}")
            print(f"   命中率: {final_hit_rate:.1%}")

            # 按期号段分析
            if total_predictions > 0:
                mid_point = 177  # 151-203的中点
                first_half = results_df[results_df['预测目标期号'].str.extract(r'(\d+)期')[0].astype(int) <= mid_point]
                second_half = results_df[results_df['预测目标期号'].str.extract(r'(\d+)期')[0].astype(int) > mid_point]

                if len(first_half) > 0:
                    first_half_hits = len(first_half[first_half['是否命中'] == '是'])
                    first_half_rate = first_half_hits / len(first_half)
                    print(f"   前半段(151-177期): {first_half_rate:.1%} ({first_half_hits}/{len(first_half)})")

                if len(second_half) > 0:
                    second_half_hits = len(second_half[second_half['是否命中'] == '是'])
                    second_half_rate = second_half_hits / len(second_half)
                    print(f"   后半段(178-203期): {second_half_rate:.1%} ({second_half_hits}/{len(second_half)})")

            # 置信度分析
            avg_confidence = results_df['预测置信度'].mean()
            confidence_std = results_df['预测置信度'].std()
            print(f"\n📊 置信度分析:")
            print(f"   平均置信度: {avg_confidence:.3f}")
            print(f"   置信度标准差: {confidence_std:.3f}")
            print(f"   置信度稳定性: {'优秀' if confidence_std < 0.01 else '良好' if confidence_std < 0.02 else '需改进'}")

            # 优化效果评估
            print(f"\n🎯 优化效果评估:")
            improvement = final_hit_rate - 0.198
            if improvement >= 0.07:  # 提升7%+
                assessment = "优秀 - 显著提升"
            elif improvement >= 0.05:  # 提升5%+
                assessment = "良好 - 明显提升"
            elif improvement >= 0.02:  # 提升2%+
                assessment = "一般 - 有所提升"
            elif improvement > 0:
                assessment = "轻微 - 略有提升"
            else:
                assessment = "无效 - 未见提升"

            print(f"   优化效果: {assessment}")
            print(f"   提升幅度: {improvement:+.1%}")
            print(f"   相对提升: {improvement/0.198*100:+.1f}%")

            # 关键发现
            print(f"\n🔍 关键发现:")

            # 找出表现最好的配置类型
            best_training = max(training_performance.items(), key=lambda x: np.mean(x[1]))
            best_weight = max(weight_performance.items(), key=lambda x: np.mean(x[1]))

            print(f"   最佳训练集配置: {best_training[0]} (平均{np.mean(best_training[1]):.1%})")
            print(f"   最佳权重配置: {best_weight[0]} (平均{np.mean(best_weight[1]):.1%})")

            # 分析权重趋势
            freq_weights = [r['weights']['frequency'] for r in self.optimization_results]
            markov_weights = [r['weights']['markov'] for r in self.optimization_results]
            stat_weights = [r['weights']['statistical'] for r in self.optimization_results]
            hit_rates = [r['hit_rate'] for r in self.optimization_results]

            # 计算权重与性能的相关性
            freq_corr = np.corrcoef(freq_weights, hit_rates)[0, 1] if len(freq_weights) > 1 else 0
            markov_corr = np.corrcoef(markov_weights, hit_rates)[0, 1] if len(markov_weights) > 1 else 0
            stat_corr = np.corrcoef(stat_weights, hit_rates)[0, 1] if len(stat_weights) > 1 else 0

            print(f"   权重与性能相关性:")
            print(f"     频率分析: {freq_corr:+.3f}")
            print(f"     马尔可夫: {markov_corr:+.3f}")
            print(f"     统计方法: {stat_corr:+.3f}")

            # 推荐配置
            print(f"\n💡 推荐配置:")
            print(f"   训练集: {self.best_config['training_config']}")
            print(f"   权重组合: 频率{self.best_config['weights']['frequency']:.0%} + 马尔可夫{self.best_config['weights']['markov']:.0%} + 统计{self.best_config['weights']['statistical']:.0%}")
            print(f"   预期性能: {final_hit_rate:.1%} 命中率")
            print(f"   适用场景: 2025年后续期号预测")

        except Exception as e:
            print(f"⚠️ 分析报告生成失败: {e}")

def main():
    """主函数"""
    print("🎯 优化预测验证系统")
    print("通过训练集配置和方法权重优化提升预测命中率")
    print("=" * 80)

    print("📋 优化目标:")
    print("   当前命中率: 19.8%")
    print("   目标命中率: 25%+")
    print("   优化方法: 训练集配置 + 方法权重组合")
    print("   验证集: 2025年1-150期")
    print("   测试集: 2025年151-203期")

    system = OptimizedPredictionValidationSystem()

    # 确认执行
    print("\n⚠️ 注意：此操作将测试多种配置组合，可能需要较长时间")
    confirm = input("确认开始优化预测验证? (y/n): ").strip().lower()

    if confirm != 'y':
        print("❌ 操作已取消")
        return

    # 执行优化
    success = system.run_optimization_analysis()

    if success:
        print(f"\n🎉 优化预测验证完成！")
        print(f"配置分析文件: 优化配置分析报告.csv")
        print(f"最终结果文件: 优化预测验证结果.csv")
        print(f"通过系统化优化，找到了最佳的预测配置组合")
    else:
        print(f"\n❌ 优化预测验证失败")

if __name__ == "__main__":
    main()
