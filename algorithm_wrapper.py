#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强预测算法包装器
自动生成的集成代码
"""

import os
import json
from algorithm_integration_manager import AlgorithmIntegrationManager
import logging

logger = logging.getLogger(__name__)

# 全局算法管理器实例
_algorithm_manager = None

def get_algorithm_manager():
    """获取算法管理器实例（单例模式）"""
    global _algorithm_manager

    if _algorithm_manager is None:
        # 加载配置
        config_file = 'algorithm_config.json'
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                algorithm_config = config.get('algorithm_config', {})
            except Exception as e:
                logger.warning(f"加载配置失败，使用默认配置: {e}")
                algorithm_config = None
        else:
            algorithm_config = None

        # 创建管理器
        _algorithm_manager = AlgorithmIntegrationManager(algorithm_config)

        # 尝试加载训练数据
        try:
            import pandas as pd
            training_file = 'prediction_data.csv'
            if os.path.exists(training_file):
                data = pd.read_csv(training_file)

                # 准备训练数据
                processed_data = []
                for _, row in data.iterrows():
                    if 'actual_numbers' in row:
                        processed_data.append(row['actual_numbers'])
                    elif '实际数字1' in row and pd.notna(row['实际数字1']):
                        numbers = [
                            row['实际数字1'], row['实际数字2'], row['实际数字3'],
                            row['实际数字4'], row['实际数字5'], row['实际数字6']
                        ]
                        processed_data.append(numbers)

                if processed_data:
                    _algorithm_manager.train_all_algorithms(processed_data)
        except Exception as e:
            logger.warning(f"自动训练失败: {e}")

    return _algorithm_manager

# 向后兼容的函数别名
def predict_numbers(previous_numbers, context=None):
    """预测数字（向后兼容）"""
    manager = get_algorithm_manager()
    result = manager.predict_ensemble(previous_numbers, context)
    return result['predicted_numbers']

def get_prediction(previous_numbers, context=None):
    """获取预测（向后兼容）"""
    manager = get_algorithm_manager()
    result = manager.predict_ensemble(previous_numbers, context)
    return {
        'predicted_numbers': result['predicted_numbers'],
        'probabilities': result['probabilities']
    }

def calculate_prediction(previous_numbers, context=None):
    """计算预测（向后兼容）"""
    return get_prediction(previous_numbers, context)

# 新系统专用函数
def get_enhanced_prediction(previous_numbers, context=None):
    """获取增强预测"""
    manager = get_algorithm_manager()
    return manager.predict_ensemble(previous_numbers, context)

def update_prediction_result(prediction_result, actual_numbers):
    """更新预测结果"""
    manager = get_algorithm_manager()
    return manager.update_performance(prediction_result, actual_numbers)

def get_algorithm_performance():
    """获取算法性能"""
    manager = get_algorithm_manager()
    return manager.get_performance_report()

def export_performance_data(filename):
    """导出性能数据"""
    manager = get_algorithm_manager()
    return manager.export_performance_data(filename)

# 部署信息
DEPLOYMENT_INFO = {
    'deployment_id': '20250715_180855',
    'deployment_time': '2025-07-15T18:08:56.652768',
    'system_version': '1.0'
}
