#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
状态评估器核心架构设计
回答"现在是投注的好时机吗？"的智能择时系统
基于历史表现回溯、模式稳定性分析、波动性指标的综合评估
"""

import pandas as pd
import numpy as np
import json
from collections import defaultdict, deque
from datetime import datetime, timedelta
from scipy import stats
from abc import ABC, abstractmethod
import warnings
warnings.filterwarnings('ignore')

class StateEvaluatorCore:
    """
    状态评估器核心类
    提供智能择时决策的综合评估系统
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.data = None
        self.train_data = None
        self.test_data = None
        
        # 核心组件
        self.historical_performance_tracker = None
        self.pattern_stability_analyzer = None
        self.volatility_indicator = None
        self.betting_confidence_generator = None
        
        # 配置参数
        self.config = {
            'lookback_window': 90,      # 历史回溯窗口（期数）
            'stability_window': 60,     # 稳定性分析窗口（期数）
            'volatility_window': 30,    # 波动性分析窗口（期数）
            'confidence_threshold': 0.8, # 投注建议阈值
            'min_historical_samples': 10 # 最小历史样本数
        }
        
        # 评估结果存储
        self.state_evaluations = []
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            # 使用最优配置
            self.train_data = self.data[(self.data['年份'] >= 2023) & (self.data['年份'] < 2025)].copy()
            self.test_data = self.data[(self.data['年份'] == 2025) & (self.data['期号'] <= 182)].copy()
            
            print(f"✅ 数据加载成功")
            print(f"  训练数据: {len(self.train_data)}期")
            print(f"  测试数据: {len(self.test_data)}期")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def initialize_components(self):
        """初始化各个组件"""
        print(f"\n🔧 初始化状态评估器组件")
        
        # 1. 历史表现回溯器
        self.historical_performance_tracker = HistoricalPerformanceTracker(
            self.data, self.config
        )
        
        # 2. 模式稳定性分析器
        self.pattern_stability_analyzer = PatternStabilityAnalyzer(
            self.data, self.config
        )
        
        # 3. 波动性指标计算器
        self.volatility_indicator = VolatilityIndicator(
            self.data, self.config
        )
        
        # 4. 投注建议指数生成器
        self.betting_confidence_generator = BettingConfidenceGenerator(
            self.config
        )
        
        print(f"✅ 所有组件初始化完成")
        return True
    
    def evaluate_state(self, current_period_idx, previous_numbers):
        """评估当前状态的投注建议指数"""
        
        # 1. 历史表现回溯
        historical_score = self.historical_performance_tracker.get_historical_performance(
            previous_numbers, current_period_idx
        )
        
        # 2. 模式稳定性分析
        stability_score = self.pattern_stability_analyzer.analyze_stability(
            current_period_idx
        )
        
        # 3. 波动性指标
        volatility_score = self.volatility_indicator.calculate_volatility(
            current_period_idx
        )
        
        # 4. 综合投注建议指数
        betting_confidence = self.betting_confidence_generator.generate_confidence(
            historical_score, stability_score, volatility_score
        )
        
        return {
            'period_idx': current_period_idx,
            'previous_numbers': list(previous_numbers),
            'historical_score': historical_score,
            'stability_score': stability_score,
            'volatility_score': volatility_score,
            'betting_confidence': betting_confidence,
            'recommendation': 'BET' if betting_confidence > self.config['confidence_threshold'] else 'SKIP'
        }
    
    def run_comprehensive_evaluation(self):
        """运行综合状态评估"""
        print(f"\n🎯 运行综合状态评估")
        print("=" * 60)
        
        bet_recommendations = 0
        skip_recommendations = 0
        
        for idx, test_row in self.test_data.iterrows():
            period_num = test_row['期号']
            year = test_row['年份']
            
            # 获取前一期数字
            if idx == self.test_data.index[0]:
                prev_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                prev_numbers = set([self.test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            # 状态评估
            evaluation = self.evaluate_state(idx, prev_numbers)
            evaluation['year'] = year
            evaluation['period'] = period_num
            
            self.state_evaluations.append(evaluation)
            
            if evaluation['recommendation'] == 'BET':
                bet_recommendations += 1
            else:
                skip_recommendations += 1
            
            # 显示进度
            if idx % 30 == 0:
                print(f"  评估进度: {idx}/{len(self.test_data)} "
                      f"(投注建议: {bet_recommendations}, 跳过建议: {skip_recommendations})")
        
        print(f"\n✅ 状态评估完成")
        print(f"  总期数: {len(self.state_evaluations)}")
        print(f"  投注建议: {bet_recommendations} ({bet_recommendations/len(self.state_evaluations):.1%})")
        print(f"  跳过建议: {skip_recommendations} ({skip_recommendations/len(self.state_evaluations):.1%})")
        
        return self.state_evaluations
    
    def analyze_evaluation_effectiveness(self):
        """分析状态评估的有效性"""
        print(f"\n📊 分析状态评估有效性")
        print("=" * 60)
        
        if not self.state_evaluations:
            print("❌ 无评估结果")
            return None
        
        # 需要实际预测结果来验证有效性
        # 这里先提供框架，具体实现需要结合马尔可夫预测结果
        
        bet_periods = [e for e in self.state_evaluations if e['recommendation'] == 'BET']
        skip_periods = [e for e in self.state_evaluations if e['recommendation'] == 'SKIP']
        
        print(f"投注建议期数: {len(bet_periods)}")
        print(f"跳过建议期数: {len(skip_periods)}")
        
        # 分析各指标的分布
        all_historical = [e['historical_score'] for e in self.state_evaluations]
        all_stability = [e['stability_score'] for e in self.state_evaluations]
        all_volatility = [e['volatility_score'] for e in self.state_evaluations]
        all_confidence = [e['betting_confidence'] for e in self.state_evaluations]
        
        print(f"\n指标分布统计:")
        print(f"  历史表现分数: {np.mean(all_historical):.3f} ± {np.std(all_historical):.3f}")
        print(f"  稳定性分数: {np.mean(all_stability):.3f} ± {np.std(all_stability):.3f}")
        print(f"  波动性分数: {np.mean(all_volatility):.3f} ± {np.std(all_volatility):.3f}")
        print(f"  投注置信度: {np.mean(all_confidence):.3f} ± {np.std(all_confidence):.3f}")
        
        return {
            'bet_periods': len(bet_periods),
            'skip_periods': len(skip_periods),
            'bet_ratio': len(bet_periods) / len(self.state_evaluations),
            'score_statistics': {
                'historical': {'mean': np.mean(all_historical), 'std': np.std(all_historical)},
                'stability': {'mean': np.mean(all_stability), 'std': np.std(all_stability)},
                'volatility': {'mean': np.mean(all_volatility), 'std': np.std(all_volatility)},
                'confidence': {'mean': np.mean(all_confidence), 'std': np.std(all_confidence)}
            }
        }

# 抽象基类定义各个组件接口
class StateEvaluatorComponent(ABC):
    """状态评估器组件抽象基类"""
    
    def __init__(self, data, config):
        self.data = data
        self.config = config
    
    @abstractmethod
    def calculate_score(self, *args, **kwargs):
        """计算组件分数"""
        pass

class HistoricalPerformanceTracker(StateEvaluatorComponent):
    """历史表现回溯器"""
    
    def __init__(self, data, config):
        super().__init__(data, config)
        self.state_performance_map = {}
        self._build_performance_map()
    
    def _build_performance_map(self):
        """构建状态-性能映射表"""
        print(f"  构建历史表现映射表...")
        # 这里是简化实现，实际需要详细的历史回测
        # 基于前一期数字组合的历史表现
        self.state_performance_map = defaultdict(list)
        
        # 示例：基于数字和的历史表现
        for i in range(len(self.data) - 1):
            current_numbers = [self.data.iloc[i][f'数字{j}'] for j in range(1, 7)]
            state_key = sum(current_numbers) // 10  # 简化的状态表示
            
            # 这里需要实际的预测成功率数据
            # 暂时使用随机值作为示例
            performance = np.random.uniform(0.2, 0.4)
            self.state_performance_map[state_key].append(performance)
    
    def get_historical_performance(self, previous_numbers, current_idx):
        """获取历史表现分数"""
        state_key = sum(previous_numbers) // 10
        
        if state_key in self.state_performance_map:
            performances = self.state_performance_map[state_key]
            if len(performances) >= self.config['min_historical_samples']:
                return np.mean(performances)
        
        return 0.25  # 默认基准性能

class PatternStabilityAnalyzer(StateEvaluatorComponent):
    """模式稳定性分析器"""
    
    def analyze_stability(self, current_idx):
        """分析模式稳定性"""
        # 简化实现：基于最近性能的趋势分析
        window = self.config['stability_window']
        
        if current_idx < window:
            return 0.5  # 数据不足时返回中性分数
        
        # 模拟最近窗口的性能趋势
        recent_performances = np.random.uniform(0.2, 0.4, window)
        
        # 计算趋势斜率
        x = np.arange(window)
        slope, _, r_value, _, _ = stats.linregress(x, recent_performances)
        
        # 转换为0-1分数，正斜率得高分
        stability_score = max(0, min(1, 0.5 + slope * 10))
        
        return stability_score

class VolatilityIndicator(StateEvaluatorComponent):
    """波动性指标计算器"""
    
    def calculate_volatility(self, current_idx):
        """计算波动性指标"""
        window = self.config['volatility_window']
        
        if current_idx < window:
            return 0.5
        
        # 计算最近窗口的数字分布波动性
        start_idx = max(0, current_idx - window)
        recent_data = self.data.iloc[start_idx:current_idx]
        
        # 计算数字分布的标准差
        all_numbers = []
        for _, row in recent_data.iterrows():
            numbers = [row[f'数字{j}'] for j in range(1, 7)]
            all_numbers.extend(numbers)
        
        volatility = np.std(all_numbers) / 49  # 归一化
        
        # 低波动性得高分（稳定模式）
        volatility_score = max(0, min(1, 1 - volatility * 2))
        
        return volatility_score

class BettingConfidenceGenerator(StateEvaluatorComponent):
    """投注建议指数生成器"""
    
    def __init__(self, config):
        self.config = config
    
    def generate_confidence(self, historical_score, stability_score, volatility_score):
        """生成综合投注置信度"""
        # 加权组合各项指标
        weights = {
            'historical': 0.5,    # 历史表现权重最高
            'stability': 0.3,     # 稳定性次之
            'volatility': 0.2     # 波动性权重最低
        }
        
        confidence = (
            weights['historical'] * historical_score +
            weights['stability'] * stability_score +
            weights['volatility'] * volatility_score
        )
        
        return max(0, min(1, confidence))

def main():
    """主函数"""
    print("🎯 状态评估器核心架构设计")
    print("智能择时决策系统")
    print("=" * 70)
    
    # 初始化状态评估器
    evaluator = StateEvaluatorCore()
    
    # 1. 加载数据
    if not evaluator.load_data():
        return
    
    # 2. 初始化组件
    if not evaluator.initialize_components():
        return
    
    # 3. 运行综合评估
    evaluations = evaluator.run_comprehensive_evaluation()
    
    # 4. 分析评估有效性
    effectiveness = evaluator.analyze_evaluation_effectiveness()
    
    # 5. 保存结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"状态评估器设计结果_{timestamp}.json"
    
    # 处理numpy类型
    def convert_numpy_types(obj):
        if isinstance(obj, dict):
            return {k: convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(v) for v in obj]
        elif hasattr(obj, 'item'):
            return obj.item()
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, (np.integer, int)):
            return int(obj)
        elif isinstance(obj, (np.floating, float)):
            return float(obj)
        else:
            return obj
    
    results = {
        'system_config': evaluator.config,
        'evaluations': convert_numpy_types(evaluations[:20]),  # 保存前20个评估结果
        'effectiveness_analysis': convert_numpy_types(effectiveness),
        'design_timestamp': datetime.now().isoformat()
    }
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 状态评估器设计结果已保存: {results_file}")
    
    # 6. 设计总结
    print(f"\n🎉 状态评估器核心架构设计完成")
    print("=" * 50)
    print(f"✅ 核心组件: 4个（历史回溯、稳定性、波动性、置信度生成）")
    print(f"✅ 评估期数: {len(evaluations)}")
    print(f"✅ 投注建议比例: {effectiveness['bet_ratio']:.1%}")
    print(f"✅ 平均置信度: {effectiveness['score_statistics']['confidence']['mean']:.3f}")
    
    print(f"\n💡 下一步开发重点:")
    print(f"  1. 实现详细的历史表现回溯系统")
    print(f"  2. 开发精确的模式稳定性分析器")
    print(f"  3. 构建敏感的波动性指标计算器")
    print(f"  4. 验证状态评估器的实际有效性")

if __name__ == "__main__":
    main()
