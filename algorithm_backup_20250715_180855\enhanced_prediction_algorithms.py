#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强预测算法集合
Enhanced Prediction Algorithms Collection

包含多种先进预测算法的集合，可以单独使用或集成到现有系统中
支持增强马尔可夫链、频率分析、模式匹配和趋势识别等算法

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter, deque
from datetime import datetime
import logging
import json
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedMarkovChain:
    """增强马尔可夫链算法"""
    
    def __init__(self, config=None):
        """初始化增强马尔可夫链"""
        self.config = config or self._get_default_config()
        self.first_order_prob = {}
        self.second_order_prob = {}
        self.third_order_prob = {}
        self.is_trained = False
        
        logger.info("🔗 增强马尔可夫链初始化完成")
    
    def _get_default_config(self):
        """获取默认配置"""
        return {
            'max_order': 3,
            'smoothing_factor': 0.1,
            'min_probability': 0.001,
            'weight_first_order': 0.3,
            'weight_second_order': 0.5,
            'weight_third_order': 0.2
        }
    
    def train(self, data):
        """训练马尔可夫模型"""
        logger.info("🔄 训练增强马尔可夫链")
        
        if isinstance(data, pd.DataFrame):
            # 从DataFrame中提取数据
            sequences = []
            for i in range(len(data) - 1):
                current_seq = []
                next_seq = []
                
                # 获取当前期和下一期的数字
                for j in range(1, 7):  # 假设每期有6个数字
                    if f'数字{j}' in data.columns:
                        current_seq.append(data.iloc[i][f'数字{j}'])
                        next_seq.append(data.iloc[i+1][f'数字{j}'])
                
                if current_seq and next_seq:
                    sequences.append((current_seq, next_seq))
        else:
            # 直接使用提供的序列数据
            sequences = data
        
        # 构建一阶马尔可夫模型
        first_order_transitions = defaultdict(lambda: defaultdict(int))
        
        # 构建二阶马尔可夫模型
        second_order_transitions = defaultdict(lambda: defaultdict(int))
        
        # 构建三阶马尔可夫模型
        third_order_transitions = defaultdict(lambda: defaultdict(int))
        
        # 统计转移次数
        for i in range(len(sequences)):
            current_numbers = set(sequences[i][0])
            next_numbers = set(sequences[i][1])
            
            # 一阶转移
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    first_order_transitions[curr_num][next_num] += 1
            
            # 二阶转移
            if i >= 1 and i < len(sequences):
                prev_numbers = set(sequences[i-1][0])
                for prev_num in prev_numbers:
                    for curr_num in current_numbers:
                        state = (prev_num, curr_num)
                        for next_num in next_numbers:
                            second_order_transitions[state][next_num] += 1
            
            # 三阶转移
            if i >= 2 and i < len(sequences):
                prev2_numbers = set(sequences[i-2][0])
                prev1_numbers = set(sequences[i-1][0])
                for prev2_num in prev2_numbers:
                    for prev1_num in prev1_numbers:
                        for curr_num in current_numbers:
                            state = (prev2_num, prev1_num, curr_num)
                            for next_num in next_numbers:
                                third_order_transitions[state][next_num] += 1
        
        # 计算转移概率
        self.first_order_prob = {}
        for curr_num, transitions in first_order_transitions.items():
            total = sum(transitions.values())
            if total > 0:
                self.first_order_prob[curr_num] = {
                    next_num: (count + self.config['smoothing_factor']) / (total + self.config['smoothing_factor'] * 49)
                    for next_num, count in transitions.items()
                }
                
                # 添加平滑处理，确保所有可能的转移都有概率
                for next_num in range(1, 50):
                    if next_num not in self.first_order_prob[curr_num]:
                        self.first_order_prob[curr_num][next_num] = self.config['smoothing_factor'] / (total + self.config['smoothing_factor'] * 49)
        
        # 计算二阶转移概率
        self.second_order_prob = {}
        for state, transitions in second_order_transitions.items():
            total = sum(transitions.values())
            if total > 0:
                self.second_order_prob[state] = {
                    next_num: (count + self.config['smoothing_factor']) / (total + self.config['smoothing_factor'] * 49)
                    for next_num, count in transitions.items()
                }
                
                # 添加平滑处理
                for next_num in range(1, 50):
                    if next_num not in self.second_order_prob[state]:
                        self.second_order_prob[state][next_num] = self.config['smoothing_factor'] / (total + self.config['smoothing_factor'] * 49)
        
        # 计算三阶转移概率
        self.third_order_prob = {}
        for state, transitions in third_order_transitions.items():
            total = sum(transitions.values())
            if total > 0:
                self.third_order_prob[state] = {
                    next_num: (count + self.config['smoothing_factor']) / (total + self.config['smoothing_factor'] * 49)
                    for next_num, count in transitions.items()
                }
                
                # 添加平滑处理
                for next_num in range(1, 50):
                    if next_num not in self.third_order_prob[state]:
                        self.third_order_prob[state][next_num] = self.config['smoothing_factor'] / (total + self.config['smoothing_factor'] * 49)
        
        self.is_trained = True
        logger.info(f"✅ 马尔可夫链训练完成")
        logger.info(f"  一阶状态数: {len(self.first_order_prob)}")
        logger.info(f"  二阶状态数: {len(self.second_order_prob)}")
        logger.info(f"  三阶状态数: {len(self.third_order_prob)}")
        
        return self
    
    def predict(self, previous_numbers, num_predictions=2):
        """使用马尔可夫链进行预测"""
        if not self.is_trained:
            logger.warning("⚠️ 模型尚未训练")
            return [], {}
        
        # 计算各阶马尔可夫预测
        first_order_probs = self._predict_first_order(previous_numbers)
        second_order_probs = self._predict_second_order(previous_numbers)
        third_order_probs = self._predict_third_order(previous_numbers)
        
        # 融合各阶预测结果
        combined_probs = self._combine_predictions(
            first_order_probs, 
            second_order_probs, 
            third_order_probs
        )
        
        # 选择概率最高的数字作为预测结果
        sorted_probs = sorted(combined_probs.items(), key=lambda x: x[1], reverse=True)
        predicted_numbers = [num for num, _ in sorted_probs[:num_predictions]]
        
        return predicted_numbers, combined_probs
    
    def _predict_first_order(self, previous_numbers):
        """一阶马尔可夫预测"""
        number_probs = defaultdict(float)
        total_weight = 0
        
        for prev_num in previous_numbers:
            if prev_num in self.first_order_prob:
                weight = 1.0 / len(previous_numbers) if previous_numbers else 1.0
                for next_num, prob in self.first_order_prob[prev_num].items():
                    number_probs[next_num] += prob * weight
                    total_weight += weight
        
        # 标准化概率
        if total_weight > 0:
            for num in number_probs:
                number_probs[num] /= total_weight
        else:
            # 如果没有匹配的状态，使用均匀分布
            for num in range(1, 50):
                number_probs[num] = 1.0 / 49
        
        return dict(number_probs)
    
    def _predict_second_order(self, previous_numbers):
        """二阶马尔可夫预测"""
        if len(previous_numbers) < 2:
            return {num: 1.0/49 for num in range(1, 50)}
        
        number_probs = defaultdict(float)
        total_weight = 0
        
        # 考虑所有可能的二阶状态
        for i in range(len(previous_numbers)):
            for j in range(len(previous_numbers)):
                if i != j:
                    state = (previous_numbers[i], previous_numbers[j])
                    if state in self.second_order_prob:
                        weight = 1.0 / (len(previous_numbers) * (len(previous_numbers) - 1)) if len(previous_numbers) > 1 else 1.0
                        for next_num, prob in self.second_order_prob[state].items():
                            number_probs[next_num] += prob * weight
                            total_weight += weight
        
        # 标准化概率
        if total_weight > 0:
            for num in number_probs:
                number_probs[num] /= total_weight
        else:
            # 如果没有匹配的状态，使用均匀分布
            for num in range(1, 50):
                number_probs[num] = 1.0 / 49
        
        return dict(number_probs)
    
    def _predict_third_order(self, previous_numbers):
        """三阶马尔可夫预测"""
        if len(previous_numbers) < 3:
            return {num: 1.0/49 for num in range(1, 50)}
        
        number_probs = defaultdict(float)
        total_weight = 0
        
        # 考虑所有可能的三阶状态
        for i in range(len(previous_numbers)):
            for j in range(len(previous_numbers)):
                for k in range(len(previous_numbers)):
                    if i != j and j != k and i != k:
                        state = (previous_numbers[i], previous_numbers[j], previous_numbers[k])
                        if state in self.third_order_prob:
                            weight = 1.0 / (len(previous_numbers) * (len(previous_numbers) - 1) * (len(previous_numbers) - 2)) if len(previous_numbers) > 2 else 1.0
                            for next_num, prob in self.third_order_prob[state].items():
                                number_probs[next_num] += prob * weight
                                total_weight += weight
        
        # 标准化概率
        if total_weight > 0:
            for num in number_probs:
                number_probs[num] /= total_weight
        else:
            # 如果没有匹配的状态，使用均匀分布
            for num in range(1, 50):
                number_probs[num] = 1.0 / 49
        
        return dict(number_probs)
    
    def _combine_predictions(self, first_order_probs, second_order_probs, third_order_probs):
        """融合各阶预测结果"""
        combined_probs = defaultdict(float)
        
        # 加权融合
        for num in range(1, 50):
            first_prob = first_order_probs.get(num, self.config['min_probability'])
            second_prob = second_order_probs.get(num, self.config['min_probability'])
            third_prob = third_order_probs.get(num, self.config['min_probability'])
            
            combined_probs[num] = (
                self.config['weight_first_order'] * first_prob +
                self.config['weight_second_order'] * second_prob +
                self.config['weight_third_order'] * third_prob
            )
        
        # 标准化概率
        total_prob = sum(combined_probs.values())
        if total_prob > 0:
            for num in combined_probs:
                combined_probs[num] /= total_prob
        
        return dict(combined_probs)
    
    def get_model_info(self):
        """获取模型信息"""
        return {
            'algorithm': 'EnhancedMarkovChain',
            'is_trained': self.is_trained,
            'first_order_states': len(self.first_order_prob),
            'second_order_states': len(self.second_order_prob),
            'third_order_states': len(self.third_order_prob),
            'config': self.config
        }

class FrequencyAnalyzer:
    """频率分析算法"""
    
    def __init__(self, config=None):
        """初始化频率分析器"""
        self.config = config or self._get_default_config()
        self.frequency_data = {}
        self.trend_data = {}
        self.is_trained = False
        
        logger.info("📊 频率分析器初始化完成")
    
    def _get_default_config(self):
        """获取默认配置"""
        return {
            'time_windows': [20, 50, 100, 200],
            'window_weights': [0.4, 0.3, 0.2, 0.1],
            'trend_factor': 0.3,
            'recency_factor': 0.2,
            'min_probability': 0.001
        }
    
    def train(self, data):
        """训练频率分析器"""
        logger.info("🔄 训练频率分析器")
        
        if isinstance(data, pd.DataFrame):
            # 从DataFrame中提取数据
            all_numbers = []
            for i in range(len(data)):
                period_numbers = []
                for j in range(1, 7):  # 假设每期有6个数字
                    if f'数字{j}' in data.columns:
                        period_numbers.append(data.iloc[i][f'数字{j}'])
                all_numbers.append(period_numbers)
        else:
            # 直接使用提供的数据
            all_numbers = data
        
        # 计算各时间窗口的频率
        self.frequency_data = {}
        for window_size in self.config['time_windows']:
            if len(all_numbers) >= window_size:
                recent_data = all_numbers[-window_size:]
                self.frequency_data[window_size] = self._calculate_frequency(recent_data)
        
        # 计算趋势
        self.trend_data = self._calculate_trends(all_numbers)
        
        self.is_trained = True
        logger.info(f"✅ 频率分析器训练完成")
        logger.info(f"  时间窗口数: {len(self.frequency_data)}")
        
        return self
    
    def _calculate_frequency(self, data):
        """计算频率"""
        frequencies = defaultdict(int)
        total_count = 0
        
        for period in data:
            for num in period:
                frequencies[num] += 1
                total_count += 1
        
        # 计算概率
        probabilities = {}
        if total_count > 0:
            for num in range(1, 50):
                probabilities[num] = frequencies.get(num, 0) / total_count
        
        return probabilities
    
    def _calculate_trends(self, data):
        """计算趋势"""
        if len(data) < 50:
            return {num: 1.0 for num in range(1, 50)}
        
        # 计算前半段和后半段的频率
        mid_point = len(data) // 2
        early_data = data[:mid_point]
        recent_data = data[mid_point:]
        
        early_freq = self._calculate_frequency(early_data)
        recent_freq = self._calculate_frequency(recent_data)
        
        # 计算趋势因子
        trends = {}
        for num in range(1, 50):
            early_rate = early_freq.get(num, self.config['min_probability'])
            recent_rate = recent_freq.get(num, self.config['min_probability'])
            
            if early_rate > 0:
                trend_factor = recent_rate / early_rate
                trends[num] = min(2.0, max(0.5, trend_factor))  # 限制趋势因子范围
            else:
                trends[num] = 1.0
        
        return trends
    
    def predict(self, previous_numbers=None, num_predictions=2):
        """使用频率分析进行预测"""
        if not self.is_trained:
            logger.warning("⚠️ 模型尚未训练")
            return [], {}
        
        # 计算加权频率
        weighted_probs = defaultdict(float)
        
        # 融合各时间窗口的频率
        for i, window_size in enumerate(self.config['time_windows']):
            if window_size in self.frequency_data:
                weight = self.config['window_weights'][i]
                for num, prob in self.frequency_data[window_size].items():
                    weighted_probs[num] += prob * weight
        
        # 应用趋势因子
        for num in range(1, 50):
            trend_factor = self.trend_data.get(num, 1.0)
            weighted_probs[num] *= (1.0 + self.config['trend_factor'] * (trend_factor - 1.0))
        
        # 应用最近性因子（如果提供了前期数字）
        if previous_numbers:
            recency_boost = self.config['recency_factor'] / len(previous_numbers) if previous_numbers else 0
            for num in previous_numbers:
                weighted_probs[num] *= (1.0 + recency_boost)
        
        # 标准化概率
        total_prob = sum(weighted_probs.values())
        if total_prob > 0:
            for num in weighted_probs:
                weighted_probs[num] /= total_prob
        
        # 选择概率最高的数字作为预测结果
        sorted_probs = sorted(weighted_probs.items(), key=lambda x: x[1], reverse=True)
        predicted_numbers = [num for num, _ in sorted_probs[:num_predictions]]
        
        return predicted_numbers, dict(weighted_probs)
    
    def get_model_info(self):
        """获取模型信息"""
        return {
            'algorithm': 'FrequencyAnalyzer',
            'is_trained': self.is_trained,
            'time_windows': list(self.frequency_data.keys()),
            'config': self.config
        }

class PatternMatcher:
    """模式匹配算法"""

    def __init__(self, config=None):
        """初始化模式匹配器"""
        self.config = config or self._get_default_config()
        self.pattern_database = []
        self.pattern_features = {}
        self.is_trained = False

        logger.info("🔍 模式匹配器初始化完成")

    def _get_default_config(self):
        """获取默认配置"""
        return {
            'similarity_threshold': 0.7,
            'max_patterns': 1000,
            'feature_weights': {
                'odd_ratio': 0.2,
                'large_ratio': 0.2,
                'sum_level': 0.2,
                'span': 0.15,
                'consecutive_ratio': 0.15,
                'distribution': 0.1
            },
            'min_probability': 0.001
        }

    def train(self, data):
        """训练模式匹配器"""
        logger.info("🔄 训练模式匹配器")

        if isinstance(data, pd.DataFrame):
            # 从DataFrame中提取数据
            sequences = []
            for i in range(len(data) - 1):
                current_numbers = []
                next_numbers = []

                for j in range(1, 7):  # 假设每期有6个数字
                    if f'数字{j}' in data.columns:
                        # 确保数据是整数类型
                        curr_val = data.iloc[i][f'数字{j}']
                        next_val = data.iloc[i+1][f'数字{j}']

                        if pd.notna(curr_val) and pd.notna(next_val):
                            current_numbers.append(int(curr_val))
                            next_numbers.append(int(next_val))

                if current_numbers and next_numbers:
                    sequences.append((current_numbers, next_numbers))
        else:
            # 直接使用提供的序列数据
            sequences = []
            for i in range(len(data) - 1):
                if isinstance(data[i], (list, tuple)) and isinstance(data[i+1], (list, tuple)):
                    current_numbers = [int(x) for x in data[i] if pd.notna(x)]
                    next_numbers = [int(x) for x in data[i+1] if pd.notna(x)]
                    if current_numbers and next_numbers:
                        sequences.append((current_numbers, next_numbers))

        # 构建模式数据库
        self.pattern_database = []
        for i, (current_numbers, next_numbers) in enumerate(sequences):
            pattern = {
                'index': i,
                'current_numbers': current_numbers,
                'next_numbers': next_numbers,
                'features': self._extract_features(current_numbers)
            }
            self.pattern_database.append(pattern)

        # 限制模式数据库大小
        if len(self.pattern_database) > self.config['max_patterns']:
            # 保留最近的模式
            self.pattern_database = self.pattern_database[-self.config['max_patterns']:]

        self.is_trained = True
        logger.info(f"✅ 模式匹配器训练完成")
        logger.info(f"  模式数量: {len(self.pattern_database)}")

        return self

    def _extract_features(self, numbers):
        """提取数字模式特征"""
        if not numbers:
            return {}

        features = {}

        # 奇偶比例
        odd_count = sum(1 for n in numbers if n % 2 == 1)
        features['odd_ratio'] = odd_count / len(numbers)

        # 大小比例
        large_count = sum(1 for n in numbers if n > 25)
        features['large_ratio'] = large_count / len(numbers)

        # 和值等级
        total_sum = sum(numbers)
        if total_sum < 120:
            features['sum_level'] = 0  # 低
        elif total_sum < 180:
            features['sum_level'] = 1  # 中
        else:
            features['sum_level'] = 2  # 高

        # 跨度
        features['span'] = max(numbers) - min(numbers)

        # 连号比例
        sorted_nums = sorted(numbers)
        consecutive_count = 0
        for i in range(len(sorted_nums) - 1):
            if sorted_nums[i+1] - sorted_nums[i] == 1:
                consecutive_count += 1
        features['consecutive_ratio'] = consecutive_count / max(1, len(numbers) - 1)

        # 分布特征（数字在1-49范围内的分布）
        distribution_zones = [0, 0, 0, 0, 0]  # 5个区间
        for num in numbers:
            zone = min(4, (num - 1) // 10)
            distribution_zones[zone] += 1
        features['distribution'] = tuple(d / len(numbers) for d in distribution_zones)

        return features

    def _calculate_similarity(self, features1, features2):
        """计算两个模式的相似度"""
        if not features1 or not features2:
            return 0.0

        similarities = []
        weights = self.config['feature_weights']

        # 比较各个特征
        for feature, weight in weights.items():
            if feature in features1 and feature in features2:
                if feature == 'distribution':
                    # 分布特征使用欧几里得距离
                    dist1 = features1[feature]
                    dist2 = features2[feature]
                    if len(dist1) == len(dist2):
                        euclidean_dist = np.sqrt(sum((a - b) ** 2 for a, b in zip(dist1, dist2)))
                        similarity = 1 - min(1, euclidean_dist)
                        similarities.append(similarity * weight)
                elif feature in ['odd_ratio', 'large_ratio', 'consecutive_ratio']:
                    # 比例特征
                    diff = abs(features1[feature] - features2[feature])
                    similarity = 1 - diff
                    similarities.append(similarity * weight)
                elif feature == 'sum_level':
                    # 等级特征
                    similarity = 1 if features1[feature] == features2[feature] else 0
                    similarities.append(similarity * weight)
                elif feature == 'span':
                    # 跨度特征
                    diff = abs(features1[feature] - features2[feature]) / 48  # 最大跨度为48
                    similarity = 1 - min(1, diff)
                    similarities.append(similarity * weight)

        return sum(similarities) if similarities else 0.0

    def predict(self, previous_numbers, num_predictions=2):
        """使用模式匹配进行预测"""
        if not self.is_trained:
            logger.warning("⚠️ 模型尚未训练")
            return [], {}

        # 提取当前模式特征
        current_features = self._extract_features(previous_numbers)

        # 查找相似模式
        similar_patterns = []
        for pattern in self.pattern_database:
            similarity = self._calculate_similarity(current_features, pattern['features'])
            if similarity >= self.config['similarity_threshold']:
                similar_patterns.append({
                    'pattern': pattern,
                    'similarity': similarity
                })

        # 按相似度排序
        similar_patterns.sort(key=lambda x: x['similarity'], reverse=True)

        # 基于相似模式生成预测概率
        number_probs = defaultdict(float)
        total_weight = 0

        for similar_pattern in similar_patterns[:20]:  # 最多使用前20个相似模式
            weight = similar_pattern['similarity']
            next_numbers = similar_pattern['pattern']['next_numbers']

            for num in next_numbers:
                number_probs[num] += weight
                total_weight += weight

        # 标准化概率
        if total_weight > 0:
            for num in number_probs:
                number_probs[num] /= total_weight
        else:
            # 如果没有找到相似模式，使用均匀分布
            for num in range(1, 50):
                number_probs[num] = 1.0 / 49

        # 选择概率最高的数字作为预测结果
        sorted_probs = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
        predicted_numbers = [num for num, _ in sorted_probs[:num_predictions]]

        return predicted_numbers, dict(number_probs)

    def get_model_info(self):
        """获取模型信息"""
        return {
            'algorithm': 'PatternMatcher',
            'is_trained': self.is_trained,
            'pattern_count': len(self.pattern_database),
            'config': self.config
        }

class TrendAnalyzer:
    """趋势识别算法"""

    def __init__(self, config=None):
        """初始化趋势分析器"""
        self.config = config or self._get_default_config()
        self.trend_data = {}
        self.cycle_data = {}
        self.momentum_data = {}
        self.is_trained = False

        logger.info("📈 趋势分析器初始化完成")

    def _get_default_config(self):
        """获取默认配置"""
        return {
            'trend_windows': [10, 20, 50],
            'cycle_periods': [7, 14, 30],
            'momentum_factor': 0.3,
            'cycle_factor': 0.2,
            'trend_factor': 0.5,
            'min_probability': 0.001
        }

    def train(self, data):
        """训练趋势分析器"""
        logger.info("🔄 训练趋势分析器")

        if isinstance(data, pd.DataFrame):
            # 从DataFrame中提取数据
            all_numbers = []
            for i in range(len(data)):
                period_numbers = []
                for j in range(1, 7):  # 假设每期有6个数字
                    if f'数字{j}' in data.columns:
                        period_numbers.append(data.iloc[i][f'数字{j}'])
                all_numbers.append(period_numbers)
        else:
            # 直接使用提供的数据
            all_numbers = data

        # 计算趋势
        self.trend_data = self._calculate_trends(all_numbers)

        # 计算周期性
        self.cycle_data = self._calculate_cycles(all_numbers)

        # 计算动量
        self.momentum_data = self._calculate_momentum(all_numbers)

        self.is_trained = True
        logger.info(f"✅ 趋势分析器训练完成")

        return self

    def _calculate_trends(self, data):
        """计算趋势"""
        trends = {}

        for window in self.config['trend_windows']:
            if len(data) >= window * 2:
                # 计算短期和长期频率
                recent_data = data[-window:]
                earlier_data = data[-window*2:-window]

                recent_freq = self._calculate_frequency(recent_data)
                earlier_freq = self._calculate_frequency(earlier_data)

                # 计算趋势强度
                window_trends = {}
                for num in range(1, 50):
                    recent_rate = recent_freq.get(num, 0)
                    earlier_rate = earlier_freq.get(num, 0)

                    if earlier_rate > 0:
                        trend_strength = (recent_rate - earlier_rate) / earlier_rate
                    else:
                        trend_strength = recent_rate

                    window_trends[num] = trend_strength

                trends[window] = window_trends

        return trends

    def _calculate_cycles(self, data):
        """计算周期性"""
        cycles = {}

        for period in self.config['cycle_periods']:
            if len(data) >= period * 3:
                cycle_strength = {}

                for num in range(1, 50):
                    # 计算该数字在不同周期位置的出现频率
                    position_freq = defaultdict(int)
                    total_count = 0

                    for i, period_numbers in enumerate(data):
                        if num in period_numbers:
                            position = i % period
                            position_freq[position] += 1
                            total_count += 1

                    # 计算周期性强度（方差）
                    if total_count > 0:
                        expected_freq = total_count / period
                        variance = sum((freq - expected_freq) ** 2 for freq in position_freq.values()) / period
                        cycle_strength[num] = variance / max(1, expected_freq)
                    else:
                        cycle_strength[num] = 0

                cycles[period] = cycle_strength

        return cycles

    def _calculate_momentum(self, data):
        """计算动量"""
        if len(data) < 10:
            return {num: 0 for num in range(1, 50)}

        momentum = {}
        recent_data = data[-10:]  # 最近10期

        for num in range(1, 50):
            # 计算该数字最近的出现模式
            appearances = []
            for i, period_numbers in enumerate(recent_data):
                if num in period_numbers:
                    appearances.append(i)

            if len(appearances) >= 2:
                # 计算出现间隔的变化趋势
                intervals = [appearances[i+1] - appearances[i] for i in range(len(appearances)-1)]
                if len(intervals) >= 2:
                    # 间隔是否在缩短（正动量）
                    momentum_score = (intervals[0] - intervals[-1]) / max(1, intervals[0])
                    momentum[num] = momentum_score
                else:
                    momentum[num] = 0
            else:
                momentum[num] = 0

        return momentum

    def _calculate_frequency(self, data):
        """计算频率"""
        frequencies = defaultdict(int)
        total_count = 0

        for period in data:
            for num in period:
                frequencies[num] += 1
                total_count += 1

        # 计算概率
        probabilities = {}
        if total_count > 0:
            for num in range(1, 50):
                probabilities[num] = frequencies.get(num, 0) / total_count

        return probabilities

    def predict(self, previous_numbers=None, period_index=None, num_predictions=2):
        """使用趋势分析进行预测"""
        if not self.is_trained:
            logger.warning("⚠️ 模型尚未训练")
            return [], {}

        number_probs = defaultdict(float)

        # 应用趋势因子
        for window, trends in self.trend_data.items():
            weight = 1.0 / len(self.trend_data)
            for num, trend_strength in trends.items():
                # 将趋势强度转换为概率调整
                trend_adjustment = 1.0 + self.config['trend_factor'] * trend_strength
                number_probs[num] += trend_adjustment * weight

        # 应用周期性因子
        if period_index is not None:
            for period, cycles in self.cycle_data.items():
                position = period_index % period
                weight = 1.0 / len(self.cycle_data)

                for num, cycle_strength in cycles.items():
                    # 基于周期位置调整概率
                    cycle_adjustment = 1.0 + self.config['cycle_factor'] * cycle_strength * np.sin(2 * np.pi * position / period)
                    number_probs[num] += cycle_adjustment * weight

        # 应用动量因子
        for num, momentum_score in self.momentum_data.items():
            momentum_adjustment = 1.0 + self.config['momentum_factor'] * momentum_score
            number_probs[num] *= momentum_adjustment

        # 标准化概率
        total_prob = sum(number_probs.values())
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        else:
            # 如果没有有效的趋势数据，使用均匀分布
            for num in range(1, 50):
                number_probs[num] = 1.0 / 49

        # 选择概率最高的数字作为预测结果
        sorted_probs = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
        predicted_numbers = [num for num, _ in sorted_probs[:num_predictions]]

        return predicted_numbers, dict(number_probs)

    def get_model_info(self):
        """获取模型信息"""
        return {
            'algorithm': 'TrendAnalyzer',
            'is_trained': self.is_trained,
            'trend_windows': list(self.trend_data.keys()),
            'cycle_periods': list(self.cycle_data.keys()),
            'config': self.config
        }
