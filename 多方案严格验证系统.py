#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多预测方案严格验证系统
严格避免过拟合和数据泄露，对2025年1-185期进行全面预测验证
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class StrictValidationSystem:
    """严格验证系统 - 避免过拟合和数据泄露"""
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.full_data = None
        self.train_data = None
        self.test_data = None
        
        # 预测方案配置
        self.prediction_schemes = {
            '马尔可夫基线': self._markov_baseline_prediction,
            '频率分析': self._frequency_analysis_prediction,
            '热冷数字': self._hot_cold_prediction,
            '奇偶平衡': self._odd_even_prediction,
            '数字和预测': self._sum_based_prediction,
            '间隔分析': self._interval_analysis_prediction,
            '组合优化': self._combination_optimization_prediction
        }
        
        self.results = {}
        
    def load_and_prepare_data(self):
        """加载数据并严格分割训练测试集"""
        print(f"🔒 严格数据分割 - 避免数据泄露")
        print("=" * 50)
        
        try:
            # 加载完整数据
            self.full_data = pd.read_csv(self.data_file)
            self.full_data = self.full_data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            print(f"✅ 完整数据加载: {len(self.full_data)}期")
            
            # 严格时间分割：只使用2024年及之前的数据作为训练集
            self.train_data = self.full_data[self.full_data['年份'] <= 2024].copy()
            
            # 测试集：2025年1-185期
            self.test_data = self.full_data[
                (self.full_data['年份'] == 2025) & 
                (self.full_data['期号'] <= 185)
            ].copy()
            
            print(f"✅ 训练集: {len(self.train_data)}期 (≤2024年)")
            print(f"✅ 测试集: {len(self.test_data)}期 (2025年1-185期)")
            print(f"🔒 数据泄露检查: 训练集最新期号 {self.train_data['年份'].max()}年{self.train_data['期号'].max()}期")
            print(f"🔒 测试集范围: 2025年{self.test_data['期号'].min()}-{self.test_data['期号'].max()}期")
            
            # 验证数据分割的严格性
            if self.train_data['年份'].max() >= 2025:
                raise ValueError("❌ 数据泄露风险：训练集包含2025年数据")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def _markov_baseline_prediction(self, prev_numbers, period_info):
        """马尔可夫基线预测"""
        # 构建转移概率矩阵（仅使用训练数据）
        if not hasattr(self, '_markov_prob'):
            self._build_markov_model()
        
        number_probs = defaultdict(float)
        total_prob = 0.0
        
        for prev_num in prev_numbers:
            if prev_num in self._markov_prob:
                for next_num, prob in self._markov_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        # 选择概率最高的2个数字
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            return [num for num, prob in sorted_numbers[:2]]
        else:
            return [1, 2]  # 默认预测
    
    def _build_markov_model(self):
        """构建马尔可夫模型（仅使用训练数据）"""
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 计算转移概率
        self._markov_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                self._markov_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
    
    def _frequency_analysis_prediction(self, prev_numbers, period_info):
        """频率分析预测"""
        # 统计训练集中每个数字的出现频率
        if not hasattr(self, '_freq_stats'):
            self._build_frequency_stats()
        
        # 选择频率最高的2个数字
        sorted_freq = sorted(self._freq_stats.items(), key=lambda x: x[1], reverse=True)
        return [num for num, freq in sorted_freq[:2]]
    
    def _build_frequency_stats(self):
        """构建频率统计（仅使用训练数据）"""
        self._freq_stats = defaultdict(int)
        
        for _, row in self.train_data.iterrows():
            for j in range(1, 7):
                self._freq_stats[row[f'数字{j}']] += 1
    
    def _hot_cold_prediction(self, prev_numbers, period_info):
        """热冷数字预测"""
        # 分析最近N期的热门数字
        if not hasattr(self, '_hot_cold_stats'):
            self._build_hot_cold_stats()
        
        # 选择最热门的2个数字
        sorted_hot = sorted(self._hot_cold_stats.items(), key=lambda x: x[1], reverse=True)
        return [num for num, count in sorted_hot[:2]]
    
    def _build_hot_cold_stats(self):
        """构建热冷统计（仅使用训练数据最近50期）"""
        self._hot_cold_stats = defaultdict(int)
        
        # 使用训练数据的最后50期
        recent_data = self.train_data.tail(50)
        
        for _, row in recent_data.iterrows():
            for j in range(1, 7):
                self._hot_cold_stats[row[f'数字{j}']] += 1
    
    def _odd_even_prediction(self, prev_numbers, period_info):
        """奇偶平衡预测"""
        # 分析训练集的奇偶分布
        if not hasattr(self, '_odd_even_stats'):
            self._build_odd_even_stats()
        
        # 基于奇偶平衡选择数字
        odd_candidates = [n for n in range(1, 50) if n % 2 == 1]
        even_candidates = [n for n in range(1, 50) if n % 2 == 0]
        
        # 选择1个奇数和1个偶数
        odd_choice = np.random.choice(odd_candidates)
        even_choice = np.random.choice(even_candidates)
        
        return [odd_choice, even_choice]
    
    def _build_odd_even_stats(self):
        """构建奇偶统计"""
        self._odd_even_stats = {'odd': 0, 'even': 0}
        
        for _, row in self.train_data.iterrows():
            for j in range(1, 7):
                if row[f'数字{j}'] % 2 == 1:
                    self._odd_even_stats['odd'] += 1
                else:
                    self._odd_even_stats['even'] += 1
    
    def _sum_based_prediction(self, prev_numbers, period_info):
        """基于数字和的预测"""
        # 分析训练集中6个数字和的分布
        if not hasattr(self, '_sum_stats'):
            self._build_sum_stats()
        
        # 选择接近平均和的数字组合
        target_sum = self._avg_sum / 3  # 2个数字的目标和
        
        best_pair = [1, 2]
        min_diff = float('inf')
        
        for i in range(1, 49):
            for j in range(i+1, 50):
                pair_sum = i + j
                diff = abs(pair_sum - target_sum)
                if diff < min_diff:
                    min_diff = diff
                    best_pair = [i, j]
        
        return best_pair
    
    def _build_sum_stats(self):
        """构建数字和统计"""
        sums = []
        for _, row in self.train_data.iterrows():
            period_sum = sum([row[f'数字{j}'] for j in range(1, 7)])
            sums.append(period_sum)
        
        self._avg_sum = np.mean(sums)
        self._sum_std = np.std(sums)
    
    def _interval_analysis_prediction(self, prev_numbers, period_info):
        """间隔分析预测"""
        # 分析数字出现的间隔模式
        if not hasattr(self, '_interval_stats'):
            self._build_interval_stats()
        
        # 基于间隔模式选择数字
        candidates = []
        for num in range(1, 50):
            if num in self._interval_stats and len(self._interval_stats[num]) > 0:
                avg_interval = np.mean(self._interval_stats[num])
                if 1 <= avg_interval <= 10:  # 选择间隔适中的数字
                    candidates.append(num)
        
        if len(candidates) >= 2:
            return np.random.choice(candidates, 2, replace=False).tolist()
        else:
            return [1, 2]
    
    def _build_interval_stats(self):
        """构建间隔统计"""
        self._interval_stats = defaultdict(list)
        
        # 记录每个数字的出现位置
        number_positions = defaultdict(list)
        
        for idx, row in self.train_data.iterrows():
            for j in range(1, 7):
                number_positions[row[f'数字{j}']].append(idx)
        
        # 计算间隔
        for num, positions in number_positions.items():
            if len(positions) > 1:
                intervals = [positions[i] - positions[i-1] for i in range(1, len(positions))]
                self._interval_stats[num] = intervals
    
    def _combination_optimization_prediction(self, prev_numbers, period_info):
        """组合优化预测"""
        # 综合多种方法的结果
        methods = [
            self._frequency_analysis_prediction,
            self._hot_cold_prediction,
            self._markov_baseline_prediction
        ]
        
        all_predictions = []
        for method in methods:
            try:
                pred = method(prev_numbers, period_info)
                all_predictions.extend(pred)
            except:
                continue
        
        # 统计出现频率最高的数字
        if all_predictions:
            from collections import Counter
            counter = Counter(all_predictions)
            most_common = counter.most_common(2)
            return [num for num, count in most_common]
        else:
            return [1, 2]
    
    def run_comprehensive_validation(self):
        """运行全面验证"""
        print(f"\n🔍 开始多方案全面验证")
        print("=" * 60)
        
        # 初始化结果存储
        for scheme_name in self.prediction_schemes.keys():
            self.results[scheme_name] = {
                'predictions': [],
                'hits': [],
                'total_periods': 0,
                'hit_periods': 0,
                'hit_rate': 0.0
            }
        
        # 逐期预测验证
        print(f"预测期数: {len(self.test_data)}期")
        print(f"{'期号':<6} {'实际开奖':<25} {'预测结果':<50}")
        print("-" * 85)
        
        for idx, test_row in self.test_data.iterrows():
            period = test_row['期号']
            actual_numbers = [test_row[f'数字{j}'] for j in range(1, 7)]
            
            # 获取前一期数字作为预测输入
            if idx == self.test_data.index[0]:
                # 第一期使用训练集最后一期
                prev_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                # 使用测试集中的前一期（这是已知的真实数据）
                prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                prev_numbers = set([self.test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            period_info = {'year': test_row['年份'], 'period': period}
            
            # 对每个预测方案进行预测
            period_predictions = {}
            for scheme_name, prediction_func in self.prediction_schemes.items():
                try:
                    predicted = prediction_func(prev_numbers, period_info)
                    period_predictions[scheme_name] = predicted
                    
                    # 计算命中情况
                    predicted_set = set(predicted)
                    actual_set = set(actual_numbers)
                    hit_count = len(predicted_set & actual_set)
                    is_hit = hit_count >= 1
                    
                    # 记录结果
                    self.results[scheme_name]['predictions'].append(predicted)
                    self.results[scheme_name]['hits'].append(is_hit)
                    self.results[scheme_name]['total_periods'] += 1
                    if is_hit:
                        self.results[scheme_name]['hit_periods'] += 1
                        
                except Exception as e:
                    print(f"⚠️ {scheme_name}预测失败: {e}")
                    period_predictions[scheme_name] = [1, 2]
            
            # 显示前10期的详细结果
            if idx - self.test_data.index[0] < 10:
                pred_summary = " | ".join([f"{name}:{pred}" for name, pred in period_predictions.items()])
                print(f"{period:<6} {str(actual_numbers):<25} {pred_summary}")
        
        if len(self.test_data) > 10:
            print(f"... (共{len(self.test_data)}期)")
        
        # 计算最终命中率
        for scheme_name in self.results:
            result = self.results[scheme_name]
            if result['total_periods'] > 0:
                result['hit_rate'] = result['hit_periods'] / result['total_periods']
        
        print(f"\n✅ 全面验证完成")
    
    def generate_performance_report(self):
        """生成性能报告"""
        print(f"\n📊 多方案性能报告")
        print("=" * 70)
        
        # 按命中率排序
        sorted_results = sorted(self.results.items(), key=lambda x: x[1]['hit_rate'], reverse=True)
        
        print(f"{'排名':<4} {'预测方案':<15} {'总期数':<8} {'命中期数':<8} {'命中率':<10} {'性能等级'}")
        print("-" * 70)
        
        for rank, (scheme_name, result) in enumerate(sorted_results, 1):
            hit_rate = result['hit_rate']
            total_periods = result['total_periods']
            hit_periods = result['hit_periods']
            
            # 性能等级评定
            if hit_rate >= 0.4:
                level = "⭐⭐⭐⭐⭐"
            elif hit_rate >= 0.3:
                level = "⭐⭐⭐⭐"
            elif hit_rate >= 0.25:
                level = "⭐⭐⭐"
            elif hit_rate >= 0.2:
                level = "⭐⭐"
            else:
                level = "⭐"
            
            medal = "🥇" if rank == 1 else "🥈" if rank == 2 else "🥉" if rank == 3 else "  "
            
            print(f"{medal:<4} {scheme_name:<15} {total_periods:<8} {hit_periods:<8} {hit_rate:.3f}      {level}")
        
        # 统计分析
        print(f"\n📈 统计分析")
        print("-" * 40)
        
        hit_rates = [result['hit_rate'] for result in self.results.values()]
        print(f"平均命中率: {np.mean(hit_rates):.3f}")
        print(f"最高命中率: {np.max(hit_rates):.3f}")
        print(f"最低命中率: {np.min(hit_rates):.3f}")
        print(f"标准差: {np.std(hit_rates):.3f}")
        
        # 基线对比
        random_baseline = 2/49 * 2  # 理论随机基线
        print(f"\n📊 基线对比")
        print("-" * 40)
        print(f"理论随机基线: {random_baseline:.3f} ({random_baseline*100:.1f}%)")
        
        for scheme_name, result in sorted_results:
            diff = result['hit_rate'] - random_baseline
            print(f"{scheme_name}: {diff:+.3f} ({diff*100:+.1f}个百分点)")
    
    def create_visualization(self):
        """创建可视化图表"""
        print(f"\n🎨 生成可视化分析")
        print("=" * 50)
        
        # 创建综合分析图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 命中率对比
        scheme_names = list(self.results.keys())
        hit_rates = [self.results[name]['hit_rate'] for name in scheme_names]
        
        colors = plt.cm.Set3(np.linspace(0, 1, len(scheme_names)))
        bars1 = ax1.bar(scheme_names, hit_rates, color=colors, alpha=0.8, edgecolor='black')
        
        # 添加基线
        random_baseline = 2/49 * 2
        ax1.axhline(y=random_baseline, color='red', linestyle='--', linewidth=2, 
                   label=f'理论基线({random_baseline:.3f})')
        
        for bar, rate in zip(bars1, hit_rates):
            ax1.text(bar.get_x() + bar.get_width()/2., rate + 0.005,
                    f'{rate:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=9)
        
        ax1.set_title('多方案命中率对比', fontsize=14, fontweight='bold')
        ax1.set_ylabel('命中率', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45, ha='right')
        
        # 2. 命中期数统计
        hit_periods = [self.results[name]['hit_periods'] for name in scheme_names]
        total_periods = [self.results[name]['total_periods'] for name in scheme_names]
        
        bars2 = ax2.bar(scheme_names, hit_periods, color=colors, alpha=0.8, edgecolor='black')
        
        for bar, hit, total in zip(bars2, hit_periods, total_periods):
            ax2.text(bar.get_x() + bar.get_width()/2., hit + 1,
                    f'{hit}/{total}', ha='center', va='bottom', fontweight='bold', fontsize=9)
        
        ax2.set_title('命中期数统计', fontsize=14, fontweight='bold')
        ax2.set_ylabel('命中期数', fontsize=12)
        ax2.grid(True, alpha=0.3)
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45, ha='right')
        
        # 3. 性能分布
        ax3.hist(hit_rates, bins=10, alpha=0.7, color='skyblue', edgecolor='black')
        ax3.axvline(x=np.mean(hit_rates), color='red', linestyle='--', linewidth=2, label='平均值')
        ax3.axvline(x=random_baseline, color='orange', linestyle='--', linewidth=2, label='理论基线')
        
        ax3.set_title('命中率分布', fontsize=14, fontweight='bold')
        ax3.set_xlabel('命中率', fontsize=12)
        ax3.set_ylabel('方案数量', fontsize=12)
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 累积命中率趋势（以最佳方案为例）
        best_scheme = max(self.results.keys(), key=lambda x: self.results[x]['hit_rate'])
        best_hits = self.results[best_scheme]['hits']
        
        if len(best_hits) > 0:
            cumulative_hits = np.cumsum(best_hits)
            periods = np.arange(1, len(best_hits) + 1)
            cumulative_rates = cumulative_hits / periods
            
            ax4.plot(periods[:50], cumulative_rates[:50], linewidth=2, marker='o', markersize=3,
                    label=f'{best_scheme}累积命中率')
            ax4.axhline(y=random_baseline, color='red', linestyle='--', linewidth=2, label='理论基线')
            
            ax4.set_title(f'{best_scheme} 累积命中率趋势', fontsize=14, fontweight='bold')
            ax4.set_xlabel('期数', fontsize=12)
            ax4.set_ylabel('累积命中率', fontsize=12)
            ax4.legend()
            ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('多方案严格验证分析图.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✅ 可视化图表已生成: 多方案严格验证分析图.png")
    
    def save_results(self):
        """保存验证结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存详细结果
        results_file = f"多方案严格验证结果_{timestamp}.json"
        
        # 处理numpy类型
        def convert_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_types(v) for v in obj]
            else:
                return obj
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(convert_types(self.results), f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 验证结果已保存: {results_file}")

def main():
    """主函数"""
    print("🔒 多预测方案严格验证系统")
    print("严格避免过拟合和数据泄露")
    print("=" * 80)
    
    # 设置随机种子确保可重现性
    np.random.seed(42)
    
    validator = StrictValidationSystem()
    
    # 1. 加载和准备数据
    if not validator.load_and_prepare_data():
        return
    
    # 2. 运行全面验证
    validator.run_comprehensive_validation()
    
    # 3. 生成性能报告
    validator.generate_performance_report()
    
    # 4. 创建可视化
    validator.create_visualization()
    
    # 5. 保存结果
    validator.save_results()
    
    print(f"\n🎉 多方案严格验证完成")
    print("=" * 50)
    print("✅ 严格避免了数据泄露")
    print("✅ 严格避免了过拟合")
    print("✅ 提供了可靠的性能评估")

if __name__ == "__main__":
    main()
