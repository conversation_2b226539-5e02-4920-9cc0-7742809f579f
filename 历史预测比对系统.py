#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
历史预测比对系统
基于真实的lottery_data_clean_no_special.csv数据
对1-204期进行预测和比对，生成完整的预测比对CSV文件
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import warnings
warnings.filterwarnings('ignore')

class HistoricalPredictionComparisonSystem:
    """历史预测比对系统"""
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.output_file = "历史预测比对结果.csv"
        
        # 加载真实数据
        self.real_data = None
        self.prediction_results = []
        
        # 预测模型参数
        self.ensemble_weights = {
            'frequency': 0.4,
            'markov': 0.35,
            'statistical': 0.25
        }
        
    def load_real_data(self):
        """加载真实数据"""
        try:
            self.real_data = pd.read_csv(self.data_file, encoding='utf-8')
            # 移除空行
            self.real_data = self.real_data.dropna()
            print(f"✅ 成功加载真实数据: {len(self.real_data)} 条记录")
            print(f"   数据范围: {int(self.real_data.iloc[0]['年份'])}年{int(self.real_data.iloc[0]['期号'])}期 - {int(self.real_data.iloc[-1]['年份'])}年{int(self.real_data.iloc[-1]['期号'])}期")
            return True
        except Exception as e:
            print(f"❌ 加载真实数据失败: {e}")
            return False
    
    def build_prediction_models(self, train_data):
        """基于训练数据构建预测模型"""
        # 1. 频率分析模型
        all_numbers = []
        for _, row in train_data.iterrows():
            for i in range(1, 7):
                num = row[f'数字{i}']
                if pd.notna(num):
                    all_numbers.append(int(num))
        
        number_freq = Counter(all_numbers)
        total_count = sum(number_freq.values())
        number_probs = {num: count/total_count for num, count in number_freq.items()}
        
        # 2. 马尔可夫转移模型
        transition_probs = defaultdict(lambda: defaultdict(float))
        
        for i in range(1, len(train_data)):
            prev_numbers = []
            curr_numbers = []
            
            # 获取前一期数字
            for j in range(1, 7):
                num = train_data.iloc[i-1][f'数字{j}']
                if pd.notna(num):
                    prev_numbers.append(int(num))
            
            # 获取当前期数字
            for j in range(1, 7):
                num = train_data.iloc[i][f'数字{j}']
                if pd.notna(num):
                    curr_numbers.append(int(num))
            
            # 构建转移概率
            for prev_num in prev_numbers:
                for curr_num in curr_numbers:
                    transition_probs[prev_num][curr_num] += 1
        
        # 归一化转移概率
        for prev_num in transition_probs:
            total = sum(transition_probs[prev_num].values())
            if total > 0:
                for curr_num in transition_probs[prev_num]:
                    transition_probs[prev_num][curr_num] /= total
        
        # 3. 统计特征模型
        sums = []
        ranges = []
        
        for _, row in train_data.iterrows():
            numbers = []
            for i in range(1, 7):
                num = row[f'数字{i}']
                if pd.notna(num):
                    numbers.append(int(num))
            
            if len(numbers) >= 2:
                sums.append(sum(numbers))
                ranges.append(max(numbers) - min(numbers))
        
        statistical_features = {
            'avg_sum': np.mean(sums) if sums else 150,
            'avg_range': np.mean(ranges) if ranges else 40
        }
        
        return {
            'number_probs': number_probs,
            'transition_probs': transition_probs,
            'statistical_features': statistical_features
        }
    
    def predict_next_period(self, models, current_numbers, period_info):
        """预测下期数字"""
        try:
            # 1. 频率分析预测
            freq_candidates = sorted(models['number_probs'].items(), 
                                   key=lambda x: x[1], reverse=True)[:15]
            
            # 2. 马尔可夫预测
            markov_probs = defaultdict(float)
            for curr_num in current_numbers:
                if curr_num in models['transition_probs']:
                    for next_num, prob in models['transition_probs'][curr_num].items():
                        markov_probs[next_num] += prob
            
            # 3. 集成预测
            final_scores = defaultdict(float)
            
            # 频率权重
            for num, prob in freq_candidates:
                final_scores[num] += prob * self.ensemble_weights['frequency']
            
            # 马尔可夫权重
            total_markov = sum(markov_probs.values())
            if total_markov > 0:
                for num, prob in markov_probs.items():
                    final_scores[num] += (prob / total_markov) * self.ensemble_weights['markov']
            
            # 统计权重
            target_avg = models['statistical_features']['avg_sum'] / 6
            for num in range(1, 50):
                distance_factor = 1.0 / (1.0 + abs(num - target_avg) / 10)
                final_scores[num] += distance_factor * self.ensemble_weights['statistical']
            
            # 选择得分最高的2个数字作为预测
            sorted_scores = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
            predicted_numbers = [num for num, _ in sorted_scores[:2]]
            
            # 计算置信度
            confidence = (sorted_scores[0][1] + sorted_scores[1][1]) / 2
            
            # 计算评分
            score = self.calculate_prediction_score(predicted_numbers, confidence, period_info)
            
            return {
                'predicted_numbers': predicted_numbers,
                'confidence': confidence,
                'score': score
            }
            
        except Exception as e:
            print(f"⚠️ 预测失败 (期号{period_info['period']}): {e}")
            # 返回默认预测
            return {
                'predicted_numbers': [25, 30],
                'confidence': 0.15,
                'score': {'score': 25.0, 'grade': 'B (预测失败)', 'risk_level': 'medium'}
            }
    
    def calculate_prediction_score(self, predicted_numbers, confidence, period_info):
        """计算预测评分"""
        try:
            # 基础评分
            base_score = confidence * 1000
            
            # 期号调整因子
            period = period_info['period']
            if period <= 50:
                period_factor = 0.7
            elif period <= 100:
                period_factor = 0.85
            elif period <= 150:
                period_factor = 1.0
            else:
                period_factor = 1.15
            
            # 数字特征调整
            num_sum = sum(predicted_numbers)
            if 20 <= num_sum <= 80:
                feature_factor = 1.15
            else:
                feature_factor = 0.95
            
            # 年份调整（早期数据不确定性更高）
            year = period_info['year']
            if year <= 2021:
                year_factor = 0.8
            elif year <= 2022:
                year_factor = 0.9
            elif year <= 2023:
                year_factor = 1.0
            else:
                year_factor = 1.1
            
            # 最终评分
            final_score = base_score * period_factor * feature_factor * year_factor
            final_score = max(8.0, min(46.0, final_score))
            
            # 确定等级
            if final_score >= 40:
                grade = "A+ (极高概率)"
                risk_level = "very_low"
            elif final_score >= 32:
                grade = "A (较高概率)"
                risk_level = "low"
            elif final_score >= 26:
                grade = "B+ (中高概率)"
                risk_level = "medium_low"
            elif final_score >= 20:
                grade = "B (中等概率)"
                risk_level = "medium"
            elif final_score >= 15:
                grade = "C+ (中低概率)"
                risk_level = "medium_high"
            elif final_score >= 12:
                grade = "C (较低概率)"
                risk_level = "high"
            else:
                grade = "D (低概率)"
                risk_level = "very_high"
            
            return {
                'score': round(final_score, 1),
                'grade': grade,
                'risk_level': risk_level
            }
            
        except Exception as e:
            return {
                'score': 25.0,
                'grade': 'B (评分失败)',
                'risk_level': 'medium'
            }
    
    def compare_prediction_with_actual(self, predicted_numbers, actual_numbers):
        """比较预测与实际结果"""
        try:
            pred_set = set(predicted_numbers)
            actual_set = set(actual_numbers)
            
            # 计算命中情况
            hit_numbers = pred_set & actual_set
            hit_count = len(hit_numbers)
            is_hit = "是" if hit_count > 0 else "否"
            hit_numbers_str = ",".join(map(str, sorted(hit_numbers))) if hit_numbers else ""
            
            # 计算命中率
            hit_rate = hit_count / len(pred_set) if len(pred_set) > 0 else 0
            
            return {
                'hit_count': hit_count,
                'is_hit': is_hit,
                'hit_numbers': hit_numbers_str,
                'hit_rate': hit_rate
            }
            
        except Exception as e:
            return {
                'hit_count': 0,
                'is_hit': '否',
                'hit_numbers': '',
                'hit_rate': 0.0
            }
    
    def run_historical_prediction_comparison(self):
        """运行历史预测比对"""
        print("🚀 开始历史预测比对...")
        print("=" * 60)
        
        if not self.load_real_data():
            return False
        
        # 初始化结果列表
        self.prediction_results = []
        
        # 设置最小训练数据量
        min_train_size = 10
        
        print(f"📊 开始逐期预测比对...")
        print(f"   最小训练数据量: {min_train_size} 期")
        print(f"   预测范围: 第{min_train_size + 1}期 - 第{len(self.real_data)}期")
        
        # 逐期进行预测和比对
        for i in range(min_train_size, len(self.real_data)):
            try:
                # 获取训练数据（前i期）
                train_data = self.real_data.iloc[:i].copy()
                
                # 获取当前期信息（用于预测下期）
                current_row = self.real_data.iloc[i-1]
                current_year = int(current_row['年份'])
                current_period = int(current_row['期号'])
                current_numbers = [int(current_row[f'数字{j}']) for j in range(1, 7)]
                
                # 获取实际的下期数据（用于比对）
                actual_row = self.real_data.iloc[i]
                actual_year = int(actual_row['年份'])
                actual_period = int(actual_row['期号'])
                actual_numbers = [int(actual_row[f'数字{j}']) for j in range(1, 7)]
                
                # 构建预测模型
                models = self.build_prediction_models(train_data)
                
                # 进行预测
                period_info = {'year': current_year, 'period': current_period}
                prediction_result = self.predict_next_period(models, current_numbers, period_info)
                
                # 比对预测结果
                comparison_result = self.compare_prediction_with_actual(
                    prediction_result['predicted_numbers'], actual_numbers
                )
                
                # 记录结果
                result_record = {
                    '训练数据量': i,
                    '基于期号': f"{current_year}年{current_period}期",
                    '基于开奖': str(current_numbers),
                    '预测期号': f"{actual_year}年{actual_period}期",
                    '预测数字1': prediction_result['predicted_numbers'][0],
                    '预测数字2': prediction_result['predicted_numbers'][1],
                    '预测置信度': round(prediction_result['confidence'], 4),
                    '预测评分': prediction_result['score']['score'],
                    '评分等级': prediction_result['score']['grade'],
                    '风险等级': prediction_result['score']['risk_level'],
                    '实际数字1': actual_numbers[0],
                    '实际数字2': actual_numbers[1],
                    '实际数字3': actual_numbers[2],
                    '实际数字4': actual_numbers[3],
                    '实际数字5': actual_numbers[4],
                    '实际数字6': actual_numbers[5],
                    '命中数量': comparison_result['hit_count'],
                    '是否命中': comparison_result['is_hit'],
                    '命中数字': comparison_result['hit_numbers'],
                    '命中率': round(comparison_result['hit_rate'], 4),
                    '预测方法': 'Best_Ensemble_Method_Historical'
                }
                
                self.prediction_results.append(result_record)
                
                # 显示进度
                if (i - min_train_size + 1) % 50 == 0 or i == len(self.real_data) - 1:
                    progress = (i - min_train_size + 1) / (len(self.real_data) - min_train_size) * 100
                    print(f"   进度: {progress:.1f}% ({i - min_train_size + 1}/{len(self.real_data) - min_train_size})")
                
            except Exception as e:
                print(f"⚠️ 处理第{i+1}期时出错: {e}")
                continue
        
        print(f"✅ 历史预测比对完成，共处理 {len(self.prediction_results)} 期")
        
        # 保存结果
        return self.save_results()
    
    def save_results(self):
        """保存预测比对结果"""
        try:
            # 转换为DataFrame
            results_df = pd.DataFrame(self.prediction_results)
            
            # 保存到CSV文件
            results_df.to_csv(self.output_file, index=False, encoding='utf-8')
            
            print(f"✅ 结果已保存到: {self.output_file}")
            
            # 生成统计报告
            self.generate_statistics_report(results_df)
            
            return True
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
            return False
    
    def generate_statistics_report(self, results_df):
        """生成统计报告"""
        print(f"\n📊 预测比对统计报告")
        print("=" * 50)
        
        try:
            total_predictions = len(results_df)
            hit_predictions = len(results_df[results_df['是否命中'] == '是'])
            overall_hit_rate = hit_predictions / total_predictions if total_predictions > 0 else 0
            
            print(f"总预测次数: {total_predictions}")
            print(f"命中次数: {hit_predictions}")
            print(f"整体命中率: {overall_hit_rate:.1%}")
            
            # 按年份统计
            print(f"\n📅 按年份统计:")
            for year in sorted(results_df['预测期号'].str[:4].unique()):
                year_data = results_df[results_df['预测期号'].str.startswith(year)]
                year_hits = len(year_data[year_data['是否命中'] == '是'])
                year_total = len(year_data)
                year_rate = year_hits / year_total if year_total > 0 else 0
                print(f"   {year}年: {year_rate:.1%} ({year_hits}/{year_total})")
            
            # 按评分等级统计
            print(f"\n🏆 按评分等级统计:")
            grade_stats = results_df.groupby('评分等级').agg({
                '是否命中': lambda x: (x == '是').sum(),
                '预测期号': 'count'
            }).rename(columns={'是否命中': '命中次数', '预测期号': '总次数'})
            
            grade_stats['命中率'] = grade_stats['命中次数'] / grade_stats['总次数']
            
            for grade, stats in grade_stats.iterrows():
                print(f"   {grade}: {stats['命中率']:.1%} ({int(stats['命中次数'])}/{int(stats['总次数'])})")
            
            # 平均评分
            avg_score = results_df['预测评分'].mean()
            print(f"\n📈 平均预测评分: {avg_score:.1f}")
            
            # 置信度统计
            avg_confidence = results_df['预测置信度'].mean()
            print(f"📈 平均置信度: {avg_confidence:.3f}")
            
        except Exception as e:
            print(f"⚠️ 统计报告生成失败: {e}")

def main():
    """主函数"""
    print("🎯 历史预测比对系统")
    print("基于真实数据进行1-204期预测和比对")
    print("=" * 70)
    
    system = HistoricalPredictionComparisonSystem()
    
    # 确认执行
    print("⚠️ 注意：此操作将对所有历史数据进行预测比对，可能需要较长时间")
    confirm = input("确认开始历史预测比对? (y/n): ").strip().lower()
    
    if confirm != 'y':
        print("❌ 操作已取消")
        return
    
    # 执行预测比对
    success = system.run_historical_prediction_comparison()
    
    if success:
        print(f"\n🎉 历史预测比对完成！")
        print(f"结果文件: 历史预测比对结果.csv")
        print(f"可以使用Excel或其他工具查看详细结果")
    else:
        print(f"\n❌ 历史预测比对失败")

if __name__ == "__main__":
    main()
