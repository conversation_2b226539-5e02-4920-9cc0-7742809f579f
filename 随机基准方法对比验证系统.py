#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
随机基准方法对比验证系统
实施8种随机/统计方法与29.2%马尔可夫基线进行对比验证
确定马尔可夫基线是否真正优于纯随机方法
"""

import pandas as pd
import numpy as np
import random
import json
from collections import defaultdict
from datetime import datetime
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class RandomBaselineComparison:
    """
    随机基准方法对比验证系统
    实施多种随机方法与马尔可夫基线进行严格对比
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.data = None
        self.test_data = None
        self.markov_baseline = 0.292  # 29.2%马尔可夫基线
        
        # 随机方法配置
        self.random_methods = {
            'bernoulli_process': {
                'name': '伯努利过程',
                'description': '基于固定概率p的独立试验',
                'params': {'p': 0.12}  # 每个数字被选中的概率
            },
            'iid_sampling': {
                'name': '独立同分布抽样',
                'description': '从均匀分布中独立抽取数字',
                'params': {}
            },
            'simple_random': {
                'name': '简单随机抽样',
                'description': '无放回的随机选择',
                'params': {}
            },
            'pure_random': {
                'name': '纯随机决策算法',
                'description': '完全随机的数字生成',
                'params': {}
            },
            'monte_carlo': {
                'name': '蒙特卡洛独立抽样法',
                'description': '基于概率分布的随机抽样',
                'params': {'distribution': 'uniform'}
            },
            'random_walk': {
                'name': '随机游走',
                'description': '基于前一状态的随机移动',
                'params': {'step_size': 5}
            },
            'random_restart_hill': {
                'name': '随机重启爬山法',
                'description': '带重启机制的局部搜索',
                'params': {'restart_prob': 0.1}
            },
            'simulated_annealing': {
                'name': '模拟退火',
                'description': '基于温度衰减的随机优化',
                'params': {'initial_temp': 100, 'cooling_rate': 0.95}
            }
        }
        
        self.results = {}
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            # 使用2025年1-182期作为测试数据
            self.test_data = self.data[(self.data['年份'] == 2025) & (self.data['期号'] <= 182)].copy()
            
            print(f"✅ 数据加载成功: {len(self.data)}期")
            print(f"✅ 测试数据: {len(self.test_data)}期 (2025年1-182期)")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def bernoulli_process_prediction(self, params):
        """伯努利过程预测"""
        p = params['p']
        candidates = []
        
        # 对每个数字进行伯努利试验
        for num in range(1, 50):
            if random.random() < p:
                candidates.append(num)
        
        # 确保有至少2个候选数字
        if len(candidates) < 2:
            candidates = random.sample(range(1, 50), 2)
        elif len(candidates) > 2:
            candidates = random.sample(candidates, 2)
        
        return candidates[:2]
    
    def iid_sampling_prediction(self, params):
        """独立同分布抽样预测"""
        # 从1-49的均匀分布中独立抽取2个数字
        return random.sample(range(1, 50), 2)
    
    def simple_random_prediction(self, params):
        """简单随机抽样预测"""
        # 无放回随机选择2个数字
        return random.sample(range(1, 50), 2)
    
    def pure_random_prediction(self, params):
        """纯随机决策算法预测"""
        # 完全随机生成2个数字（允许重复）
        return [random.randint(1, 49) for _ in range(2)]
    
    def monte_carlo_prediction(self, params):
        """蒙特卡洛独立抽样法预测"""
        distribution = params['distribution']
        
        if distribution == 'uniform':
            # 均匀分布抽样
            return random.sample(range(1, 50), 2)
        else:
            # 默认均匀分布
            return random.sample(range(1, 50), 2)
    
    def random_walk_prediction(self, params, previous_numbers=None):
        """随机游走预测"""
        step_size = params['step_size']
        
        if previous_numbers is None or len(previous_numbers) == 0:
            # 初始随机位置
            start_pos = random.randint(1, 49)
        else:
            # 从前一期的某个数字开始
            start_pos = random.choice(list(previous_numbers))
        
        predictions = []
        current_pos = start_pos
        
        for _ in range(2):
            # 随机游走步长
            step = random.randint(-step_size, step_size)
            current_pos = max(1, min(49, current_pos + step))
            predictions.append(current_pos)
            
            # 为下一个预测调整位置
            current_pos = random.randint(1, 49)
        
        return predictions
    
    def random_restart_hill_prediction(self, params, previous_numbers=None):
        """随机重启爬山法预测"""
        restart_prob = params['restart_prob']
        
        if previous_numbers is None or len(previous_numbers) == 0:
            current_best = random.sample(range(1, 50), 2)
        else:
            # 从前一期数字开始爬山
            current_best = random.sample(list(previous_numbers), min(2, len(previous_numbers)))
            if len(current_best) < 2:
                current_best.extend(random.sample(range(1, 50), 2 - len(current_best)))
        
        # 随机重启机制
        if random.random() < restart_prob:
            current_best = random.sample(range(1, 50), 2)
        
        # 局部搜索（小幅调整）
        for i in range(len(current_best)):
            adjustment = random.randint(-3, 3)
            current_best[i] = max(1, min(49, current_best[i] + adjustment))
        
        return current_best[:2]
    
    def simulated_annealing_prediction(self, params, iteration=0):
        """模拟退火预测"""
        initial_temp = params['initial_temp']
        cooling_rate = params['cooling_rate']
        
        # 当前温度
        temperature = initial_temp * (cooling_rate ** iteration)
        
        # 初始解
        current_solution = random.sample(range(1, 50), 2)
        
        # 模拟退火过程
        for _ in range(10):  # 简化的退火过程
            # 生成邻域解
            neighbor = current_solution.copy()
            idx = random.randint(0, 1)
            neighbor[idx] = max(1, min(49, neighbor[idx] + random.randint(-5, 5)))
            
            # 接受概率（简化的能量函数）
            if temperature > 0:
                accept_prob = np.exp(-abs(neighbor[idx] - current_solution[idx]) / temperature)
                if random.random() < accept_prob:
                    current_solution = neighbor
        
        return current_solution
    
    def predict_with_method(self, method_name, params, previous_numbers=None, iteration=0):
        """使用指定方法进行预测"""
        if method_name == 'bernoulli_process':
            return self.bernoulli_process_prediction(params)
        elif method_name == 'iid_sampling':
            return self.iid_sampling_prediction(params)
        elif method_name == 'simple_random':
            return self.simple_random_prediction(params)
        elif method_name == 'pure_random':
            return self.pure_random_prediction(params)
        elif method_name == 'monte_carlo':
            return self.monte_carlo_prediction(params)
        elif method_name == 'random_walk':
            return self.random_walk_prediction(params, previous_numbers)
        elif method_name == 'random_restart_hill':
            return self.random_restart_hill_prediction(params, previous_numbers)
        elif method_name == 'simulated_annealing':
            return self.simulated_annealing_prediction(params, iteration)
        else:
            return random.sample(range(1, 50), 2)
    
    def validate_random_method(self, method_name, method_config, num_runs=100):
        """验证随机方法性能（多次运行取平均）"""
        print(f"\n🎲 验证 {method_config['name']}")
        print("-" * 40)
        
        all_success_rates = []
        
        # 多次运行以获得稳定的随机方法性能
        for run in range(num_runs):
            predictions = []
            correct_predictions = 0
            
            for idx, test_row in self.test_data.iterrows():
                period_num = test_row['期号']
                actual_numbers = set([test_row[f'数字{j}'] for j in range(1, 7)])
                
                # 获取前一期数字（用于某些需要历史信息的方法）
                if idx == self.test_data.index[0]:
                    prev_numbers = set(range(1, 7))  # 默认前一期
                else:
                    prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                    prev_numbers = set([self.test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
                
                # 预测
                predicted_numbers = self.predict_with_method(
                    method_name, 
                    method_config['params'], 
                    prev_numbers, 
                    iteration=idx
                )
                
                # 评估 - 使用统一验证标准：2个预测中至少1个命中
                hit_count = len(set(predicted_numbers) & actual_numbers)
                is_success = hit_count >= 1
                
                if is_success:
                    correct_predictions += 1
                
                predictions.append({
                    'period': period_num,
                    'predicted': predicted_numbers,
                    'actual': list(actual_numbers),
                    'hits': hit_count,
                    'success': is_success
                })
            
            # 计算本次运行的成功率
            success_rate = correct_predictions / len(predictions) if len(predictions) > 0 else 0
            all_success_rates.append(success_rate)
        
        # 计算统计指标
        mean_success_rate = np.mean(all_success_rates)
        std_success_rate = np.std(all_success_rates)
        
        print(f"  运行次数: {num_runs}")
        print(f"  平均成功率: {mean_success_rate:.3f} ({mean_success_rate*100:.1f}%)")
        print(f"  标准差: {std_success_rate:.3f}")
        print(f"  vs马尔可夫基线: {(mean_success_rate - self.markov_baseline)*100:+.1f}个百分点")
        
        return {
            'method_name': method_config['name'],
            'method_key': method_name,
            'description': method_config['description'],
            'num_runs': num_runs,
            'mean_success_rate': mean_success_rate,
            'std_success_rate': std_success_rate,
            'all_success_rates': all_success_rates,
            'improvement_vs_baseline': mean_success_rate - self.markov_baseline,
            'sample_predictions': predictions[:10]  # 保存前10个预测作为样本
        }
    
    def run_all_random_methods(self):
        """运行所有随机方法验证"""
        print(f"\n🎯 随机基准方法对比验证")
        print(f"马尔可夫基线: {self.markov_baseline:.3f} (29.2%)")
        print("=" * 60)
        
        for method_key, method_config in self.random_methods.items():
            result = self.validate_random_method(method_key, method_config, num_runs=50)
            self.results[method_key] = result
        
        print(f"\n✅ 所有随机方法验证完成")
    
    def statistical_significance_tests(self):
        """统计显著性检验"""
        print(f"\n📊 统计显著性检验")
        print("=" * 60)
        
        significance_results = {}
        
        for method_key, result in self.results.items():
            mean_rate = result['mean_success_rate']
            std_rate = result['std_success_rate']
            n_runs = result['num_runs']
            
            # 使用t检验比较与基线的差异
            t_stat = (mean_rate - self.markov_baseline) / (std_rate / np.sqrt(n_runs))
            p_value = 2 * (1 - stats.t.cdf(abs(t_stat), n_runs - 1))  # 双侧检验
            
            is_significant = p_value < 0.05
            
            significance_results[method_key] = {
                'p_value': float(p_value),
                'significant': is_significant,
                'direction': 'better' if mean_rate > self.markov_baseline else 'worse',
                't_statistic': float(t_stat)
            }
            
            print(f"  {result['method_name']}:")
            print(f"    平均成功率: {mean_rate:.3f}")
            print(f"    t统计量: {t_stat:.3f}")
            print(f"    p值: {p_value:.4f}")
            print(f"    显著性: {'是' if is_significant else '否'}")
            print(f"    方向: {'优于' if mean_rate > self.markov_baseline else '劣于'}基线")
        
        return significance_results

def main():
    """主函数"""
    print("🎯 随机基准方法对比验证系统")
    print("验证29.2%马尔可夫基线是否真正优于纯随机方法")
    print("=" * 70)
    
    # 设置随机种子以确保可重现性
    random.seed(42)
    np.random.seed(42)
    
    # 初始化验证系统
    validator = RandomBaselineComparison()
    
    # 1. 加载数据
    if not validator.load_data():
        return
    
    # 2. 运行所有随机方法验证
    validator.run_all_random_methods()
    
    # 3. 统计显著性检验
    significance_results = validator.statistical_significance_tests()
    
    # 4. 生成性能对比表格
    print(f"\n📋 性能对比表格")
    print("=" * 80)
    print(f"{'方法名称':<20} {'平均成功率':<12} {'标准差':<10} {'vs基线':<12} {'显著性':<8}")
    print("-" * 80)
    
    # 马尔可夫基线
    print(f"{'马尔可夫基线':<20} {validator.markov_baseline:.3f}        基准       +0.000       基准")
    
    # 随机方法结果
    for method_key, result in validator.results.items():
        sig_result = significance_results[method_key]
        sig_mark = "✓" if sig_result['significant'] else "✗"
        
        print(f"{result['method_name']:<20} {result['mean_success_rate']:.3f}        "
              f"{result['std_success_rate']:.3f}      "
              f"{result['improvement_vs_baseline']:+.3f}       {sig_mark}")
    
    # 5. 保存详细结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"随机基准方法对比验证结果_{timestamp}.json"
    
    # 准备保存数据
    save_data = {
        'markov_baseline': float(validator.markov_baseline),
        'test_periods': len(validator.test_data),
        'validation_date': datetime.now().isoformat(),
        'methods_tested': len(validator.random_methods),
        'results': {},
        'significance_tests': {}
    }
    
    for method_key, result in validator.results.items():
        save_data['results'][method_key] = {
            'method_name': result['method_name'],
            'description': result['description'],
            'mean_success_rate': float(result['mean_success_rate']),
            'std_success_rate': float(result['std_success_rate']),
            'improvement_vs_baseline': float(result['improvement_vs_baseline']),
            'num_runs': int(result['num_runs'])
        }
    
    for method_key, sig_result in significance_results.items():
        save_data['significance_tests'][method_key] = {
            'p_value': float(sig_result['p_value']),
            'significant': bool(sig_result['significant']),
            'direction': str(sig_result['direction']),
            't_statistic': float(sig_result['t_statistic'])
        }
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(save_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 详细结果已保存: {results_file}")
    
    # 6. 分析结论
    print(f"\n🎯 分析结论")
    print("=" * 50)
    
    better_methods = [k for k, v in validator.results.items() 
                     if v['mean_success_rate'] > validator.markov_baseline]
    
    significant_better = [k for k, v in significance_results.items() 
                         if v['significant'] and v['direction'] == 'better']
    
    if significant_better:
        print(f"⚠️ 发现显著优于马尔可夫基线的随机方法:")
        for method_key in significant_better:
            result = validator.results[method_key]
            improvement = (result['mean_success_rate'] - validator.markov_baseline) * 100
            print(f"  - {result['method_name']}: +{improvement:.1f}个百分点")
    elif better_methods:
        print(f"📊 发现略优于马尔可夫基线的随机方法（但未达到统计显著性）:")
        for method_key in better_methods:
            result = validator.results[method_key]
            improvement = (result['mean_success_rate'] - validator.markov_baseline) * 100
            print(f"  - {result['method_name']}: +{improvement:.1f}个百分点")
    else:
        print(f"✅ 马尔可夫基线优于所有随机方法")
        print(f"   这验证了29.2%基线的真实价值")
    
    print(f"\n💡 核心洞察:")
    print(f"  1. 马尔可夫基线相对于随机方法的优势程度")
    print(f"  2. 复杂预测方法相对于简单随机方法的真实价值")
    print(f"  3. 为预测系统提供了更全面的性能基准")

if __name__ == "__main__":
    main()
