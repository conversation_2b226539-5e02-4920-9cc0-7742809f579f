#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测数字命中概率深度分析
Deep Analysis of Prediction Hit Probability

基于验证数据深入分析预测数字在当期命中的概率

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from collections import Counter
import seaborn as sns
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class PredictionProbabilityAnalyzer:
    """预测概率分析器"""

    def __init__(self):
        """初始化分析器"""
        self.data = None
        self.analysis_results = {}

        print("🎯 预测数字命中概率深度分析")
        print("="*50)

    def load_validation_data(self):
        """加载验证数据"""
        print("📊 加载验证数据")
        print("="*20)

        df = pd.read_csv('comprehensive_validation_results_20250715_203038.csv')

        # 数据预处理
        processed_data = []
        for _, row in df.iterrows():
            # 解析命中数字
            hit_numbers_str = row['命中数字']
            if hit_numbers_str == '[]':
                hit_numbers = []
            else:
                # 简单解析，去掉方括号和引号
                hit_numbers_str = hit_numbers_str.strip('[]')
                if hit_numbers_str:
                    hit_numbers = [int(x.strip()) for x in hit_numbers_str.split(',') if x.strip()]
                else:
                    hit_numbers = []

            processed_data.append({
                'period': row['期号'],
                'pred_num1': row['预测数字1'],
                'pred_num2': row['预测数字2'],
                'actual_nums': [row['实际数字1'], row['实际数字2'], row['实际数字3'],
                               row['实际数字4'], row['实际数字5'], row['实际数字6']],
                'hit_numbers': hit_numbers,
                'hit_count': row['命中数量'],
                'is_hit': row['是否命中'] == '是',
                'original_confidence': row['原始置信度'],
                'optimized_confidence': row['优化置信度']
            })

        self.data = processed_data

        print(f"数据加载完成:")
        print(f"  总期数: {len(processed_data)}")
        print(f"  命中期数: {sum(1 for d in processed_data if d['is_hit'])}")
        print(f"  总体命中率: {np.mean([d['is_hit'] for d in processed_data]):.1%}")

        return processed_data

    def analyze_basic_probability(self):
        """分析基本概率"""
        print("\n🎲 基本概率分析")
        print("="*30)

        total_periods = len(self.data)
        hit_periods = sum(1 for d in self.data if d['is_hit'])

        # 基本统计
        basic_stats = {
            'total_periods': total_periods,
            'hit_periods': hit_periods,
            'miss_periods': total_periods - hit_periods,
            'hit_rate': hit_periods / total_periods,
            'miss_rate': (total_periods - hit_periods) / total_periods
        }

        # 命中数量分布
        hit_count_distribution = Counter([d['hit_count'] for d in self.data])

        # 理论概率计算
        # 从49个数字中选6个，预测2个数字至少命中1个的概率
        from math import comb

        total_combinations = comb(49, 6)  # 总的可能组合
        no_hit_combinations = comb(47, 6)  # 预测的2个数字都不在开奖号码中的组合
        at_least_one_hit = total_combinations - no_hit_combinations
        theoretical_probability = at_least_one_hit / total_combinations

        basic_stats['theoretical_probability'] = theoretical_probability
        basic_stats['hit_count_distribution'] = dict(hit_count_distribution)

        print(f"基本概率统计:")
        print(f"  总期数: {total_periods}")
        print(f"  命中期数: {hit_periods}")
        print(f"  实际命中率: {basic_stats['hit_rate']:.1%}")
        print(f"  理论命中率: {theoretical_probability:.1%}")
        print(f"  实际vs理论差异: {(basic_stats['hit_rate'] - theoretical_probability)*100:+.1f}个百分点")

        print(f"\n命中数量分布:")
        for count, freq in sorted(hit_count_distribution.items()):
            print(f"  命中{count}个: {freq}期 ({freq/total_periods:.1%})")

        self.analysis_results['basic_stats'] = basic_stats
        return basic_stats

    def analyze_number_frequency(self):
        """分析数字频率"""
        print("\n🔢 数字频率分析")
        print("="*30)

        # 预测数字频率
        pred_numbers = []
        for d in self.data:
            pred_numbers.extend([d['pred_num1'], d['pred_num2']])

        pred_freq = Counter(pred_numbers)

        # 实际开奖数字频率
        actual_numbers = []
        for d in self.data:
            actual_numbers.extend(d['actual_nums'])

        actual_freq = Counter(actual_numbers)

        # 命中数字频率
        hit_numbers = []
        for d in self.data:
            hit_numbers.extend(d['hit_numbers'])

        hit_freq = Counter(hit_numbers)

        # 计算每个数字的命中率
        number_hit_rates = {}
        for num in range(1, 50):  # 1-49的数字
            pred_count = pred_freq.get(num, 0)
            hit_count = hit_freq.get(num, 0)

            if pred_count > 0:
                hit_rate = hit_count / pred_count
                number_hit_rates[num] = {
                    'predicted_times': pred_count,
                    'hit_times': hit_count,
                    'hit_rate': hit_rate
                }

        # 找出表现最好和最差的数字
        sorted_numbers = sorted(number_hit_rates.items(),
                               key=lambda x: x[1]['hit_rate'], reverse=True)

        print(f"预测数字频率分析:")
        print(f"  最常预测的数字: {pred_freq.most_common(5)}")
        print(f"  预测数字种类: {len(pred_freq)} 种")

        print(f"\n命中率最高的预测数字 (至少预测5次):")
        for num, stats in sorted_numbers[:10]:
            if stats['predicted_times'] >= 5:
                print(f"  数字{num}: 预测{stats['predicted_times']}次, 命中{stats['hit_times']}次, "
                      f"命中率{stats['hit_rate']:.1%}")

        print(f"\n命中率最低的预测数字 (至少预测5次):")
        for num, stats in sorted_numbers[-10:]:
            if stats['predicted_times'] >= 5:
                print(f"  数字{num}: 预测{stats['predicted_times']}次, 命中{stats['hit_times']}次, "
                      f"命中率{stats['hit_rate']:.1%}")

        self.analysis_results['number_analysis'] = {
            'pred_freq': dict(pred_freq),
            'actual_freq': dict(actual_freq),
            'hit_freq': dict(hit_freq),
            'number_hit_rates': number_hit_rates
        }

        return number_hit_rates

    def analyze_prediction_patterns(self):
        """分析预测模式"""
        print("\n📈 预测模式分析")
        print("="*30)

        # 数字组合分析
        combinations = []
        for d in self.data:
            combo = tuple(sorted([d['pred_num1'], d['pred_num2']]))
            combinations.append({
                'combination': combo,
                'is_hit': d['is_hit'],
                'hit_count': d['hit_count'],
                'period': d['period']
            })

        # 组合频率和成功率
        combo_stats = {}
        for combo_data in combinations:
            combo = combo_data['combination']
            if combo not in combo_stats:
                combo_stats[combo] = {
                    'total_times': 0,
                    'hit_times': 0,
                    'hit_counts': []
                }

            combo_stats[combo]['total_times'] += 1
            if combo_data['is_hit']:
                combo_stats[combo]['hit_times'] += 1
            combo_stats[combo]['hit_counts'].append(combo_data['hit_count'])

        # 计算组合成功率
        for combo in combo_stats:
            stats = combo_stats[combo]
            stats['hit_rate'] = stats['hit_times'] / stats['total_times']
            stats['avg_hit_count'] = np.mean(stats['hit_counts'])

        # 找出最成功的组合
        successful_combos = [(combo, stats) for combo, stats in combo_stats.items()
                           if stats['total_times'] >= 2 and stats['hit_rate'] > 0]
        successful_combos.sort(key=lambda x: (x[1]['hit_rate'], x[1]['total_times']), reverse=True)

        print(f"预测组合分析:")
        print(f"  总组合数: {len(combo_stats)}")
        print(f"  重复使用的组合: {sum(1 for stats in combo_stats.values() if stats['total_times'] > 1)}")

        print(f"\n最成功的预测组合 (使用2次以上且有命中):")
        for combo, stats in successful_combos[:10]:
            print(f"  {combo}: 使用{stats['total_times']}次, 命中{stats['hit_times']}次, "
                  f"成功率{stats['hit_rate']:.1%}, 平均命中{stats['avg_hit_count']:.1f}个")

        # 数字间距分析
        gaps = []
        for d in self.data:
            gap = abs(d['pred_num1'] - d['pred_num2'])
            gaps.append({
                'gap': gap,
                'is_hit': d['is_hit'],
                'hit_count': d['hit_count']
            })

        # 按间距分组分析
        gap_stats = {}
        for gap_data in gaps:
            gap = gap_data['gap']
            if gap not in gap_stats:
                gap_stats[gap] = {'total': 0, 'hits': 0}

            gap_stats[gap]['total'] += 1
            if gap_data['is_hit']:
                gap_stats[gap]['hits'] += 1

        # 计算各间距的命中率
        for gap in gap_stats:
            gap_stats[gap]['hit_rate'] = gap_stats[gap]['hits'] / gap_stats[gap]['total']

        print(f"\n数字间距与命中率分析:")
        sorted_gaps = sorted(gap_stats.items(), key=lambda x: x[1]['hit_rate'], reverse=True)
        for gap, stats in sorted_gaps[:10]:
            if stats['total'] >= 3:  # 至少出现3次
                print(f"  间距{gap}: 出现{stats['total']}次, 命中{stats['hits']}次, "
                      f"命中率{stats['hit_rate']:.1%}")

        self.analysis_results['pattern_analysis'] = {
            'combo_stats': combo_stats,
            'gap_stats': gap_stats,
            'successful_combos': successful_combos[:10]
        }

        return combo_stats, gap_stats

    def calculate_conditional_probabilities(self):
        """计算条件概率"""
        print("\n🧮 条件概率分析")
        print("="*30)

        # 基于历史表现的条件概率
        conditions = {
            'high_confidence': [],  # 高置信度预测
            'low_confidence': [],   # 低置信度预测
            'early_period': [],     # 早期预测
            'late_period': [],      # 后期预测
            'common_numbers': [],   # 常见数字
            'rare_numbers': []      # 罕见数字
        }

        # 计算置信度阈值
        confidences = [d['optimized_confidence'] for d in self.data]
        confidence_median = np.median(confidences)

        # 计算期数阈值
        periods = [d['period'] for d in self.data]
        period_median = np.median(periods)

        # 获取常见和罕见数字
        number_analysis = self.analysis_results.get('number_analysis', {})
        pred_freq = number_analysis.get('pred_freq', {})
        freq_median = np.median(list(pred_freq.values())) if pred_freq else 1

        common_nums = {num for num, freq in pred_freq.items() if freq > freq_median}
        rare_nums = {num for num, freq in pred_freq.items() if freq <= freq_median}

        # 分类数据
        for d in self.data:
            # 置信度条件
            if d['optimized_confidence'] > confidence_median:
                conditions['high_confidence'].append(d['is_hit'])
            else:
                conditions['low_confidence'].append(d['is_hit'])

            # 期数条件
            if d['period'] < period_median:
                conditions['early_period'].append(d['is_hit'])
            else:
                conditions['late_period'].append(d['is_hit'])

            # 数字常见性条件
            pred_nums = {d['pred_num1'], d['pred_num2']}
            if pred_nums.intersection(common_nums):
                conditions['common_numbers'].append(d['is_hit'])
            if pred_nums.intersection(rare_nums):
                conditions['rare_numbers'].append(d['is_hit'])

        # 计算条件概率
        conditional_probs = {}
        for condition, hits in conditions.items():
            if hits:  # 确保有数据
                conditional_probs[condition] = {
                    'sample_size': len(hits),
                    'hit_rate': np.mean(hits),
                    'confidence_interval': self._calculate_confidence_interval(hits)
                }

        print(f"条件概率分析:")
        print(f"  置信度阈值: {confidence_median:.3f}")
        print(f"  期数阈值: {period_median:.0f}")

        for condition, prob_data in conditional_probs.items():
            ci_lower, ci_upper = prob_data['confidence_interval']
            print(f"  {condition}: {prob_data['hit_rate']:.1%} "
                  f"(样本{prob_data['sample_size']}, 95%CI: {ci_lower:.1%}-{ci_upper:.1%})")

        self.analysis_results['conditional_probs'] = conditional_probs
        return conditional_probs

    def _calculate_confidence_interval(self, binary_data, confidence=0.95):
        """计算二项分布的置信区间"""
        n = len(binary_data)
        p = np.mean(binary_data)

        # 使用正态近似
        z = 1.96  # 95%置信区间
        margin = z * np.sqrt(p * (1 - p) / n)

        return max(0, p - margin), min(1, p + margin)

    def create_probability_visualizations(self):
        """创建概率可视化"""
        print("\n📊 生成概率可视化")
        print("="*30)

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('预测数字命中概率深度分析', fontsize=16, fontweight='bold')

        # 1. 基本命中率对比
        ax1 = axes[0, 0]
        basic_stats = self.analysis_results['basic_stats']

        categories = ['实际命中率', '理论命中率']
        values = [basic_stats['hit_rate'], basic_stats['theoretical_probability']]
        colors = ['#FF6B6B', '#4ECDC4']

        bars = ax1.bar(categories, values, color=colors, alpha=0.7)
        ax1.set_ylabel('概率')
        ax1.set_title('实际 vs 理论命中率')
        ax1.set_ylim(0, max(values) * 1.2)

        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{value:.1%}', ha='center', va='bottom', fontweight='bold')

        ax1.grid(True, alpha=0.3)

        # 2. 命中数量分布
        ax2 = axes[0, 1]
        hit_dist = basic_stats['hit_count_distribution']

        counts = list(hit_dist.keys())
        frequencies = list(hit_dist.values())

        ax2.bar(counts, frequencies, color='#45B7D1', alpha=0.7)
        ax2.set_xlabel('命中数量')
        ax2.set_ylabel('频次')
        ax2.set_title('命中数量分布')
        ax2.grid(True, alpha=0.3)

        # 3. 数字命中率热力图
        ax3 = axes[0, 2]
        number_analysis = self.analysis_results.get('number_analysis', {})
        number_hit_rates = number_analysis.get('number_hit_rates', {})

        # 准备热力图数据
        hit_rate_matrix = np.zeros((7, 7))  # 7x7网格显示1-49的数字
        for num in range(1, 50):
            if num in number_hit_rates and number_hit_rates[num]['predicted_times'] >= 2:
                row = (num - 1) // 7
                col = (num - 1) % 7
                hit_rate_matrix[row, col] = number_hit_rates[num]['hit_rate']

        im = ax3.imshow(hit_rate_matrix, cmap='RdYlBu_r', aspect='auto')
        ax3.set_title('数字命中率热力图')
        ax3.set_xlabel('数字 (按7x7排列)')
        ax3.set_ylabel('数字组')

        # 添加颜色条
        plt.colorbar(im, ax=ax3, label='命中率')

        # 4. 条件概率对比
        ax4 = axes[1, 0]
        conditional_probs = self.analysis_results.get('conditional_probs', {})

        if conditional_probs:
            conditions = list(conditional_probs.keys())
            hit_rates = [conditional_probs[cond]['hit_rate'] for cond in conditions]

            bars = ax4.barh(conditions, hit_rates, color='#96CEB4', alpha=0.7)
            ax4.set_xlabel('命中率')
            ax4.set_title('不同条件下的命中率')

            for bar, rate in zip(bars, hit_rates):
                width = bar.get_width()
                ax4.text(width + 0.01, bar.get_y() + bar.get_height()/2.,
                        f'{rate:.1%}', ha='left', va='center', fontsize=9)

        ax4.grid(True, alpha=0.3)

        # 5. 数字间距与命中率
        ax5 = axes[1, 1]
        pattern_analysis = self.analysis_results.get('pattern_analysis', {})
        gap_stats = pattern_analysis.get('gap_stats', {})

        if gap_stats:
            gaps = []
            hit_rates = []
            sizes = []

            for gap, stats in gap_stats.items():
                if stats['total'] >= 2:  # 至少出现2次
                    gaps.append(gap)
                    hit_rates.append(stats['hit_rate'])
                    sizes.append(stats['total'] * 20)  # 调整点的大小

            scatter = ax5.scatter(gaps, hit_rates, s=sizes, alpha=0.6, c=hit_rates,
                                cmap='viridis')
            ax5.set_xlabel('数字间距')
            ax5.set_ylabel('命中率')
            ax5.set_title('数字间距与命中率关系')
            ax5.grid(True, alpha=0.3)

            plt.colorbar(scatter, ax=ax5, label='命中率')

        # 6. 时间序列命中率
        ax6 = axes[1, 2]
        periods = [d['period'] for d in self.data]
        hit_flags = [d['is_hit'] for d in self.data]

        # 计算滚动命中率
        window_size = 20
        rolling_periods = []
        rolling_hit_rates = []

        for i in range(window_size, len(periods)):
            window_hits = hit_flags[i-window_size:i]
            rolling_periods.append(periods[i])
            rolling_hit_rates.append(np.mean(window_hits))

        ax6.plot(rolling_periods, rolling_hit_rates, 'b-', linewidth=2, alpha=0.7)
        ax6.axhline(y=basic_stats['hit_rate'], color='red', linestyle='--',
                   label=f'总体命中率 {basic_stats["hit_rate"]:.1%}')
        ax6.axhline(y=basic_stats['theoretical_probability'], color='green', linestyle='--',
                   label=f'理论命中率 {basic_stats["theoretical_probability"]:.1%}')

        ax6.set_xlabel('期号')
        ax6.set_ylabel('滚动命中率')
        ax6.set_title(f'滚动命中率趋势 (窗口: {window_size}期)')
        ax6.legend()
        ax6.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'prediction_probability_analysis_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')

        print(f"概率分析图表已保存: {filename}")
        return filename

    def generate_probability_report(self):
        """生成概率分析报告"""
        print("\n📋 生成概率分析报告")
        print("="*30)

        basic_stats = self.analysis_results['basic_stats']
        number_analysis = self.analysis_results.get('number_analysis', {})
        pattern_analysis = self.analysis_results.get('pattern_analysis', {})
        conditional_probs = self.analysis_results.get('conditional_probs', {})

        # 创建详细报告 (处理JSON序列化问题)
        def convert_for_json(obj):
            """转换对象为JSON可序列化格式"""
            if isinstance(obj, dict):
                return {str(k): convert_for_json(v) for k, v in obj.items()}
            elif isinstance(obj, (list, tuple)):
                return [convert_for_json(item) for item in obj]
            else:
                return obj

        report = {
            'analysis_info': {
                'analysis_date': datetime.now().isoformat(),
                'data_periods': f"{self.data[0]['period']}-{self.data[-1]['period']}",
                'total_predictions': len(self.data),
                'analysis_version': '概率深度分析 v1.0'
            },
            'basic_probability': convert_for_json(basic_stats),
            'number_frequency_analysis': convert_for_json(number_analysis),
            'pattern_analysis': convert_for_json(pattern_analysis),
            'conditional_probabilities': convert_for_json(conditional_probs),
            'key_insights': self._generate_key_insights()
        }

        # 保存JSON报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_filename = f'probability_analysis_report_{timestamp}.json'

        import json
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)

        # 生成文本报告
        text_report = self._generate_text_report(report)
        text_filename = f'probability_analysis_summary_{timestamp}.txt'

        with open(text_filename, 'w', encoding='utf-8') as f:
            f.write(text_report)

        print(f"详细报告已保存: {json_filename}")
        print(f"摘要报告已保存: {text_filename}")

        return json_filename, text_filename

    def _generate_key_insights(self):
        """生成关键洞察"""
        basic_stats = self.analysis_results['basic_stats']
        number_analysis = self.analysis_results.get('number_analysis', {})
        conditional_probs = self.analysis_results.get('conditional_probs', {})

        insights = []

        # 基本概率洞察
        actual_rate = basic_stats['hit_rate']
        theoretical_rate = basic_stats['theoretical_probability']

        if actual_rate > theoretical_rate * 1.1:
            insights.append(f"✅ 实际命中率({actual_rate:.1%})显著高于理论值({theoretical_rate:.1%})，预测系统表现良好")
        elif actual_rate < theoretical_rate * 0.9:
            insights.append(f"❌ 实际命中率({actual_rate:.1%})低于理论值({theoretical_rate:.1%})，预测系统需要改进")
        else:
            insights.append(f"⚖️ 实际命中率({actual_rate:.1%})接近理论值({theoretical_rate:.1%})，符合随机预期")

        # 数字频率洞察
        if number_analysis:
            number_hit_rates = number_analysis.get('number_hit_rates', {})
            if number_hit_rates:
                best_numbers = sorted(number_hit_rates.items(),
                                    key=lambda x: x[1]['hit_rate'], reverse=True)[:3]
                worst_numbers = sorted(number_hit_rates.items(),
                                     key=lambda x: x[1]['hit_rate'])[:3]

                best_nums = [str(num) for num, stats in best_numbers
                           if stats['predicted_times'] >= 3]
                worst_nums = [str(num) for num, stats in worst_numbers
                            if stats['predicted_times'] >= 3]

                if best_nums:
                    insights.append(f"🎯 表现最佳的预测数字: {', '.join(best_nums)}")
                if worst_nums:
                    insights.append(f"⚠️ 表现最差的预测数字: {', '.join(worst_nums)}")

        # 条件概率洞察
        if conditional_probs:
            high_conf_rate = conditional_probs.get('high_confidence', {}).get('hit_rate', 0)
            low_conf_rate = conditional_probs.get('low_confidence', {}).get('hit_rate', 0)

            if high_conf_rate > low_conf_rate * 1.2:
                insights.append(f"📈 高置信度预测命中率({high_conf_rate:.1%})明显高于低置信度({low_conf_rate:.1%})")
            else:
                insights.append(f"📊 置信度与实际命中率关联性较弱")

        return insights

    def _generate_text_report(self, report):
        """生成文本报告"""
        basic_stats = report['basic_probability']

        text = f"""
预测数字命中概率深度分析报告
{'='*60}

分析信息:
{'='*30}
分析日期: {report['analysis_info']['analysis_date'][:19]}
数据期间: 第{report['analysis_info']['data_periods']}期
预测总数: {report['analysis_info']['total_predictions']}次
分析版本: {report['analysis_info']['analysis_version']}

基本概率分析:
{'='*30}
实际命中率: {basic_stats['hit_rate']:.1%} ({basic_stats['hit_periods']}/{basic_stats['total_periods']})
理论命中率: {basic_stats['theoretical_probability']:.1%}
差异: {(basic_stats['hit_rate'] - basic_stats['theoretical_probability'])*100:+.1f}个百分点

命中数量分布:"""

        for count, freq in sorted(basic_stats['hit_count_distribution'].items()):
            percentage = freq / basic_stats['total_periods'] * 100
            text += f"\n  命中{count}个: {freq}次 ({percentage:.1f}%)"

        # 添加关键洞察
        text += f"""

关键洞察:
{'='*30}"""

        for insight in report['key_insights']:
            text += f"\n{insight}"

        # 添加条件概率分析
        if report['conditional_probabilities']:
            text += f"""

条件概率分析:
{'='*30}"""

            for condition, prob_data in report['conditional_probabilities'].items():
                ci_lower, ci_upper = prob_data['confidence_interval']
                text += f"""
{condition}:
  命中率: {prob_data['hit_rate']:.1%}
  样本数: {prob_data['sample_size']}
  95%置信区间: {ci_lower:.1%} - {ci_upper:.1%}"""

        text += f"""

思辨结论:
{'='*30}
基于{basic_stats['total_periods']}期的历史数据分析，预测数字在当期命中的概率为:

1. 整体概率: {basic_stats['hit_rate']:.1%}
   - 这是基于实际历史表现的经验概率
   - 与理论概率{basic_stats['theoretical_probability']:.1%}的差异反映了预测系统的实际效果

2. 概率解释:
   - 每次预测2个数字，在6个开奖号码中至少命中1个的概率
   - 理论上应该约为{basic_stats['theoretical_probability']:.1%}
   - 实际表现{'优于' if basic_stats['hit_rate'] > basic_stats['theoretical_probability'] else '等于' if abs(basic_stats['hit_rate'] - basic_stats['theoretical_probability']) < 0.02 else '劣于'}理论预期

3. 实用建议:
   - 基于历史数据，每次预测有约{basic_stats['hit_rate']:.1%}的机会至少命中1个数字
   - 这个概率相对较低，反映了彩票预测的固有难度
   - 建议理性对待预测结果，不要过度依赖

---
报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
分析工具版本: 预测概率分析器 v1.0
"""

        return text

    def run_comprehensive_analysis(self):
        """运行综合分析"""
        print("🚀 启动预测数字命中概率深度分析")
        print("="*60)

        try:
            # 1. 加载数据
            self.load_validation_data()

            # 2. 基本概率分析
            basic_stats = self.analyze_basic_probability()

            # 3. 数字频率分析
            number_hit_rates = self.analyze_number_frequency()

            # 4. 预测模式分析
            combo_stats, gap_stats = self.analyze_prediction_patterns()

            # 5. 条件概率分析
            conditional_probs = self.calculate_conditional_probabilities()

            # 6. 创建可视化
            chart_filename = self.create_probability_visualizations()

            # 7. 生成报告
            json_report, text_report = self.generate_probability_report()

            print("\n" + "="*60)
            print("✅ 预测数字命中概率深度分析完成！")
            print("="*60)

            print(f"\n🎯 核心发现:")
            print(f"  实际命中率: {basic_stats['hit_rate']:.1%}")
            print(f"  理论命中率: {basic_stats['theoretical_probability']:.1%}")
            print(f"  差异: {(basic_stats['hit_rate'] - basic_stats['theoretical_probability'])*100:+.1f}个百分点")
            print(f"  分析期数: {basic_stats['total_periods']}期")

            print(f"\n📁 生成文件:")
            print(f"  可视化图表: {chart_filename}")
            print(f"  详细报告: {json_report}")
            print(f"  摘要报告: {text_report}")

            return {
                'basic_stats': basic_stats,
                'analysis_results': self.analysis_results,
                'chart_filename': chart_filename,
                'json_report': json_report,
                'text_report': text_report
            }

        except Exception as e:
            print(f"\n❌ 分析过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    analyzer = PredictionProbabilityAnalyzer()
    results = analyzer.run_comprehensive_analysis()

    if results:
        print(f"\n🎉 概率分析成功完成！")
        basic_stats = results['basic_stats']
        print(f"\n💡 思辨答案:")
        print(f"预测数字在当期命中的概率约为 {basic_stats['hit_rate']:.1%}")
        print(f"这是基于{basic_stats['total_periods']}期历史数据的实际表现")
    else:
        print(f"\n❌ 概率分析失败！")

if __name__ == "__main__":
    main()