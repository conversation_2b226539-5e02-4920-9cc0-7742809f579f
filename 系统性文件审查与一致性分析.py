#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统性文件审查与一致性分析
搜索和分析所有预测报告、CSV数据文件和验证结果
重点验证29.2%命中率的来源和一致性
"""

import os
import pandas as pd
import numpy as np
import json
import re
from datetime import datetime
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class SystematicFileAuditor:
    """
    系统性文件审查器
    全面分析所有预测相关文件的一致性
    """
    
    def __init__(self):
        self.workspace_root = "."
        self.file_inventory = {}
        self.performance_records = []
        self.data_sources = {}
        self.consistency_issues = []
        
    def scan_all_files(self):
        """扫描所有相关文件"""
        print(f"📁 扫描工作目录中的所有相关文件")
        print("=" * 60)
        
        file_categories = {
            'prediction_results': [],    # 预测结果文件
            'validation_reports': [],    # 验证报告
            'csv_data': [],             # CSV数据文件
            'json_reports': [],         # JSON报告
            'markdown_docs': [],        # Markdown文档
            'python_scripts': []        # Python脚本
        }
        
        # 扫描当前目录
        for root, dirs, files in os.walk(self.workspace_root):
            for file in files:
                file_path = os.path.join(root, file)
                file_lower = file.lower()
                
                # 分类文件
                if any(keyword in file_lower for keyword in ['预测', 'prediction', '结果', 'result']):
                    if file_lower.endswith('.csv'):
                        file_categories['csv_data'].append(file_path)
                    elif file_lower.endswith('.json'):
                        file_categories['json_reports'].append(file_path)
                    elif file_lower.endswith('.md'):
                        file_categories['markdown_docs'].append(file_path)
                    else:
                        file_categories['prediction_results'].append(file_path)
                
                elif any(keyword in file_lower for keyword in ['验证', 'validation', '实验', 'experiment']):
                    if file_lower.endswith('.json'):
                        file_categories['json_reports'].append(file_path)
                    elif file_lower.endswith('.md'):
                        file_categories['validation_reports'].append(file_path)
                    elif file_lower.endswith('.py'):
                        file_categories['python_scripts'].append(file_path)
                
                elif file_lower.endswith('.csv') and any(keyword in file_lower for keyword in ['lottery', '彩票', 'data']):
                    file_categories['csv_data'].append(file_path)
                
                elif file_lower.endswith('.py') and any(keyword in file_lower for keyword in ['预测', 'prediction', '分析', 'analysis']):
                    file_categories['python_scripts'].append(file_path)
        
        self.file_inventory = file_categories
        
        # 显示文件清单
        print(f"文件扫描结果:")
        for category, files in file_categories.items():
            print(f"  {category}: {len(files)}个文件")
            for file_path in files[:5]:  # 显示前5个
                print(f"    - {os.path.basename(file_path)}")
            if len(files) > 5:
                print(f"    ... 还有{len(files)-5}个文件")
        
        return file_categories
    
    def analyze_performance_claims(self):
        """分析所有性能声明"""
        print(f"\n🔍 分析所有文件中的性能声明")
        print("=" * 60)
        
        performance_patterns = [
            r'29\.2%',
            r'0\.292',
            r'命中率.*?(\d+\.?\d*)%',
            r'准确率.*?(\d+\.?\d*)%',
            r'成功率.*?(\d+\.?\d*)%',
            r'accuracy.*?(\d+\.?\d*)',
            r'hit.*?rate.*?(\d+\.?\d*)'
        ]
        
        # 分析所有文本文件
        text_files = (
            self.file_inventory['markdown_docs'] + 
            self.file_inventory['python_scripts']
        )
        
        for file_path in text_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 搜索性能相关的模式
                for pattern in performance_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        self.performance_records.append({
                            'file': os.path.basename(file_path),
                            'file_path': file_path,
                            'pattern': pattern,
                            'matches': matches,
                            'file_type': 'text'
                        })
            except Exception as e:
                print(f"⚠️ 无法读取文件 {file_path}: {e}")
        
        # 分析JSON文件
        for file_path in self.file_inventory['json_reports']:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 递归搜索JSON中的性能数据
                performance_data = self._extract_performance_from_json(data)
                if performance_data:
                    self.performance_records.append({
                        'file': os.path.basename(file_path),
                        'file_path': file_path,
                        'performance_data': performance_data,
                        'file_type': 'json'
                    })
            except Exception as e:
                print(f"⚠️ 无法读取JSON文件 {file_path}: {e}")
        
        print(f"✅ 发现 {len(self.performance_records)} 个文件包含性能数据")
        return self.performance_records
    
    def _extract_performance_from_json(self, data, path=""):
        """从JSON中递归提取性能数据"""
        performance_data = []
        
        if isinstance(data, dict):
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key
                
                # 检查是否是性能相关的键
                if any(keyword in key.lower() for keyword in ['accuracy', 'rate', 'success', '准确', '命中', '成功']):
                    if isinstance(value, (int, float)):
                        performance_data.append({
                            'path': current_path,
                            'key': key,
                            'value': value
                        })
                
                # 递归搜索
                if isinstance(value, (dict, list)):
                    performance_data.extend(self._extract_performance_from_json(value, current_path))
        
        elif isinstance(data, list):
            for i, item in enumerate(data):
                current_path = f"{path}[{i}]" if path else f"[{i}]"
                if isinstance(item, (dict, list)):
                    performance_data.extend(self._extract_performance_from_json(item, current_path))
        
        return performance_data
    
    def trace_292_baseline_origin(self):
        """追踪29.2%基线的来源"""
        print(f"\n🎯 追踪29.2%基线的来源")
        print("=" * 60)
        
        baseline_sources = []
        
        # 搜索包含29.2%的所有记录
        for record in self.performance_records:
            if record['file_type'] == 'text':
                # 检查是否包含29.2%
                content_matches = any('29.2' in str(match) for match in record['matches'])
                if content_matches:
                    baseline_sources.append({
                        'file': record['file'],
                        'file_path': record['file_path'],
                        'type': 'text_mention',
                        'evidence': record['matches']
                    })
            
            elif record['file_type'] == 'json':
                # 检查JSON中的性能数据
                for perf_data in record['performance_data']:
                    value = perf_data['value']
                    if isinstance(value, float) and abs(value - 0.292) < 0.001:
                        baseline_sources.append({
                            'file': record['file'],
                            'file_path': record['file_path'],
                            'type': 'json_data',
                            'path': perf_data['path'],
                            'value': value
                        })
        
        print(f"发现 {len(baseline_sources)} 个29.2%基线来源:")
        for i, source in enumerate(baseline_sources, 1):
            print(f"\n{i}. 文件: {source['file']}")
            print(f"   类型: {source['type']}")
            if source['type'] == 'text_mention':
                print(f"   证据: {source['evidence']}")
            elif source['type'] == 'json_data':
                print(f"   路径: {source['path']}")
                print(f"   数值: {source['value']}")
        
        return baseline_sources
    
    def analyze_data_sources(self):
        """分析数据源"""
        print(f"\n📊 分析数据源")
        print("=" * 60)
        
        # 分析CSV数据文件
        for csv_file in self.file_inventory['csv_data']:
            try:
                # 读取CSV文件的基本信息
                df = pd.read_csv(csv_file)
                
                # 分析数据结构
                data_info = {
                    'file': os.path.basename(csv_file),
                    'file_path': csv_file,
                    'rows': len(df),
                    'columns': list(df.columns),
                    'date_range': None,
                    'data_type': 'unknown'
                }
                
                # 尝试识别日期范围
                if '年份' in df.columns and '期号' in df.columns:
                    min_year = df['年份'].min()
                    max_year = df['年份'].max()
                    min_period = df[df['年份'] == min_year]['期号'].min()
                    max_period = df[df['年份'] == max_year]['期号'].max()
                    
                    data_info['date_range'] = f"{min_year}年{min_period}期 - {max_year}年{max_period}期"
                    data_info['data_type'] = 'lottery_data'
                
                # 检查是否包含预测结果
                if any('预测' in col or 'predicted' in col.lower() for col in df.columns):
                    data_info['data_type'] = 'prediction_results'
                
                self.data_sources[csv_file] = data_info
                
                print(f"CSV文件: {data_info['file']}")
                print(f"  行数: {data_info['rows']}")
                print(f"  列数: {len(data_info['columns'])}")
                print(f"  日期范围: {data_info['date_range']}")
                print(f"  数据类型: {data_info['data_type']}")
                
            except Exception as e:
                print(f"⚠️ 无法分析CSV文件 {csv_file}: {e}")
        
        return self.data_sources
    
    def verify_validation_consistency(self):
        """验证验证实验的一致性"""
        print(f"\n🔬 验证验证实验的一致性")
        print("=" * 60)
        
        validation_results = []
        
        # 查找最近的验证实验结果
        recent_validation_files = [
            f for f in self.file_inventory['validation_reports'] + self.file_inventory['json_reports']
            if '验证' in f or 'validation' in f.lower()
        ]
        
        for file_path in recent_validation_files:
            try:
                if file_path.endswith('.md'):
                    # 分析Markdown报告
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 提取关键数据
                    accuracy_matches = re.findall(r'准确率.*?(\d+\.?\d*)%', content)
                    method_matches = re.findall(r'(单期预测|连续预测|滚动预测|概率分布)', content)
                    
                    validation_results.append({
                        'file': os.path.basename(file_path),
                        'type': 'markdown_report',
                        'accuracies': accuracy_matches,
                        'methods': method_matches,
                        'file_path': file_path
                    })
                
                elif file_path.endswith('.json'):
                    # 分析JSON报告
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 提取验证结果
                    if 'method_comparison' in data or 'evaluation_results' in data:
                        validation_results.append({
                            'file': os.path.basename(file_path),
                            'type': 'json_report',
                            'data_keys': list(data.keys()),
                            'file_path': file_path
                        })
            
            except Exception as e:
                print(f"⚠️ 无法分析验证文件 {file_path}: {e}")
        
        print(f"发现 {len(validation_results)} 个验证结果文件:")
        for result in validation_results:
            print(f"  - {result['file']} ({result['type']})")
        
        return validation_results
    
    def detect_consistency_issues(self):
        """检测一致性问题"""
        print(f"\n⚠️ 检测一致性问题")
        print("=" * 60)
        
        issues = []
        
        # 1. 检查29.2%基线的一致性
        baseline_values = []
        for record in self.performance_records:
            if record['file_type'] == 'json':
                for perf_data in record['performance_data']:
                    value = perf_data['value']
                    if isinstance(value, float) and 0.25 <= value <= 0.35:  # 可能的准确率范围
                        baseline_values.append({
                            'file': record['file'],
                            'value': value,
                            'path': perf_data['path']
                        })
        
        # 检查是否有不一致的基线值
        unique_values = set(round(v['value'], 3) for v in baseline_values)
        if len(unique_values) > 1:
            issues.append({
                'type': 'inconsistent_baseline',
                'description': f'发现不一致的基线值: {unique_values}',
                'files': [v['file'] for v in baseline_values]
            })
        
        # 2. 检查数据源的时间一致性
        date_ranges = []
        for file_path, data_info in self.data_sources.items():
            if data_info['date_range']:
                date_ranges.append({
                    'file': data_info['file'],
                    'range': data_info['date_range']
                })
        
        unique_ranges = set(dr['range'] for dr in date_ranges)
        if len(unique_ranges) > 1:
            issues.append({
                'type': 'inconsistent_date_ranges',
                'description': f'发现不一致的数据时间范围: {unique_ranges}',
                'files': [dr['file'] for dr in date_ranges]
            })
        
        # 3. 检查可能的数据泄露
        # 这里可以添加更多的数据泄露检测逻辑
        
        self.consistency_issues = issues
        
        if issues:
            print(f"发现 {len(issues)} 个一致性问题:")
            for i, issue in enumerate(issues, 1):
                print(f"\n{i}. {issue['type']}")
                print(f"   描述: {issue['description']}")
                print(f"   涉及文件: {', '.join(issue['files'])}")
        else:
            print("✅ 未发现明显的一致性问题")
        
        return issues
    
    def generate_comprehensive_audit_report(self):
        """生成综合审查报告"""
        print(f"\n📋 生成综合审查报告")
        print("=" * 60)
        
        report = {
            'audit_metadata': {
                'audit_time': datetime.now().isoformat(),
                'workspace_root': self.workspace_root,
                'total_files_scanned': sum(len(files) for files in self.file_inventory.values())
            },
            'file_inventory': {
                category: [os.path.basename(f) for f in files]
                for category, files in self.file_inventory.items()
            },
            'performance_analysis': {
                'total_performance_records': len(self.performance_records),
                'files_with_performance_data': len(set(r['file'] for r in self.performance_records))
            },
            'baseline_analysis': {
                'baseline_sources_found': len([r for r in self.performance_records 
                                             if any('29.2' in str(m) for m in r.get('matches', []))]),
            },
            'data_source_analysis': {
                'csv_files_analyzed': len(self.data_sources),
                'data_sources': {k: v for k, v in self.data_sources.items()}
            },
            'consistency_issues': self.consistency_issues,
            'recommendations': []
        }
        
        # 生成建议
        if not self.consistency_issues:
            report['recommendations'].append("数据一致性良好，可以信任当前的验证结果")
        else:
            report['recommendations'].append("需要解决发现的一致性问题")
            report['recommendations'].append("建议重新验证有问题的数据源")
        
        return report

def main():
    """主函数"""
    print("🔍 系统性文件审查与一致性分析")
    print("全面搜索和分析所有预测相关文件")
    print("=" * 80)
    
    # 初始化审查器
    auditor = SystematicFileAuditor()
    
    # 1. 扫描所有文件
    file_inventory = auditor.scan_all_files()
    
    # 2. 分析性能声明
    performance_records = auditor.analyze_performance_claims()
    
    # 3. 追踪29.2%基线来源
    baseline_sources = auditor.trace_292_baseline_origin()
    
    # 4. 分析数据源
    data_sources = auditor.analyze_data_sources()
    
    # 5. 验证验证实验一致性
    validation_results = auditor.verify_validation_consistency()
    
    # 6. 检测一致性问题
    consistency_issues = auditor.detect_consistency_issues()
    
    # 7. 生成综合报告
    audit_report = auditor.generate_comprehensive_audit_report()
    
    # 8. 保存报告
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f"系统性文件审查报告_{timestamp}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(audit_report, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 审查报告已保存: {report_file}")
    
    # 9. 显示关键发现
    print(f"\n🎉 审查总结")
    print("=" * 50)
    print(f"✅ 扫描文件总数: {audit_report['audit_metadata']['total_files_scanned']}")
    print(f"✅ 性能数据记录: {audit_report['performance_analysis']['total_performance_records']}")
    print(f"✅ 29.2%基线来源: {len(baseline_sources)}个")
    print(f"✅ 数据源分析: {len(data_sources)}个CSV文件")
    print(f"✅ 一致性问题: {len(consistency_issues)}个")
    
    if consistency_issues:
        print(f"\n⚠️ 需要关注的问题:")
        for issue in consistency_issues:
            print(f"  - {issue['description']}")
    else:
        print(f"\n✅ 数据一致性验证通过")

if __name__ == "__main__":
    main()
