#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测验证系统 - 用户输入真实号码验证预测结果
专门用于更新prediction_data.csv文件中的验证数据
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class PredictionVerificationSystem:
    """预测验证系统"""
    
    def __init__(self):
        self.prediction_data_file = "prediction_data.csv"
        self.main_data_file = "data/processed/lottery_data_clean_no_special.csv"
        
    def load_prediction_data(self):
        """加载预测数据"""
        try:
            if not os.path.exists(self.prediction_data_file):
                print(f"❌ 预测数据文件不存在: {self.prediction_data_file}")
                return False
            
            self.prediction_df = pd.read_csv(self.prediction_data_file, encoding='utf-8-sig')
            print(f"✅ 预测数据加载完成: {len(self.prediction_df)}条记录")
            return True
            
        except Exception as e:
            print(f"❌ 加载预测数据失败: {e}")
            return False
    
    def load_main_data(self):
        """加载主数据"""
        try:
            if not os.path.exists(self.main_data_file):
                print(f"❌ 主数据文件不存在: {self.main_data_file}")
                return False
            
            self.main_df = pd.read_csv(self.main_data_file)
            print(f"✅ 主数据加载完成: {len(self.main_df)}期")
            return True
            
        except Exception as e:
            print(f"❌ 加载主数据失败: {e}")
            return False
    
    def show_unverified_predictions(self):
        """显示未验证的预测"""
        try:
            # 查找未验证的预测 (实际数字1为空或NaN)
            unverified = self.prediction_df[
                (self.prediction_df['实际数字1'].isna()) | 
                (self.prediction_df['实际数字1'] == '') |
                (self.prediction_df['实际数字1'] == 'nan')
            ].copy()
            
            if len(unverified) == 0:
                print("✅ 所有预测都已验证")
                return []
            
            print(f"\n📋 未验证的预测 ({len(unverified)}条)")
            print("=" * 80)
            
            for idx, row in unverified.iterrows():
                print(f"序号: {idx}")
                print(f"预测期号: {row['预测期号']}")
                print(f"预测数字: [{row['预测数字1']}, {row['预测数字2']}]")
                print(f"预测日期: {row['预测日期']} {row['预测时间']}")
                print(f"预测置信度: {row['预测置信度']}")
                print("-" * 40)
            
            return unverified.index.tolist()
            
        except Exception as e:
            print(f"❌ 显示未验证预测失败: {e}")
            return []
    
    def input_actual_numbers(self):
        """输入实际开奖号码"""
        print("\n📝 请输入实际开奖号码")
        print("=" * 40)
        
        while True:
            try:
                # 输入期号信息
                year = input("请输入年份 (如: 2025): ").strip()
                period = input("请输入期号 (如: 188): ").strip()
                
                if not year.isdigit() or not period.isdigit():
                    print("❌ 年份和期号必须是数字，请重新输入")
                    continue
                
                year = int(year)
                period = int(period)
                
                # 输入开奖数字
                print("请输入6个实际开奖数字:")
                print("支持格式: 空格分隔(5 12 23 31 40 45) 或 逗号分隔(5,12,23,31,40,45)")
                numbers_input = input("实际开奖: ").strip()
                
                # 解析数字 - 支持多种分隔符
                if ',' in numbers_input:
                    # 逗号分隔
                    numbers_str = numbers_input.replace(' ', '').split(',')
                else:
                    # 空格分隔
                    numbers_str = numbers_input.split()
                
                # 转换为整数并处理前导零
                numbers = []
                for num_str in numbers_str:
                    num_str = num_str.strip()
                    if num_str:  # 非空字符串
                        numbers.append(int(num_str))
                
                # 验证输入
                if len(numbers) != 6:
                    print("❌ 必须输入6个数字，请重新输入")
                    continue
                
                if not all(1 <= num <= 49 for num in numbers):
                    print("❌ 数字必须在1-49范围内，请重新输入")
                    continue
                
                if len(set(numbers)) != 6:
                    print("❌ 数字不能重复，请重新输入")
                    continue
                
                return {
                    'year': year,
                    'period': period,
                    'numbers': sorted(numbers)
                }
                
            except ValueError:
                print("❌ 输入格式错误，请重新输入")
            except KeyboardInterrupt:
                print("\n👋 用户取消输入")
                return None
    
    def find_matching_prediction(self, year, period):
        """查找匹配的预测记录"""
        try:
            # 查找对应的预测记录
            target_period_name = f"{year}年{period}期"
            
            matching_predictions = self.prediction_df[
                self.prediction_df['预测期号'] == target_period_name
            ]
            
            if len(matching_predictions) == 0:
                print(f"❌ 没有找到{target_period_name}的预测记录")
                return None
            
            if len(matching_predictions) > 1:
                print(f"⚠️ 找到多条{target_period_name}的预测记录，使用最新的一条")
                return matching_predictions.iloc[-1]
            
            return matching_predictions.iloc[0]
            
        except Exception as e:
            print(f"❌ 查找预测记录失败: {e}")
            return None
    
    def calculate_hit_result(self, predicted_numbers, actual_numbers):
        """计算命中结果"""
        try:
            predicted_set = set(predicted_numbers)
            actual_set = set(actual_numbers)
            
            # 计算命中的数字
            hit_numbers = predicted_set & actual_set
            hit_count = len(hit_numbers)
            is_hit = hit_count >= 1
            
            return {
                'hit_count': hit_count,
                'is_hit': is_hit,
                'hit_numbers': sorted(list(hit_numbers))
            }
            
        except Exception as e:
            print(f"❌ 计算命中结果失败: {e}")
            return None
    
    def update_prediction_record(self, prediction_row, actual_data, hit_result):
        """更新预测记录"""
        try:
            # 获取记录的索引
            if hasattr(prediction_row, 'name'):
                idx = prediction_row.name
            else:
                # 如果是Series，查找对应的索引
                target_period = prediction_row['预测期号']
                matching_rows = self.prediction_df[self.prediction_df['预测期号'] == target_period]
                if len(matching_rows) == 0:
                    print("❌ 无法找到要更新的记录")
                    return False
                idx = matching_rows.index[-1]  # 使用最新的记录
            
            # 更新实际数字
            actual_numbers = actual_data['numbers']
            for i, num in enumerate(actual_numbers, 1):
                self.prediction_df.loc[idx, f'实际数字{i}'] = num
            
            # 更新命中结果
            self.prediction_df.loc[idx, '命中数量'] = hit_result['hit_count']
            self.prediction_df.loc[idx, '是否命中'] = '是' if hit_result['is_hit'] else '否'
            self.prediction_df.loc[idx, '命中数字'] = ','.join(map(str, hit_result['hit_numbers'])) if hit_result['hit_numbers'] else ''
            
            # 更新备注
            current_note = self.prediction_df.loc[idx, '备注']
            if pd.isna(current_note) or current_note == '':
                self.prediction_df.loc[idx, '备注'] = '用户验证'
            else:
                self.prediction_df.loc[idx, '备注'] = f"{current_note},用户验证"
            
            print(f"✅ 预测记录已更新")
            return True
            
        except Exception as e:
            print(f"❌ 更新预测记录失败: {e}")
            return False
    
    def save_prediction_data(self):
        """保存预测数据"""
        try:
            self.prediction_df.to_csv(self.prediction_data_file, index=False, encoding='utf-8-sig')
            print(f"✅ 预测数据已保存到: {self.prediction_data_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存预测数据失败: {e}")
            return False
    
    def add_to_main_data(self, year, period, numbers):
        """添加到主数据文件"""
        try:
            # 检查数据是否已存在
            existing = self.main_df[
                (self.main_df['年份'] == year) & 
                (self.main_df['期号'] == period)
            ]
            
            if len(existing) > 0:
                print(f"⚠️ {year}年{period}期数据已存在于主文件中")
                choice = input("是否更新主文件中的数据？(y/n): ").strip().lower()
                if choice == 'y':
                    # 更新现有数据
                    idx = existing.index[0]
                    for i, num in enumerate(sorted(numbers), 1):
                        self.main_df.loc[idx, f'数字{i}'] = num
                    print(f"✅ 更新主文件中的{year}年{period}期数据")
                else:
                    print("跳过主文件更新")
                    return True
            else:
                # 添加新数据
                new_row = {
                    '年份': year,
                    '期号': period
                }
                for i, num in enumerate(sorted(numbers), 1):
                    new_row[f'数字{i}'] = num
                
                # 使用pd.concat替代append
                new_df = pd.DataFrame([new_row])
                self.main_df = pd.concat([self.main_df, new_df], ignore_index=True)
                self.main_df = self.main_df.sort_values(['年份', '期号']).reset_index(drop=True)
                print(f"✅ 添加{year}年{period}期数据到主文件")
            
            # 保存主数据文件
            self.main_df.to_csv(self.main_data_file, index=False, encoding='utf-8')
            print(f"✅ 主数据文件已更新")
            
            return True
            
        except Exception as e:
            print(f"❌ 更新主数据文件失败: {e}")
            return False
    
    def verify_prediction(self):
        """验证预测"""
        print("\n🎯 预测验证流程")
        print("=" * 50)
        
        # 1. 输入实际开奖号码
        actual_data = self.input_actual_numbers()
        if not actual_data:
            return False
        
        year = actual_data['year']
        period = actual_data['period']
        actual_numbers = actual_data['numbers']
        
        print(f"\n📊 验证{year}年{period}期")
        print(f"实际开奖: {actual_numbers}")
        
        # 2. 查找对应的预测记录
        prediction_row = self.find_matching_prediction(year, period)
        if prediction_row is None:
            print("❌ 无法找到对应的预测记录")
            return False
        
        # 3. 获取预测数字
        predicted_numbers = [prediction_row['预测数字1'], prediction_row['预测数字2']]
        print(f"预测数字: {predicted_numbers}")
        
        # 4. 计算命中结果
        hit_result = self.calculate_hit_result(predicted_numbers, actual_numbers)
        if hit_result is None:
            return False
        
        # 5. 显示验证结果
        print(f"\n🎯 验证结果:")
        print(f"预测数字: {predicted_numbers}")
        print(f"实际开奖: {actual_numbers}")
        print(f"命中数字: {hit_result['hit_numbers'] if hit_result['hit_numbers'] else '无'}")
        print(f"命中数量: {hit_result['hit_count']}")
        print(f"命中状态: {'✅ 命中' if hit_result['is_hit'] else '❌ 未命中'}")
        
        # 6. 确认更新
        confirm = input("\n是否更新预测数据文件？(y/n): ").strip().lower()
        if confirm != 'y':
            print("取消更新")
            return False
        
        # 7. 更新预测记录
        if not self.update_prediction_record(prediction_row, actual_data, hit_result):
            return False
        
        # 8. 保存预测数据
        if not self.save_prediction_data():
            return False
        
        # 9. 询问是否添加到主数据文件
        add_to_main = input("是否同时添加到主数据文件？(y/n): ").strip().lower()
        if add_to_main == 'y':
            self.add_to_main_data(year, period, actual_numbers)
        
        print(f"\n🎉 验证完成！")
        return True
    
    def show_recent_statistics(self):
        """显示最近统计"""
        try:
            print(f"\n📊 预测统计")
            print("=" * 50)
            
            total_predictions = len(self.prediction_df)
            
            # 已验证的预测 (实际数字1不为空)
            verified_predictions = self.prediction_df[
                (~self.prediction_df['实际数字1'].isna()) & 
                (self.prediction_df['实际数字1'] != '') &
                (self.prediction_df['实际数字1'] != 'nan')
            ].copy()
            
            unverified_count = total_predictions - len(verified_predictions)
            
            print(f"总预测期数: {total_predictions}")
            print(f"已验证期数: {len(verified_predictions)}")
            print(f"待验证期数: {unverified_count}")
            
            if len(verified_predictions) > 0:
                hits = len(verified_predictions[verified_predictions['是否命中'] == '是'])
                hit_rate = hits / len(verified_predictions)
                
                print(f"命中期数: {hits}")
                print(f"命中率: {hit_rate:.3f} ({hit_rate:.1%})")
                
                # 最近5期预测
                recent = verified_predictions.tail(5)
                print(f"\n最近{len(recent)}期验证结果:")
                for _, row in recent.iterrows():
                    status = "✅" if row['是否命中'] == '是' else "❌"
                    hit_info = f"命中{row['命中数字']}" if row['命中数字'] else "未命中"
                    print(f"  {row['预测期号']}: 预测[{row['预测数字1']},{row['预测数字2']}] {status} {hit_info}")
            
        except Exception as e:
            print(f"❌ 显示统计失败: {e}")
    
    def main_menu(self):
        """主菜单"""
        while True:
            print(f"\n🎯 预测验证系统")
            print("=" * 40)
            print("1. 输入实际开奖号码验证预测")
            print("2. 查看未验证的预测")
            print("3. 查看预测统计")
            print("4. 退出系统")
            
            choice = input("\n请选择操作 (1-4): ").strip()
            
            if choice == '1':
                self.verify_prediction()
            
            elif choice == '2':
                self.show_unverified_predictions()
            
            elif choice == '3':
                self.show_recent_statistics()
            
            elif choice == '4':
                print("👋 感谢使用，再见！")
                break
            
            else:
                print("❌ 无效选择，请重新输入")

def main():
    """主函数"""
    print("🎯 预测验证系统")
    print("专门用于验证prediction_data.csv中的预测结果")
    print("=" * 60)
    
    system = PredictionVerificationSystem()
    
    # 初始化系统
    if not system.load_prediction_data():
        return
    
    if not system.load_main_data():
        return
    
    print("✅ 系统初始化完成")
    print("\n💡 功能说明:")
    print("1. 输入实际开奖号码验证预测结果")
    print("2. 自动计算命中情况并更新CSV文件")
    print("3. 可选择同时更新主数据文件")
    
    # 启动主菜单
    system.main_menu()

if __name__ == "__main__":
    main()
