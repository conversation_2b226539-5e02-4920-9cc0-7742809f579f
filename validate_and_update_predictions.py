#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证预测数据与真实开奖数据，并更新prediction_data.csv
Validate prediction data against actual lottery results and update prediction_data.csv
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

def load_data():
    """加载数据文件"""
    print("📂 加载数据文件")
    print("="*40)
    
    try:
        # 加载真实开奖数据
        lottery_data = pd.read_csv('data/processed/lottery_data_clean_no_special.csv', encoding='utf-8')
        print(f"✅ 真实开奖数据: {len(lottery_data)}条记录")
        print(f"   期号范围: {lottery_data['年份'].min()}年{lottery_data['期号'].min()}期 - {lottery_data['年份'].max()}年{lottery_data['期号'].max()}期")
        
        # 加载预测数据
        prediction_data = pd.read_csv('prediction_data.csv', encoding='utf-8')
        print(f"✅ 预测数据: {len(prediction_data)}条记录")
        print(f"   期号范围: {prediction_data['当期年份'].min()}年{prediction_data['当期期号'].min()}期 - {prediction_data['当期年份'].max()}年{prediction_data['当期期号'].max()}期")
        
        return lottery_data, prediction_data
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None, None

def validate_current_period_data(lottery_data, prediction_data):
    """验证当期开奖数据的准确性"""
    print(f"\n🔍 验证当期开奖数据准确性")
    print("="*40)
    
    validation_results = []
    errors_found = 0
    
    for idx, pred_row in prediction_data.iterrows():
        year = int(pred_row['当期年份'])
        period = int(pred_row['当期期号'])
        
        # 在真实数据中查找对应期号
        actual_row = lottery_data[(lottery_data['年份'] == year) & (lottery_data['期号'] == period)]
        
        if len(actual_row) == 0:
            validation_results.append({
                'index': idx,
                'period': f"{year}年{period}期",
                'status': 'missing',
                'message': '真实数据中未找到对应期号'
            })
            errors_found += 1
            continue
        
        actual_row = actual_row.iloc[0]
        
        # 比较当期开奖数据
        pred_numbers = [
            pred_row['当期数字1'], pred_row['当期数字2'], pred_row['当期数字3'],
            pred_row['当期数字4'], pred_row['当期数字5'], pred_row['当期数字6']
        ]
        
        actual_numbers = [
            actual_row['数字1'], actual_row['数字2'], actual_row['数字3'],
            actual_row['数字4'], actual_row['数字5'], actual_row['数字6']
        ]
        
        if pred_numbers == actual_numbers:
            validation_results.append({
                'index': idx,
                'period': f"{year}年{period}期",
                'status': 'correct',
                'message': '当期数据正确'
            })
        else:
            validation_results.append({
                'index': idx,
                'period': f"{year}年{period}期",
                'status': 'incorrect',
                'message': f'数据不匹配: 预测{pred_numbers} vs 实际{actual_numbers}',
                'pred_numbers': pred_numbers,
                'actual_numbers': actual_numbers
            })
            errors_found += 1
    
    print(f"验证结果:")
    print(f"  总记录数: {len(validation_results)}")
    print(f"  正确记录: {len([r for r in validation_results if r['status'] == 'correct'])}")
    print(f"  错误记录: {errors_found}")
    
    if errors_found > 0:
        print(f"\n❌ 发现 {errors_found} 条错误记录:")
        for result in validation_results:
            if result['status'] != 'correct':
                print(f"   {result['period']}: {result['message']}")
    else:
        print(f"\n✅ 所有当期开奖数据验证正确")
    
    return validation_results

def calculate_hit_results(lottery_data, prediction_data):
    """计算预测命中结果"""
    print(f"\n🎯 计算预测命中结果")
    print("="*40)
    
    hit_results = []
    total_predictions = 0
    total_hits = 0
    
    for idx, pred_row in prediction_data.iterrows():
        year = int(pred_row['当期年份'])
        period = int(pred_row['当期期号'])
        
        # 预测的是下一期，所以查找period+1期的真实数据
        next_period = period + 1
        next_year = year
        
        # 处理跨年情况（假设每年最多200期）
        if next_period > 200:
            next_period = 1
            next_year += 1
        
        # 查找下期真实开奖数据
        actual_next = lottery_data[(lottery_data['年份'] == next_year) & (lottery_data['期号'] == next_period)]
        
        if len(actual_next) == 0:
            # 下期数据不存在（可能是最新预测）
            hit_results.append({
                'index': idx,
                'period': f"{year}年{period}期",
                'predicted_period': f"{next_year}年{next_period}期",
                'status': 'pending',
                'predicted_numbers': [pred_row['预测数字1'], pred_row['预测数字2']],
                'actual_numbers': None,
                'hit_count': None,
                'hit_numbers': None
            })
            continue
        
        actual_next = actual_next.iloc[0]
        total_predictions += 1
        
        # 获取预测数字和实际数字
        predicted_numbers = [int(pred_row['预测数字1']), int(pred_row['预测数字2'])]
        actual_numbers = [
            actual_next['数字1'], actual_next['数字2'], actual_next['数字3'],
            actual_next['数字4'], actual_next['数字5'], actual_next['数字6']
        ]
        
        # 计算命中情况
        hit_numbers = []
        for pred_num in predicted_numbers:
            if pred_num in actual_numbers:
                hit_numbers.append(pred_num)
        
        hit_count = len(hit_numbers)
        is_hit = hit_count > 0
        
        if is_hit:
            total_hits += 1
        
        hit_results.append({
            'index': idx,
            'period': f"{year}年{period}期",
            'predicted_period': f"{next_year}年{next_period}期",
            'status': 'completed',
            'predicted_numbers': predicted_numbers,
            'actual_numbers': actual_numbers,
            'hit_count': hit_count,
            'hit_numbers': hit_numbers,
            'is_hit': is_hit
        })
    
    # 计算命中率
    if total_predictions > 0:
        hit_rate = (total_hits / total_predictions) * 100
        print(f"命中统计:")
        print(f"  总预测数: {total_predictions}")
        print(f"  命中次数: {total_hits}")
        print(f"  命中率: {hit_rate:.1f}%")
    else:
        print(f"暂无可统计的预测结果")
    
    return hit_results

def update_prediction_data(prediction_data, hit_results):
    """更新预测数据文件"""
    print(f"\n📝 更新预测数据文件")
    print("="*40)
    
    updated_data = prediction_data.copy()
    updates_made = 0
    
    for result in hit_results:
        idx = result['index']
        
        if result['status'] == 'completed':
            # 更新实际开奖数据
            actual_numbers = result['actual_numbers']
            updated_data.loc[idx, '实际数字1'] = actual_numbers[0]
            updated_data.loc[idx, '实际数字2'] = actual_numbers[1]
            updated_data.loc[idx, '实际数字3'] = actual_numbers[2]
            updated_data.loc[idx, '实际数字4'] = actual_numbers[3]
            updated_data.loc[idx, '实际数字5'] = actual_numbers[4]
            updated_data.loc[idx, '实际数字6'] = actual_numbers[5]
            
            # 更新命中信息
            updated_data.loc[idx, '命中数量'] = result['hit_count']
            updated_data.loc[idx, '是否命中'] = '是' if result['is_hit'] else '否'
            
            if result['hit_numbers']:
                hit_numbers_str = ','.join(map(str, result['hit_numbers']))
                updated_data.loc[idx, '命中数字'] = hit_numbers_str
            else:
                updated_data.loc[idx, '命中数字'] = ''
            
            updates_made += 1
        
        elif result['status'] == 'pending':
            # 清空待定记录的实际数据
            updated_data.loc[idx, '实际数字1'] = ''
            updated_data.loc[idx, '实际数字2'] = ''
            updated_data.loc[idx, '实际数字3'] = ''
            updated_data.loc[idx, '实际数字4'] = ''
            updated_data.loc[idx, '实际数字5'] = ''
            updated_data.loc[idx, '实际数字6'] = ''
            updated_data.loc[idx, '命中数量'] = ''
            updated_data.loc[idx, '是否命中'] = ''
            updated_data.loc[idx, '命中数字'] = ''
    
    print(f"更新统计:")
    print(f"  已更新记录: {updates_made}")
    print(f"  待定记录: {len([r for r in hit_results if r['status'] == 'pending'])}")
    
    return updated_data

def generate_validation_report(hit_results):
    """生成验证报告"""
    print(f"\n📊 生成验证报告")
    print("="*40)
    
    completed_results = [r for r in hit_results if r['status'] == 'completed']
    pending_results = [r for r in hit_results if r['status'] == 'pending']
    
    if completed_results:
        # 命中统计
        total_completed = len(completed_results)
        total_hits = len([r for r in completed_results if r['is_hit']])
        hit_rate = (total_hits / total_completed) * 100 if total_completed > 0 else 0
        
        # 命中数量分布
        hit_count_dist = {}
        for result in completed_results:
            count = result['hit_count']
            hit_count_dist[count] = hit_count_dist.get(count, 0) + 1
        
        print(f"📈 命中率分析:")
        print(f"   总预测数: {total_completed}")
        print(f"   命中次数: {total_hits}")
        print(f"   命中率: {hit_rate:.1f}%")
        
        print(f"\n📊 命中数量分布:")
        for count in sorted(hit_count_dist.keys()):
            percentage = (hit_count_dist[count] / total_completed) * 100
            print(f"   命中{count}个数字: {hit_count_dist[count]}次 ({percentage:.1f}%)")
        
        # 显示最近的命中情况
        print(f"\n🎯 最近10次预测结果:")
        recent_results = completed_results[-10:] if len(completed_results) >= 10 else completed_results
        for result in recent_results:
            hit_status = "✅" if result['is_hit'] else "❌"
            hit_info = f"命中{result['hit_count']}个" if result['is_hit'] else "未命中"
            print(f"   {result['period']} → {result['predicted_period']}: "
                  f"预测{result['predicted_numbers']} {hit_status} {hit_info}")
    
    if pending_results:
        print(f"\n⏳ 待验证预测:")
        for result in pending_results:
            print(f"   {result['period']} → {result['predicted_period']}: "
                  f"预测{result['predicted_numbers']} (等待开奖)")
    
    return {
        'total_completed': len(completed_results),
        'total_hits': len([r for r in completed_results if r['is_hit']]),
        'hit_rate': (len([r for r in completed_results if r['is_hit']]) / len(completed_results) * 100) if completed_results else 0,
        'pending_count': len(pending_results)
    }

def main():
    """主函数"""
    print("🔍 验证预测数据与真实开奖数据")
    print("="*60)
    
    # 1. 加载数据
    lottery_data, prediction_data = load_data()
    if lottery_data is None or prediction_data is None:
        return
    
    # 2. 验证当期数据准确性
    validation_results = validate_current_period_data(lottery_data, prediction_data)
    
    # 3. 计算预测命中结果
    hit_results = calculate_hit_results(lottery_data, prediction_data)
    
    # 4. 更新预测数据
    updated_data = update_prediction_data(prediction_data, hit_results)
    
    # 5. 保存更新后的数据
    backup_filename = f'prediction_data_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
    prediction_data.to_csv(backup_filename, index=False, encoding='utf-8')
    print(f"📁 原文件已备份: {backup_filename}")
    
    updated_data.to_csv('prediction_data.csv', index=False, encoding='utf-8')
    print(f"✅ 预测数据已更新保存")
    
    # 6. 生成验证报告
    report = generate_validation_report(hit_results)
    
    print(f"\n🎉 验证和更新完成!")
    print(f"📊 最终统计:")
    print(f"   已验证预测: {report['total_completed']}次")
    print(f"   成功命中: {report['total_hits']}次")
    print(f"   命中率: {report['hit_rate']:.1f}%")
    print(f"   待验证预测: {report['pending_count']}次")

if __name__ == "__main__":
    main()
