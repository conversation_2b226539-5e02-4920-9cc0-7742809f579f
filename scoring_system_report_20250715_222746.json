{"system_info": {"creation_date": "2025-07-15T22:27:46.048597", "model_type": "RandomForestClassifier", "training_samples": 153, "test_samples": 39, "total_features": 20, "base_hit_rate": 0.3541666666666667}, "model_performance": {"train_auc": 0.9994232987312572, "test_auc": 0.4812834224598931, "overall_auc": 0.9582542694497154}, "scoring_system": {"score_ranges": [{"score_range": "0-20", "description": "极低命中概率", "expected_hit_rate": "10-20%", "recommendation": "不建议"}, {"score_range": "21-40", "description": "低命中概率", "expected_hit_rate": "20-30%", "recommendation": "谨慎考虑"}, {"score_range": "41-60", "description": "中等命中概率", "expected_hit_rate": "30-40%", "recommendation": "可以考虑"}, {"score_range": "61-80", "description": "较高命中概率", "expected_hit_rate": "40-50%", "recommendation": "值得关注"}, {"score_range": "81-100", "description": "高命中概率", "expected_hit_rate": "50%+", "recommendation": "重点关注"}], "threshold_analysis": [{"range": "0.0-0.2", "label": "极低风险", "count": "68", "avg_prob": 0.14031855153880224, "actual_hit_rate": 0.0, "calibration_error": 0.14031855153880224}, {"range": "0.2-0.3", "label": "低风险", "count": "37", "avg_prob": 0.2486173283342485, "actual_hit_rate": 0.13513513513513514, "calibration_error": 0.11348219319911335}, {"range": "0.3-0.4", "label": "中低风险", "count": "26", "avg_prob": 0.34918828914136024, "actual_hit_rate": 0.34615384615384615, "calibration_error": 0.0030344429875140966}, {"range": "0.4-0.5", "label": "中等风险", "count": "11", "avg_prob": 0.44545611583194034, "actual_hit_rate": 0.6363636363636364, "calibration_error": 0.19090752053169602}, {"range": "0.5-0.6", "label": "中高风险", "count": "20", "avg_prob": 0.5501667298938854, "actual_hit_rate": 0.85, "calibration_error": 0.2998332701061146}, {"range": "0.6-0.7", "label": "高风险", "count": "17", "avg_prob": 0.668083446180033, "actual_hit_rate": 1.0, "calibration_error": 0.331916553819967}, {"range": "0.7-0.8", "label": "极高风险", "count": "11", "avg_prob": 0.7444614568838801, "actual_hit_rate": 1.0, "calibration_error": 0.2555385431161199}, {"range": "0.8-1.0", "label": "超高风险", "count": "2", "avg_prob": 0.8179407157599881, "actual_hit_rate": 1.0, "calibration_error": 0.18205928424001194}]}, "threshold_analysis": [{"threshold": 0.5, "score_threshold": 50.0, "sample_count": "50", "sample_percentage": 26.041666666666668, "actual_hit_rate": 0.94, "avg_predicted_prob": 0.6437142128036184}, {"threshold": 0.6, "score_threshold": 60.0, "sample_count": "30", "sample_percentage": 15.625, "actual_hit_rate": 1.0, "avg_predicted_prob": 0.7060792014101072}, {"threshold": 0.7, "score_threshold": 70.0, "sample_count": "13", "sample_percentage": 6.770833333333333, "actual_hit_rate": 1.0, "avg_predicted_prob": 0.7557659582494352}, {"threshold": 0.8, "score_threshold": 80.0, "sample_count": "2", "sample_percentage": 1.0416666666666665, "actual_hit_rate": 1.0, "avg_predicted_prob": 0.8179407157599881}], "feature_importance": [{"feature": "confidence_improvement", "importance": 0.08647639065924846}, {"feature": "period_normalized", "importance": 0.08323092517112492}, {"feature": "composite_factor", "importance": 0.08248515988018947}, {"feature": "number_historical_performance", "importance": 0.08230564667218915}, {"feature": "original_confidence", "importance": 0.07127903749910401}, {"feature": "recent_confidence_trend", "importance": 0.07055101910894948}, {"feature": "stability_factor", "importance": 0.06607805007577411}, {"feature": "recent_avg_confidence", "importance": 0.06039379498233483}, {"feature": "calibration_factor", "importance": 0.059739277487329774}, {"feature": "trend_factor", "importance": 0.058861911958998724}], "generated_files": {"visualization": "hit_rate_scoring_system_20250715_222744.png"}}