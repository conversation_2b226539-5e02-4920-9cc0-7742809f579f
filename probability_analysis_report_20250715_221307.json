{"analysis_info": {"analysis_date": "2025-07-15T22:13:07.068659", "data_periods": "2-194", "total_predictions": 192, "analysis_version": "概率深度分析 v1.0"}, "basic_probability": {"total_periods": 192, "hit_periods": 68, "miss_periods": 124, "hit_rate": 0.3541666666666667, "miss_rate": 0.6458333333333334, "theoretical_probability": 0.23214285714285715, "hit_count_distribution": {"0": 124, "1": 65, "2": 3}}, "number_frequency_analysis": {"pred_freq": {"30": 136, "43": 8, "29": 19, "5": 28, "40": 38, "17": 3, "2": 19, "3": 58, "31": 6, "15": 41, "49": 5, "19": 3, "11": 1, "22": 5, "16": 5, "42": 1, "25": 3, "32": 1, "27": 1, "10": 2, "24": 1}, "actual_freq": {"13.0": 21, "4.0": 24, "10.0": 24, "6.0": 22, "5.0": 19, "28.0": 26, "1.0": 21, "16.0": 22, "47.0": 23, "18.0": 27, "42.0": 26, "45.0": 30, "17.0": 24, "7.0": 21, "37.0": 24, "35.0": 28, "21.0": 16, "3.0": 32, "32.0": 25, "40.0": 29, "12.0": 20, "48.0": 21, "39.0": 31, "24.0": 25, "23.0": 30, "20.0": 23, "43.0": 24, "30.0": 39, "15.0": 23, "36.0": 17, "11.0": 21, "25.0": 24, "41.0": 19, "2.0": 21, "22.0": 32, "27.0": 22, "29.0": 21, "49.0": 19, "33.0": 23, "44.0": 17, "26.0": 14, "14.0": 17, "19.0": 22, "31.0": 27, "9.0": 20, "34.0": 20, "46.0": 23, "8.0": 29, "38.0": 24}, "hit_freq": {"40": 8, "30": 31, "15": 10, "29": 3, "5": 4, "49": 2, "3": 11, "16": 1, "22": 1}, "number_hit_rates": {"2": {"predicted_times": 19, "hit_times": 0, "hit_rate": 0.0}, "3": {"predicted_times": 58, "hit_times": 11, "hit_rate": 0.1896551724137931}, "5": {"predicted_times": 28, "hit_times": 4, "hit_rate": 0.14285714285714285}, "10": {"predicted_times": 2, "hit_times": 0, "hit_rate": 0.0}, "11": {"predicted_times": 1, "hit_times": 0, "hit_rate": 0.0}, "15": {"predicted_times": 41, "hit_times": 10, "hit_rate": 0.24390243902439024}, "16": {"predicted_times": 5, "hit_times": 1, "hit_rate": 0.2}, "17": {"predicted_times": 3, "hit_times": 0, "hit_rate": 0.0}, "19": {"predicted_times": 3, "hit_times": 0, "hit_rate": 0.0}, "22": {"predicted_times": 5, "hit_times": 1, "hit_rate": 0.2}, "24": {"predicted_times": 1, "hit_times": 0, "hit_rate": 0.0}, "25": {"predicted_times": 3, "hit_times": 0, "hit_rate": 0.0}, "27": {"predicted_times": 1, "hit_times": 0, "hit_rate": 0.0}, "29": {"predicted_times": 19, "hit_times": 3, "hit_rate": 0.15789473684210525}, "30": {"predicted_times": 136, "hit_times": 31, "hit_rate": 0.22794117647058823}, "31": {"predicted_times": 6, "hit_times": 0, "hit_rate": 0.0}, "32": {"predicted_times": 1, "hit_times": 0, "hit_rate": 0.0}, "40": {"predicted_times": 38, "hit_times": 8, "hit_rate": 0.21052631578947367}, "42": {"predicted_times": 1, "hit_times": 0, "hit_rate": 0.0}, "43": {"predicted_times": 8, "hit_times": 0, "hit_rate": 0.0}, "49": {"predicted_times": 5, "hit_times": 2, "hit_rate": 0.4}}}, "pattern_analysis": {"combo_stats": {"(30, 43)": {"total_times": 7, "hit_times": 0, "hit_counts": [0, 0, 0, 0, 0, 0, 0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(29, 30)": {"total_times": 8, "hit_times": 2, "hit_counts": [0, 0, 0, 0, 0, 1, 0, 1], "hit_rate": 0.25, "avg_hit_count": 0.25}, "(5, 30)": {"total_times": 13, "hit_times": 4, "hit_counts": [0, 0, 1, 0, 0, 2, 1, 0, 0, 0, 0, 0, 1], "hit_rate": 0.3076923076923077, "avg_hit_count": 0.38461538461538464}, "(30, 40)": {"total_times": 15, "hit_times": 8, "hit_counts": [1, 0, 1, 0, 1, 0, 0, 1, 0, 0, 2, 1, 1, 0, 1], "hit_rate": 0.5333333333333333, "avg_hit_count": 0.6}, "(2, 17)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(17, 30)": {"total_times": 2, "hit_times": 0, "hit_counts": [0, 0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(3, 5)": {"total_times": 5, "hit_times": 2, "hit_counts": [0, 0, 1, 1, 0], "hit_rate": 0.4, "avg_hit_count": 0.4}, "(29, 31)": {"total_times": 2, "hit_times": 0, "hit_counts": [0, 0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(30, 31)": {"total_times": 1, "hit_times": 1, "hit_counts": [1], "hit_rate": 1.0, "avg_hit_count": 1.0}, "(15, 30)": {"total_times": 30, "hit_times": 15, "hit_counts": [1, 1, 0, 1, 0, 1, 0, 0, 0, 1, 1, 0, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 0], "hit_rate": 0.5, "avg_hit_count": 0.5}, "(15, 40)": {"total_times": 4, "hit_times": 1, "hit_counts": [0, 1, 0, 0], "hit_rate": 0.25, "avg_hit_count": 0.25}, "(30, 49)": {"total_times": 3, "hit_times": 2, "hit_counts": [0, 1, 1], "hit_rate": 0.6666666666666666, "avg_hit_count": 0.6666666666666666}, "(19, 29)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(29, 40)": {"total_times": 2, "hit_times": 1, "hit_counts": [1, 0], "hit_rate": 0.5, "avg_hit_count": 0.5}, "(3, 30)": {"total_times": 34, "hit_times": 17, "hit_counts": [0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 0, 1, 1, 0, 2, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1], "hit_rate": 0.5, "avg_hit_count": 0.5294117647058824}, "(19, 30)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(3, 40)": {"total_times": 7, "hit_times": 2, "hit_counts": [0, 1, 1, 0, 0, 0, 0], "hit_rate": 0.2857142857142857, "avg_hit_count": 0.2857142857142857}, "(11, 30)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(22, 30)": {"total_times": 4, "hit_times": 1, "hit_counts": [0, 0, 1, 0], "hit_rate": 0.25, "avg_hit_count": 0.25}, "(31, 40)": {"total_times": 1, "hit_times": 1, "hit_counts": [1], "hit_rate": 1.0, "avg_hit_count": 1.0}, "(2, 40)": {"total_times": 5, "hit_times": 1, "hit_counts": [0, 1, 0, 0, 0], "hit_rate": 0.2, "avg_hit_count": 0.2}, "(2, 30)": {"total_times": 9, "hit_times": 3, "hit_counts": [1, 0, 0, 0, 1, 1, 0, 0, 0], "hit_rate": 0.3333333333333333, "avg_hit_count": 0.3333333333333333}, "(15, 16)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(3, 49)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(30, 42)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(5, 40)": {"total_times": 2, "hit_times": 0, "hit_counts": [0, 0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(25, 30)": {"total_times": 2, "hit_times": 0, "hit_counts": [0, 0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(3, 15)": {"total_times": 3, "hit_times": 1, "hit_counts": [0, 0, 1], "hit_rate": 0.3333333333333333, "avg_hit_count": 0.3333333333333333}, "(2, 5)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(3, 16)": {"total_times": 1, "hit_times": 1, "hit_counts": [1], "hit_rate": 1.0, "avg_hit_count": 1.0}, "(30, 32)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(40, 49)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(2, 15)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(3, 31)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(3, 19)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(3, 27)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(10, 30)": {"total_times": 2, "hit_times": 0, "hit_counts": [0, 0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(3, 29)": {"total_times": 2, "hit_times": 1, "hit_counts": [1, 0], "hit_rate": 0.5, "avg_hit_count": 0.5}, "(25, 40)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(5, 29)": {"total_times": 4, "hit_times": 1, "hit_counts": [0, 1, 0, 0], "hit_rate": 0.25, "avg_hit_count": 0.25}, "(2, 16)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(16, 31)": {"total_times": 1, "hit_times": 1, "hit_counts": [1], "hit_rate": 1.0, "avg_hit_count": 1.0}, "(5, 22)": {"total_times": 1, "hit_times": 1, "hit_counts": [1], "hit_rate": 1.0, "avg_hit_count": 1.0}, "(24, 30)": {"total_times": 1, "hit_times": 1, "hit_counts": [1], "hit_rate": 1.0, "avg_hit_count": 1.0}, "(2, 3)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(5, 15)": {"total_times": 2, "hit_times": 0, "hit_counts": [0, 0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(3, 43)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}, "(16, 30)": {"total_times": 1, "hit_times": 0, "hit_counts": [0], "hit_rate": 0.0, "avg_hit_count": 0.0}}, "gap_stats": {"13": {"total": 11, "hits": 1, "hit_rate": 0.09090909090909091}, "1": {"total": 11, "hits": 3, "hit_rate": 0.2727272727272727}, "25": {"total": 17, "hits": 5, "hit_rate": 0.29411764705882354}, "10": {"total": 18, "hits": 8, "hit_rate": 0.4444444444444444}, "15": {"total": 33, "hits": 16, "hit_rate": 0.48484848484848486}, "2": {"total": 8, "hits": 2, "hit_rate": 0.25}, "19": {"total": 4, "hits": 2, "hit_rate": 0.5}, "11": {"total": 3, "hits": 1, "hit_rate": 0.3333333333333333}, "27": {"total": 34, "hits": 17, "hit_rate": 0.5}, "37": {"total": 7, "hits": 2, "hit_rate": 0.2857142857142857}, "8": {"total": 4, "hits": 1, "hit_rate": 0.25}, "9": {"total": 2, "hits": 1, "hit_rate": 0.5}, "38": {"total": 5, "hits": 1, "hit_rate": 0.2}, "28": {"total": 10, "hits": 3, "hit_rate": 0.3}, "46": {"total": 1, "hits": 0, "hit_rate": 0.0}, "12": {"total": 4, "hits": 1, "hit_rate": 0.25}, "35": {"total": 2, "hits": 0, "hit_rate": 0.0}, "5": {"total": 2, "hits": 0, "hit_rate": 0.0}, "3": {"total": 1, "hits": 0, "hit_rate": 0.0}, "16": {"total": 1, "hits": 0, "hit_rate": 0.0}, "24": {"total": 5, "hits": 1, "hit_rate": 0.2}, "20": {"total": 2, "hits": 0, "hit_rate": 0.0}, "26": {"total": 2, "hits": 1, "hit_rate": 0.5}, "14": {"total": 2, "hits": 0, "hit_rate": 0.0}, "17": {"total": 1, "hits": 1, "hit_rate": 1.0}, "6": {"total": 1, "hits": 1, "hit_rate": 1.0}, "40": {"total": 1, "hits": 0, "hit_rate": 0.0}}, "successful_combos": [[[30, 49], {"total_times": 3, "hit_times": 2, "hit_counts": [0, 1, 1], "hit_rate": 0.6666666666666666, "avg_hit_count": 0.6666666666666666}], [[30, 40], {"total_times": 15, "hit_times": 8, "hit_counts": [1, 0, 1, 0, 1, 0, 0, 1, 0, 0, 2, 1, 1, 0, 1], "hit_rate": 0.5333333333333333, "avg_hit_count": 0.6}], [[3, 30], {"total_times": 34, "hit_times": 17, "hit_counts": [0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 0, 1, 1, 0, 2, 0, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1], "hit_rate": 0.5, "avg_hit_count": 0.5294117647058824}], [[15, 30], {"total_times": 30, "hit_times": 15, "hit_counts": [1, 1, 0, 1, 0, 1, 0, 0, 0, 1, 1, 0, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 0], "hit_rate": 0.5, "avg_hit_count": 0.5}], [[29, 40], {"total_times": 2, "hit_times": 1, "hit_counts": [1, 0], "hit_rate": 0.5, "avg_hit_count": 0.5}], [[3, 29], {"total_times": 2, "hit_times": 1, "hit_counts": [1, 0], "hit_rate": 0.5, "avg_hit_count": 0.5}], [[3, 5], {"total_times": 5, "hit_times": 2, "hit_counts": [0, 0, 1, 1, 0], "hit_rate": 0.4, "avg_hit_count": 0.4}], [[2, 30], {"total_times": 9, "hit_times": 3, "hit_counts": [1, 0, 0, 0, 1, 1, 0, 0, 0], "hit_rate": 0.3333333333333333, "avg_hit_count": 0.3333333333333333}], [[3, 15], {"total_times": 3, "hit_times": 1, "hit_counts": [0, 0, 1], "hit_rate": 0.3333333333333333, "avg_hit_count": 0.3333333333333333}], [[5, 30], {"total_times": 13, "hit_times": 4, "hit_counts": [0, 0, 1, 0, 0, 2, 1, 0, 0, 0, 0, 0, 1], "hit_rate": 0.3076923076923077, "avg_hit_count": 0.38461538461538464}]]}, "conditional_probabilities": {"high_confidence": {"sample_size": 72, "hit_rate": 0.375, "confidence_interval": [0.2631733111760286, 0.4868266888239714]}, "low_confidence": {"sample_size": 120, "hit_rate": 0.3416666666666667, "confidence_interval": [0.2568092754043149, 0.42652405792901843]}, "early_period": {"sample_size": 96, "hit_rate": 0.2916666666666667, "confidence_interval": [0.20074179561409974, 0.3825915377192336]}, "late_period": {"sample_size": 96, "hit_rate": 0.4166666666666667, "confidence_interval": [0.3180447971439886, 0.5152885361893448]}, "common_numbers": {"sample_size": 192, "hit_rate": 0.3541666666666667, "confidence_interval": [0.28651639776031385, 0.4218169355730195]}, "rare_numbers": {"sample_size": 31, "hit_rate": 0.22580645161290322, "confidence_interval": [0.07861988610517928, 0.37299301712062716]}}, "key_insights": ["✅ 实际命中率(35.4%)显著高于理论值(23.2%)，预测系统表现良好", "🎯 表现最佳的预测数字: 49, 15, 30", "⚠️ 表现最差的预测数字: 2", "📊 置信度与实际命中率关联性较弱"]}