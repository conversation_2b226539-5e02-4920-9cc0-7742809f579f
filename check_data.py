import pandas as pd

df = pd.read_csv('prediction_data.csv')
print('年份分布:')
print(df['当期年份'].value_counts().sort_index())
print('\n期号范围:')
print(f'最小期号: {df["当期期号"].min()}')
print(f'最大期号: {df["当期期号"].max()}')
print(f'\n数据总量: {len(df)}')

# 检查有实际结果的数据
actual_data = df[df['实际数字1'].notna()]
print(f'\n有实际结果的数据: {len(actual_data)}')
print(f'实际结果期号范围: {actual_data["当期期号"].min()} - {actual_data["当期期号"].max()}')
