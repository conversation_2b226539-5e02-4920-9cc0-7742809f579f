#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析系统为什么频繁预测数字30和40
Analyze why the system frequently predicts numbers 30 and 40
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from collections import Counter
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def analyze_prediction_frequency():
    """分析预测数字频率"""
    print("🔍 分析预测数字频率")
    print("="*40)
    
    # 加载预测数据
    df = pd.read_csv('prediction_data.csv')
    
    # 统计所有预测数字
    all_predictions = []
    for _, row in df.iterrows():
        all_predictions.extend([row['预测数字1'], row['预测数字2']])
    
    # 计算频率
    pred_freq = Counter(all_predictions)
    
    print(f"预测数字频率统计 (Top 15):")
    for num, freq in pred_freq.most_common(15):
        percentage = freq / len(df) * 100
        print(f"数字{num:2d}: {freq:3d}次 ({percentage:5.1f}%)")
    
    # 特别关注30和40
    freq_30 = pred_freq.get(30, 0)
    freq_40 = pred_freq.get(40, 0)
    
    print(f"\n🎯 重点分析:")
    print(f"数字30: {freq_30}次 ({freq_30/len(df)*100:.1f}%)")
    print(f"数字40: {freq_40}次 ({freq_40/len(df)*100:.1f}%)")
    print(f"30+40合计: {freq_30+freq_40}次 ({(freq_30+freq_40)/len(df)*100:.1f}%)")
    
    return pred_freq

def analyze_actual_frequency():
    """分析实际开奖数字频率"""
    print(f"\n🔍 分析实际开奖数字频率")
    print("="*40)
    
    # 加载真实开奖数据
    lottery_df = pd.read_csv('data/processed/lottery_data_clean_no_special.csv')
    
    # 统计所有实际开奖数字
    all_actual = []
    for _, row in lottery_df.iterrows():
        all_actual.extend([row[f'数字{i}'] for i in range(1, 7)])
    
    # 计算频率
    actual_freq = Counter(all_actual)
    
    print(f"实际开奖数字频率统计 (Top 15):")
    for num, freq in actual_freq.most_common(15):
        percentage = freq / len(lottery_df) * 100
        print(f"数字{num:2d}: {freq:4d}次 ({percentage:5.1f}%)")
    
    # 特别关注30和40
    freq_30 = actual_freq.get(30, 0)
    freq_40 = actual_freq.get(40, 0)
    
    print(f"\n🎯 重点分析:")
    print(f"数字30: {freq_30}次 ({freq_30/len(lottery_df)*100:.1f}%)")
    print(f"数字40: {freq_40}次 ({freq_40/len(lottery_df)*100:.1f}%)")
    
    return actual_freq

def analyze_algorithm_bias():
    """分析算法偏好"""
    print(f"\n🔍 分析34.3%增强马尔可夫算法偏好")
    print("="*50)
    
    # 从集成预测系统的代码中分析算法设置
    print("算法设置分析:")
    print("high_freq_numbers = [5, 15, 3, 40, 30]")
    print("low_freq_numbers = [41, 1, 8, 48, 47]")
    print("rising_numbers = [30, 39, 4, 8, 22]")
    print("falling_numbers = [5, 26, 44, 36, 15]")
    
    print(f"\n数字30的算法地位:")
    print(f"✅ 在high_freq_numbers中 (权重: 1.15)")
    print(f"✅ 在rising_numbers中 (权重: 1.10)")
    print(f"✅ 复合权重: 1.15 × 1.10 = 1.265")
    
    print(f"\n数字40的算法地位:")
    print(f"✅ 在high_freq_numbers中 (权重: 1.15)")
    print(f"❌ 不在rising_numbers中 (权重: 1.00)")
    print(f"✅ 复合权重: 1.15 × 1.00 = 1.15")
    
    print(f"\n算法偏好解释:")
    print(f"1. 数字30和40都被标记为'高频数字'，获得15%的权重提升")
    print(f"2. 数字30还被标记为'上升趋势数字'，额外获得10%提升")
    print(f"3. 在马尔可夫转移概率计算中，这些数字更容易被选中")

def analyze_prediction_patterns():
    """分析预测模式"""
    print(f"\n🔍 分析预测模式")
    print("="*40)
    
    df = pd.read_csv('prediction_data.csv')
    
    # 分析30和40的组合模式
    combinations_with_30 = []
    combinations_with_40 = []
    combinations_30_40 = 0
    
    for _, row in df.iterrows():
        pred1, pred2 = row['预测数字1'], row['预测数字2']
        
        if 30 in [pred1, pred2]:
            other_num = pred2 if pred1 == 30 else pred1
            combinations_with_30.append(other_num)
            
        if 40 in [pred1, pred2]:
            other_num = pred2 if pred1 == 40 else pred1
            combinations_with_40.append(other_num)
            
        if set([pred1, pred2]) == {30, 40}:
            combinations_30_40 += 1
    
    print(f"数字30的组合伙伴 (Top 10):")
    combo_30_freq = Counter(combinations_with_30)
    for num, freq in combo_30_freq.most_common(10):
        print(f"  30 + {num}: {freq}次")
    
    print(f"\n数字40的组合伙伴 (Top 10):")
    combo_40_freq = Counter(combinations_with_40)
    for num, freq in combo_40_freq.most_common(10):
        print(f"  40 + {num}: {freq}次")
    
    print(f"\n30和40同时预测: {combinations_30_40}次")

def analyze_performance():
    """分析30和40的预测表现"""
    print(f"\n🔍 分析30和40的预测表现")
    print("="*40)
    
    df = pd.read_csv('prediction_data.csv')
    
    # 分析包含30的预测
    predictions_with_30 = df[
        (df['预测数字1'] == 30) | (df['预测数字2'] == 30)
    ].copy()
    
    # 分析包含40的预测
    predictions_with_40 = df[
        (df['预测数字1'] == 40) | (df['预测数字2'] == 40)
    ].copy()
    
    # 计算命中率
    hit_rate_30 = len(predictions_with_30[predictions_with_30['是否命中'] == '是']) / len(predictions_with_30)
    hit_rate_40 = len(predictions_with_40[predictions_with_40['是否命中'] == '是']) / len(predictions_with_40)
    overall_hit_rate = len(df[df['是否命中'] == '是']) / len(df)
    
    print(f"预测表现分析:")
    print(f"包含数字30的预测: {len(predictions_with_30)}期")
    print(f"  命中率: {hit_rate_30:.1%}")
    print(f"  vs 总体命中率: {hit_rate_30-overall_hit_rate:+.1%}")
    
    print(f"\n包含数字40的预测: {len(predictions_with_40)}期")
    print(f"  命中率: {hit_rate_40:.1%}")
    print(f"  vs 总体命中率: {hit_rate_40-overall_hit_rate:+.1%}")
    
    print(f"\n总体命中率: {overall_hit_rate:.1%}")
    
    # 分析30在实际开奖中的表现
    hit_30_count = 0
    hit_40_count = 0
    
    for _, row in predictions_with_30.iterrows():
        actual_nums = [row[f'实际数字{i}'] for i in range(1, 7) if pd.notna(row[f'实际数字{i}'])]
        if 30 in actual_nums:
            hit_30_count += 1
    
    for _, row in predictions_with_40.iterrows():
        actual_nums = [row[f'实际数字{i}'] for i in range(1, 7) if pd.notna(row[f'实际数字{i}'])]
        if 40 in actual_nums:
            hit_40_count += 1
    
    print(f"\n数字本身的命中情况:")
    print(f"数字30被预测时实际开出: {hit_30_count}/{len(predictions_with_30)} = {hit_30_count/len(predictions_with_30):.1%}")
    print(f"数字40被预测时实际开出: {hit_40_count}/{len(predictions_with_40)} = {hit_40_count/len(predictions_with_40):.1%}")

def create_visualization():
    """创建可视化图表"""
    print(f"\n📊 创建可视化图表")
    print("="*40)
    
    df = pd.read_csv('prediction_data.csv')
    
    # 统计预测频率
    all_predictions = []
    for _, row in df.iterrows():
        all_predictions.extend([row['预测数字1'], row['预测数字2']])
    
    pred_freq = Counter(all_predictions)
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('数字30和40频繁预测分析', fontsize=16, fontweight='bold')
    
    # 1. 预测频率柱状图
    ax1 = axes[0, 0]
    numbers = list(range(1, 50))
    frequencies = [pred_freq.get(num, 0) for num in numbers]
    
    bars = ax1.bar(numbers, frequencies, alpha=0.7, color='skyblue')
    
    # 高亮30和40
    bars[29].set_color('red')  # 数字30
    bars[39].set_color('orange')  # 数字40
    
    ax1.set_xlabel('数字')
    ax1.set_ylabel('预测次数')
    ax1.set_title('预测数字频率分布')
    ax1.grid(True, alpha=0.3)
    
    # 标注30和40
    ax1.annotate(f'30: {pred_freq[30]}次', xy=(30, pred_freq[30]), 
                xytext=(30, pred_freq[30]+5), ha='center', fontweight='bold', color='red')
    ax1.annotate(f'40: {pred_freq[40]}次', xy=(40, pred_freq[40]), 
                xytext=(40, pred_freq[40]+5), ha='center', fontweight='bold', color='orange')
    
    # 2. Top 15预测数字
    ax2 = axes[0, 1]
    top_15 = pred_freq.most_common(15)
    nums, freqs = zip(*top_15)
    
    colors = ['red' if num == 30 else 'orange' if num == 40 else 'skyblue' for num in nums]
    bars = ax2.bar(range(len(nums)), freqs, color=colors, alpha=0.7)
    
    ax2.set_xlabel('数字排名')
    ax2.set_ylabel('预测次数')
    ax2.set_title('Top 15 预测数字')
    ax2.set_xticks(range(len(nums)))
    ax2.set_xticklabels(nums)
    ax2.grid(True, alpha=0.3)
    
    # 3. 30和40的时间序列
    ax3 = axes[1, 0]
    
    periods = []
    has_30 = []
    has_40 = []
    
    for i, (_, row) in enumerate(df.iterrows()):
        periods.append(i+1)
        has_30.append(1 if 30 in [row['预测数字1'], row['预测数字2']] else 0)
        has_40.append(1 if 40 in [row['预测数字1'], row['预测数字2']] else 0)
    
    ax3.plot(periods, np.cumsum(has_30), 'r-', label='数字30累计', linewidth=2)
    ax3.plot(periods, np.cumsum(has_40), 'orange', label='数字40累计', linewidth=2)
    
    ax3.set_xlabel('预测期数')
    ax3.set_ylabel('累计预测次数')
    ax3.set_title('30和40的累计预测趋势')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 命中率对比
    ax4 = axes[1, 1]
    
    # 计算不同数字的命中率
    number_hit_rates = {}
    for num in range(1, 50):
        predictions_with_num = df[
            (df['预测数字1'] == num) | (df['预测数字2'] == num)
        ]
        if len(predictions_with_num) >= 5:  # 至少预测5次
            hit_rate = len(predictions_with_num[predictions_with_num['是否命中'] == '是']) / len(predictions_with_num)
            number_hit_rates[num] = hit_rate
    
    # 绘制命中率
    nums = list(number_hit_rates.keys())
    hit_rates = list(number_hit_rates.values())
    
    colors = ['red' if num == 30 else 'orange' if num == 40 else 'lightblue' for num in nums]
    ax4.scatter(nums, hit_rates, c=colors, s=60, alpha=0.7)
    
    # 标注30和40
    if 30 in number_hit_rates:
        ax4.annotate(f'30: {number_hit_rates[30]:.1%}', 
                    xy=(30, number_hit_rates[30]), xytext=(30, number_hit_rates[30]+0.05),
                    ha='center', fontweight='bold', color='red')
    if 40 in number_hit_rates:
        ax4.annotate(f'40: {number_hit_rates[40]:.1%}', 
                    xy=(40, number_hit_rates[40]), xytext=(40, number_hit_rates[40]+0.05),
                    ha='center', fontweight='bold', color='orange')
    
    ax4.set_xlabel('数字')
    ax4.set_ylabel('命中率')
    ax4.set_title('不同数字的预测命中率')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f'frequent_predictions_analysis_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    
    print(f"可视化图表已保存: {filename}")
    return filename

def generate_insights():
    """生成深度洞察"""
    print(f"\n💡 深度洞察：为什么系统频繁预测30和40")
    print("="*60)
    
    print(f"根本原因分析:")
    print(f"1. 算法设计偏好")
    print(f"   - 30和40都被标记为'高频数字'，获得15%权重提升")
    print(f"   - 数字30还被标记为'上升趋势'，额外获得10%提升")
    print(f"   - 在马尔可夫转移概率中，这些数字更容易被选中")
    
    print(f"\n2. 历史数据影响")
    print(f"   - 算法基于2023-2024年数据训练")
    print(f"   - 在训练期间，30和40可能表现突出")
    print(f"   - 算法'学会'了偏好这些数字")
    
    print(f"\n3. 马尔可夫链特性")
    print(f"   - 马尔可夫算法基于状态转移概率")
    print(f"   - 如果30和40在历史转移中频繁出现")
    print(f"   - 算法会持续选择这些数字")
    
    print(f"\n4. 权重累积效应")
    print(f"   - 频率权重 × 趋势权重 = 复合权重")
    print(f"   - 数字30: 1.15 × 1.10 = 1.265")
    print(f"   - 数字40: 1.15 × 1.00 = 1.15")
    print(f"   - 权重优势导致选择偏好")
    
    print(f"\n实际表现评估:")
    df = pd.read_csv('prediction_data.csv')
    
    predictions_with_30 = df[(df['预测数字1'] == 30) | (df['预测数字2'] == 30)]
    predictions_with_40 = df[(df['预测数字1'] == 40) | (df['预测数字2'] == 40)]
    
    hit_rate_30 = len(predictions_with_30[predictions_with_30['是否命中'] == '是']) / len(predictions_with_30)
    hit_rate_40 = len(predictions_with_40[predictions_with_40['是否命中'] == '是']) / len(predictions_with_40)
    overall_hit_rate = len(df[df['是否命中'] == '是']) / len(df)
    
    print(f"   - 包含30的预测命中率: {hit_rate_30:.1%}")
    print(f"   - 包含40的预测命中率: {hit_rate_40:.1%}")
    print(f"   - 总体命中率: {overall_hit_rate:.1%}")
    
    if hit_rate_30 > overall_hit_rate:
        print(f"   ✅ 数字30的偏好是合理的")
    else:
        print(f"   ❌ 数字30的偏好可能过度")
        
    if hit_rate_40 > overall_hit_rate:
        print(f"   ✅ 数字40的偏好是合理的")
    else:
        print(f"   ❌ 数字40的偏好可能过度")
    
    print(f"\n改进建议:")
    print(f"1. 重新评估数字分类")
    print(f"   - 基于更长期的历史数据重新分类")
    print(f"   - 动态调整高频/低频数字列表")
    
    print(f"2. 平衡权重设置")
    print(f"   - 降低单个数字的权重优势")
    print(f"   - 增加随机性和多样性")
    
    print(f"3. 引入反馈机制")
    print(f"   - 根据实际表现调整数字权重")
    print(f"   - 实现自适应的算法参数")

def main():
    """主函数"""
    print("🔍 分析系统为什么频繁预测数字30和40")
    print("="*70)
    
    try:
        # 1. 分析预测频率
        pred_freq = analyze_prediction_frequency()
        
        # 2. 分析实际频率
        actual_freq = analyze_actual_frequency()
        
        # 3. 分析算法偏好
        analyze_algorithm_bias()
        
        # 4. 分析预测模式
        analyze_prediction_patterns()
        
        # 5. 分析预测表现
        analyze_performance()
        
        # 6. 创建可视化
        chart_filename = create_visualization()
        
        # 7. 生成深度洞察
        generate_insights()
        
        print(f"\n🎯 总结:")
        print(f"系统频繁预测30和40的主要原因是算法设计中的权重偏好。")
        print(f"这种偏好在一定程度上是合理的，但可能需要进一步优化。")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
