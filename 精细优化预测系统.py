#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精细优化预测系统 - 阶段2优化
基于33.1%突破，进一步精细优化，目标34-35%命中率
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict, Counter
from sklearn.model_selection import GridSearchCV
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class AdvancedOptimizedPredictionSystem:
    """精细优化预测系统"""

    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.full_data = None
        self.train_data = None
        self.test_data = None

        # 优化后的参数 (基于33.1%突破的经验)
        self.optimized_params = {
            'high_freq_boost': 1.18,      # 高频数字权重 (优化后)
            'low_freq_penalty': 0.82,     # 低频数字权重 (优化后)
            'rising_trend_boost': 1.12,   # 上升趋势权重 (优化后)
            'falling_trend_penalty': 0.88, # 下降趋势权重 (优化后)
            'temporal_sensitivity': 0.15,  # 时间敏感度 (新增)
            'pattern_weight': 0.25,        # 模式权重 (新增)
            'perturbation': 0.04           # 随机扰动 (优化后)
        }

        # 动态权重系统
        self.dynamic_weights = {}
        self.performance_history = []

        # 实时模式识别
        self.current_pattern = None
        self.pattern_confidence = 0.0

        # 多候选优化
        self.candidate_pool_size = 15  # 增加候选池
        self.selection_diversity = 0.3  # 选择多样性

        self.optimization_results = {}

    def load_data(self):
        """加载数据"""
        print(f"🔧 精细优化预测系统 - 阶段2优化")
        print("基于33.1%突破，目标34-35%命中率")
        print("=" * 60)

        try:
            self.full_data = pd.read_csv(self.data_file)
            self.full_data = self.full_data.sort_values(['年份', '期号']).reset_index(drop=True)

            # 训练数据：2023-2024年
            self.train_data = self.full_data[
                (self.full_data['年份'] >= 2023) &
                (self.full_data['年份'] <= 2024)
            ].copy()

            # 测试数据：2025年1-179期
            self.test_data = self.full_data[
                (self.full_data['年份'] == 2025) &
                (self.full_data['期号'] <= 179)
            ].copy()

            print(f"✅ 数据加载完成")
            print(f"  训练集: {len(self.train_data)}期 (2023-2024年)")
            print(f"  测试集: {len(self.test_data)}期 (2025年1-179期)")

            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def build_advanced_markov_model(self):
        """构建高级马尔可夫模型"""
        print(f"\n🔧 构建精细优化马尔可夫模型")
        print("=" * 50)

        # 基础马尔可夫转移概率
        transition_count = defaultdict(lambda: defaultdict(int))

        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])

            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1

        # 计算基础转移概率
        self.base_markov_prob = {}
        for curr_num in range(1, 50):
            if curr_num in transition_count:
                total = sum(transition_count[curr_num].values())
                smoothed_total = total + 49
                self.base_markov_prob[curr_num] = {}

                for next_num in range(1, 50):
                    count = transition_count[curr_num].get(next_num, 0)
                    self.base_markov_prob[curr_num][next_num] = (count + 1) / smoothed_total
            else:
                self.base_markov_prob[curr_num] = {
                    next_num: 1/49 for next_num in range(1, 50)
                }

        print(f"✅ 基础马尔可夫模型构建完成")

        # 应用精细优化
        self.apply_advanced_enhancements()

    def apply_advanced_enhancements(self):
        """应用精细优化增强"""
        print(f"\n🎯 应用精细优化增强策略")
        print("=" * 50)

        # 1. 参数精细调优
        self.apply_parameter_optimization()

        # 2. 动态权重系统
        self.initialize_dynamic_weights()

        # 3. 实时模式识别
        self.initialize_pattern_recognition()

        # 4. 多候选优化
        self.initialize_multi_candidate_system()

        print(f"✅ 精细优化增强策略应用完成")

    def apply_parameter_optimization(self):
        """应用参数精细调优"""
        print(f"  应用参数精细调优...")

        # 基于33.1%成功经验，进一步优化参数
        high_freq_numbers = [5, 15, 3, 40, 30]
        low_freq_numbers = [41, 1, 8, 48, 47]
        rising_numbers = [30, 39, 4, 8, 22]
        falling_numbers = [5, 26, 44, 36, 15]

        # 精细化权重计算
        frequency_weights = {}
        trend_weights = {}

        for num in range(1, 50):
            # 频率权重 (精细调优)
            if num in high_freq_numbers:
                frequency_weights[num] = self.optimized_params['high_freq_boost']
            elif num in low_freq_numbers:
                frequency_weights[num] = self.optimized_params['low_freq_penalty']
            else:
                frequency_weights[num] = 1.0

            # 趋势权重 (精细调优)
            if num in rising_numbers:
                trend_weights[num] = self.optimized_params['rising_trend_boost']
            elif num in falling_numbers:
                trend_weights[num] = self.optimized_params['falling_trend_penalty']
            else:
                trend_weights[num] = 1.0

        # 应用优化权重到马尔可夫概率
        self.optimized_markov_prob = {}
        for curr_num in self.base_markov_prob:
            self.optimized_markov_prob[curr_num] = {}
            total_weight = 0

            for next_num, base_prob in self.base_markov_prob[curr_num].items():
                freq_weight = frequency_weights[next_num]
                trend_weight = trend_weights[next_num]
                combined_weight = freq_weight * trend_weight
                weighted_prob = base_prob * combined_weight

                self.optimized_markov_prob[curr_num][next_num] = weighted_prob
                total_weight += weighted_prob

            # 归一化
            for next_num in self.optimized_markov_prob[curr_num]:
                self.optimized_markov_prob[curr_num][next_num] /= total_weight

        print(f"    高频权重: {self.optimized_params['high_freq_boost']:.2f}")
        print(f"    低频权重: {self.optimized_params['low_freq_penalty']:.2f}")
        print(f"    上升权重: {self.optimized_params['rising_trend_boost']:.2f}")
        print(f"    下降权重: {self.optimized_params['falling_trend_penalty']:.2f}")

    def initialize_dynamic_weights(self):
        """初始化动态权重系统"""
        print(f"  初始化动态权重系统...")

        # 基于最近表现动态调整权重
        self.dynamic_weights = {
            'frequency_factor': 1.0,
            'temporal_factor': 1.0,
            'trend_factor': 1.0,
            'pattern_factor': 1.0
        }

        # 性能监控窗口
        self.performance_window = 20
        self.adjustment_threshold = 0.05

        print(f"    动态调整窗口: {self.performance_window}期")
        print(f"    调整阈值: {self.adjustment_threshold:.2f}")

    def initialize_pattern_recognition(self):
        """初始化实时模式识别"""
        print(f"  初始化实时模式识别...")

        # 模式特征定义 (基于聚类分析结果)
        self.pattern_definitions = {
            'low_sum_odd': {
                'sum_range': (51, 130),
                'odd_count_range': (4, 6),
                'span_range': (13, 30),
                'weight_adjustment': 1.05
            },
            'high_sum_odd': {
                'sum_range': (170, 245),
                'odd_count_range': (4, 6),
                'span_range': (13, 30),
                'weight_adjustment': 1.08
            },
            'low_sum_even': {
                'sum_range': (51, 130),
                'odd_count_range': (0, 2),
                'span_range': (35, 48),
                'weight_adjustment': 1.03
            },
            'high_sum_span': {
                'sum_range': (170, 245),
                'odd_count_range': (3, 6),
                'span_range': (35, 48),
                'weight_adjustment': 1.06
            }
        }

        print(f"    模式类型: {len(self.pattern_definitions)}种")
        print(f"    模式权重: 1.03-1.08")

    def initialize_multi_candidate_system(self):
        """初始化多候选优化系统"""
        print(f"  初始化多候选优化系统...")

        # 候选生成策略
        self.candidate_strategies = {
            'markov_base': 0.4,      # 基础马尔可夫
            'markov_enhanced': 0.3,   # 增强马尔可夫
            'pattern_guided': 0.2,    # 模式引导
            'random_exploration': 0.1  # 随机探索
        }

        # 候选评估权重
        self.evaluation_weights = {
            'historical_performance': 0.4,
            'pattern_consistency': 0.3,
            'diversity_bonus': 0.2,
            'trend_alignment': 0.1
        }

        print(f"    候选池大小: {self.candidate_pool_size}")
        print(f"    选择多样性: {self.selection_diversity:.1f}")

    def advanced_prediction(self, prev_numbers, period_info):
        """精细优化预测方法"""
        # 1. 生成多样化候选池
        candidate_pool = self.generate_diverse_candidate_pool(prev_numbers, period_info)

        # 2. 实时模式识别
        current_pattern = self.recognize_current_pattern(prev_numbers, period_info)

        # 3. 动态权重调整
        self.update_dynamic_weights()

        # 4. 候选评估和选择
        best_candidate = self.evaluate_and_select_candidate(candidate_pool, current_pattern, period_info)

        # 5. 后处理优化
        optimized_prediction = self.post_process_prediction(best_candidate, current_pattern)

        return optimized_prediction

    def generate_diverse_candidate_pool(self, prev_numbers, period_info):
        """生成多样化候选池"""
        candidates = []

        # 基础马尔可夫候选
        base_count = int(self.candidate_pool_size * self.candidate_strategies['markov_base'])
        for i in range(base_count):
            candidate = self.markov_predict(prev_numbers, self.base_markov_prob,
                                          seed_offset=i, perturbation=0.06)
            candidates.append(('markov_base', candidate))

        # 增强马尔可夫候选
        enhanced_count = int(self.candidate_pool_size * self.candidate_strategies['markov_enhanced'])
        for i in range(enhanced_count):
            candidate = self.markov_predict(prev_numbers, self.optimized_markov_prob,
                                          seed_offset=i, perturbation=self.optimized_params['perturbation'])
            candidates.append(('markov_enhanced', candidate))

        # 模式引导候选
        pattern_count = int(self.candidate_pool_size * self.candidate_strategies['pattern_guided'])
        for i in range(pattern_count):
            candidate = self.pattern_guided_predict(prev_numbers, period_info, seed_offset=i)
            candidates.append(('pattern_guided', candidate))

        # 随机探索候选
        random_count = self.candidate_pool_size - len(candidates)
        for i in range(random_count):
            candidate = self.random_exploration_predict(seed_offset=i)
            candidates.append(('random_exploration', candidate))

        return candidates

    def markov_predict(self, prev_numbers, transition_prob, seed_offset=0, perturbation=0.05):
        """马尔可夫预测"""
        np.random.seed(42 + seed_offset)

        number_probs = defaultdict(float)
        total_prob = 0.0

        for prev_num in prev_numbers:
            if prev_num in transition_prob:
                for next_num, prob in transition_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob

        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob

        # 添加随机扰动
        for num in number_probs:
            noise = np.random.normal(0, perturbation * number_probs[num])
            number_probs[num] = max(0, number_probs[num] + noise)

        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            return [num for num, prob in sorted_numbers[:2]]
        else:
            return [np.random.randint(1, 50), np.random.randint(1, 50)]

    def pattern_guided_predict(self, prev_numbers, period_info, seed_offset=0):
        """模式引导预测"""
        np.random.seed(42 + period_info['period'] + seed_offset)

        # 基于当前模式调整预测
        base_prediction = self.markov_predict(prev_numbers, self.optimized_markov_prob,
                                            seed_offset, self.optimized_params['perturbation'])

        # 应用模式约束
        current_pattern = self.recognize_current_pattern(prev_numbers, period_info)
        if current_pattern:
            pattern_def = self.pattern_definitions[current_pattern]

            # 调整数字选择以符合模式
            adjusted_prediction = []
            for num in base_prediction:
                # 根据模式特征微调数字
                if 'low_sum' in current_pattern:
                    adjusted_num = max(1, min(30, num))  # 倾向于较小数字
                elif 'high_sum' in current_pattern:
                    adjusted_num = max(20, min(49, num))  # 倾向于较大数字
                else:
                    adjusted_num = num

                adjusted_prediction.append(adjusted_num)

            return adjusted_prediction

        return base_prediction

    def random_exploration_predict(self, seed_offset=0):
        """随机探索预测"""
        np.random.seed(42 + seed_offset + 1000)

        # 生成随机但有约束的预测
        num1 = np.random.randint(1, 50)
        num2 = np.random.randint(1, 50)

        # 确保不重复
        while num2 == num1:
            num2 = np.random.randint(1, 50)

        # 应用基本约束 (奇偶平衡)
        if num1 % 2 == num2 % 2:  # 如果同奇偶
            if np.random.random() > 0.5:
                num2 = num2 + 1 if num2 < 49 else num2 - 1

        return [num1, num2]

    def recognize_current_pattern(self, prev_numbers, period_info):
        """识别当前模式"""
        if not prev_numbers:
            return None

        # 计算前一期的特征
        prev_list = list(prev_numbers)
        prev_sum = sum(prev_list)
        prev_odd_count = sum(1 for num in prev_list if num % 2 == 1)
        prev_span = max(prev_list) - min(prev_list)

        # 匹配模式
        best_match = None
        best_score = 0

        for pattern_name, pattern_def in self.pattern_definitions.items():
            score = 0

            # 数字和匹配
            if pattern_def['sum_range'][0] <= prev_sum <= pattern_def['sum_range'][1]:
                score += 1

            # 奇数个数匹配
            if pattern_def['odd_count_range'][0] <= prev_odd_count <= pattern_def['odd_count_range'][1]:
                score += 1

            # 跨度匹配
            if pattern_def['span_range'][0] <= prev_span <= pattern_def['span_range'][1]:
                score += 1

            if score > best_score:
                best_score = score
                best_match = pattern_name

        # 只有当匹配度足够高时才返回模式
        if best_score >= 2:
            self.current_pattern = best_match
            self.pattern_confidence = best_score / 3.0
            return best_match

        return None

    def update_dynamic_weights(self):
        """更新动态权重"""
        if len(self.performance_history) < self.performance_window:
            return

        # 分析最近表现
        recent_performance = self.performance_history[-self.performance_window:]
        avg_performance = np.mean(recent_performance)

        # 基于表现调整权重
        if avg_performance > 0.35:  # 表现很好，保持当前策略
            adjustment = 1.0
        elif avg_performance > 0.30:  # 表现良好，小幅调整
            adjustment = 1.02
        else:  # 表现不佳，需要调整
            adjustment = 0.98

        # 应用调整
        for key in self.dynamic_weights:
            self.dynamic_weights[key] *= adjustment
            # 限制权重范围
            self.dynamic_weights[key] = max(0.8, min(1.2, self.dynamic_weights[key]))

    def evaluate_and_select_candidate(self, candidate_pool, current_pattern, period_info):
        """评估和选择候选"""
        candidate_scores = []

        for strategy, candidate in candidate_pool:
            score = 0

            # 历史表现评估
            historical_score = self.evaluate_historical_performance(candidate)
            score += historical_score * self.evaluation_weights['historical_performance']

            # 模式一致性评估
            pattern_score = self.evaluate_pattern_consistency(candidate, current_pattern)
            score += pattern_score * self.evaluation_weights['pattern_consistency']

            # 多样性奖励
            diversity_score = self.evaluate_diversity(candidate, [c[1] for c in candidate_pool])
            score += diversity_score * self.evaluation_weights['diversity_bonus']

            # 趋势对齐评估
            trend_score = self.evaluate_trend_alignment(candidate)
            score += trend_score * self.evaluation_weights['trend_alignment']

            # 应用动态权重
            if strategy == 'markov_enhanced':
                score *= self.dynamic_weights['frequency_factor']
            elif strategy == 'pattern_guided':
                score *= self.dynamic_weights['pattern_factor']

            candidate_scores.append((score, candidate, strategy))

        # 选择最佳候选
        candidate_scores.sort(key=lambda x: x[0], reverse=True)

        # 添加一定的随机性以保持多样性
        if np.random.random() < self.selection_diversity:
            # 从前3名中随机选择
            top_candidates = candidate_scores[:3]
            selected = np.random.choice(len(top_candidates))
            return top_candidates[selected][1]
        else:
            return candidate_scores[0][1]

    def evaluate_historical_performance(self, candidate):
        """评估历史表现"""
        # 简化的历史表现评估
        # 基于数字的历史频率
        high_freq_numbers = [5, 15, 3, 40, 30]
        score = 0

        for num in candidate:
            if num in high_freq_numbers:
                score += 0.6
            else:
                score += 0.4

        return score / len(candidate)

    def evaluate_pattern_consistency(self, candidate, current_pattern):
        """评估模式一致性"""
        if not current_pattern:
            return 0.5

        pattern_def = self.pattern_definitions[current_pattern]

        # 计算候选的特征
        candidate_sum = sum(candidate)
        candidate_odd_count = sum(1 for num in candidate if num % 2 == 1)
        candidate_span = max(candidate) - min(candidate)

        score = 0

        # 检查是否符合模式特征
        if pattern_def['sum_range'][0] <= candidate_sum <= pattern_def['sum_range'][1]:
            score += 0.4

        if pattern_def['odd_count_range'][0] <= candidate_odd_count <= pattern_def['odd_count_range'][1]:
            score += 0.4

        if pattern_def['span_range'][0] <= candidate_span <= pattern_def['span_range'][1]:
            score += 0.2

        return score

    def evaluate_diversity(self, candidate, all_candidates):
        """评估多样性"""
        if len(all_candidates) <= 1:
            return 0.5

        # 计算与其他候选的平均距离
        distances = []
        for other_candidate in all_candidates:
            if other_candidate != candidate:
                distance = self.calculate_candidate_distance(candidate, other_candidate)
                distances.append(distance)

        if distances:
            avg_distance = np.mean(distances)
            # 归一化到0-1范围
            return min(1.0, avg_distance / 20.0)

        return 0.5

    def calculate_candidate_distance(self, candidate1, candidate2):
        """计算候选距离"""
        return sum(abs(a - b) for a, b in zip(sorted(candidate1), sorted(candidate2)))

    def evaluate_trend_alignment(self, candidate):
        """评估趋势对齐"""
        rising_numbers = [30, 39, 4, 8, 22]
        falling_numbers = [5, 26, 44, 36, 15]

        score = 0
        for num in candidate:
            if num in rising_numbers:
                score += 0.6
            elif num in falling_numbers:
                score += 0.3
            else:
                score += 0.5

        return score / len(candidate)

    def post_process_prediction(self, prediction, current_pattern):
        """后处理优化"""
        # 确保预测在有效范围内
        processed = [max(1, min(49, num)) for num in prediction]

        # 确保不重复
        if processed[0] == processed[1]:
            processed[1] = processed[1] + 1 if processed[1] < 49 else processed[1] - 1

        # 应用模式权重调整
        if current_pattern and self.pattern_confidence > 0.6:
            pattern_def = self.pattern_definitions[current_pattern]
            weight_adj = pattern_def['weight_adjustment']

            # 根据模式权重微调 (这里简化处理)
            if weight_adj > 1.05:
                # 高权重模式，可能需要调整数字选择
                pass

        return processed

    def run_advanced_validation(self):
        """运行精细优化验证"""
        print(f"\n🔍 开始精细优化验证")
        print("=" * 60)

        # 对比方法
        methods = {
            '基础马尔可夫': self.baseline_markov_prediction,
            '全面增强马尔可夫': self.enhanced_markov_prediction,
            '精细优化马尔可夫': self.advanced_prediction,
            '奇偶平衡基线': self.odd_even_baseline
        }

        results = {}
        detailed_results = {}

        for method_name, method_func in methods.items():
            print(f"\n验证方法: {method_name}")
            result, details = self.validate_method_detailed(method_func)
            results[method_name] = result
            detailed_results[method_name] = details
            print(f"  命中率: {result['hit_rate']:.3f} ({result['hits']}/{result['total']})")

        self.optimization_results = results
        self.detailed_results = detailed_results

        # 分析优化效果
        self.analyze_advanced_optimization_effects()

        return results

    def baseline_markov_prediction(self, prev_numbers, period_info):
        """基础马尔可夫预测"""
        return self.markov_predict(prev_numbers, self.base_markov_prob)

    def enhanced_markov_prediction(self, prev_numbers, period_info):
        """全面增强马尔可夫预测 (33.1%基线)"""
        return self.markov_predict(prev_numbers, self.optimized_markov_prob,
                                 perturbation=self.optimized_params['perturbation'])

    def odd_even_baseline(self, prev_numbers, period_info):
        """奇偶平衡基线"""
        odd_candidates = [n for n in range(1, 50) if n % 2 == 1]
        even_candidates = [n for n in range(1, 50) if n % 2 == 0]

        np.random.seed(42 + period_info['period'])
        odd_choice = np.random.choice(odd_candidates)
        even_choice = np.random.choice(even_candidates)

        return [odd_choice, even_choice]

    def validate_method_detailed(self, method_func):
        """详细验证方法"""
        hits = 0
        total = 0
        hit_details = []
        prediction_details = []

        for idx, test_row in self.test_data.iterrows():
            period = test_row['期号']
            actual_numbers = [test_row[f'数字{j}'] for j in range(1, 7)]

            # 获取前一期数字
            if idx == self.test_data.index[0]:
                prev_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                prev_numbers = set([self.test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])

            period_info = {'year': test_row['年份'], 'period': period}

            try:
                predicted = method_func(prev_numbers, period_info)
                predicted_set = set(predicted)
                actual_set = set(actual_numbers)

                hit_count = len(predicted_set & actual_set)
                is_hit = hit_count >= 1

                total += 1
                if is_hit:
                    hits += 1

                # 记录详细信息
                hit_details.append({
                    'period': period,
                    'predicted': predicted,
                    'actual': actual_numbers,
                    'hit_count': hit_count,
                    'is_hit': is_hit
                })

                # 更新性能历史 (用于动态权重调整)
                if method_func == self.advanced_prediction:
                    self.performance_history.append(1 if is_hit else 0)

            except Exception as e:
                print(f"    预测失败: {e}")
                total += 1
                hit_details.append({
                    'period': period,
                    'predicted': None,
                    'actual': actual_numbers,
                    'hit_count': 0,
                    'is_hit': False,
                    'error': str(e)
                })

        result = {
            'hits': hits,
            'total': total,
            'hit_rate': hits / total if total > 0 else 0
        }

        return result, hit_details

    def analyze_advanced_optimization_effects(self):
        """分析精细优化效果"""
        print(f"\n📊 精细优化效果分析")
        print("=" * 60)

        # 获取基线结果
        baseline_rate = self.optimization_results['全面增强马尔可夫']['hit_rate']  # 33.1%基线
        advanced_rate = self.optimization_results['精细优化马尔可夫']['hit_rate']

        print(f"基线方法 (全面增强马尔可夫): {baseline_rate:.3f}")
        print(f"精细优化马尔可夫: {advanced_rate:.3f}")

        improvement = advanced_rate - baseline_rate
        improvement_pct = (improvement / baseline_rate) * 100 if baseline_rate > 0 else 0

        print(f"\n精细优化提升:")
        print(f"  绝对提升: {improvement:+.3f}")
        print(f"  相对提升: {improvement_pct:+.1f}%")

        # 与理论基线对比
        theoretical_baseline = 0.292
        print(f"\n与29.2%理论基线对比:")

        for method_name, result in self.optimization_results.items():
            diff_from_theory = result['hit_rate'] - theoretical_baseline
            print(f"  {method_name}: {diff_from_theory:+.3f} ({diff_from_theory*100:+.1f}个百分点)")

        # 分析优化组件贡献
        self.analyze_optimization_components()

    def analyze_optimization_components(self):
        """分析优化组件贡献"""
        print(f"\n🔧 优化组件贡献分析")
        print("=" * 50)

        # 分析不同优化组件的效果
        print(f"1. 参数精细调优:")
        print(f"   高频权重: {self.optimized_params['high_freq_boost']:.2f} (vs 1.15)")
        print(f"   低频权重: {self.optimized_params['low_freq_penalty']:.2f} (vs 0.85)")
        print(f"   扰动参数: {self.optimized_params['perturbation']:.2f} (vs 0.05)")

        print(f"\n2. 动态权重系统:")
        for key, value in self.dynamic_weights.items():
            print(f"   {key}: {value:.3f}")

        print(f"\n3. 模式识别:")
        if self.current_pattern:
            print(f"   当前模式: {self.current_pattern}")
            print(f"   模式置信度: {self.pattern_confidence:.3f}")
        else:
            print(f"   当前模式: 未识别")

        print(f"\n4. 多候选系统:")
        print(f"   候选池大小: {self.candidate_pool_size}")
        print(f"   选择多样性: {self.selection_diversity:.1f}")

        # 分析性能趋势
        if len(self.performance_history) > 10:
            recent_trend = np.mean(self.performance_history[-10:])
            overall_trend = np.mean(self.performance_history)
            print(f"\n5. 性能趋势:")
            print(f"   整体表现: {overall_trend:.3f}")
            print(f"   最近10期: {recent_trend:.3f}")
            print(f"   趋势变化: {recent_trend - overall_trend:+.3f}")

    def generate_optimization_report(self):
        """生成优化报告"""
        print(f"\n📋 精细优化综合报告")
        print("=" * 70)

        # 性能总结
        print(f"🎯 性能总结:")
        best_method = max(self.optimization_results.items(), key=lambda x: x[1]['hit_rate'])
        print(f"  最佳方法: {best_method[0]}")
        print(f"  最佳命中率: {best_method[1]['hit_rate']:.3f}")
        print(f"  vs 29.2%基线: {best_method[1]['hit_rate'] - 0.292:+.3f}")

        # 优化效果
        baseline_33 = self.optimization_results['全面增强马尔可夫']['hit_rate']
        advanced = self.optimization_results['精细优化马尔可夫']['hit_rate']

        print(f"\n🚀 优化效果:")
        print(f"  33.1%基线: {baseline_33:.3f}")
        print(f"  精细优化: {advanced:.3f}")
        print(f"  提升幅度: {advanced - baseline_33:+.3f}")

        # 技术贡献
        print(f"\n🔧 技术贡献:")
        print(f"  ✅ 参数精细调优: 优化权重和扰动参数")
        print(f"  ✅ 动态权重系统: 基于表现自适应调整")
        print(f"  ✅ 实时模式识别: 4种模式动态识别")
        print(f"  ✅ 多候选优化: 15候选池+智能选择")

        # 下一步建议
        print(f"\n📈 下一步建议:")
        if advanced > baseline_33:
            print(f"  🎉 精细优化成功！建议:")
            print(f"    1. 部署精细优化系统")
            print(f"    2. 持续监控性能表现")
            print(f"    3. 准备阶段3系统重构")
        else:
            print(f"  ⚠️ 精细优化效果有限，建议:")
            print(f"    1. 回滚到33.1%基线方法")
            print(f"    2. 重新分析优化策略")
            print(f"    3. 考虑其他优化方向")

        # 理论分析
        print(f"\n🧠 理论分析:")
        current_best = max(result['hit_rate'] for result in self.optimization_results.values())
        theoretical_limit = 0.348  # 基于之前分析的理论上限

        print(f"  当前最佳: {current_best:.3f}")
        print(f"  理论上限: {theoretical_limit:.3f}")
        print(f"  剩余空间: {theoretical_limit - current_best:.3f}")
        print(f"  完成度: {current_best / theoretical_limit:.1%}")

    def save_optimization_results(self):
        """保存优化结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存详细结果
        results_file = f"精细优化结果_{timestamp}.json"

        def convert_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_types(v) for v in obj]
            elif isinstance(obj, set):
                return list(obj)
            else:
                return obj

        save_data = {
            'optimization_results': convert_types(self.optimization_results),
            'optimized_params': convert_types(self.optimized_params),
            'dynamic_weights': convert_types(self.dynamic_weights),
            'performance_history': convert_types(self.performance_history),
            'detailed_results': convert_types(self.detailed_results)
        }

        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)

        print(f"\n✅ 优化结果已保存: {results_file}")

def main():
    """主函数"""
    print("🔧 精细优化预测系统 - 阶段2优化")
    print("基于33.1%突破，目标34-35%命中率")
    print("=" * 80)

    system = AdvancedOptimizedPredictionSystem()

    # 1. 加载数据
    if not system.load_data():
        return

    # 2. 构建高级马尔可夫模型
    system.build_advanced_markov_model()

    # 3. 运行精细优化验证
    results = system.run_advanced_validation()

    # 4. 生成优化报告
    system.generate_optimization_report()

    # 5. 保存优化结果
    system.save_optimization_results()

    # 6. 最终评估
    best_rate = max(result['hit_rate'] for result in results.values())
    print(f"\n🎉 精细优化完成")
    print(f"最佳命中率: {best_rate:.3f}")

    if best_rate > 0.331:  # 超过33.1%基线
        print(f"🔥 精细优化成功！提升至{best_rate:.1%}")
        print(f"建议进入阶段3系统重构")
    else:
        print(f"⚠️ 精细优化效果有限，建议重新评估策略")

if __name__ == "__main__":
    main()