#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
置信度评估集成适配器
Confidence Evaluation Integration Adapter

用于将新的多维度置信度评估器无缝集成到现有预测系统中
支持渐进式迁移和A/B测试

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0
"""

import pandas as pd
import numpy as np
from production_confidence_evaluator import ProductionConfidenceEvaluator
from datetime import datetime
import logging
import json
import os

logger = logging.getLogger(__name__)

class ConfidenceIntegrationAdapter:
    """置信度评估集成适配器"""
    
    def __init__(self, migration_mode='gradual', ab_test_ratio=0.5):
        """
        初始化适配器
        
        Args:
            migration_mode: 迁移模式 ('immediate', 'gradual', 'ab_test')
            ab_test_ratio: A/B测试中使用新系统的比例
        """
        self.migration_mode = migration_mode
        self.ab_test_ratio = ab_test_ratio
        
        # 初始化新的置信度评估器
        self.new_evaluator = ProductionConfidenceEvaluator()
        
        # 统计信息
        self.usage_stats = {
            'total_evaluations': 0,
            'new_system_usage': 0,
            'old_system_usage': 0,
            'start_time': datetime.now()
        }
        
        logger.info(f"🔧 置信度评估集成适配器初始化完成")
        logger.info(f"  迁移模式: {migration_mode}")
        logger.info(f"  A/B测试比例: {ab_test_ratio}")
    
    def calculate_confidence(self, predicted_numbers, prediction_context=None, force_new=False):
        """
        计算预测置信度（适配器主入口）
        
        Args:
            predicted_numbers: 预测数字列表
            prediction_context: 预测上下文（新系统需要）
            force_new: 强制使用新系统
        
        Returns:
            dict: 置信度评估结果
        """
        self.usage_stats['total_evaluations'] += 1
        
        # 决定使用哪个系统
        use_new_system = self._should_use_new_system(force_new)
        
        if use_new_system:
            return self._use_new_system(predicted_numbers, prediction_context)
        else:
            return self._use_old_system(predicted_numbers, prediction_context)
    
    def _should_use_new_system(self, force_new):
        """决定是否使用新系统"""
        if force_new:
            return True
        
        if self.migration_mode == 'immediate':
            return True
        elif self.migration_mode == 'gradual':
            # 渐进式迁移：随时间增加新系统使用率
            days_since_start = (datetime.now() - self.usage_stats['start_time']).days
            gradual_ratio = min(1.0, days_since_start * 0.1)  # 每天增加10%
            return np.random.random() < gradual_ratio
        elif self.migration_mode == 'ab_test':
            # A/B测试：固定比例
            return np.random.random() < self.ab_test_ratio
        else:
            return False
    
    def _use_new_system(self, predicted_numbers, prediction_context):
        """使用新的多维度置信度评估系统"""
        self.usage_stats['new_system_usage'] += 1
        
        try:
            # 确保预测上下文完整
            if prediction_context is None:
                prediction_context = self._create_default_context(predicted_numbers)
            
            # 使用新系统评估
            result = self.new_evaluator.evaluate_confidence(predicted_numbers, prediction_context)
            
            # 添加系统标识
            result['system_used'] = 'new_multidimensional'
            result['adapter_version'] = '1.0'
            
            return result
            
        except Exception as e:
            logger.error(f"新系统评估失败，回退到旧系统: {e}")
            return self._use_old_system(predicted_numbers, prediction_context)
    
    def _use_old_system(self, predicted_numbers, prediction_context):
        """使用原有的简单置信度计算系统"""
        self.usage_stats['old_system_usage'] += 1
        
        try:
            # 模拟原有系统的置信度计算
            base_confidence = self._calculate_legacy_confidence(predicted_numbers, prediction_context)
            
            result = {
                'final_confidence': base_confidence,
                'confidence_details': {
                    'base_prediction': base_confidence,
                    'historical_performance': base_confidence,
                    'pattern_stability': base_confidence,
                    'data_quality': base_confidence,
                    'prediction_consistency': base_confidence
                },
                'evaluation_timestamp': datetime.now().isoformat(),
                'system_used': 'legacy_simple',
                'adapter_version': '1.0'
            }
            
            return result
            
        except Exception as e:
            logger.error(f"旧系统评估失败: {e}")
            return self._get_emergency_confidence()
    
    def _calculate_legacy_confidence(self, predicted_numbers, prediction_context):
        """计算传统的简单置信度"""
        # 模拟原有系统的简单置信度计算
        if prediction_context and 'number_probs' in prediction_context:
            number_probs = prediction_context['number_probs']
            pred_probs = [number_probs.get(num, 0.02) for num in predicted_numbers]
            base_confidence = np.mean(pred_probs)
            
            # 添加一些随机变化以模拟原系统
            variation = np.random.normal(0, 0.005)
            return max(0.02, min(0.04, base_confidence + variation))
        else:
            # 如果没有概率信息，返回固定范围内的随机值
            return np.random.uniform(0.025, 0.035)
    
    def _create_default_context(self, predicted_numbers):
        """为新系统创建默认的预测上下文"""
        return {
            'previous_numbers': [],
            'number_probs': {i: 1/49 for i in range(1, 50)},
            'candidates': [predicted_numbers],
            'data_source': '未知',
            'period_idx': 0
        }
    
    def _get_emergency_confidence(self):
        """紧急情况下的备用置信度"""
        return {
            'final_confidence': 0.03,
            'confidence_details': {
                'base_prediction': 0.03,
                'historical_performance': 0.03,
                'pattern_stability': 0.03,
                'data_quality': 0.03,
                'prediction_consistency': 0.03
            },
            'evaluation_timestamp': datetime.now().isoformat(),
            'system_used': 'emergency_fallback',
            'adapter_version': '1.0'
        }
    
    def update_prediction_result(self, prediction_result):
        """更新预测结果（传递给新系统）"""
        try:
            self.new_evaluator.update_prediction_result(prediction_result)
        except Exception as e:
            logger.error(f"更新预测结果失败: {e}")
    
    def get_migration_stats(self):
        """获取迁移统计信息"""
        total = self.usage_stats['total_evaluations']
        if total == 0:
            return {
                'total_evaluations': 0,
                'new_system_ratio': 0,
                'old_system_ratio': 0,
                'migration_progress': 0
            }
        
        new_ratio = self.usage_stats['new_system_usage'] / total
        old_ratio = self.usage_stats['old_system_usage'] / total
        
        return {
            'total_evaluations': total,
            'new_system_usage': self.usage_stats['new_system_usage'],
            'old_system_usage': self.usage_stats['old_system_usage'],
            'new_system_ratio': new_ratio,
            'old_system_ratio': old_ratio,
            'migration_progress': new_ratio,
            'runtime_days': (datetime.now() - self.usage_stats['start_time']).days
        }
    
    def set_migration_mode(self, mode, ab_ratio=None):
        """设置迁移模式"""
        self.migration_mode = mode
        if ab_ratio is not None:
            self.ab_test_ratio = ab_ratio
        
        logger.info(f"迁移模式已更新为: {mode}")
        if ab_ratio is not None:
            logger.info(f"A/B测试比例已更新为: {ab_ratio}")
    
    def export_migration_report(self, filename):
        """导出迁移报告"""
        try:
            stats = self.get_migration_stats()
            system_status = self.new_evaluator.get_system_status()
            
            report = {
                'migration_stats': stats,
                'system_status': system_status,
                'migration_mode': self.migration_mode,
                'ab_test_ratio': self.ab_test_ratio,
                'report_timestamp': datetime.now().isoformat()
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"迁移报告已导出到: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"导出迁移报告失败: {e}")
            return False

# 全局适配器实例（单例模式）
_global_adapter = None

def get_confidence_adapter(migration_mode='gradual', ab_test_ratio=0.5):
    """获取全局置信度适配器实例"""
    global _global_adapter
    if _global_adapter is None:
        _global_adapter = ConfidenceIntegrationAdapter(migration_mode, ab_test_ratio)
    return _global_adapter

def calculate_prediction_confidence(predicted_numbers, prediction_context=None, force_new=False):
    """
    计算预测置信度的全局函数（可直接替换现有系统中的置信度计算）
    
    Args:
        predicted_numbers: 预测数字列表
        prediction_context: 预测上下文（可选）
        force_new: 强制使用新系统（可选）
    
    Returns:
        dict: 置信度评估结果
    """
    adapter = get_confidence_adapter()
    return adapter.calculate_confidence(predicted_numbers, prediction_context, force_new)

def update_prediction_result(prediction_result):
    """更新预测结果的全局函数"""
    adapter = get_confidence_adapter()
    adapter.update_prediction_result(prediction_result)

def get_system_migration_stats():
    """获取系统迁移统计的全局函数"""
    adapter = get_confidence_adapter()
    return adapter.get_migration_stats()

# 现有系统集成示例
class ExistingSystemIntegration:
    """现有系统集成示例"""
    
    def __init__(self):
        self.adapter = get_confidence_adapter('gradual')  # 使用渐进式迁移
    
    def predict_with_confidence(self, predicted_numbers, additional_context=None):
        """
        现有系统的预测函数示例
        只需要替换置信度计算部分
        """
        # 构建预测上下文
        prediction_context = {
            'previous_numbers': additional_context.get('previous_numbers', []) if additional_context else [],
            'number_probs': additional_context.get('number_probs', {}) if additional_context else {},
            'candidates': additional_context.get('candidates', [predicted_numbers]) if additional_context else [predicted_numbers],
            'data_source': additional_context.get('data_source', '历史数据') if additional_context else '历史数据',
            'period_idx': additional_context.get('period_idx', 0) if additional_context else 0
        }
        
        # 使用适配器计算置信度（这里替换了原有的简单计算）
        confidence_result = self.adapter.calculate_confidence(predicted_numbers, prediction_context)
        
        # 构建预测结果
        prediction_result = {
            'predicted_numbers': predicted_numbers,
            'confidence': confidence_result['final_confidence'],
            'confidence_details': confidence_result['confidence_details'],
            'system_used': confidence_result['system_used'],
            'timestamp': datetime.now().isoformat()
        }
        
        return prediction_result
    
    def validate_prediction(self, prediction_result, actual_numbers):
        """验证预测结果"""
        # 计算命中情况
        hit_numbers = list(set(prediction_result['predicted_numbers']) & set(actual_numbers))
        is_hit = len(hit_numbers) >= 1
        
        # 更新预测结果
        prediction_result.update({
            'actual_numbers': actual_numbers,
            'hit_numbers': hit_numbers,
            'is_hit': is_hit
        })
        
        # 更新到适配器
        self.adapter.update_prediction_result(prediction_result)
        
        return prediction_result

if __name__ == "__main__":
    # 演示集成使用
    print("🔧 置信度评估集成适配器演示")
    
    # 创建现有系统集成实例
    system = ExistingSystemIntegration()
    
    # 示例预测
    context = {
        'previous_numbers': [1, 15, 23, 30, 35, 42],
        'number_probs': {i: 0.02 for i in range(1, 50)},
        'candidates': [[5, 40], [3, 30]],
        'data_source': '真实数据',
        'period_idx': 100
    }
    
    # 进行预测
    result = system.predict_with_confidence([5, 40], context)
    
    print(f"\n🎯 预测结果:")
    print(f"  预测数字: {result['predicted_numbers']}")
    print(f"  置信度: {result['confidence']:.3f}")
    print(f"  使用系统: {result['system_used']}")
    
    # 模拟验证
    validated_result = system.validate_prediction(result, [5, 15, 25, 35, 40, 45])
    print(f"  命中情况: {'✅ 成功' if validated_result['is_hit'] else '❌ 失败'}")
    
    # 获取迁移统计
    stats = get_system_migration_stats()
    print(f"\n📊 迁移统计:")
    print(f"  总评估次数: {stats['total_evaluations']}")
    print(f"  新系统使用率: {stats['new_system_ratio']:.1%}")
    print(f"  迁移进度: {stats['migration_progress']:.1%}")
