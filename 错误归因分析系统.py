#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误归因分析系统
深度分析70.8%失败案例的模式，识别模型失效的特定条件和边界
理解马尔可夫预测系统的局限性和改进方向
"""

import pandas as pd
import numpy as np
import json
from collections import defaultdict, Counter
from datetime import datetime
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class ErrorAttributionAnalyzer:
    """
    错误归因分析器
    深度分析预测失败案例，识别模型边界
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.data = None
        self.train_data = None
        self.test_data = None
        self.transition_prob = {}
        
        # 分析结果存储
        self.predictions_with_analysis = []
        self.error_patterns = {}
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            # 使用最优配置：2023-2024训练，2025测试
            self.train_data = self.data[(self.data['年份'] >= 2023) & (self.data['年份'] < 2025)].copy()
            self.test_data = self.data[(self.data['年份'] == 2025) & (self.data['期号'] <= 182)].copy()
            
            print(f"✅ 数据加载成功")
            print(f"  训练数据: {len(self.train_data)}期 (2023-2024年)")
            print(f"  测试数据: {len(self.test_data)}期 (2025年1-182期)")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def build_markov_model(self):
        """构建马尔可夫转移矩阵"""
        print(f"\n🔬 构建马尔可夫模型")
        
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 转换为概率
        self.transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                self.transition_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        print(f"✅ 马尔可夫模型构建完成，状态数量: {len(self.transition_prob)}")
        return True
    
    def predict_with_detailed_analysis(self, previous_numbers):
        """带详细分析的预测"""
        if not self.transition_prob:
            return [1, 2], 0.0, {}
        
        # 计算各数字的转移概率
        number_probs = defaultdict(float)
        total_prob = 0.0
        transition_details = {}
        
        for prev_num in previous_numbers:
            if prev_num in self.transition_prob:
                transition_details[prev_num] = self.transition_prob[prev_num].copy()
                for next_num, prob in self.transition_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        # 归一化概率
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        # 选择概率最高的2个数字
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            predicted_2digits = [num for num, prob in sorted_numbers[:2]]
            confidence = np.mean([prob for num, prob in sorted_numbers[:2]])
            
            # 详细分析信息
            analysis_details = {
                'all_probabilities': dict(sorted_numbers),
                'top_10_candidates': sorted_numbers[:10],
                'transition_details': transition_details,
                'total_transition_prob': total_prob,
                'prediction_confidence': confidence,
                'previous_numbers_coverage': len([pn for pn in previous_numbers if pn in self.transition_prob])
            }
        else:
            predicted_2digits = [1, 2]
            confidence = 0.1
            analysis_details = {
                'error': 'insufficient_transition_data',
                'previous_numbers_coverage': 0
            }
        
        return predicted_2digits, confidence, analysis_details
    
    def run_detailed_prediction_analysis(self):
        """运行详细预测分析"""
        print(f"\n🔍 详细预测分析")
        print("=" * 60)
        
        success_count = 0
        failure_count = 0
        
        for idx, test_row in self.test_data.iterrows():
            period_num = test_row['期号']
            actual_numbers = set([test_row[f'数字{j}'] for j in range(1, 7)])
            
            # 获取前一期数字
            if idx == self.test_data.index[0]:
                prev_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                prev_numbers = set([self.test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            # 详细预测
            predicted_numbers, confidence, analysis_details = self.predict_with_detailed_analysis(prev_numbers)
            
            # 评估结果
            hit_count = len(set(predicted_numbers) & actual_numbers)
            is_success = hit_count >= 1
            
            if is_success:
                success_count += 1
            else:
                failure_count += 1
            
            # 存储详细分析
            prediction_analysis = {
                'period': period_num,
                'previous_numbers': list(prev_numbers),
                'predicted_numbers': predicted_numbers,
                'actual_numbers': list(actual_numbers),
                'hit_count': hit_count,
                'is_success': is_success,
                'confidence': confidence,
                'analysis_details': analysis_details
            }
            
            self.predictions_with_analysis.append(prediction_analysis)
        
        success_rate = success_count / len(self.test_data)
        failure_rate = failure_count / len(self.test_data)
        
        print(f"  总预测期数: {len(self.test_data)}")
        print(f"  成功预测: {success_count} ({success_rate:.3f})")
        print(f"  失败预测: {failure_count} ({failure_rate:.3f})")
        
        return success_rate, failure_rate
    
    def analyze_failure_patterns(self):
        """分析失败模式"""
        print(f"\n🔍 失败模式分析")
        print("=" * 60)
        
        failures = [p for p in self.predictions_with_analysis if not p['is_success']]
        successes = [p for p in self.predictions_with_analysis if p['is_success']]
        
        print(f"  失败案例数: {len(failures)}")
        print(f"  成功案例数: {len(successes)}")
        
        # 1. 置信度分析
        failure_confidences = [f['confidence'] for f in failures if f['confidence'] > 0]
        success_confidences = [s['confidence'] for s in successes if s['confidence'] > 0]
        
        print(f"\n1. 置信度分析:")
        if failure_confidences and success_confidences:
            print(f"  失败案例平均置信度: {np.mean(failure_confidences):.3f}")
            print(f"  成功案例平均置信度: {np.mean(success_confidences):.3f}")
            
            # t检验
            t_stat, p_value = stats.ttest_ind(failure_confidences, success_confidences)
            print(f"  置信度差异显著性: p={p_value:.4f} ({'显著' if p_value < 0.05 else '不显著'})")
        
        # 2. 前期数字覆盖率分析
        failure_coverage = [f['analysis_details'].get('previous_numbers_coverage', 0) for f in failures]
        success_coverage = [s['analysis_details'].get('previous_numbers_coverage', 0) for s in successes]
        
        print(f"\n2. 前期数字覆盖率分析:")
        print(f"  失败案例平均覆盖率: {np.mean(failure_coverage):.1f}/6")
        print(f"  成功案例平均覆盖率: {np.mean(success_coverage):.1f}/6")
        
        # 3. 预测数字特征分析
        print(f"\n3. 预测数字特征分析:")
        
        # 预测数字的分布
        failure_predicted = [num for f in failures for num in f['predicted_numbers']]
        success_predicted = [num for s in successes for num in s['predicted_numbers']]
        
        failure_pred_dist = Counter(failure_predicted)
        success_pred_dist = Counter(success_predicted)
        
        print(f"  失败案例最常预测数字: {failure_pred_dist.most_common(5)}")
        print(f"  成功案例最常预测数字: {success_pred_dist.most_common(5)}")
        
        # 4. 实际开出数字特征分析
        print(f"\n4. 实际开出数字特征分析:")
        
        failure_actual = [num for f in failures for num in f['actual_numbers']]
        success_actual = [num for s in successes for num in s['actual_numbers']]
        
        failure_actual_dist = Counter(failure_actual)
        success_actual_dist = Counter(success_actual)
        
        print(f"  失败案例实际开出频次: {failure_actual_dist.most_common(5)}")
        print(f"  成功案例实际开出频次: {success_actual_dist.most_common(5)}")
        
        # 5. 数字范围分析
        print(f"\n5. 数字范围分析:")
        
        def analyze_number_ranges(numbers, label):
            small_nums = [n for n in numbers if 1 <= n <= 16]
            medium_nums = [n for n in numbers if 17 <= n <= 33]
            large_nums = [n for n in numbers if 34 <= n <= 49]
            
            total = len(numbers)
            if total > 0:
                print(f"  {label}:")
                print(f"    小数字(1-16): {len(small_nums)} ({len(small_nums)/total:.1%})")
                print(f"    中数字(17-33): {len(medium_nums)} ({len(medium_nums)/total:.1%})")
                print(f"    大数字(34-49): {len(large_nums)} ({len(large_nums)/total:.1%})")
        
        analyze_number_ranges(failure_predicted, "失败案例预测数字分布")
        analyze_number_ranges(success_predicted, "成功案例预测数字分布")
        
        # 6. 奇偶性分析
        print(f"\n6. 奇偶性分析:")
        
        def analyze_odd_even(numbers, label):
            odd_nums = [n for n in numbers if n % 2 == 1]
            even_nums = [n for n in numbers if n % 2 == 0]
            
            total = len(numbers)
            if total > 0:
                print(f"  {label}:")
                print(f"    奇数: {len(odd_nums)} ({len(odd_nums)/total:.1%})")
                print(f"    偶数: {len(even_nums)} ({len(even_nums)/total:.1%})")
        
        analyze_odd_even(failure_predicted, "失败案例预测数字")
        analyze_odd_even(success_predicted, "成功案例预测数字")
        
        return {
            'failure_count': len(failures),
            'success_count': len(successes),
            'confidence_analysis': {
                'failure_avg_confidence': np.mean(failure_confidences) if failure_confidences else 0,
                'success_avg_confidence': np.mean(success_confidences) if success_confidences else 0
            },
            'coverage_analysis': {
                'failure_avg_coverage': np.mean(failure_coverage),
                'success_avg_coverage': np.mean(success_coverage)
            },
            'distribution_analysis': {
                'failure_predicted_top5': failure_pred_dist.most_common(5),
                'success_predicted_top5': success_pred_dist.most_common(5)
            }
        }
    
    def identify_model_boundaries(self):
        """识别模型边界和局限性"""
        print(f"\n🎯 模型边界识别")
        print("=" * 60)
        
        # 1. 转移概率覆盖率分析
        total_possible_numbers = 49
        covered_numbers = len(self.transition_prob)
        coverage_rate = covered_numbers / total_possible_numbers
        
        print(f"1. 转移概率覆盖率:")
        print(f"  覆盖数字: {covered_numbers}/{total_possible_numbers} ({coverage_rate:.1%})")
        
        uncovered_numbers = [i for i in range(1, 50) if i not in self.transition_prob]
        if uncovered_numbers:
            print(f"  未覆盖数字: {uncovered_numbers[:10]}{'...' if len(uncovered_numbers) > 10 else ''}")
        
        # 2. 转移概率质量分析
        transition_qualities = []
        for num, transitions in self.transition_prob.items():
            quality = len(transitions)  # 转移目标数量
            transition_qualities.append(quality)
        
        print(f"\n2. 转移概率质量:")
        print(f"  平均转移目标数: {np.mean(transition_qualities):.1f}")
        print(f"  最少转移目标数: {np.min(transition_qualities)}")
        print(f"  最多转移目标数: {np.max(transition_qualities)}")
        
        # 3. 预测失败的系统性原因
        failures = [p for p in self.predictions_with_analysis if not p['is_success']]
        
        # 分析失败原因
        failure_reasons = {
            'low_confidence': 0,
            'poor_coverage': 0,
            'insufficient_data': 0,
            'random_variance': 0
        }
        
        for failure in failures:
            confidence = failure['confidence']
            coverage = failure['analysis_details'].get('previous_numbers_coverage', 0)
            
            if confidence < 0.1:
                failure_reasons['low_confidence'] += 1
            elif coverage < 3:
                failure_reasons['poor_coverage'] += 1
            elif 'error' in failure['analysis_details']:
                failure_reasons['insufficient_data'] += 1
            else:
                failure_reasons['random_variance'] += 1
        
        print(f"\n3. 失败原因归类:")
        total_failures = len(failures)
        for reason, count in failure_reasons.items():
            percentage = count / total_failures if total_failures > 0 else 0
            print(f"  {reason}: {count} ({percentage:.1%})")
        
        return {
            'coverage_rate': coverage_rate,
            'transition_quality': {
                'avg_targets': np.mean(transition_qualities),
                'min_targets': np.min(transition_qualities),
                'max_targets': np.max(transition_qualities)
            },
            'failure_reasons': failure_reasons
        }

def main():
    """主函数"""
    print("🎯 错误归因分析系统")
    print("深度分析70.8%失败案例的模式")
    print("=" * 70)
    
    # 初始化分析器
    analyzer = ErrorAttributionAnalyzer()
    
    # 1. 加载数据
    if not analyzer.load_data():
        return
    
    # 2. 构建马尔可夫模型
    if not analyzer.build_markov_model():
        return
    
    # 3. 运行详细预测分析
    success_rate, failure_rate = analyzer.run_detailed_prediction_analysis()
    
    # 4. 分析失败模式
    failure_analysis = analyzer.analyze_failure_patterns()
    
    # 5. 识别模型边界
    boundary_analysis = analyzer.identify_model_boundaries()
    
    # 6. 保存分析结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"错误归因分析结果_{timestamp}.json"
    
    # 处理numpy类型
    def convert_numpy_types(obj):
        if isinstance(obj, dict):
            return {k: convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(v) for v in obj]
        elif hasattr(obj, 'item'):
            return obj.item()
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, (np.integer, int)):
            return int(obj)
        elif isinstance(obj, (np.floating, float)):
            return float(obj)
        else:
            return obj
    
    comprehensive_results = {
        'performance_summary': {
            'success_rate': float(success_rate),
            'failure_rate': float(failure_rate),
            'total_predictions': len(analyzer.test_data)
        },
        'failure_analysis': convert_numpy_types(failure_analysis),
        'boundary_analysis': convert_numpy_types(boundary_analysis),
        'detailed_predictions': convert_numpy_types(analyzer.predictions_with_analysis[:20]),  # 保存前20个详细案例
        'analysis_timestamp': datetime.now().isoformat()
    }
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(comprehensive_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 错误归因分析结果已保存: {results_file}")
    
    # 7. 关键洞察总结
    print(f"\n🎉 错误归因分析关键洞察")
    print("=" * 50)
    print(f"✅ 失败率: {failure_rate:.1%} ({failure_analysis['failure_count']}个案例)")
    print(f"✅ 主要失败原因: {max(boundary_analysis['failure_reasons'].items(), key=lambda x: x[1])}")
    print(f"✅ 模型覆盖率: {boundary_analysis['coverage_rate']:.1%}")
    print(f"✅ 平均转移目标: {boundary_analysis['transition_quality']['avg_targets']:.1f}个")
    
    print(f"\n💡 改进建议:")
    if boundary_analysis['coverage_rate'] < 0.8:
        print(f"  1. 扩大训练数据以提高数字覆盖率")
    if failure_analysis['confidence_analysis']['failure_avg_confidence'] < 0.2:
        print(f"  2. 改进置信度计算机制")
    if boundary_analysis['failure_reasons']['poor_coverage'] > boundary_analysis['failure_reasons']['random_variance']:
        print(f"  3. 优化前期数字的利用策略")
    print(f"  4. 考虑引入更多特征来减少随机性失败")

if __name__ == "__main__":
    main()
