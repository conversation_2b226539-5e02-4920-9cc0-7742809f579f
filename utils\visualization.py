#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Visualization utilities for lottery prediction system
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any
import warnings
warnings.filterwarnings('ignore')

# Set Chinese font for matplotlib
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class LotteryVisualizer:
    """
    Comprehensive visualization tools for lottery data analysis
    """
    
    def __init__(self, figsize: tuple = (12, 8)):
        self.figsize = figsize
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                      '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
    
    def plot_data_overview(self, data: pd.DataFrame, save_path: str = None):
        """
        Create overview visualization of lottery data
        """
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('彩票数据概览分析', fontsize=16, fontweight='bold')
        
        main_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        # 1. Number frequency distribution
        all_numbers = []
        for col in main_cols:
            all_numbers.extend(data[col].tolist())
        
        number_counts = pd.Series(all_numbers).value_counts().sort_index()
        axes[0, 0].bar(number_counts.index, number_counts.values, alpha=0.7, color='skyblue')
        axes[0, 0].set_title('号码频率分布')
        axes[0, 0].set_xlabel('号码')
        axes[0, 0].set_ylabel('出现次数')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. Sum distribution
        sum_values = data[main_cols].sum(axis=1)
        axes[0, 1].hist(sum_values, bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
        axes[0, 1].set_title('和值分布')
        axes[0, 1].set_xlabel('和值')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].axvline(sum_values.mean(), color='red', linestyle='--', 
                          label=f'平均值: {sum_values.mean():.1f}')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. Odd/Even distribution
        odd_counts = data[main_cols].apply(lambda x: sum(num % 2 for num in x), axis=1)
        odd_dist = odd_counts.value_counts().sort_index()
        axes[0, 2].bar(odd_dist.index, odd_dist.values, alpha=0.7, color='orange')
        axes[0, 2].set_title('奇数个数分布')
        axes[0, 2].set_xlabel('奇数个数')
        axes[0, 2].set_ylabel('期数')
        axes[0, 2].set_xticks(range(7))
        axes[0, 2].grid(True, alpha=0.3)
        
        # 4. Big/Small distribution
        big_counts = data[main_cols].apply(lambda x: sum(num > 25 for num in x), axis=1)
        big_dist = big_counts.value_counts().sort_index()
        axes[1, 0].bar(big_dist.index, big_dist.values, alpha=0.7, color='purple')
        axes[1, 0].set_title('大数个数分布 (>25)')
        axes[1, 0].set_xlabel('大数个数')
        axes[1, 0].set_ylabel('期数')
        axes[1, 0].set_xticks(range(7))
        axes[1, 0].grid(True, alpha=0.3)
        
        # 5. Sum trend over time
        axes[1, 1].plot(data['期号'], sum_values, marker='o', markersize=3, alpha=0.7)
        axes[1, 1].set_title('和值时间趋势')
        axes[1, 1].set_xlabel('期号')
        axes[1, 1].set_ylabel('和值')
        axes[1, 1].grid(True, alpha=0.3)
        
        # 6. Number position analysis
        position_means = [data[col].mean() for col in main_cols]
        axes[1, 2].bar(range(1, 7), position_means, alpha=0.7, color='teal')
        axes[1, 2].set_title('各位置平均值')
        axes[1, 2].set_xlabel('数字位置')
        axes[1, 2].set_ylabel('平均值')
        axes[1, 2].set_xticks(range(1, 7))
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"数据概览图已保存到: {save_path}")
        
        plt.show()
    
    def plot_model_performance(self, evaluation_results: Dict, save_path: str = None):
        """
        Visualize model performance comparison
        """
        # Extract performance metrics
        model_names = []
        accuracies = []
        rmse_values = []
        
        for target_col, models in evaluation_results.items():
            for model_name, metrics in models.items():
                model_names.append(f"{model_name}_{target_col}")
                accuracies.append(metrics['accuracy'])
                rmse_values.append(metrics['rmse'])
        
        if not model_names:
            print("没有可用的评估结果")
            return
        
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle('模型性能对比', fontsize=16, fontweight='bold')
        
        # 1. Accuracy comparison
        axes[0].barh(model_names, accuracies, alpha=0.7, color='lightblue')
        axes[0].set_title('模型准确率对比')
        axes[0].set_xlabel('准确率')
        axes[0].grid(True, alpha=0.3)
        
        # Add value labels
        for i, v in enumerate(accuracies):
            axes[0].text(v + 0.001, i, f'{v:.3f}', va='center')
        
        # 2. RMSE comparison
        axes[1].barh(model_names, rmse_values, alpha=0.7, color='lightcoral')
        axes[1].set_title('模型RMSE对比')
        axes[1].set_xlabel('RMSE')
        axes[1].grid(True, alpha=0.3)
        
        # Add value labels
        for i, v in enumerate(rmse_values):
            axes[1].text(v + 0.1, i, f'{v:.2f}', va='center')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"模型性能图已保存到: {save_path}")
        
        plt.show()
    
    def plot_prediction_analysis(self, actual_data: pd.DataFrame, 
                               predictions: Dict, save_path: str = None):
        """
        Analyze prediction vs actual results
        """
        target_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        # Get best model (first available)
        model_name = None
        for target_col in target_cols:
            if target_col in predictions and predictions[target_col]:
                model_name = list(predictions[target_col].keys())[0]
                break
        
        if not model_name:
            print("没有可用的预测结果")
            return
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'预测结果分析 - {model_name}', fontsize=16, fontweight='bold')
        
        for i, target_col in enumerate(target_cols):
            row, col = i // 3, i % 3
            
            if (target_col in predictions and 
                model_name in predictions[target_col]):
                
                actual = actual_data[target_col].values
                predicted = predictions[target_col][model_name]['predictions']
                
                # Scatter plot: actual vs predicted
                axes[row, col].scatter(actual, predicted, alpha=0.6, s=50)
                
                # Perfect prediction line
                min_val, max_val = min(actual.min(), predicted.min()), max(actual.max(), predicted.max())
                axes[row, col].plot([min_val, max_val], [min_val, max_val], 
                                  'r--', alpha=0.8, label='完美预测')
                
                axes[row, col].set_title(f'{target_col}')
                axes[row, col].set_xlabel('实际值')
                axes[row, col].set_ylabel('预测值')
                axes[row, col].legend()
                axes[row, col].grid(True, alpha=0.3)
                
                # Calculate and display correlation
                correlation = np.corrcoef(actual, predicted)[0, 1]
                axes[row, col].text(0.05, 0.95, f'相关性: {correlation:.3f}', 
                                  transform=axes[row, col].transAxes, 
                                  bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"预测分析图已保存到: {save_path}")
        
        plt.show()
    
    def plot_hit_rate_analysis(self, hit_rates: Dict, save_path: str = None):
        """
        Visualize lottery hit rate analysis
        """
        if not hit_rates:
            print("没有可用的命中率数据")
            return
        
        model_names = list(hit_rates.keys())
        n_models = len(model_names)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('彩票命中率分析', fontsize=16, fontweight='bold')
        
        # 1. Average hits comparison
        avg_hits = [hit_rates[model]['avg_hits'] for model in model_names]
        axes[0, 0].bar(model_names, avg_hits, alpha=0.7, color='skyblue')
        axes[0, 0].set_title('各模型平均命中数/期')
        axes[0, 0].set_ylabel('平均命中数')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # Add value labels
        for i, v in enumerate(avg_hits):
            axes[0, 0].text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
        
        # 2. Hit distribution for best model
        best_model = max(model_names, key=lambda x: hit_rates[x]['avg_hits'])
        hit_dist = hit_rates[best_model]['hit_distribution']
        
        axes[0, 1].bar(range(7), hit_dist, alpha=0.7, color='lightgreen')
        axes[0, 1].set_title(f'命中分布 - {best_model}')
        axes[0, 1].set_xlabel('每期命中数')
        axes[0, 1].set_ylabel('期数')
        axes[0, 1].set_xticks(range(7))
        
        # 3. Hits over time for best model
        hits_per_period = hit_rates[best_model]['hits_per_period']
        periods = range(161, 161 + len(hits_per_period))
        
        axes[1, 0].plot(periods, hits_per_period, marker='o', linewidth=2, markersize=6)
        axes[1, 0].set_title(f'各期命中数变化 - {best_model}')
        axes[1, 0].set_xlabel('期号')
        axes[1, 0].set_ylabel('命中数')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. Model comparison heatmap
        if n_models > 1:
            metrics_data = []
            for model in model_names:
                metrics_data.append([
                    hit_rates[model]['avg_hits'],
                    hit_rates[model]['max_hits']
                ])
            
            metrics_df = pd.DataFrame(metrics_data, 
                                    index=model_names, 
                                    columns=['平均命中', '最高命中'])
            
            sns.heatmap(metrics_df, annot=True, fmt='.3f', 
                       cmap='YlOrRd', ax=axes[1, 1])
            axes[1, 1].set_title('模型性能热力图')
        else:
            axes[1, 1].text(0.5, 0.5, '需要多个模型进行对比', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('模型对比')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"命中率分析图已保存到: {save_path}")
        
        plt.show()
    
    def plot_feature_importance(self, model, feature_names: List[str], 
                              model_name: str = "Model", save_path: str = None):
        """
        Plot feature importance for tree-based models
        """
        if not hasattr(model, 'feature_importances_'):
            print(f"模型 {model_name} 不支持特征重要性分析")
            return
        
        # Get feature importance
        importance = model.feature_importances_
        feature_importance = pd.DataFrame({
            'feature': feature_names,
            'importance': importance
        }).sort_values('importance', ascending=False)
        
        # Plot top 20 features
        top_features = feature_importance.head(20)
        
        plt.figure(figsize=(10, 8))
        plt.barh(range(len(top_features)), top_features['importance'], alpha=0.7)
        plt.yticks(range(len(top_features)), top_features['feature'])
        plt.xlabel('特征重要性')
        plt.title(f'特征重要性分析 - {model_name}')
        plt.gca().invert_yaxis()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"特征重要性图已保存到: {save_path}")
        
        plt.show()
        
        return feature_importance


if __name__ == "__main__":
    print("可视化工具模块加载完成")
