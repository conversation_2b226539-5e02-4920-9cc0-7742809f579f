#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
历史表现回溯系统
针对每个历史状态回测模型的历史平均成功率
建立状态-性能映射表，为状态评估器提供核心数据支撑
"""

import pandas as pd
import numpy as np
import json
from collections import defaultdict, Counter
from datetime import datetime
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class HistoricalPerformanceBacktester:
    """
    历史表现回溯器
    为每个可能的历史状态建立性能映射表
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.data = None
        self.train_data = None
        self.test_data = None
        self.transition_prob = {}
        
        # 状态定义方式
        self.state_definitions = {
            'number_sum': True,      # 基于数字和的状态
            'number_range': True,    # 基于数字范围的状态
            'odd_even': True,        # 基于奇偶分布的状态
            'gap_pattern': True,     # 基于间隔模式的状态
            'sequence_type': True    # 基于序列类型的状态
        }
        
        # 状态-性能映射表
        self.state_performance_maps = {}
        
        # 回测结果
        self.backtest_results = {}
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            # 使用更大的历史数据进行回测
            self.train_data = self.data[(self.data['年份'] >= 2021) & (self.data['年份'] < 2025)].copy()
            self.test_data = self.data[(self.data['年份'] == 2025) & (self.data['期号'] <= 182)].copy()
            
            print(f"✅ 数据加载成功")
            print(f"  历史数据: {len(self.train_data)}期 (2021-2024年)")
            print(f"  测试数据: {len(self.test_data)}期 (2025年)")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def build_markov_model(self, data_subset):
        """构建马尔可夫模型"""
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(data_subset) - 1):
            current_numbers = set([data_subset.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([data_subset.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                transition_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        return transition_prob
    
    def predict_with_markov(self, previous_numbers, transition_prob):
        """使用马尔可夫模型预测"""
        if not transition_prob:
            return [1, 2]
        
        number_probs = defaultdict(float)
        total_prob = 0.0
        
        for prev_num in previous_numbers:
            if prev_num in transition_prob:
                for next_num, prob in transition_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            return [num for num, prob in sorted_numbers[:2]]
        else:
            return [1, 2]
    
    def define_state(self, numbers):
        """定义状态特征"""
        state_features = {}
        
        if self.state_definitions['number_sum']:
            # 数字和状态
            total_sum = sum(numbers)
            state_features['sum_range'] = total_sum // 20  # 分为多个范围
        
        if self.state_definitions['number_range']:
            # 数字范围分布
            small_count = sum(1 for n in numbers if 1 <= n <= 16)
            medium_count = sum(1 for n in numbers if 17 <= n <= 33)
            large_count = sum(1 for n in numbers if 34 <= n <= 49)
            state_features['range_pattern'] = f"{small_count}-{medium_count}-{large_count}"
        
        if self.state_definitions['odd_even']:
            # 奇偶分布
            odd_count = sum(1 for n in numbers if n % 2 == 1)
            state_features['odd_even_pattern'] = f"{odd_count}-{6-odd_count}"
        
        if self.state_definitions['gap_pattern']:
            # 间隔模式
            sorted_nums = sorted(numbers)
            gaps = [sorted_nums[i+1] - sorted_nums[i] for i in range(len(sorted_nums)-1)]
            avg_gap = np.mean(gaps)
            state_features['gap_level'] = 'small' if avg_gap < 6 else 'medium' if avg_gap < 10 else 'large'
        
        if self.state_definitions['sequence_type']:
            # 序列类型
            sorted_nums = sorted(numbers)
            is_consecutive = all(sorted_nums[i+1] - sorted_nums[i] <= 2 for i in range(len(sorted_nums)-1))
            state_features['sequence_type'] = 'consecutive' if is_consecutive else 'scattered'
        
        return state_features
    
    def run_historical_backtest(self):
        """运行历史回测"""
        print(f"\n🔄 运行历史表现回测")
        print("=" * 60)
        
        # 初始化状态性能映射表
        for feature_name in self.state_definitions:
            if self.state_definitions[feature_name]:
                self.state_performance_maps[feature_name] = defaultdict(list)
        
        # 滚动窗口回测
        window_size = 731  # 2年训练窗口
        min_test_size = 90  # 最小测试窗口
        
        backtest_count = 0
        
        for start_idx in range(0, len(self.train_data) - window_size - min_test_size, 30):
            # 训练数据窗口
            train_window = self.train_data.iloc[start_idx:start_idx + window_size]
            
            # 测试数据窗口
            test_start = start_idx + window_size
            test_end = min(test_start + min_test_size, len(self.train_data))
            test_window = self.train_data.iloc[test_start:test_end]
            
            if len(test_window) < 30:
                break
            
            # 构建马尔可夫模型
            transition_prob = self.build_markov_model(train_window)
            
            if not transition_prob:
                continue
            
            # 在测试窗口上评估性能
            window_results = self.evaluate_window_performance(
                test_window, transition_prob, train_window
            )
            
            # 更新状态性能映射
            self.update_state_performance_maps(window_results)
            
            backtest_count += 1
            
            if backtest_count % 5 == 0:
                print(f"  回测进度: {backtest_count}个窗口完成")
        
        print(f"✅ 历史回测完成，共{backtest_count}个窗口")
        
        # 计算状态性能统计
        self.calculate_state_performance_statistics()
        
        return self.state_performance_maps
    
    def evaluate_window_performance(self, test_window, transition_prob, train_window):
        """评估单个窗口的性能"""
        window_results = []
        
        for idx, test_row in test_window.iterrows():
            actual_numbers = set([test_row[f'数字{j}'] for j in range(1, 7)])
            
            # 获取前一期数字
            if idx == test_window.index[0]:
                prev_numbers = set([train_window.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                prev_idx = test_window.index[test_window.index.get_loc(idx) - 1]
                prev_numbers = set([test_window.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            # 预测
            predicted_numbers = self.predict_with_markov(prev_numbers, transition_prob)
            
            # 评估
            hit_count = len(set(predicted_numbers) & actual_numbers)
            is_success = hit_count >= 1
            
            # 定义前一期状态
            state_features = self.define_state(list(prev_numbers))
            
            window_results.append({
                'state_features': state_features,
                'success': is_success,
                'hit_count': hit_count,
                'predicted': predicted_numbers,
                'actual': list(actual_numbers)
            })
        
        return window_results
    
    def update_state_performance_maps(self, window_results):
        """更新状态性能映射表"""
        for result in window_results:
            state_features = result['state_features']
            success = result['success']
            
            for feature_name, feature_value in state_features.items():
                if feature_name in self.state_performance_maps:
                    self.state_performance_maps[feature_name][feature_value].append(1 if success else 0)
    
    def calculate_state_performance_statistics(self):
        """计算状态性能统计"""
        print(f"\n📊 计算状态性能统计")
        print("-" * 40)
        
        self.backtest_results = {}
        
        for feature_name, state_map in self.state_performance_maps.items():
            feature_stats = {}
            
            for state_value, performances in state_map.items():
                if len(performances) >= 10:  # 最少10个样本
                    success_rate = np.mean(performances)
                    sample_count = len(performances)
                    std_error = np.std(performances) / np.sqrt(sample_count)
                    
                    feature_stats[state_value] = {
                        'success_rate': success_rate,
                        'sample_count': sample_count,
                        'std_error': std_error,
                        'confidence_interval': [
                            success_rate - 1.96 * std_error,
                            success_rate + 1.96 * std_error
                        ]
                    }
            
            self.backtest_results[feature_name] = feature_stats
            
            print(f"  {feature_name}: {len(feature_stats)}个有效状态")
        
        return self.backtest_results
    
    def get_state_performance(self, numbers, feature_name='sum_range'):
        """获取指定状态的历史性能"""
        state_features = self.define_state(numbers)
        
        if feature_name in state_features and feature_name in self.backtest_results:
            state_value = state_features[feature_name]
            
            if state_value in self.backtest_results[feature_name]:
                return self.backtest_results[feature_name][state_value]['success_rate']
        
        return 0.25  # 默认基准性能
    
    def analyze_best_worst_states(self):
        """分析最佳和最差状态"""
        print(f"\n🎯 分析最佳和最差状态")
        print("=" * 60)
        
        for feature_name, feature_stats in self.backtest_results.items():
            if not feature_stats:
                continue
            
            # 按成功率排序
            sorted_states = sorted(
                feature_stats.items(), 
                key=lambda x: x[1]['success_rate'], 
                reverse=True
            )
            
            print(f"\n{feature_name} 特征分析:")
            
            # 最佳状态
            if len(sorted_states) >= 1:
                best_state, best_stats = sorted_states[0]
                print(f"  最佳状态: {best_state}")
                print(f"    成功率: {best_stats['success_rate']:.3f} ({best_stats['success_rate']*100:.1f}%)")
                print(f"    样本数: {best_stats['sample_count']}")
            
            # 最差状态
            if len(sorted_states) >= 2:
                worst_state, worst_stats = sorted_states[-1]
                print(f"  最差状态: {worst_state}")
                print(f"    成功率: {worst_stats['success_rate']:.3f} ({worst_stats['success_rate']*100:.1f}%)")
                print(f"    样本数: {worst_stats['sample_count']}")
            
            # 性能差异
            if len(sorted_states) >= 2:
                performance_gap = sorted_states[0][1]['success_rate'] - sorted_states[-1][1]['success_rate']
                print(f"  性能差异: {performance_gap:.3f} ({performance_gap*100:.1f}个百分点)")

def main():
    """主函数"""
    print("🎯 历史表现回溯系统")
    print("建立状态-性能映射表")
    print("=" * 70)
    
    # 初始化回测器
    backtester = HistoricalPerformanceBacktester()
    
    # 1. 加载数据
    if not backtester.load_data():
        return
    
    # 2. 运行历史回测
    state_maps = backtester.run_historical_backtest()
    
    # 3. 分析最佳最差状态
    backtester.analyze_best_worst_states()
    
    # 4. 保存结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"历史表现回溯结果_{timestamp}.json"
    
    # 处理numpy类型
    def convert_numpy_types(obj):
        if isinstance(obj, dict):
            return {k: convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(v) for v in obj]
        elif hasattr(obj, 'item'):
            return obj.item()
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, (np.integer, int)):
            return int(obj)
        elif isinstance(obj, (np.floating, float)):
            return float(obj)
        else:
            return obj
    
    results = {
        'state_definitions': backtester.state_definitions,
        'backtest_results': convert_numpy_types(backtester.backtest_results),
        'analysis_timestamp': datetime.now().isoformat()
    }
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 历史表现回溯结果已保存: {results_file}")
    
    # 5. 总结
    total_states = sum(len(stats) for stats in backtester.backtest_results.values())
    
    print(f"\n🎉 历史表现回溯系统完成")
    print("=" * 50)
    print(f"✅ 状态特征类型: {len(backtester.backtest_results)}")
    print(f"✅ 有效状态总数: {total_states}")
    print(f"✅ 状态性能映射表构建完成")
    
    # 展示一个示例查询
    if backtester.backtest_results:
        example_numbers = [1, 15, 23, 31, 42, 49]
        example_performance = backtester.get_state_performance(example_numbers)
        print(f"\n💡 示例查询:")
        print(f"  数字组合: {example_numbers}")
        print(f"  历史性能: {example_performance:.3f} ({example_performance*100:.1f}%)")

if __name__ == "__main__":
    main()
