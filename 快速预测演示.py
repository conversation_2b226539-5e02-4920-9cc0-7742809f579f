#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速预测演示 - 展示正确的使用流程
基于2025年185期数据预测186期
"""

import sys
import os
sys.path.append('.')

from 手动输入预测系统 import ManualPredictionSystem

def quick_demo():
    """快速演示预测流程"""
    print("🎯 快速预测演示")
    print("=" * 50)
    
    # 初始化系统
    system = ManualPredictionSystem()
    
    print("🔧 正在初始化系统...")
    if not system.load_data_and_build_model():
        print("❌ 系统初始化失败")
        return
    
    print("✅ 系统初始化完成")
    
    # 演示数据 - 2025年186期
    demo_data = {
        'year': 2025,
        'period': 186,
        'numbers': [13, 40, 22, 20, 17, 9]  # 您提供的数据
    }
    
    print(f"\n📝 演示输入数据:")
    print(f"年份: {demo_data['year']}")
    print(f"期号: {demo_data['period']}")
    print(f"开奖数字: {demo_data['numbers']}")
    
    # 执行预测
    print(f"\n🎯 基于{demo_data['year']}年{demo_data['period']}期数据进行预测")
    print("=" * 50)
    
    try:
        # 调用预测方法
        predicted_numbers, confidence = system.predict_next_period(demo_data['numbers'])
        
        print(f"当期开奖: {demo_data['numbers']}")
        print(f"预测下期: {predicted_numbers}")
        print(f"预测置信度: {confidence:.3f}")
        print(f"预测期号: {demo_data['year']}年{demo_data['period']+1}期")
        
        # 保存预测记录
        prediction_record = {
            'prediction_date': '2025-07-13T18:30:00',
            'current_year': demo_data['year'],
            'current_period': demo_data['period'],
            'predicted_period': f"{demo_data['year']}年{demo_data['period']+1}期",
            'current_numbers': demo_data['numbers'],
            'predicted_numbers': predicted_numbers,
            'confidence': confidence,
            'method': '34.3%增强马尔可夫',
            'actual_numbers': None,
            'hit_count': None,
            'is_hit': None,
            'notes': '演示预测'
        }
        
        # 添加到历史记录
        system.prediction_history.append(prediction_record)
        system.save_prediction_history()
        
        print(f"\n✅ 预测记录已保存")
        
        # 显示使用说明
        print(f"\n💡 使用说明:")
        print(f"1. 当前预测: {demo_data['year']}年{demo_data['period']+1}期 → {predicted_numbers}")
        print(f"2. 等待开奖: {demo_data['year']}年{demo_data['period']+1}期开奖后")
        print(f"3. 验证结果: 运行系统选择'2. 输入实际开奖结果验证预测'")
        print(f"4. 输入实际: 输入{demo_data['period']+1}期的实际开奖数字")
        print(f"5. 查看命中: 系统自动计算命中情况")
        
        return prediction_record
        
    except Exception as e:
        print(f"❌ 预测失败: {e}")
        return None

def show_input_format_guide():
    """显示输入格式指南"""
    print(f"\n📋 输入格式指南")
    print("=" * 40)
    
    print(f"✅ 正确格式示例:")
    print(f"  空格分隔: 13 40 22 20 17 9")
    print(f"  逗号分隔: 13,40,22,20,17,9")
    print(f"  混合格式: 13, 40, 22, 20, 17, 9")
    
    print(f"\n❌ 错误格式:")
    print(f"  包含前导零: 13,40,22,20,17,09 (应该是9)")
    print(f"  特殊字符: 13-40-22-20-17-9")
    print(f"  数字不足: 13 40 22 20 17 (缺少1个)")
    print(f"  数字过多: 13 40 22 20 17 9 5 (多了1个)")
    
    print(f"\n🔧 输入技巧:")
    print(f"  1. 数字范围: 1-49")
    print(f"  2. 数字个数: 必须6个")
    print(f"  3. 不能重复: 6个数字必须不同")
    print(f"  4. 前导零: 09应该输入为9")

def show_complete_workflow():
    """显示完整工作流程"""
    print(f"\n🔄 完整使用流程")
    print("=" * 40)
    
    print(f"步骤1: 启动系统")
    print(f"  命令: python 手动输入预测系统.py")
    print(f"  选择: 1. 输入当期数据并预测下期")
    
    print(f"\n步骤2: 输入当期数据")
    print(f"  年份: 2025")
    print(f"  期号: 186")
    print(f"  数字: 13 40 22 20 17 9")
    
    print(f"\n步骤3: 获得预测结果")
    print(f"  系统显示: 预测下期数字和置信度")
    print(f"  自动保存: 预测记录保存到文件")
    
    print(f"\n步骤4: 等待下期开奖")
    print(f"  等待: 2025年187期开奖")
    print(f"  记录: 记住预测的数字")
    
    print(f"\n步骤5: 验证预测结果")
    print(f"  启动: 再次运行系统")
    print(f"  选择: 2. 输入实际开奖结果验证预测")
    print(f"  输入: 187期的实际开奖数字")
    
    print(f"\n步骤6: 查看统计分析")
    print(f"  选择: 3. 查看预测统计")
    print(f"  查看: 命中率、预测历史等")

def main():
    """主函数"""
    print("🎯 快速预测演示 - 2025年186期")
    print("基于您提供的数据: 13,40,22,20,17,09")
    print("=" * 60)
    
    # 1. 显示输入格式指南
    show_input_format_guide()
    
    # 2. 执行快速演示
    prediction_result = quick_demo()
    
    # 3. 显示完整工作流程
    show_complete_workflow()
    
    if prediction_result:
        print(f"\n🎉 演示完成")
        print("=" * 40)
        print(f"✅ 预测已生成: {prediction_result['predicted_numbers']}")
        print(f"✅ 记录已保存: prediction_history.json")
        print(f"✅ 系统就绪: 可以继续使用")
        
        print(f"\n🚀 下一步操作:")
        print(f"1. 等待2025年187期开奖")
        print(f"2. 运行: python 手动输入预测系统.py")
        print(f"3. 选择: 2. 输入实际开奖结果验证预测")
        print(f"4. 输入187期实际开奖数字验证命中情况")
    else:
        print(f"\n❌ 演示失败，请检查系统配置")

if __name__ == "__main__":
    main()
