#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
过拟合和数据泄露深度验证分析
对预测系统进行严格的过拟合检测和数据泄露验证
"""

import pandas as pd
import numpy as np
from collections import Counter, defaultdict
import json
from datetime import datetime, timedelta
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class OverfittingDataLeakageAnalyzer:
    """过拟合和数据泄露分析器"""
    
    def __init__(self, data_file='prediction_data.csv'):
        """初始化分析器"""
        self.data_file = data_file
        self.prediction_data = None
        self.analysis_results = {}
        
    def load_data(self):
        """加载预测数据"""
        try:
            self.prediction_data = pd.read_csv(self.data_file, encoding='utf-8')
            print(f"✅ 成功加载 {len(self.prediction_data)} 条预测记录")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def detect_overfitting(self):
        """检测过拟合问题"""
        print("\n🔍 检测过拟合问题...")
        
        valid_data = self.prediction_data.dropna(subset=['是否命中'])
        
        # 1. 时间序列性能分析
        temporal_performance = self.analyze_temporal_performance(valid_data)
        
        # 2. 训练测试性能差异分析
        train_test_gap = self.analyze_train_test_performance_gap(valid_data)
        
        # 3. 模型复杂度分析
        model_complexity = self.analyze_model_complexity()
        
        # 4. 泛化能力评估
        generalization_ability = self.evaluate_generalization_ability(valid_data)
        
        # 5. 预测稳定性分析
        prediction_stability = self.analyze_prediction_stability(valid_data)
        
        overfitting_analysis = {
            'temporal_performance': temporal_performance,
            'train_test_gap': train_test_gap,
            'model_complexity': model_complexity,
            'generalization_ability': generalization_ability,
            'prediction_stability': prediction_stability,
            'overfitting_risk_score': 0  # 将在后面计算
        }
        
        # 计算过拟合风险评分
        risk_score = self.calculate_overfitting_risk_score(overfitting_analysis)
        overfitting_analysis['overfitting_risk_score'] = risk_score
        
        print(f"   ✅ 过拟合风险评分: {risk_score:.1f}/100")
        
        return overfitting_analysis
    
    def analyze_temporal_performance(self, data):
        """分析时间序列性能"""
        # 按时间顺序分析性能变化
        data_sorted = data.sort_values('当期期号')
        
        # 分成多个时间窗口
        window_size = 20
        window_performances = []
        
        for i in range(window_size, len(data_sorted), 5):  # 每5期滑动一次
            window_data = data_sorted.iloc[i-window_size:i]
            hit_rate = (window_data['是否命中'] == '是').mean()
            window_performances.append({
                'start_period': window_data['当期期号'].iloc[0],
                'end_period': window_data['当期期号'].iloc[-1],
                'hit_rate': hit_rate,
                'sample_size': len(window_data)
            })
        
        # 计算性能变化趋势
        hit_rates = [wp['hit_rate'] for wp in window_performances]
        performance_trend = np.polyfit(range(len(hit_rates)), hit_rates, 1)[0]  # 线性趋势
        performance_volatility = np.std(hit_rates)
        
        return {
            'window_performances': window_performances,
            'performance_trend': performance_trend,
            'performance_volatility': performance_volatility,
            'avg_performance': np.mean(hit_rates),
            'performance_decline': performance_trend < -0.001  # 性能是否显著下降
        }
    
    def analyze_train_test_performance_gap(self, data):
        """分析训练测试性能差异"""
        # 按时间分割数据（前70%训练，后30%测试）
        split_point = int(len(data) * 0.7)
        train_data = data.iloc[:split_point]
        test_data = data.iloc[split_point:]
        
        # 计算训练集和测试集性能
        train_hit_rate = (train_data['是否命中'] == '是').mean() if len(train_data) > 0 else 0
        test_hit_rate = (test_data['是否命中'] == '是').mean() if len(test_data) > 0 else 0
        
        # 计算性能差异
        performance_gap = train_hit_rate - test_hit_rate
        relative_gap = performance_gap / train_hit_rate if train_hit_rate > 0 else 0
        
        # 统计显著性检验
        if len(train_data) > 0 and len(test_data) > 0:
            train_hits = (train_data['是否命中'] == '是').sum()
            test_hits = (test_data['是否命中'] == '是').sum()
            
            # 使用卡方检验
            observed = [[train_hits, len(train_data) - train_hits],
                       [test_hits, len(test_data) - test_hits]]
            chi2, p_value = stats.chi2_contingency(observed)[:2]
        else:
            p_value = 1.0
        
        return {
            'train_hit_rate': train_hit_rate,
            'test_hit_rate': test_hit_rate,
            'performance_gap': performance_gap,
            'relative_gap': relative_gap,
            'statistical_significance': p_value < 0.05,
            'p_value': p_value,
            'train_sample_size': len(train_data),
            'test_sample_size': len(test_data)
        }
    
    def analyze_model_complexity(self):
        """分析模型复杂度"""
        # 分析当前使用的方法
        methods_used = self.prediction_data['预测方法'].value_counts()
        
        # 分析预测数字的多样性
        pred_num1_unique = len(self.prediction_data['预测数字1'].dropna().unique())
        pred_num2_unique = len(self.prediction_data['预测数字2'].dropna().unique())
        
        # 分析置信度分布
        confidence_data = self.prediction_data['预测置信度'].dropna()
        confidence_unique = len(confidence_data.unique())
        confidence_range = confidence_data.max() - confidence_data.min()
        
        # 复杂度评分
        complexity_indicators = {
            'method_diversity': len(methods_used),
            'prediction_space_coverage': (pred_num1_unique + pred_num2_unique) / (49 * 2),
            'confidence_granularity': confidence_unique,
            'confidence_range': confidence_range,
            'parameter_count_estimate': confidence_unique * len(methods_used)
        }
        
        # 计算复杂度评分（0-100，越高越复杂）
        complexity_score = min(100, 
            complexity_indicators['method_diversity'] * 10 +
            complexity_indicators['prediction_space_coverage'] * 50 +
            complexity_indicators['confidence_granularity'] * 0.5
        )
        
        return {
            'complexity_indicators': complexity_indicators,
            'complexity_score': complexity_score,
            'methods_distribution': dict(methods_used),
            'is_overly_complex': complexity_score > 70
        }
    
    def evaluate_generalization_ability(self, data):
        """评估泛化能力"""
        # 使用时间序列交叉验证
        cv_results = []
        window_size = 30
        test_size = 10
        
        for i in range(window_size, len(data) - test_size, test_size):
            train_window = data.iloc[i-window_size:i]
            test_window = data.iloc[i:i+test_size]
            
            train_hit_rate = (train_window['是否命中'] == '是').mean()
            test_hit_rate = (test_window['是否命中'] == '是').mean()
            
            cv_results.append({
                'train_performance': train_hit_rate,
                'test_performance': test_hit_rate,
                'performance_gap': train_hit_rate - test_hit_rate
            })
        
        if cv_results:
            avg_train_performance = np.mean([r['train_performance'] for r in cv_results])
            avg_test_performance = np.mean([r['test_performance'] for r in cv_results])
            avg_performance_gap = np.mean([r['performance_gap'] for r in cv_results])
            performance_gap_std = np.std([r['performance_gap'] for r in cv_results])
        else:
            avg_train_performance = 0
            avg_test_performance = 0
            avg_performance_gap = 0
            performance_gap_std = 0
        
        # 泛化能力评分（0-100，越高越好）
        generalization_score = max(0, 100 - abs(avg_performance_gap) * 1000 - performance_gap_std * 500)
        
        return {
            'cv_results': cv_results,
            'avg_train_performance': avg_train_performance,
            'avg_test_performance': avg_test_performance,
            'avg_performance_gap': avg_performance_gap,
            'performance_gap_std': performance_gap_std,
            'generalization_score': generalization_score,
            'cv_folds': len(cv_results)
        }
    
    def analyze_prediction_stability(self, data):
        """分析预测稳定性"""
        # 分析预测数字的变化模式
        pred_changes = []
        for i in range(1, len(data)):
            prev_pred = (data.iloc[i-1]['预测数字1'], data.iloc[i-1]['预测数字2'])
            curr_pred = (data.iloc[i]['预测数字1'], data.iloc[i]['预测数字2'])
            
            if pd.notna(prev_pred[0]) and pd.notna(curr_pred[0]):
                change_count = sum(1 for p, c in zip(prev_pred, curr_pred) if p != c)
                pred_changes.append(change_count)
        
        # 分析置信度稳定性
        confidence_data = data['预测置信度'].dropna()
        confidence_changes = []
        for i in range(1, len(confidence_data)):
            change = abs(confidence_data.iloc[i] - confidence_data.iloc[i-1])
            confidence_changes.append(change)
        
        # 稳定性评分
        avg_pred_change = np.mean(pred_changes) if pred_changes else 0
        avg_confidence_change = np.mean(confidence_changes) if confidence_changes else 0
        
        stability_score = max(0, 100 - avg_pred_change * 50 - avg_confidence_change * 1000)
        
        return {
            'avg_prediction_change': avg_pred_change,
            'avg_confidence_change': avg_confidence_change,
            'prediction_change_std': np.std(pred_changes) if pred_changes else 0,
            'confidence_change_std': np.std(confidence_changes) if confidence_changes else 0,
            'stability_score': stability_score,
            'total_predictions_analyzed': len(pred_changes)
        }
    
    def calculate_overfitting_risk_score(self, analysis):
        """计算过拟合风险评分"""
        weights = {
            'temporal_decline': 0.25,
            'train_test_gap': 0.30,
            'complexity': 0.20,
            'generalization': 0.15,
            'stability': 0.10
        }
        
        # 各项风险评分（0-100，越高风险越大）
        temporal_risk = 100 if analysis['temporal_performance']['performance_decline'] else 0
        train_test_risk = min(100, abs(analysis['train_test_gap']['relative_gap']) * 200)
        complexity_risk = analysis['model_complexity']['complexity_score']
        generalization_risk = 100 - analysis['generalization_ability']['generalization_score']
        stability_risk = 100 - analysis['prediction_stability']['stability_score']
        
        # 加权平均
        total_risk = (
            temporal_risk * weights['temporal_decline'] +
            train_test_risk * weights['train_test_gap'] +
            complexity_risk * weights['complexity'] +
            generalization_risk * weights['generalization'] +
            stability_risk * weights['stability']
        )
        
        return total_risk
    
    def detect_data_leakage(self):
        """检测数据泄露问题"""
        print("\n🔍 检测数据泄露问题...")
        
        # 1. 时间泄露检测
        temporal_leakage = self.detect_temporal_leakage()
        
        # 2. 特征泄露检测
        feature_leakage = self.detect_feature_leakage()
        
        # 3. 训练测试集重叠检测
        train_test_overlap = self.detect_train_test_overlap()
        
        # 4. 预测流程时间顺序检测
        temporal_order_check = self.check_temporal_order()
        
        # 5. 目标变量泄露检测
        target_leakage = self.detect_target_variable_leakage()
        
        leakage_analysis = {
            'temporal_leakage': temporal_leakage,
            'feature_leakage': feature_leakage,
            'train_test_overlap': train_test_overlap,
            'temporal_order_check': temporal_order_check,
            'target_leakage': target_leakage,
            'overall_leakage_risk': 0  # 将在后面计算
        }
        
        # 计算总体泄露风险
        leakage_risk = self.calculate_leakage_risk_score(leakage_analysis)
        leakage_analysis['overall_leakage_risk'] = leakage_risk
        
        print(f"   ✅ 数据泄露风险评分: {leakage_risk:.1f}/100")
        
        return leakage_analysis
    
    def detect_temporal_leakage(self):
        """检测时间泄露"""
        temporal_issues = []
        
        # 检查预测时间是否在开奖时间之后
        for _, row in self.prediction_data.iterrows():
            pred_date = pd.to_datetime(f"{row['预测日期']} {row['预测时间']}", errors='coerce')
            
            # 检查是否使用了未来信息
            if pd.notna(pred_date):
                # 假设开奖时间是当天21:00
                expected_draw_time = pred_date.replace(hour=21, minute=0, second=0)
                
                if pred_date > expected_draw_time:
                    temporal_issues.append({
                        'period': row['当期期号'],
                        'prediction_time': pred_date,
                        'expected_draw_time': expected_draw_time,
                        'issue': 'prediction_after_draw'
                    })
        
        # 检查批量预测的合理性
        batch_predictions = self.prediction_data.groupby(['预测日期', '预测时间']).size()
        large_batches = batch_predictions[batch_predictions > 50]
        
        return {
            'temporal_violations': temporal_issues,
            'large_batch_predictions': dict(large_batches),
            'temporal_leakage_detected': len(temporal_issues) > 0,
            'risk_level': 'high' if len(temporal_issues) > 10 else 'low'
        }
    
    def detect_feature_leakage(self):
        """检测特征泄露"""
        feature_issues = []
        
        # 检查是否在预测中使用了实际开奖数字
        for _, row in self.prediction_data.iterrows():
            pred_nums = [row['预测数字1'], row['预测数字2']]
            actual_nums = [row[f'实际数字{i}'] for i in range(1, 7)]
            
            # 如果预测数字完全匹配实际数字（除了合理的巧合）
            if not pd.isna(actual_nums[0]):  # 有实际开奖数据
                matches = sum(1 for pred in pred_nums if pred in actual_nums)
                if matches == 2:  # 两个预测数字都命中
                    feature_issues.append({
                        'period': row['当期期号'],
                        'prediction': pred_nums,
                        'actual': actual_nums,
                        'issue': 'perfect_match_suspicious'
                    })
        
        # 检查预测方法的一致性
        method_changes = self.prediction_data['预测方法'].value_counts()
        
        return {
            'suspicious_perfect_matches': feature_issues,
            'method_consistency': dict(method_changes),
            'feature_leakage_detected': len(feature_issues) > 5,  # 超过5次完美匹配可疑
            'risk_level': 'high' if len(feature_issues) > 5 else 'low'
        }
    
    def detect_train_test_overlap(self):
        """检测训练测试集重叠"""
        # 检查是否有重复的预测记录
        duplicates = self.prediction_data.duplicated(subset=['当期期号', '预测数字1', '预测数字2'])
        duplicate_count = duplicates.sum()
        
        # 检查相似的预测模式
        prediction_patterns = defaultdict(list)
        for _, row in self.prediction_data.iterrows():
            pattern = (row['预测数字1'], row['预测数字2'])
            prediction_patterns[pattern].append(row['当期期号'])
        
        repeated_patterns = {k: v for k, v in prediction_patterns.items() if len(v) > 1}
        
        return {
            'duplicate_records': duplicate_count,
            'repeated_prediction_patterns': dict(repeated_patterns),
            'overlap_detected': duplicate_count > 0,
            'pattern_repetition_rate': len(repeated_patterns) / len(prediction_patterns)
        }
    
    def check_temporal_order(self):
        """检查时间顺序"""
        temporal_violations = []
        
        # 检查期号是否按时间顺序
        sorted_data = self.prediction_data.sort_values('当期期号')
        
        for i in range(1, len(sorted_data)):
            current_period = sorted_data.iloc[i]['当期期号']
            prev_period = sorted_data.iloc[i-1]['当期期号']
            
            if pd.notna(current_period) and pd.notna(prev_period):
                if current_period <= prev_period:
                    temporal_violations.append({
                        'index': i,
                        'current_period': current_period,
                        'previous_period': prev_period,
                        'issue': 'non_sequential_periods'
                    })
        
        return {
            'temporal_violations': temporal_violations,
            'temporal_order_correct': len(temporal_violations) == 0,
            'violation_count': len(temporal_violations)
        }
    
    def detect_target_variable_leakage(self):
        """检测目标变量泄露"""
        target_issues = []
        
        # 检查预测准确率是否异常高
        valid_data = self.prediction_data.dropna(subset=['是否命中'])
        hit_rate = (valid_data['是否命中'] == '是').mean()
        
        # 理论上，双色球预测2个数字的命中率不应该超过30%
        if hit_rate > 0.5:  # 50%以上的命中率极其可疑
            target_issues.append({
                'issue': 'abnormally_high_accuracy',
                'hit_rate': hit_rate,
                'expected_max': 0.3
            })
        
        # 检查置信度与命中率的关系
        confidence_data = valid_data.dropna(subset=['预测置信度'])
        if len(confidence_data) > 0:
            avg_confidence = confidence_data['预测置信度'].mean()
            confidence_hit_correlation = np.corrcoef(
                confidence_data['预测置信度'],
                (confidence_data['是否命中'] == '是').astype(int)
            )[0, 1]
            
            # 如果置信度与命中率完全相关，可能存在泄露
            if abs(confidence_hit_correlation) > 0.8:
                target_issues.append({
                    'issue': 'perfect_confidence_correlation',
                    'correlation': confidence_hit_correlation
                })
        
        return {
            'target_leakage_issues': target_issues,
            'overall_hit_rate': hit_rate,
            'target_leakage_detected': len(target_issues) > 0,
            'risk_level': 'high' if len(target_issues) > 0 else 'low'
        }
    
    def calculate_leakage_risk_score(self, analysis):
        """计算数据泄露风险评分"""
        weights = {
            'temporal': 0.30,
            'feature': 0.25,
            'overlap': 0.20,
            'temporal_order': 0.15,
            'target': 0.10
        }
        
        # 各项风险评分（0-100）
        temporal_risk = 100 if analysis['temporal_leakage']['temporal_leakage_detected'] else 0
        feature_risk = 100 if analysis['feature_leakage']['feature_leakage_detected'] else 0
        overlap_risk = 100 if analysis['train_test_overlap']['overlap_detected'] else 0
        temporal_order_risk = 0 if analysis['temporal_order_check']['temporal_order_correct'] else 100
        target_risk = 100 if analysis['target_leakage']['target_leakage_detected'] else 0
        
        total_risk = (
            temporal_risk * weights['temporal'] +
            feature_risk * weights['feature'] +
            overlap_risk * weights['overlap'] +
            temporal_order_risk * weights['temporal_order'] +
            target_risk * weights['target']
        )
        
        return total_risk
    
    def run_comprehensive_analysis(self):
        """运行综合分析"""
        print("🚀 开始过拟合和数据泄露深度验证分析...")
        
        if not self.load_data():
            return False
        
        # 过拟合检测
        overfitting_analysis = self.detect_overfitting()
        
        # 数据泄露检测
        leakage_analysis = self.detect_data_leakage()
        
        # 综合评估
        comprehensive_assessment = self.generate_comprehensive_assessment(
            overfitting_analysis, leakage_analysis
        )
        
        # 保存结果
        self.analysis_results = {
            'overfitting_analysis': overfitting_analysis,
            'data_leakage_analysis': leakage_analysis,
            'comprehensive_assessment': comprehensive_assessment
        }
        
        # 保存分析结果
        self.save_analysis_results()
        
        # 生成最终报告
        self.generate_final_report()
        
        print("\n✅ 过拟合和数据泄露分析完成！")
        return True
    
    def generate_comprehensive_assessment(self, overfitting_analysis, leakage_analysis):
        """生成综合评估"""
        overfitting_risk = overfitting_analysis['overfitting_risk_score']
        leakage_risk = leakage_analysis['overall_leakage_risk']
        
        # 模型可信度评分（0-100，越高越可信）
        credibility_score = max(0, 100 - (overfitting_risk + leakage_risk) / 2)
        
        # 风险等级
        if overfitting_risk > 70 or leakage_risk > 70:
            risk_level = 'high'
        elif overfitting_risk > 40 or leakage_risk > 40:
            risk_level = 'medium'
        else:
            risk_level = 'low'
        
        # 改进建议
        improvement_suggestions = []
        
        if overfitting_risk > 50:
            improvement_suggestions.extend([
                '使用更严格的时间序列交叉验证',
                '简化模型复杂度',
                '增加正则化约束',
                '扩大训练数据集'
            ])
        
        if leakage_risk > 50:
            improvement_suggestions.extend([
                '严格检查预测时间顺序',
                '重新审查特征工程过程',
                '确保训练测试集完全分离',
                '建立数据泄露检测机制'
            ])
        
        return {
            'overfitting_risk_score': overfitting_risk,
            'data_leakage_risk_score': leakage_risk,
            'model_credibility_score': credibility_score,
            'overall_risk_level': risk_level,
            'improvement_suggestions': improvement_suggestions,
            'recommended_actions': self.generate_recommended_actions(overfitting_risk, leakage_risk)
        }
    
    def generate_recommended_actions(self, overfitting_risk, leakage_risk):
        """生成推荐行动"""
        actions = []
        
        if overfitting_risk > 70:
            actions.append({
                'priority': 'high',
                'action': '立即重新训练模型',
                'description': '使用严格的时间序列验证重新训练'
            })
        
        if leakage_risk > 70:
            actions.append({
                'priority': 'critical',
                'action': '暂停使用当前模型',
                'description': '存在严重数据泄露风险，需要彻底审查'
            })
        
        if overfitting_risk > 40 or leakage_risk > 40:
            actions.append({
                'priority': 'medium',
                'action': '增强验证机制',
                'description': '建立更严格的模型验证流程'
            })
        
        return actions
    
    def save_analysis_results(self, filename='overfitting_leakage_analysis.json'):
        """保存分析结果"""
        try:
            def convert_numpy_types(obj):
                if isinstance(obj, dict):
                    return {k: convert_numpy_types(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_numpy_types(v) for v in obj]
                elif isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif pd.isna(obj):
                    return None
                elif isinstance(obj, (bool, np.bool_)):
                    return bool(obj)
                elif isinstance(obj, pd.Timestamp):
                    return obj.isoformat()
                else:
                    return obj
            
            results = {
                'timestamp': datetime.now().isoformat(),
                'analysis_results': convert_numpy_types(self.analysis_results)
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"✅ 分析结果已保存到 {filename}")
            return True
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False
    
    def generate_final_report(self):
        """生成最终报告"""
        print("\n" + "="*60)
        print("📊 过拟合和数据泄露深度验证分析报告")
        print("="*60)
        
        overfitting = self.analysis_results['overfitting_analysis']
        leakage = self.analysis_results['data_leakage_analysis']
        assessment = self.analysis_results['comprehensive_assessment']
        
        print(f"\n🎯 综合评估结果:")
        print(f"   过拟合风险评分: {assessment['overfitting_risk_score']:.1f}/100")
        print(f"   数据泄露风险评分: {assessment['data_leakage_risk_score']:.1f}/100")
        print(f"   模型可信度评分: {assessment['model_credibility_score']:.1f}/100")
        print(f"   总体风险等级: {assessment['overall_risk_level'].upper()}")
        
        print(f"\n🔍 过拟合检测结果:")
        print(f"   训练测试性能差异: {overfitting['train_test_gap']['relative_gap']:.1%}")
        print(f"   模型复杂度评分: {overfitting['model_complexity']['complexity_score']:.1f}/100")
        print(f"   泛化能力评分: {overfitting['generalization_ability']['generalization_score']:.1f}/100")
        print(f"   预测稳定性评分: {overfitting['prediction_stability']['stability_score']:.1f}/100")
        
        print(f"\n🔍 数据泄露检测结果:")
        print(f"   时间泄露检测: {'⚠️ 发现问题' if leakage['temporal_leakage']['temporal_leakage_detected'] else '✅ 正常'}")
        print(f"   特征泄露检测: {'⚠️ 发现问题' if leakage['feature_leakage']['feature_leakage_detected'] else '✅ 正常'}")
        print(f"   训练测试重叠: {'⚠️ 发现问题' if leakage['train_test_overlap']['overlap_detected'] else '✅ 正常'}")
        print(f"   目标变量泄露: {'⚠️ 发现问题' if leakage['target_leakage']['target_leakage_detected'] else '✅ 正常'}")
        
        print(f"\n💡 改进建议:")
        if assessment['improvement_suggestions']:
            for suggestion in assessment['improvement_suggestions']:
                print(f"   • {suggestion}")
        else:
            print(f"   • 当前系统表现良好，建议保持现有策略")

        print(f"\n🎯 推荐行动:")
        if assessment['recommended_actions']:
            for action in assessment['recommended_actions']:
                print(f"   {action['priority'].upper()}: {action['action']}")
                print(f"      {action['description']}")
        else:
            print(f"   • 继续监控系统性能")
            print(f"   • 定期进行验证分析")

if __name__ == "__main__":
    analyzer = OverfittingDataLeakageAnalyzer()
    analyzer.run_comprehensive_analysis()
