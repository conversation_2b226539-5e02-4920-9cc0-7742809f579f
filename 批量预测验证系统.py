#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量预测验证系统
模拟用户的预测方法：一次性预测20个期号，然后与真实数据对比
"""

import pandas as pd
import numpy as np
from collections import defaultdict
import json
from datetime import datetime

class BatchPredictionValidator:
    """
    批量预测验证器
    模拟用户的预测方法和验证过程
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.data = None
        self.train_data = None
        self.test_data = None
        self.transition_prob = {}
        
        # 修复后的配置
        self.config = {
            'confidence_threshold': 0.025,  # 修复: 从0.4降低到合理值
            'batch_size': 20,               # 批量预测期数
            'prediction_method': '单期预测',  # 使用最佳的单期预测方法
            'validation_method': '真实数据对比'
        }
        
        # 预测结果存储
        self.batch_predictions = []
        self.validation_results = []
    
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            # 模拟用户的数据分割：使用179期之前的数据进行预测
            cutoff_idx = self.data[(self.data['年份'] == 2025) & (self.data['期号'] == 179)].index[0]
            self.train_data = self.data.iloc[:cutoff_idx+1].copy()  # 包含179期
            self.test_data = self.data.iloc[cutoff_idx+1:].copy()   # 180期之后的数据
            
            print(f"✅ 数据加载成功")
            print(f"  训练数据: {len(self.train_data)}期 (截止到2025年179期)")
            print(f"  测试数据: {len(self.test_data)}期 (2025年180期开始)")
            print(f"  模拟用户方法: 基于179期数据预测后续期间")
            
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def build_markov_model(self):
        """构建马尔可夫模型"""
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 计算转移概率
        self.transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                self.transition_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        print(f"✅ 马尔可夫模型构建完成")
        print(f"  状态数量: {len(self.transition_prob)}")
        print(f"  训练期数: {len(self.train_data)}")
        
        return True
    
    def batch_predict_20_periods(self):
        """
        批量预测20个期号
        模拟用户的预测方法：一次性预测多个期号
        """
        print(f"\n🔮 开始批量预测20个期号")
        print("=" * 60)
        
        # 使用179期数据作为基础
        base_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
        print(f"基础数据: 2025年179期 {list(base_numbers)}")
        
        predictions = []
        current_base = base_numbers
        
        for i in range(20):
            period_num = 180 + i
            
            # 单期预测方法
            predicted_numbers, confidence = self._single_period_prediction(current_base)
            
            # 生成投注建议
            should_bet = confidence >= self.config['confidence_threshold']
            
            prediction = {
                'period': period_num,
                'year': 2025,
                'base_numbers': list(current_base),
                'predicted_numbers': predicted_numbers,
                'confidence': confidence,
                'should_bet': should_bet,
                'prediction_method': '单期预测'
            }
            
            predictions.append(prediction)
            
            # 为下一期预测更新基础数据
            # 注意：这里我们使用预测结果，这是批量预测的特点
            current_base = set(predicted_numbers + list(current_base)[:4])
        
        self.batch_predictions = predictions
        
        print(f"✅ 批量预测完成")
        print(f"  预测期数: {len(predictions)}期 (2025年180-199期)")
        
        # 显示预测概览
        bet_count = sum(1 for p in predictions if p['should_bet'])
        avg_confidence = np.mean([p['confidence'] for p in predictions])
        
        print(f"  建议投注: {bet_count}期 ({bet_count/len(predictions):.1%})")
        print(f"  平均置信度: {avg_confidence:.4f}")
        
        return predictions
    
    def _single_period_prediction(self, previous_numbers):
        """单期预测方法（基于29.2%验证方法）"""
        number_probs = defaultdict(float)
        total_prob = 0.0
        coverage_count = 0
        
        for prev_num in previous_numbers:
            if prev_num in self.transition_prob:
                coverage_count += 1
                for next_num, prob in self.transition_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        # 添加随机扰动（增加多样性）
        perturbation = 0.08
        for num in number_probs:
            noise = np.random.normal(0, perturbation * number_probs[num])
            number_probs[num] = max(0, number_probs[num] + noise)
        
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            predicted_numbers = [num for num, prob in sorted_numbers[:2]]
            
            # 置信度计算
            top_2_probs = [prob for num, prob in sorted_numbers[:2]]
            base_confidence = np.mean(top_2_probs)
            coverage_boost = (coverage_count / len(previous_numbers)) * 0.01
            enhanced_confidence = base_confidence + coverage_boost
            
            return predicted_numbers, enhanced_confidence
        else:
            return [1, 2], 0.01
    
    def validate_with_real_data(self):
        """
        与真实数据对比验证
        模拟用户与网页开奖数据的对比过程
        """
        print(f"\n📊 与真实开奖数据对比验证")
        print("=" * 60)
        
        if not self.batch_predictions:
            print(f"❌ 没有预测数据，请先进行批量预测")
            return None
        
        validation_results = []
        total_predictions = 0
        bet_predictions = 0
        total_hits = 0
        bet_hits = 0
        
        print(f"{'期号':<6} {'预测数字':<15} {'实际数字':<20} {'命中':<4} {'置信度':<8} {'投注':<4}")
        print("-" * 70)
        
        for pred in self.batch_predictions:
            period = pred['period']
            predicted = pred['predicted_numbers']
            confidence = pred['confidence']
            should_bet = pred['should_bet']
            
            # 查找对应的真实数据
            real_data = self.test_data[
                (self.test_data['年份'] == 2025) & 
                (self.test_data['期号'] == period)
            ]
            
            if len(real_data) > 0:
                # 有真实数据可以验证
                actual_numbers = [real_data.iloc[0][f'数字{j}'] for j in range(1, 7)]
                
                # 计算命中
                predicted_set = set(predicted)
                actual_set = set(actual_numbers)
                hit_count = len(predicted_set & actual_set)
                is_hit = hit_count >= 1
                
                # 统计
                total_predictions += 1
                if should_bet:
                    bet_predictions += 1
                    if is_hit:
                        bet_hits += 1
                
                if is_hit:
                    total_hits += 1
                
                # 记录结果
                result = {
                    'period': period,
                    'predicted': predicted,
                    'actual': actual_numbers,
                    'hit_count': hit_count,
                    'is_hit': is_hit,
                    'confidence': confidence,
                    'should_bet': should_bet,
                    'validation_status': 'verified'
                }
                
                validation_results.append(result)
                
                # 显示结果
                pred_str = f"{predicted[0]:2d}, {predicted[1]:2d}"
                actual_str = f"{actual_numbers}"
                hit_str = "✅" if is_hit else "❌"
                bet_str = "是" if should_bet else "否"
                
                print(f"{period:<6} {pred_str:<15} {actual_str:<20} {hit_str:<4} {confidence:<8.4f} {bet_str:<4}")
            
            else:
                # 没有真实数据（未来期号）
                result = {
                    'period': period,
                    'predicted': predicted,
                    'actual': None,
                    'hit_count': None,
                    'is_hit': None,
                    'confidence': confidence,
                    'should_bet': should_bet,
                    'validation_status': 'pending'
                }
                
                validation_results.append(result)
                
                pred_str = f"{predicted[0]:2d}, {predicted[1]:2d}"
                bet_str = "是" if should_bet else "否"
                
                print(f"{period:<6} {pred_str:<15} {'待开奖':<20} {'?':<4} {confidence:<8.4f} {bet_str:<4}")
        
        self.validation_results = validation_results
        
        # 计算统计结果
        if total_predictions > 0:
            overall_hit_rate = total_hits / total_predictions
            bet_hit_rate = bet_hits / bet_predictions if bet_predictions > 0 else 0
            
            print(f"\n📈 验证统计结果")
            print("=" * 40)
            print(f"总预测期数: {total_predictions}期")
            print(f"总命中期数: {total_hits}期")
            print(f"总体命中率: {overall_hit_rate:.3f} ({overall_hit_rate*100:.1f}%)")
            print(f"")
            print(f"建议投注期数: {bet_predictions}期")
            print(f"投注命中期数: {bet_hits}期")
            print(f"投注命中率: {bet_hit_rate:.3f} ({bet_hit_rate*100:.1f}%)")
            print(f"投注比例: {bet_predictions/len(self.batch_predictions):.1%}")
            
            return {
                'total_predictions': total_predictions,
                'total_hits': total_hits,
                'overall_hit_rate': overall_hit_rate,
                'bet_predictions': bet_predictions,
                'bet_hits': bet_hits,
                'bet_hit_rate': bet_hit_rate,
                'validation_results': validation_results
            }
        
        return None
    
    def compare_with_user_results(self, user_results):
        """与用户结果对比"""
        print(f"\n🔍 与用户结果对比分析")
        print("=" * 50)
        
        print(f"用户报告结果:")
        print(f"  预测期数: {user_results['predicted_periods']}期")
        print(f"  命中期数: {user_results['hit_periods']}期")
        print(f"  命中率: {user_results['hit_rate']:.3f} ({user_results['hit_rate']*100:.1f}%)")
        
        if self.validation_results:
            verified_results = [r for r in self.validation_results if r['validation_status'] == 'verified']
            system_predictions = len(verified_results)
            system_hits = sum(1 for r in verified_results if r['is_hit'])
            system_hit_rate = system_hits / system_predictions if system_predictions > 0 else 0
            
            print(f"\n系统模拟结果:")
            print(f"  预测期数: {system_predictions}期")
            print(f"  命中期数: {system_hits}期")
            print(f"  命中率: {system_hit_rate:.3f} ({system_hit_rate*100:.1f}%)")
            
            print(f"\n对比分析:")
            hit_rate_diff = user_results['hit_rate'] - system_hit_rate
            print(f"  命中率差异: {hit_rate_diff:+.3f} ({hit_rate_diff*100:+.1f}个百分点)")
            
            if abs(hit_rate_diff) < 0.1:
                print(f"  结论: 用户结果与系统模拟基本一致")
            elif hit_rate_diff > 0:
                print(f"  结论: 用户结果优于系统模拟")
            else:
                print(f"  结论: 系统模拟优于用户结果")
    
    def save_results(self):
        """保存结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 处理numpy类型
        def convert_numpy_types(obj):
            if isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(v) for v in obj]
            elif hasattr(obj, 'item'):
                return obj.item()
            elif isinstance(obj, (np.bool_, bool)):
                return bool(obj)
            elif isinstance(obj, (np.integer, int)):
                return int(obj)
            elif isinstance(obj, (np.floating, float)):
                return float(obj)
            else:
                return obj

        # 保存预测结果
        predictions_file = f"批量预测结果_{timestamp}.json"
        with open(predictions_file, 'w', encoding='utf-8') as f:
            json.dump({
                'config': convert_numpy_types(self.config),
                'predictions': convert_numpy_types(self.batch_predictions),
                'validation_results': convert_numpy_types(self.validation_results),
                'timestamp': timestamp
            }, f, ensure_ascii=False, indent=2)

        print(f"\n✅ 结果已保存: {predictions_file}")

def main():
    """主函数"""
    print("🎯 批量预测验证系统")
    print("模拟用户方法：一次性预测20个期号，然后与真实数据对比")
    print("=" * 80)
    
    # 用户报告的结果
    user_results = {
        'predicted_periods': 11,  # 实际进行预测的期数
        'hit_periods': 4,         # 命中的期数
        'hit_rate': 4/11          # 命中率 36.4%
    }
    
    # 初始化验证器
    validator = BatchPredictionValidator()
    
    # 1. 加载数据
    if not validator.load_data():
        return
    
    # 2. 构建模型
    if not validator.build_markov_model():
        return
    
    # 3. 批量预测20个期号
    predictions = validator.batch_predict_20_periods()
    
    # 4. 与真实数据验证
    validation_stats = validator.validate_with_real_data()
    
    # 5. 与用户结果对比
    validator.compare_with_user_results(user_results)
    
    # 6. 保存结果
    validator.save_results()
    
    # 7. 总结
    print(f"\n🎉 批量预测验证完成")
    print("=" * 50)
    print(f"✅ 系统问题已修复: 置信度阈值从0.4降低到0.025")
    print(f"✅ 预测方法已验证: 模拟用户的批量预测流程")
    print(f"✅ 结果对比已完成: 分析用户与系统的差异")

if __name__ == "__main__":
    main()
