#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复验证
Final fix verification for prediction consistency and scoring issues
"""

import sys
import os
sys.path.append('.')

def test_prediction_consistency():
    """测试预测一致性"""
    print("🧪 测试预测一致性")
    print("="*40)
    
    try:
        from 集成评分系统的预测系统 import IntegratedPredictionSystem
        
        # 创建系统实例
        system = IntegratedPredictionSystem()
        
        # 初始化系统
        if not system.initialize_system():
            print("❌ 系统初始化失败")
            return False
        
        print("✅ 系统初始化成功")
        
        # 测试2025年197期数据
        test_input = [10, 12, 15, 24, 25, 43]
        
        print(f"测试输入: {test_input}")
        print("进行3次一致性测试:")
        
        predictions = []
        for i in range(3):
            pred, confidence = system.predict_next_period(test_input)
            predictions.append((pred, confidence))
            print(f"  第{i+1}次: {pred} (置信度: {confidence:.6f})")
        
        # 检查一致性
        unique_predictions = len(set(tuple(p[0]) for p in predictions))
        is_consistent = unique_predictions == 1
        
        print(f"\n一致性结果: {'✅ 一致' if is_consistent else '❌ 不一致'}")
        
        if is_consistent:
            print("🎉 预测一致性问题已修复！")
            return True, predictions[0]
        else:
            print("⚠️ 预测仍然不一致")
            return False, None
            
    except Exception as e:
        print(f"❌ 一致性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_scoring_calculation():
    """测试评分计算"""
    print(f"\n🧪 测试评分计算")
    print("="*40)
    
    try:
        from 集成评分系统的预测系统 import IntegratedPredictionSystem
        
        system = IntegratedPredictionSystem()
        
        if not system.initialize_system():
            print("❌ 系统初始化失败")
            return False
        
        # 测试评分计算
        test_data = {
            'predicted_numbers': [2, 15],
            'confidence': 0.028,
            'current_period': 197
        }
        
        print(f"测试数据: {test_data}")
        
        score_result = system.calculate_prediction_score(test_data)
        
        print(f"✅ 评分计算成功:")
        print(f"   评分: {score_result['score']:.1f}分")
        print(f"   等级: {score_result['grade']}")
        print(f"   建议: {score_result['recommendation']}")
        print(f"   概率: {score_result['probability']:.3f}")
        
        return True, score_result
        
    except Exception as e:
        print(f"❌ 评分计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_full_prediction_workflow():
    """测试完整预测流程"""
    print(f"\n🧪 测试完整预测流程")
    print("="*40)
    
    try:
        from 集成评分系统的预测系统 import IntegratedPredictionSystem
        
        system = IntegratedPredictionSystem()
        
        if not system.initialize_system():
            print("❌ 系统初始化失败")
            return False
        
        # 模拟完整预测流程
        current_numbers = [10, 12, 15, 24, 25, 43]  # 2025年197期
        
        print(f"当期开奖: {current_numbers}")
        
        # 1. 预测下期
        prediction, confidence = system.predict_next_period(current_numbers)
        print(f"预测结果: {prediction} (置信度: {confidence:.6f})")
        
        # 2. 计算评分
        score_data = {
            'predicted_numbers': prediction,
            'confidence': confidence,
            'current_period': 197
        }
        
        score_result = system.calculate_prediction_score(score_data)
        print(f"评分结果: {score_result['score']:.1f}分 ({score_result['grade']})")
        
        # 3. 保存到CSV（模拟）
        prediction_record = {
            '预测日期': '2025-07-16',
            '预测时间': '23:00:00',
            '当期年份': 2025,
            '当期期号': 197,
            '预测期号': '2025年198期',
            '当期数字1': current_numbers[0],
            '当期数字2': current_numbers[1],
            '当期数字3': current_numbers[2],
            '当期数字4': current_numbers[3],
            '当期数字5': current_numbers[4],
            '当期数字6': current_numbers[5],
            '预测数字1': prediction[0],
            '预测数字2': prediction[1],
            '预测置信度': f"{confidence:.6f}",
            '预测方法': '优化34.3%增强马尔可夫',
            '预测评分': f"{score_result['score']:.1f}",
            '评分等级': score_result['grade'],
            '评分建议': score_result['recommendation'],
            '评分概率': f"{score_result['probability']:.3f}",
            '备注': '修复后测试'
        }
        
        print(f"\n✅ 完整预测流程测试成功:")
        print(f"   预测: {prediction}")
        print(f"   置信度: {confidence:.6f}")
        print(f"   评分: {score_result['score']:.1f}分")
        print(f"   等级: {score_result['grade']}")
        
        return True, prediction_record
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def generate_fix_report():
    """生成修复报告"""
    print(f"\n📋 修复效果报告")
    print("="*50)
    
    # 运行所有测试
    consistency_ok, consistent_pred = test_prediction_consistency()
    scoring_ok, score_result = test_scoring_calculation()
    workflow_ok, workflow_result = test_full_prediction_workflow()
    
    print(f"\n🎯 修复效果总结:")
    print("="*30)
    
    print(f"1. 预测一致性: {'✅ 已修复' if consistency_ok else '❌ 仍有问题'}")
    if consistency_ok and consistent_pred:
        print(f"   - 相同输入产生相同预测: {consistent_pred[0]}")
        print(f"   - 置信度稳定: {consistent_pred[1]:.6f}")
    
    print(f"\n2. 评分计算: {'✅ 已修复' if scoring_ok else '❌ 仍有问题'}")
    if scoring_ok and score_result:
        print(f"   - 评分正常计算: {score_result['score']:.1f}分")
        print(f"   - 等级正确显示: {score_result['grade']}")
    
    print(f"\n3. 完整流程: {'✅ 正常工作' if workflow_ok else '❌ 仍有问题'}")
    if workflow_ok and workflow_result:
        print(f"   - 预测: {workflow_result['预测数字1']}, {workflow_result['预测数字2']}")
        print(f"   - 评分: {workflow_result['预测评分']}分")
    
    # 总体评估
    all_fixed = consistency_ok and scoring_ok and workflow_ok
    
    print(f"\n🎉 总体修复状态: {'✅ 完全修复' if all_fixed else '⚠️ 部分修复'}")
    
    if all_fixed:
        print("\n✅ 所有问题已修复，系统可正常使用！")
        print("修复内容:")
        print("  - 预测一致性：添加了固定随机种子控制")
        print("  - 评分计算：实现了简化但有效的评分算法")
        print("  - 数据格式：修复了数据传递格式问题")
        print("  - 错误处理：增强了异常处理机制")
        
        print("\n🚀 使用建议:")
        print("  - 现在可以正常使用集成评分系统")
        print("  - 相同输入会产生相同预测结果")
        print("  - 评分功能正常工作")
        print("  - 建议进行实际测试验证")
    else:
        print("\n⚠️ 仍有部分问题需要解决")
        print("建议:")
        if not consistency_ok:
            print("  - 检查随机种子设置")
        if not scoring_ok:
            print("  - 检查评分算法实现")
        if not workflow_ok:
            print("  - 检查整体流程逻辑")
    
    return all_fixed

def main():
    """主函数"""
    print("🔧 最终修复验证")
    print("="*60)
    
    print("验证修复效果:")
    print("1. 预测一致性问题")
    print("2. 评分计算失败问题")
    print("3. 完整预测流程")
    
    # 生成修复报告
    all_fixed = generate_fix_report()
    
    if all_fixed:
        print(f"\n🎉 恭喜！所有问题已成功修复！")
        print(f"现在可以正常使用优化后的集成评分系统。")
        
        print(f"\n📝 修复前后对比:")
        print(f"修复前:")
        print(f"  - 预测结果不一致：[2,15] → [29,2] → [其他]")
        print(f"  - 评分计算失败：'list' object has no attribute 'columns'")
        
        print(f"修复后:")
        print(f"  - 预测结果一致：相同输入产生相同输出")
        print(f"  - 评分计算正常：显示具体分数和等级")
        
        print(f"\n🎯 现在您可以:")
        print(f"  1. 运行 python 集成评分系统的预测系统.py")
        print(f"  2. 输入2025年197期数据: [10, 12, 15, 24, 25, 43]")
        print(f"  3. 获得一致的预测结果和正确的评分")
    else:
        print(f"\n⚠️ 部分问题仍需解决，请检查具体错误信息。")

if __name__ == "__main__":
    main()
