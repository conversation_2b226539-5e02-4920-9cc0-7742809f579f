#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后预测系统使用示例
Example usage of optimized prediction system
"""

from 集成评分系统的预测系统 import IntegratedPredictionSystem

def main():
    # 创建预测系统
    system = IntegratedPredictionSystem()
    
    # 加载数据（可选）
    try:
        system.load_data()
        print("✅ 数据加载成功")
    except:
        print("⚠️ 使用默认配置")
    
    # 示例：预测下一期
    current_numbers = [10, 12, 15, 24, 25, 43]
    
    try:
        result = system.predict_next_period(current_numbers)
        
        if result:
            print(f"预测结果:")
            print(f"   预测数字: {result['predicted_numbers']}")
            print(f"   置信度: {result['confidence']:.3f}")
            print(f"   评分: {result['score']:.1f}")
            print(f"   等级: {result['grade']}")
            print(f"   建议: {result['recommendation']}")
        else:
            print("预测失败")
            
    except Exception as e:
        print(f"预测错误: {e}")
    
    # 示例：直接评分
    prediction_data = {
        'predicted_numbers': [2, 15],
        'confidence': 0.030,
        'period': 100
    }
    
    score_result = system.calculate_prediction_score(prediction_data)
    print(f"\n评分结果:")
    print(f"   评分: {score_result['score']:.1f}")
    print(f"   等级: {score_result['grade']}")
    print(f"   建议: {score_result['recommendation']}")

if __name__ == "__main__":
    main()
