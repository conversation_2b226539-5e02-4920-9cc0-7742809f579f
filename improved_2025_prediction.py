#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版2025年数据预测
Improved 2025 Data Prediction

修复算法问题并增加预测多样性

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.1
"""

import pandas as pd
import numpy as np
from datetime import datetime
import json
from dynamic_confidence_wrapper import (
    predict_with_confidence,
    calculate_dynamic_confidence,
    update_prediction_feedback,
    get_system_performance
)

def fix_markov_training_data(df_2025):
    """修复马尔可夫链训练数据格式"""
    print("🔧 修复马尔可夫链训练数据格式")
    print("="*40)
    
    # 提取有实际结果的数据
    training_sequences = []
    
    for i in range(len(df_2025) - 1):
        current_row = df_2025.iloc[i]
        next_row = df_2025.iloc[i + 1]
        
        # 检查当前期的实际数字
        actual_cols = ['实际数字1', '实际数字2', '实际数字3', '实际数字4', '实际数字5', '实际数字6']
        if all(pd.notna(current_row[col]) for col in actual_cols):
            current_numbers = [int(current_row[col]) for col in actual_cols]
            
            # 检查下一期的实际数字
            if all(pd.notna(next_row[col]) for col in actual_cols):
                next_numbers = [int(next_row[col]) for col in actual_cols]
                training_sequences.append((current_numbers, next_numbers))
    
    print(f"生成训练序列: {len(training_sequences)} 对")
    return training_sequences

def enhanced_prediction_with_diversity(previous_numbers, context, diversity_factor=0.3):
    """增强预测，增加多样性"""
    
    # 基础预测
    base_prediction = predict_with_confidence(previous_numbers, context)
    
    # 增加多样性的策略
    predicted_numbers = base_prediction['predicted_numbers'].copy()
    
    # 策略1: 基于历史频率的随机调整
    if np.random.random() < diversity_factor:
        # 随机替换一个数字
        replace_idx = np.random.randint(0, len(predicted_numbers))
        
        # 生成候选数字（避免重复）
        candidates = list(range(1, 50))
        for num in predicted_numbers:
            if num in candidates:
                candidates.remove(num)
        
        if candidates:
            new_number = np.random.choice(candidates)
            predicted_numbers[replace_idx] = new_number
    
    # 策略2: 基于数字分布的调整
    if np.random.random() < diversity_factor * 0.5:
        # 确保数字分布更均匀
        if max(predicted_numbers) - min(predicted_numbers) < 20:
            # 如果跨度太小，随机增加一个较大或较小的数字
            if np.random.random() < 0.5:
                # 添加较大数字
                large_candidates = [n for n in range(35, 50) if n not in predicted_numbers]
                if large_candidates:
                    predicted_numbers[1] = np.random.choice(large_candidates)
            else:
                # 添加较小数字
                small_candidates = [n for n in range(1, 15) if n not in predicted_numbers]
                if small_candidates:
                    predicted_numbers[0] = np.random.choice(small_candidates)
    
    # 重新计算置信度
    enhanced_confidence = calculate_dynamic_confidence(predicted_numbers, context)
    
    # 构建增强预测结果
    enhanced_result = base_prediction.copy()
    enhanced_result['predicted_numbers'] = predicted_numbers
    enhanced_result['confidence'] = enhanced_confidence['final_confidence']
    enhanced_result['diversity_applied'] = True
    enhanced_result['original_prediction'] = base_prediction['predicted_numbers']
    
    return enhanced_result

def predict_with_improved_system(df_2025, num_predictions=10):
    """使用改进系统进行预测"""
    print(f"\n🚀 使用改进系统预测 {num_predictions} 期")
    print("="*50)
    
    # 获取最新期号和数字
    latest_period = df_2025['当期期号'].max()
    latest_row = df_2025[df_2025['当期期号'] == latest_period].iloc[0]
    
    # 获取基础数字
    actual_cols = ['实际数字1', '实际数字2', '实际数字3', '实际数字4', '实际数字5', '实际数字6']
    if all(pd.notna(latest_row[col]) for col in actual_cols):
        base_numbers = [int(latest_row[col]) for col in actual_cols]
    else:
        current_cols = ['当期数字1', '当期数字2', '当期数字3', '当期数字4', '当期数字5', '当期数字6']
        base_numbers = [int(latest_row[col]) for col in current_cols]
    
    print(f"基础数字: {base_numbers}")
    
    predictions = []
    current_numbers = base_numbers.copy()
    
    for i in range(num_predictions):
        period_num = latest_period + i + 1
        
        # 构建预测上下文
        context = {
            'previous_numbers': current_numbers,
            'data_source': '改进预测系统',
            'period_idx': period_num,
            'year': 2025,
            'prediction_sequence': i + 1,
            'total_predictions': num_predictions
        }
        
        # 使用增强预测（增加多样性）
        prediction_result = enhanced_prediction_with_diversity(
            current_numbers, 
            context, 
            diversity_factor=0.4  # 40%的概率应用多样性策略
        )
        
        # 记录预测结果
        prediction_record = {
            'period_num': period_num,
            'period_name': f'2025年{period_num}期',
            'base_numbers': current_numbers.copy(),
            'predicted_numbers': prediction_result['predicted_numbers'],
            'original_prediction': prediction_result.get('original_prediction', []),
            'confidence': prediction_result['confidence'],
            'diversity_applied': prediction_result.get('diversity_applied', False),
            'ensemble_method': prediction_result.get('ensemble_method', '未知'),
            'algorithm_weights': prediction_result.get('weights', {}),
            'prediction_timestamp': datetime.now().isoformat()
        }
        
        predictions.append(prediction_record)
        
        diversity_mark = "🎲" if prediction_record['diversity_applied'] else "📊"
        print(f"{diversity_mark} 期号 {period_num}: {prediction_result['predicted_numbers']} "
              f"(置信度: {prediction_result['confidence']:.3f})")
        
        if prediction_record['diversity_applied']:
            print(f"   原始预测: {prediction_record['original_prediction']}")
        
        # 更新当前数字（使用预测结果作为下一期的基础）
        # 这样可以产生更多样化的预测序列
        current_numbers = prediction_result['predicted_numbers'].copy()
    
    return predictions

def analyze_prediction_diversity(predictions):
    """分析预测多样性"""
    print(f"\n🎨 分析预测多样性")
    print("="*30)
    
    # 统计不同的预测结果
    unique_predictions = set()
    for pred in predictions:
        unique_predictions.add(tuple(sorted(pred['predicted_numbers'])))
    
    diversity_rate = len(unique_predictions) / len(predictions)
    print(f"预测多样性: {len(unique_predictions)}/{len(predictions)} ({diversity_rate:.1%})")
    
    # 统计多样性策略应用情况
    diversity_applied_count = sum(1 for pred in predictions if pred.get('diversity_applied', False))
    print(f"多样性策略应用: {diversity_applied_count}/{len(predictions)} ({diversity_applied_count/len(predictions):.1%})")
    
    # 分析数字分布
    all_numbers = []
    for pred in predictions:
        all_numbers.extend(pred['predicted_numbers'])
    
    unique_numbers = set(all_numbers)
    print(f"涉及数字范围: {len(unique_numbers)} 个不同数字")
    print(f"数字范围: {min(all_numbers)} - {max(all_numbers)}")
    
    # 最频繁的数字
    from collections import Counter
    number_counts = Counter(all_numbers)
    most_common = number_counts.most_common(5)
    print(f"最频繁数字: {most_common}")
    
    # 置信度分析
    confidences = [pred['confidence'] for pred in predictions]
    print(f"置信度统计:")
    print(f"  平均值: {np.mean(confidences):.3f}")
    print(f"  标准差: {np.std(confidences):.3f}")
    print(f"  范围: {np.min(confidences):.3f} - {np.max(confidences):.3f}")
    
    return {
        'diversity_rate': diversity_rate,
        'unique_predictions': len(unique_predictions),
        'diversity_applied_count': diversity_applied_count,
        'unique_numbers_count': len(unique_numbers),
        'confidence_stats': {
            'mean': np.mean(confidences),
            'std': np.std(confidences),
            'min': np.min(confidences),
            'max': np.max(confidences)
        }
    }

def export_improved_predictions(predictions):
    """导出改进的预测结果"""
    print(f"\n💾 导出改进预测结果")
    print("="*30)
    
    # 创建CSV格式数据
    csv_records = []
    for pred in predictions:
        # 安全获取基础数字
        base_numbers = pred.get('base_numbers', [])
        predicted_numbers = pred.get('predicted_numbers', [])
        original_prediction = pred.get('original_prediction', [])

        record = {
            '预测日期': datetime.now().strftime('%Y-%m-%d'),
            '预测时间': datetime.now().strftime('%H:%M:%S'),
            '当期年份': 2025,
            '当期期号': pred['period_num'] - 1,
            '预测期号': pred['period_name'],
            '基础数字1': base_numbers[0] if len(base_numbers) > 0 else None,
            '基础数字2': base_numbers[1] if len(base_numbers) > 1 else None,
            '基础数字3': base_numbers[2] if len(base_numbers) > 2 else None,
            '基础数字4': base_numbers[3] if len(base_numbers) > 3 else None,
            '基础数字5': base_numbers[4] if len(base_numbers) > 4 else None,
            '基础数字6': base_numbers[5] if len(base_numbers) > 5 else None,
            '预测数字1': predicted_numbers[0] if len(predicted_numbers) > 0 else None,
            '预测数字2': predicted_numbers[1] if len(predicted_numbers) > 1 else None,
            '最终置信度': pred['confidence'],
            '多样性应用': '是' if pred.get('diversity_applied', False) else '否',
            '原始预测1': original_prediction[0] if len(original_prediction) > 0 else None,
            '原始预测2': original_prediction[1] if len(original_prediction) > 1 else None,
            '预测方法': f"改进系统-{pred.get('ensemble_method', '未知')}",
            '算法权重': str(pred.get('algorithm_weights', {})),
            '备注': '改进预测系统-增加多样性'
        }
        csv_records.append(record)
    
    # 导出CSV
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f'improved_2025_predictions_{timestamp}.csv'
    
    df_export = pd.DataFrame(csv_records)
    df_export.to_csv(csv_filename, index=False, encoding='utf-8-sig')
    
    print(f"CSV文件已导出: {csv_filename}")
    
    # 导出详细JSON
    json_filename = f'improved_2025_predictions_detailed_{timestamp}.json'
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump({
            'predictions': predictions,
            'export_timestamp': datetime.now().isoformat(),
            'system_version': '改进版 v1.1',
            'improvements': [
                '修复马尔可夫链数据格式',
                '增加预测多样性策略',
                '动态基础数字更新',
                '增强置信度计算'
            ]
        }, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"详细JSON已导出: {json_filename}")
    
    return csv_filename, json_filename

def main():
    """主函数"""
    print("🚀 改进版2025年数据预测系统")
    print("="*60)
    
    try:
        # 1. 加载数据
        df = pd.read_csv('prediction_data.csv')
        df_2025 = df[df['当期年份'] == 2025]
        
        print(f"📊 数据概况:")
        print(f"  总数据量: {len(df)} 条")
        print(f"  2025年数据: {len(df_2025)} 条")
        print(f"  最新期号: {df_2025['当期期号'].max()}")
        
        # 2. 修复训练数据
        training_sequences = fix_markov_training_data(df_2025)
        
        # 3. 进行改进预测
        predictions = predict_with_improved_system(df_2025, num_predictions=10)
        
        # 4. 分析预测多样性
        diversity_stats = analyze_prediction_diversity(predictions)
        
        # 5. 导出结果
        csv_file, json_file = export_improved_predictions(predictions)
        
        print("\n" + "="*60)
        print("✅ 改进版预测完成！")
        print("="*60)
        
        print(f"\n📊 改进效果:")
        print(f"  预测多样性: {diversity_stats['diversity_rate']:.1%}")
        print(f"  不同预测结果: {diversity_stats['unique_predictions']}")
        print(f"  多样性策略应用: {diversity_stats['diversity_applied_count']}")
        print(f"  涉及数字数量: {diversity_stats['unique_numbers_count']}")
        print(f"  平均置信度: {diversity_stats['confidence_stats']['mean']:.3f}")
        
        print(f"\n📁 生成文件:")
        print(f"  CSV预测结果: {csv_file}")
        print(f"  详细JSON数据: {json_file}")
        
        print(f"\n🎯 主要改进:")
        print(f"  ✅ 修复了马尔可夫链数据格式问题")
        print(f"  ✅ 增加了预测结果多样性")
        print(f"  ✅ 实现了动态基础数字更新")
        print(f"  ✅ 保持了置信度调整机制")
        
    except Exception as e:
        print(f"\n❌ 预测过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
