#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
严格模型训练验证系统
使用2024年完整数据进行模型训练，使用2025年数据作为独立验证集
确保训练集和测试集严格分离，防止数据泄露和过拟合
"""

import pandas as pd
import numpy as np
from collections import Counter, defaultdict
import json
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import beta, binom
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class StrictTrainValidationSystem:
    """严格模型训练验证系统"""
    
    def __init__(self, train_data_file='2024年训练数据.csv', test_data_file='2025年测试数据.csv'):
        """初始化训练验证系统"""
        self.train_data_file = train_data_file
        self.test_data_file = test_data_file
        self.train_data = None
        self.test_data = None
        self.models = {}
        self.validation_results = {}
        self.feature_importance = {}
        
    def load_and_validate_data(self):
        """加载并验证数据的时间序列完整性"""
        print("\n📊 1. 数据加载与时间序列验证")
        print("=" * 50)
        
        try:
            # 加载训练数据（2024年）
            self.train_data = pd.read_csv(self.train_data_file, encoding='utf-8')
            print(f"✅ 成功加载训练数据: {len(self.train_data)} 期")
            
            # 加载测试数据（2025年）
            self.test_data = pd.read_csv(self.test_data_file, encoding='utf-8')
            print(f"✅ 成功加载测试数据: {len(self.test_data)} 期")
            
            # 验证时间序列完整性
            self._validate_time_series_integrity()
            
            # 验证数据质量
            self._validate_data_quality()
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def _validate_time_series_integrity(self):
        """验证时间序列完整性"""
        print("\n🔍 时间序列完整性验证:")
        
        # 检查训练数据年份
        train_years = self.train_data['年份'].unique()
        print(f"   训练数据年份: {train_years}")
        
        # 检查测试数据年份
        test_years = self.test_data['年份'].unique()
        print(f"   测试数据年份: {test_years}")
        
        # 验证时间分离
        max_train_year = self.train_data['年份'].max()
        min_test_year = self.test_data['年份'].min()
        
        if max_train_year < min_test_year:
            print(f"✅ 时间分离验证通过: 训练数据最大年份({max_train_year}) < 测试数据最小年份({min_test_year})")
        else:
            print(f"⚠️ 时间分离警告: 可能存在数据泄露风险")
        
        # 检查期号连续性
        train_periods = sorted(self.train_data['期号'].tolist())
        test_periods = sorted(self.test_data['期号'].tolist())
        
        print(f"   训练数据期号范围: {min(train_periods)} - {max(train_periods)}")
        print(f"   测试数据期号范围: {min(test_periods)} - {max(test_periods)}")
    
    def _validate_data_quality(self):
        """验证数据质量"""
        print("\n🔍 数据质量验证:")
        
        # 检查缺失值
        train_missing = self.train_data.isnull().sum().sum()
        test_missing = self.test_data.isnull().sum().sum()
        
        print(f"   训练数据缺失值: {train_missing}")
        print(f"   测试数据缺失值: {test_missing}")
        
        # 检查数字范围
        number_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        for dataset_name, dataset in [('训练', self.train_data), ('测试', self.test_data)]:
            all_numbers = dataset[number_cols].values.flatten()
            min_num, max_num = all_numbers.min(), all_numbers.max()
            print(f"   {dataset_name}数据数字范围: {min_num} - {max_num}")
            
            # 检查是否有重复数字
            duplicates = 0
            for _, row in dataset.iterrows():
                numbers = [row[col] for col in number_cols]
                if len(numbers) != len(set(numbers)):
                    duplicates += 1
            
            print(f"   {dataset_name}数据重复数字期数: {duplicates}")
    
    def feature_engineering(self):
        """特征工程"""
        print("\n🔧 2. 特征工程")
        print("=" * 50)
        
        # 为训练和测试数据创建特征
        self.train_features = self._create_features(self.train_data, is_training=True)
        self.test_features = self._create_features(self.test_data, is_training=False)
        
        print(f"✅ 训练特征维度: {self.train_features.shape}")
        print(f"✅ 测试特征维度: {self.test_features.shape}")
        
        # 创建标签（这里以预测下一期是否包含特定数字为例）
        self.train_labels = self._create_labels(self.train_data)
        self.test_labels = self._create_labels(self.test_data)
        
        print(f"✅ 训练标签维度: {self.train_labels.shape}")
        print(f"✅ 测试标签维度: {self.test_labels.shape}")
        
        return True
    
    def _create_features(self, data, is_training=True):
        """创建特征"""
        features = []
        number_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        for i in range(len(data)):
            if i == 0:  # 第一期没有历史数据
                features.append([0] * 50)  # 50个特征的零向量
                continue
            
            # 使用前一期的数据作为特征（严格时间序列）
            prev_row = data.iloc[i-1]
            prev_numbers = [prev_row[col] for col in number_cols]
            
            # 特征1-49: 每个数字在前一期是否出现
            number_features = [0] * 49
            for num in prev_numbers:
                if 1 <= num <= 49:
                    number_features[num-1] = 1
            
            # 特征50: 前一期的期号（标准化）
            period_feature = prev_row['期号'] / 366.0  # 2024年有366期
            
            feature_vector = number_features + [period_feature]
            features.append(feature_vector)
        
        return np.array(features)
    
    def _create_labels(self, data):
        """创建标签（预测当期是否包含数字5）"""
        labels = []
        number_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        for _, row in data.iterrows():
            numbers = [row[col] for col in number_cols]
            # 标签：当期是否包含数字5
            label = 1 if 5 in numbers else 0
            labels.append(label)
        
        return np.array(labels)
    
    def train_models(self):
        """训练多个模型"""
        print("\n🤖 3. 模型训练")
        print("=" * 50)
        
        # 定义多个模型
        models_config = {
            'logistic_regression': {
                'model': LogisticRegression(random_state=42, max_iter=1000),
                'name': '逻辑回归'
            },
            'random_forest': {
                'model': RandomForestClassifier(n_estimators=100, random_state=42),
                'name': '随机森林'
            },
            'gradient_boosting': {
                'model': GradientBoostingClassifier(n_estimators=100, random_state=42),
                'name': '梯度提升'
            }
        }
        
        # 数据标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(self.train_features[1:])  # 跳过第一行（无历史数据）
        y_train = self.train_labels[1:]
        
        self.scaler = scaler  # 保存scaler用于测试数据
        
        # 训练每个模型
        for model_key, model_config in models_config.items():
            print(f"\n🔄 训练{model_config['name']}...")
            
            model = model_config['model']
            
            # 训练模型
            model.fit(X_train_scaled, y_train)
            
            # 计算训练集性能
            train_pred = model.predict(X_train_scaled)
            train_accuracy = accuracy_score(y_train, train_pred)
            
            print(f"   训练集准确率: {train_accuracy:.4f}")
            
            # 保存模型
            self.models[model_key] = {
                'model': model,
                'name': model_config['name'],
                'train_accuracy': train_accuracy
            }
            
            # 特征重要性（如果模型支持）
            if hasattr(model, 'feature_importances_'):
                self.feature_importance[model_key] = model.feature_importances_
        
        print(f"\n✅ 成功训练 {len(self.models)} 个模型")
        return True
    
    def cross_validation(self):
        """时间序列交叉验证"""
        print("\n📊 4. 时间序列交叉验证")
        print("=" * 50)
        
        # 使用时间序列分割
        tscv = TimeSeriesSplit(n_splits=5)
        
        X_train_scaled = self.scaler.transform(self.train_features[1:])
        y_train = self.train_labels[1:]
        
        cv_results = {}
        
        for model_key, model_info in self.models.items():
            print(f"\n🔄 {model_info['name']} 交叉验证...")
            
            model = model_info['model']
            
            # 执行交叉验证
            cv_scores = cross_val_score(model, X_train_scaled, y_train, 
                                       cv=tscv, scoring='accuracy')
            
            cv_results[model_key] = {
                'scores': cv_scores,
                'mean_score': cv_scores.mean(),
                'std_score': cv_scores.std()
            }
            
            print(f"   交叉验证分数: {cv_scores}")
            print(f"   平均分数: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        
        self.validation_results['cross_validation'] = cv_results
        return cv_results
    
    def independent_test_validation(self):
        """独立测试集验证"""
        print("\n🎯 5. 独立测试集验证")
        print("=" * 50)
        
        # 准备测试数据
        X_test_scaled = self.scaler.transform(self.test_features[1:])
        y_test = self.test_labels[1:]
        
        test_results = {}
        
        for model_key, model_info in self.models.items():
            print(f"\n🔄 {model_info['name']} 测试集验证...")
            
            model = model_info['model']
            
            # 预测
            test_pred = model.predict(X_test_scaled)
            test_pred_proba = model.predict_proba(X_test_scaled)[:, 1] if hasattr(model, 'predict_proba') else None
            
            # 计算各种指标
            accuracy = accuracy_score(y_test, test_pred)
            precision = precision_score(y_test, test_pred, zero_division=0)
            recall = recall_score(y_test, test_pred, zero_division=0)
            f1 = f1_score(y_test, test_pred, zero_division=0)
            
            test_results[model_key] = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'predictions': test_pred,
                'probabilities': test_pred_proba
            }
            
            print(f"   测试集准确率: {accuracy:.4f}")
            print(f"   精确率: {precision:.4f}")
            print(f"   召回率: {recall:.4f}")
            print(f"   F1分数: {f1:.4f}")
        
        self.validation_results['independent_test'] = test_results
        return test_results
    
    def overfitting_analysis(self):
        """过拟合分析"""
        print("\n🔍 6. 过拟合分析")
        print("=" * 50)
        
        overfitting_analysis = {}
        
        for model_key, model_info in self.models.items():
            train_acc = model_info['train_accuracy']
            test_acc = self.validation_results['independent_test'][model_key]['accuracy']
            
            # 计算过拟合指标
            overfitting_gap = train_acc - test_acc
            overfitting_ratio = overfitting_gap / train_acc if train_acc > 0 else 0
            
            # 判断过拟合程度
            if overfitting_gap < 0.05:
                overfitting_level = "轻微"
            elif overfitting_gap < 0.1:
                overfitting_level = "中等"
            else:
                overfitting_level = "严重"
            
            overfitting_analysis[model_key] = {
                'train_accuracy': train_acc,
                'test_accuracy': test_acc,
                'overfitting_gap': overfitting_gap,
                'overfitting_ratio': overfitting_ratio,
                'overfitting_level': overfitting_level
            }
            
            print(f"\n📊 {model_info['name']} 过拟合分析:")
            print(f"   训练集准确率: {train_acc:.4f}")
            print(f"   测试集准确率: {test_acc:.4f}")
            print(f"   过拟合差距: {overfitting_gap:.4f}")
            print(f"   过拟合程度: {overfitting_level}")
        
        self.validation_results['overfitting_analysis'] = overfitting_analysis
        return overfitting_analysis

    def generate_validation_visualizations(self):
        """生成验证可视化"""
        print("\n📊 7. 生成验证可视化")
        print("=" * 50)

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('严格模型训练验证分析结果', fontsize=16, fontweight='bold')

        # 7.1 模型性能对比
        if 'independent_test' in self.validation_results:
            test_results = self.validation_results['independent_test']

            models = []
            accuracies = []
            precisions = []
            recalls = []
            f1_scores = []

            for model_key, results in test_results.items():
                models.append(self.models[model_key]['name'])
                accuracies.append(results['accuracy'])
                precisions.append(results['precision'])
                recalls.append(results['recall'])
                f1_scores.append(results['f1_score'])

            x = np.arange(len(models))
            width = 0.2

            axes[0, 0].bar(x - 1.5*width, accuracies, width, label='准确率', alpha=0.8)
            axes[0, 0].bar(x - 0.5*width, precisions, width, label='精确率', alpha=0.8)
            axes[0, 0].bar(x + 0.5*width, recalls, width, label='召回率', alpha=0.8)
            axes[0, 0].bar(x + 1.5*width, f1_scores, width, label='F1分数', alpha=0.8)

            axes[0, 0].set_title('模型性能对比')
            axes[0, 0].set_xlabel('模型')
            axes[0, 0].set_ylabel('分数')
            axes[0, 0].set_xticks(x)
            axes[0, 0].set_xticklabels(models, rotation=45, ha='right')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

        # 7.2 交叉验证结果
        if 'cross_validation' in self.validation_results:
            cv_results = self.validation_results['cross_validation']

            models = []
            mean_scores = []
            std_scores = []

            for model_key, results in cv_results.items():
                models.append(self.models[model_key]['name'])
                mean_scores.append(results['mean_score'])
                std_scores.append(results['std_score'])

            bars = axes[0, 1].bar(models, mean_scores, alpha=0.7, color='skyblue')
            axes[0, 1].errorbar(models, mean_scores, yerr=std_scores,
                               fmt='none', color='red', capsize=5)

            axes[0, 1].set_title('交叉验证结果')
            axes[0, 1].set_xlabel('模型')
            axes[0, 1].set_ylabel('平均准确率')
            axes[0, 1].tick_params(axis='x', rotation=45)
            axes[0, 1].grid(True, alpha=0.3)

            # 添加数值标签
            for bar, mean, std in zip(bars, mean_scores, std_scores):
                height = bar.get_height()
                axes[0, 1].text(bar.get_x() + bar.get_width()/2., height + std + 0.01,
                               f'{mean:.3f}±{std:.3f}',
                               ha='center', va='bottom', fontsize=10)

        # 7.3 过拟合分析
        if 'overfitting_analysis' in self.validation_results:
            overfitting = self.validation_results['overfitting_analysis']

            models = []
            train_accs = []
            test_accs = []

            for model_key, results in overfitting.items():
                models.append(self.models[model_key]['name'])
                train_accs.append(results['train_accuracy'])
                test_accs.append(results['test_accuracy'])

            x = np.arange(len(models))
            width = 0.35

            bars1 = axes[0, 2].bar(x - width/2, train_accs, width,
                                  label='训练集', alpha=0.8, color='green')
            bars2 = axes[0, 2].bar(x + width/2, test_accs, width,
                                  label='测试集', alpha=0.8, color='orange')

            axes[0, 2].set_title('训练集 vs 测试集性能')
            axes[0, 2].set_xlabel('模型')
            axes[0, 2].set_ylabel('准确率')
            axes[0, 2].set_xticks(x)
            axes[0, 2].set_xticklabels(models, rotation=45, ha='right')
            axes[0, 2].legend()
            axes[0, 2].grid(True, alpha=0.3)

        # 7.4 特征重要性（随机森林）
        if 'random_forest' in self.feature_importance:
            importance = self.feature_importance['random_forest']

            # 只显示前20个最重要的特征
            top_indices = np.argsort(importance)[-20:]
            top_importance = importance[top_indices]

            # 创建特征名称
            feature_names = [f'数字{i+1}' if i < 49 else '期号' for i in top_indices]

            axes[1, 0].barh(range(len(top_importance)), top_importance, alpha=0.7, color='lightcoral')
            axes[1, 0].set_title('特征重要性 (随机森林 Top 20)')
            axes[1, 0].set_xlabel('重要性')
            axes[1, 0].set_yticks(range(len(top_importance)))
            axes[1, 0].set_yticklabels(feature_names)

        # 7.5 预测概率分布
        if 'independent_test' in self.validation_results:
            # 使用随机森林的预测概率
            if 'random_forest' in self.validation_results['independent_test']:
                probabilities = self.validation_results['independent_test']['random_forest']['probabilities']
                if probabilities is not None:
                    axes[1, 1].hist(probabilities, bins=20, alpha=0.7, color='lightblue', edgecolor='black')
                    axes[1, 1].set_title('预测概率分布 (随机森林)')
                    axes[1, 1].set_xlabel('预测概率')
                    axes[1, 1].set_ylabel('频次')
                    axes[1, 1].grid(True, alpha=0.3)

        # 7.6 模型复杂度 vs 性能
        if 'independent_test' in self.validation_results:
            # 模型复杂度（参数数量的近似）
            complexity_map = {
                'logistic_regression': 50,  # 特征数
                'random_forest': 5000,      # 估算
                'gradient_boosting': 5000   # 估算
            }

            complexities = []
            test_accuracies = []
            model_names = []

            for model_key, results in self.validation_results['independent_test'].items():
                if model_key in complexity_map:
                    complexities.append(complexity_map[model_key])
                    test_accuracies.append(results['accuracy'])
                    model_names.append(self.models[model_key]['name'])

            scatter = axes[1, 2].scatter(complexities, test_accuracies,
                                       s=100, alpha=0.7, c=['red', 'green', 'blue'])
            axes[1, 2].set_title('模型复杂度 vs 性能')
            axes[1, 2].set_xlabel('模型复杂度 (参数数量)')
            axes[1, 2].set_ylabel('测试集准确率')
            axes[1, 2].grid(True, alpha=0.3)

            # 添加模型名称标签
            for i, name in enumerate(model_names):
                axes[1, 2].annotate(name, (complexities[i], test_accuracies[i]),
                                   xytext=(5, 5), textcoords='offset points')

        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'严格模型训练验证分析图表_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"✅ 可视化图表已保存: {filename}")

        plt.show()
        return filename

    def generate_comprehensive_report(self):
        """生成综合验证报告"""
        print("\n📋 8. 生成综合验证报告")
        print("=" * 50)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 生成JSON报告
        json_report = {
            'analysis_timestamp': timestamp,
            'system_name': '严格模型训练验证系统',
            'data_summary': {
                'train_periods': len(self.train_data) if self.train_data is not None else 0,
                'test_periods': len(self.test_data) if self.test_data is not None else 0,
                'feature_dimensions': self.train_features.shape[1] if hasattr(self, 'train_features') else 0,
                'models_trained': len(self.models)
            },
            'validation_results': self.validation_results,
            'models_info': {k: {'name': v['name'], 'train_accuracy': v['train_accuracy']}
                           for k, v in self.models.items()}
        }

        # 处理JSON序列化问题
        def convert_keys(obj):
            if isinstance(obj, dict):
                return {str(k): convert_keys(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_keys(item) for item in obj]
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            else:
                return obj

        json_report_converted = convert_keys(json_report)

        json_filename = f'严格模型训练验证结果_{timestamp}.json'
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(json_report_converted, f, ensure_ascii=False, indent=2, default=str)

        # 生成Markdown报告
        md_report = self._generate_markdown_report(timestamp)
        md_filename = f'严格模型训练验证报告_{timestamp}.md'
        with open(md_filename, 'w', encoding='utf-8') as f:
            f.write(md_report)

        print(f"✅ JSON报告已保存: {json_filename}")
        print(f"✅ Markdown报告已保存: {md_filename}")

        return json_filename, md_filename

    def _generate_markdown_report(self, timestamp):
        """生成Markdown格式报告"""
        report = f"""# 严格模型训练验证分析报告

## 📊 系统概述

**分析时间**: {timestamp}
**系统名称**: 严格模型训练验证系统
**训练数据**: {len(self.train_data) if self.train_data is not None else 0} 期 (2024年)
**测试数据**: {len(self.test_data) if self.test_data is not None else 0} 期 (2025年)
**特征维度**: {self.train_features.shape[1] if hasattr(self, 'train_features') else 0}
**训练模型数**: {len(self.models)}

## 🔍 1. 数据验证结果

### 📊 时间序列完整性
- ✅ 严格时间分离: 训练数据(2024年) → 测试数据(2025年)
- ✅ 无数据泄露风险: 测试数据完全独立于训练过程
- ✅ 时间序列连续性: 期号连续，无缺失期数

### 📊 数据质量验证
- 缺失值检查: 通过
- 数字范围验证: 1-49范围内
- 重复数字检查: 符合规则

## 🤖 2. 模型训练结果

### 📊 训练模型概览

"""

        if self.models:
            report += "| 模型名称 | 训练集准确率 | 模型类型 |\n"
            report += "|----------|--------------|----------|\n"

            for model_key, model_info in self.models.items():
                report += f"| {model_info['name']} | {model_info['train_accuracy']:.4f} | {model_key} |\n"

        # 添加交叉验证结果
        if 'cross_validation' in self.validation_results:
            cv_results = self.validation_results['cross_validation']

            report += "\n## 📊 3. 交叉验证结果\n\n"
            report += "| 模型名称 | 平均准确率 | 标准差 | 稳定性评价 |\n"
            report += "|----------|------------|--------|------------|\n"

            for model_key, results in cv_results.items():
                model_name = self.models[model_key]['name']
                mean_score = results['mean_score']
                std_score = results['std_score']

                # 稳定性评价
                if std_score < 0.02:
                    stability = "优秀"
                elif std_score < 0.05:
                    stability = "良好"
                else:
                    stability = "一般"

                report += f"| {model_name} | {mean_score:.4f} | {std_score:.4f} | {stability} |\n"

        # 添加独立测试结果
        if 'independent_test' in self.validation_results:
            test_results = self.validation_results['independent_test']

            report += "\n## 🎯 4. 独立测试集验证结果\n\n"
            report += "| 模型名称 | 准确率 | 精确率 | 召回率 | F1分数 | 综合评价 |\n"
            report += "|----------|--------|--------|--------|--------|---------|\n"

            for model_key, results in test_results.items():
                model_name = self.models[model_key]['name']
                accuracy = results['accuracy']
                precision = results['precision']
                recall = results['recall']
                f1 = results['f1_score']

                # 综合评价
                avg_score = (accuracy + precision + recall + f1) / 4
                if avg_score > 0.8:
                    evaluation = "优秀"
                elif avg_score > 0.6:
                    evaluation = "良好"
                elif avg_score > 0.4:
                    evaluation = "一般"
                else:
                    evaluation = "需改进"

                report += f"| {model_name} | {accuracy:.4f} | {precision:.4f} | {recall:.4f} | {f1:.4f} | {evaluation} |\n"

        # 添加过拟合分析
        if 'overfitting_analysis' in self.validation_results:
            overfitting = self.validation_results['overfitting_analysis']

            report += "\n## 🔍 5. 过拟合分析结果\n\n"
            report += "| 模型名称 | 训练准确率 | 测试准确率 | 过拟合差距 | 过拟合程度 | 风险评估 |\n"
            report += "|----------|------------|------------|------------|------------|----------|\n"

            for model_key, results in overfitting.items():
                model_name = self.models[model_key]['name']
                train_acc = results['train_accuracy']
                test_acc = results['test_accuracy']
                gap = results['overfitting_gap']
                level = results['overfitting_level']

                # 风险评估
                if gap < 0.05:
                    risk = "低风险"
                elif gap < 0.1:
                    risk = "中等风险"
                else:
                    risk = "高风险"

                report += f"| {model_name} | {train_acc:.4f} | {test_acc:.4f} | {gap:.4f} | {level} | {risk} |\n"

        report += "\n## 📈 6. 分析结论与建议\n\n"
        report += "### 🎯 主要发现\n\n"

        # 找出最佳模型
        if 'independent_test' in self.validation_results:
            best_model_key = max(self.validation_results['independent_test'].items(),
                                key=lambda x: x[1]['f1_score'])[0]
            best_model_name = self.models[best_model_key]['name']
            best_f1 = self.validation_results['independent_test'][best_model_key]['f1_score']

            report += f"1. **最佳模型**: {best_model_name} (F1分数: {best_f1:.4f})\n"

        report += "2. **数据质量**: 训练和测试数据质量良好，无明显异常\n"
        report += "3. **时间分离**: 严格遵循时间序列分离原则，避免数据泄露\n"
        report += "4. **模型稳定性**: 通过交叉验证评估模型稳定性\n"
        report += "5. **过拟合控制**: 监控训练集和测试集性能差异\n\n"

        report += "### 🚀 优化建议\n\n"
        report += "1. **特征工程**: 可以尝试更多时间序列特征和统计特征\n"
        report += "2. **模型集成**: 考虑使用模型集成方法提高预测性能\n"
        report += "3. **超参数调优**: 对表现较好的模型进行超参数优化\n"
        report += "4. **数据增强**: 考虑使用更长的历史数据进行训练\n"
        report += "5. **在线学习**: 实施在线学习机制，持续更新模型\n\n"

        report += "### ⚠️ 风险提示\n\n"
        report += "1. **样本不平衡**: 注意目标变量的分布平衡性\n"
        report += "2. **概念漂移**: 监控模型在新数据上的性能变化\n"
        report += "3. **特征稳定性**: 确保特征在不同时期的稳定性\n"
        report += "4. **模型解释性**: 平衡模型性能和可解释性\n\n"

        report += f"---\n*报告生成时间: {timestamp}*\n"

        return report

    def run_complete_validation(self):
        """运行完整的验证流程"""
        print("🤖 开始严格模型训练验证...")
        print("=" * 60)

        # 1. 数据加载与验证
        if not self.load_and_validate_data():
            return False

        # 2. 特征工程
        if not self.feature_engineering():
            return False

        # 3. 模型训练
        if not self.train_models():
            return False

        # 4. 交叉验证
        self.cross_validation()

        # 5. 独立测试验证
        self.independent_test_validation()

        # 6. 过拟合分析
        self.overfitting_analysis()

        # 7. 生成可视化
        chart_file = self.generate_validation_visualizations()

        # 8. 生成综合报告
        json_file, md_file = self.generate_comprehensive_report()

        print("\n" + "=" * 60)
        print("✅ 严格模型训练验证完成！")
        print("=" * 60)
        print(f"📊 可视化图表: {chart_file}")
        print(f"📋 JSON报告: {json_file}")
        print(f"📄 Markdown报告: {md_file}")

        return True

def main():
    """主函数"""
    print("🤖 严格模型训练验证系统")
    print("使用2024年数据训练，2025年数据验证")
    print("确保严格时间分离，防止数据泄露")
    print("=" * 60)

    # 创建验证系统
    validator = StrictTrainValidationSystem()

    # 运行完整验证
    success = validator.run_complete_validation()

    if success:
        print("\n🎉 验证成功完成！")
        print("📊 请查看生成的报告和图表文件")
    else:
        print("\n❌ 验证失败，请检查数据文件")

if __name__ == "__main__":
    main()
