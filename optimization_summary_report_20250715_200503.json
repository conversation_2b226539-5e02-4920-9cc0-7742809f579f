{"optimization_summary": {"optimization_date": "2025-07-15T20:05:03.383539", "target_period": 195, "system_version": "优化版 v2.1", "optimization_status": "成功完成"}, "prediction_results": {"predicted_numbers": [14, 17], "base_numbers": [6.0, 8.0, 12.0, 22.0, 27.0, 42.0], "confidence_analysis": {"original_confidence": 0.029140900000000004, "adjusted_confidence": 0.1, "final_confidence": 0.13, "adjustment_factors": {"accuracy_factor": 1.3, "calibration_factor": 0.8500000000000001, "stability_factor": 1.03, "trend_factor": 0.9929230769230769, "composite_factor": 1.0802923076923079}, "adjustment_ratio": 1.080292307692308, "calibration_applied": true, "confidence_interval": "区间1: 0.1-0.2"}, "confidence_interpretation": "低置信度 - 预测可靠性很低"}, "optimization_improvements": {"confidence_range_expansion": "0.01-0.95 → 0.1-0.8", "adjustment_factor_enhancement": "0.3-3.0 → 0.3-5.0", "calibration_method_upgrade": "线性调整 → Isotonic Regression", "multi_dimensional_weighting": "准确率40% + 校准30% + 稳定性20% + 趋势10%", "adaptive_model_update": "每10期自动更新校准模型", "confidence_interval_segmentation": "7个置信度区间细分"}, "performance_metrics": {"confidence_improvement_ratio": 4.461083906125068, "adjustment_factor_stability": 1.0802923076923079, "calibration_applied": true, "confidence_interval": "区间1: 0.1-0.2"}, "comparison_with_original": {"original_typical_confidence": 0.05, "optimized_confidence": 0.13, "improvement_percentage": 346.10839061250675, "original_calibration_error": 0.391, "expected_calibration_improvement": "显著改善（待验证）"}, "next_steps": ["收集第195期实际结果进行验证", "基于实际结果调整校准参数", "扩展优化到更多历史期数", "建立长期性能监控机制", "优化预测算法本身"]}