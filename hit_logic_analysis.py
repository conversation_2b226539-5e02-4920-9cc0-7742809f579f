#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
命中逻辑分析
Analysis of hit detection logic and scoring algorithm issues
"""

import pandas as pd
import numpy as np

def analyze_hit_detection_errors():
    """分析命中检测错误"""
    print("🔍 分析命中检测逻辑错误")
    print("="*60)
    
    try:
        # 加载预测数据
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        
        print(f"总预测记录: {len(df)}")
        
        # 查找有实际开奖数据的记录
        valid_data = df[
            df['实际数字1'].notna() & 
            (df['实际数字1'] != '') &
            df['预测数字1'].notna() & 
            df['预测数字2'].notna()
        ].copy()
        
        print(f"有效验证记录: {len(valid_data)}")
        
        # 手动重新计算命中情况
        errors_found = []
        
        for idx, row in valid_data.iterrows():
            # 获取预测数字
            pred_num1 = int(float(row['预测数字1']))
            pred_num2 = int(float(row['预测数字2']))
            predicted_numbers = [pred_num1, pred_num2]
            
            # 获取实际开奖数字
            try:
                actual_numbers = [
                    int(float(row['实际数字1'])),
                    int(float(row['实际数字2'])),
                    int(float(row['实际数字3'])),
                    int(float(row['实际数字4'])),
                    int(float(row['实际数字5'])),
                    int(float(row['实际数字6']))
                ]
            except (ValueError, TypeError):
                continue
            
            # 重新计算命中情况
            hit_numbers = []
            for pred_num in predicted_numbers:
                if pred_num in actual_numbers:
                    hit_numbers.append(pred_num)
            
            actual_hit_count = len(hit_numbers)
            should_hit = actual_hit_count > 0
            
            # 获取记录中的命中状态
            recorded_hit = str(row['是否命中']).strip()
            recorded_hit_count = row['命中数量'] if pd.notna(row['命中数量']) else 0
            
            # 检查是否有错误
            if should_hit and recorded_hit == '否':
                errors_found.append({
                    'index': idx,
                    'period': f"{int(row['当期年份'])}年{int(row['当期期号'])}期",
                    'predicted': predicted_numbers,
                    'actual': actual_numbers,
                    'should_hit': True,
                    'recorded_hit': False,
                    'actual_hit_numbers': hit_numbers,
                    'actual_hit_count': actual_hit_count,
                    'recorded_hit_count': recorded_hit_count,
                    'error_type': '应该命中但记录为未命中'
                })
            elif not should_hit and recorded_hit == '是':
                errors_found.append({
                    'index': idx,
                    'period': f"{int(row['当期年份'])}年{int(row['当期期号'])}期",
                    'predicted': predicted_numbers,
                    'actual': actual_numbers,
                    'should_hit': False,
                    'recorded_hit': True,
                    'actual_hit_numbers': hit_numbers,
                    'actual_hit_count': actual_hit_count,
                    'recorded_hit_count': recorded_hit_count,
                    'error_type': '不应该命中但记录为命中'
                })
            elif should_hit and recorded_hit == '是' and actual_hit_count != recorded_hit_count:
                errors_found.append({
                    'index': idx,
                    'period': f"{int(row['当期年份'])}年{int(row['当期期号'])}期",
                    'predicted': predicted_numbers,
                    'actual': actual_numbers,
                    'should_hit': True,
                    'recorded_hit': True,
                    'actual_hit_numbers': hit_numbers,
                    'actual_hit_count': actual_hit_count,
                    'recorded_hit_count': recorded_hit_count,
                    'error_type': '命中数量计算错误'
                })
        
        print(f"\n🚨 发现 {len(errors_found)} 个命中检测错误:")
        
        for error in errors_found:
            print(f"\n❌ 错误记录 #{error['index']+1}:")
            print(f"   期号: {error['period']}")
            print(f"   预测: {error['predicted']}")
            print(f"   实际: {error['actual']}")
            print(f"   应该命中: {error['actual_hit_numbers']} ({error['actual_hit_count']}个)")
            print(f"   记录状态: {'命中' if error['recorded_hit'] else '未命中'}")
            print(f"   记录数量: {error['recorded_hit_count']}")
            print(f"   错误类型: {error['error_type']}")
        
        return errors_found
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def analyze_specific_case():
    """分析具体的错误案例"""
    print(f"\n🔍 分析具体错误案例")
    print("="*40)
    
    # 分析第197行的案例
    print("案例1: 2025年197期预测198期")
    print("   当期开奖: [10, 12, 15, 24, 25, 43]")
    print("   预测数字: [43, 2]")
    print("   下期实际: [3, 7, 15, 33, 39, 45]")
    print("   分析:")
    print("     - 预测数字43不在实际开奖[3,7,15,33,39,45]中")
    print("     - 预测数字2不在实际开奖[3,7,15,33,39,45]中")
    print("     - 应该判断为: 未命中 ❌")
    print("     - 记录状态: 未命中 ❌")
    print("     - 结论: 这个记录是正确的！")
    
    print(f"\n让我查找真正的错误案例...")

def find_real_hit_errors():
    """查找真正的命中错误"""
    print(f"\n🔍 查找真正的命中判断错误")
    print("="*50)
    
    try:
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        
        # 查找所有有完整数据的记录
        complete_data = df[
            df['实际数字1'].notna() & 
            (df['实际数字1'] != '') &
            df['预测数字1'].notna() & 
            df['预测数字2'].notna() &
            df['是否命中'].notna()
        ].copy()
        
        print(f"完整数据记录: {len(complete_data)}")
        
        real_errors = []
        
        for idx, row in complete_data.iterrows():
            try:
                # 预测数字
                pred1 = int(float(row['预测数字1']))
                pred2 = int(float(row['预测数字2']))
                
                # 实际开奖数字
                actual = [
                    int(float(row['实际数字1'])),
                    int(float(row['实际数字2'])),
                    int(float(row['实际数字3'])),
                    int(float(row['实际数字4'])),
                    int(float(row['实际数字5'])),
                    int(float(row['实际数字6']))
                ]
                
                # 计算真实命中情况
                hit_nums = []
                if pred1 in actual:
                    hit_nums.append(pred1)
                if pred2 in actual:
                    hit_nums.append(pred2)
                
                true_hit = len(hit_nums) > 0
                true_hit_count = len(hit_nums)
                
                # 记录的命中情况
                recorded_hit = str(row['是否命中']).strip() == '是'
                recorded_count = float(row['命中数量']) if pd.notna(row['命中数量']) else 0
                
                # 检查错误
                if true_hit != recorded_hit or (true_hit and true_hit_count != recorded_count):
                    real_errors.append({
                        'row': idx + 1,
                        'period': f"{int(row['当期年份'])}年{int(row['当期期号'])}期",
                        'predicted': [pred1, pred2],
                        'actual': actual,
                        'true_hit': true_hit,
                        'true_hit_count': true_hit_count,
                        'true_hit_numbers': hit_nums,
                        'recorded_hit': recorded_hit,
                        'recorded_count': recorded_count,
                        'score': row['预测评分'],
                        'grade': row['评分等级']
                    })
                    
            except (ValueError, TypeError) as e:
                continue
        
        print(f"\n🚨 发现 {len(real_errors)} 个真实错误:")
        
        for i, error in enumerate(real_errors[:10]):  # 只显示前10个
            print(f"\n❌ 错误 #{i+1} (第{error['row']}行):")
            print(f"   期号: {error['period']}")
            print(f"   预测: {error['predicted']}")
            print(f"   实际: {error['actual']}")
            print(f"   真实命中: {'是' if error['true_hit'] else '否'} ({error['true_hit_count']}个: {error['true_hit_numbers']})")
            print(f"   记录命中: {'是' if error['recorded_hit'] else '否'} ({error['recorded_count']}个)")
            print(f"   评分: {error['score']} ({error['grade']})")
        
        return real_errors
        
    except Exception as e:
        print(f"❌ 查找失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def analyze_scoring_logic_issues():
    """分析评分逻辑问题"""
    print(f"\n📊 分析评分逻辑问题")
    print("="*40)
    
    try:
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        
        # 查找评分异常的记录
        scoring_issues = []
        
        for idx, row in df.iterrows():
            try:
                score = float(row['预测评分'])
                grade = str(row['评分等级'])
                confidence = float(row['预测置信度'])
                
                # 检查评分与等级的一致性
                if score >= 80 and 'A+' not in grade:
                    scoring_issues.append({
                        'row': idx + 1,
                        'issue': '高分但等级不匹配',
                        'score': score,
                        'grade': grade,
                        'confidence': confidence
                    })
                elif score >= 70 and score < 80 and 'A' not in grade:
                    scoring_issues.append({
                        'row': idx + 1,
                        'issue': '中高分但等级不匹配',
                        'score': score,
                        'grade': grade,
                        'confidence': confidence
                    })
                elif score < 50 and 'D' not in grade:
                    scoring_issues.append({
                        'row': idx + 1,
                        'issue': '低分但等级不匹配',
                        'score': score,
                        'grade': grade,
                        'confidence': confidence
                    })
                
                # 检查异常高分
                if score > 95:
                    scoring_issues.append({
                        'row': idx + 1,
                        'issue': '异常高分',
                        'score': score,
                        'grade': grade,
                        'confidence': confidence
                    })
                
            except (ValueError, TypeError):
                continue
        
        print(f"发现 {len(scoring_issues)} 个评分问题:")
        
        for issue in scoring_issues[:10]:
            print(f"   第{issue['row']}行: {issue['issue']} - 评分:{issue['score']} 等级:{issue['grade']}")
        
        return scoring_issues
        
    except Exception as e:
        print(f"❌ 评分分析失败: {e}")
        return []

def create_fix_script():
    """创建修复脚本"""
    print(f"\n🔧 创建命中判断修复脚本")
    print("="*40)
    
    fix_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复命中判断错误
Fix hit detection errors in prediction data
"""

import pandas as pd
import numpy as np

def fix_hit_detection():
    """修复命中检测错误"""
    print("🔧 修复命中检测错误")
    print("="*40)
    
    # 加载数据
    df = pd.read_csv('prediction_data.csv', encoding='utf-8')
    
    # 备份原文件
    df.to_csv('prediction_data_backup_before_fix.csv', index=False, encoding='utf-8')
    print("✅ 原文件已备份")
    
    fixes_made = 0
    
    for idx, row in df.iterrows():
        # 检查是否有完整的预测和实际数据
        if (pd.notna(row['预测数字1']) and pd.notna(row['预测数字2']) and 
            pd.notna(row['实际数字1']) and row['实际数字1'] != ''):
            
            try:
                # 获取预测数字
                pred1 = int(float(row['预测数字1']))
                pred2 = int(float(row['预测数字2']))
                
                # 获取实际数字
                actual = [
                    int(float(row['实际数字1'])),
                    int(float(row['实际数字2'])),
                    int(float(row['实际数字3'])),
                    int(float(row['实际数字4'])),
                    int(float(row['实际数字5'])),
                    int(float(row['实际数字6']))
                ]
                
                # 重新计算命中情况
                hit_numbers = []
                if pred1 in actual:
                    hit_numbers.append(pred1)
                if pred2 in actual:
                    hit_numbers.append(pred2)
                
                hit_count = len(hit_numbers)
                is_hit = hit_count > 0
                
                # 更新记录
                df.loc[idx, '命中数量'] = hit_count
                df.loc[idx, '是否命中'] = '是' if is_hit else '否'
                df.loc[idx, '命中数字'] = ','.join(map(str, hit_numbers)) if hit_numbers else ''
                
                fixes_made += 1
                
            except (ValueError, TypeError):
                continue
    
    # 保存修复后的文件
    df.to_csv('prediction_data.csv', index=False, encoding='utf-8')
    print(f"✅ 修复完成，共处理 {fixes_made} 条记录")
    
    return fixes_made

if __name__ == "__main__":
    fix_hit_detection()
'''
    
    with open('fix_hit_detection.py', 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    print("✅ 修复脚本已创建: fix_hit_detection.py")

def main():
    """主函数"""
    print("🔍 命中逻辑和评分算法问题分析")
    print("="*60)
    
    # 1. 分析命中检测错误
    errors = analyze_hit_detection_errors()
    
    # 2. 分析具体案例
    analyze_specific_case()
    
    # 3. 查找真正的错误
    real_errors = find_real_hit_errors()
    
    # 4. 分析评分逻辑问题
    scoring_issues = analyze_scoring_logic_issues()
    
    # 5. 创建修复脚本
    create_fix_script()
    
    print(f"\n📋 分析总结:")
    print(f"   命中检测错误: {len(real_errors)}个")
    print(f"   评分逻辑问题: {len(scoring_issues)}个")
    
    if len(real_errors) > 0:
        print(f"\n🔧 建议:")
        print(f"   1. 运行 python fix_hit_detection.py 修复命中判断错误")
        print(f"   2. 重新验证修复后的数据")
        print(f"   3. 检查评分算法的一致性")
    else:
        print(f"\n✅ 命中判断逻辑基本正确")
        print(f"   可能是用户看到的个别案例理解有误")

if __name__ == "__main__":
    main()
