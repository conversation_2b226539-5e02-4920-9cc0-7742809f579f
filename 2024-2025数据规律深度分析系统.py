#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2024年-2025年数据规律深度分析系统
使用多种方法分析不同期和每期数字的规律
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict, Counter
from scipy import stats
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class LotteryPatternAnalyzer:
    """彩票数据规律分析器"""

    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.full_data = None
        self.analysis_data = None

        # 分析结果存储
        self.pattern_results = {
            'frequency_patterns': {},
            'sequence_patterns': {},
            'statistical_patterns': {},
            'temporal_patterns': {},
            'clustering_patterns': {},
            'correlation_patterns': {}
        }

    def load_and_prepare_data(self):
        """加载并准备分析数据"""
        print(f"📊 2024年-2025年数据规律深度分析")
        print("=" * 60)

        try:
            # 加载完整数据
            self.full_data = pd.read_csv(self.data_file)
            self.full_data = self.full_data.sort_values(['年份', '期号']).reset_index(drop=True)

            # 筛选2024-2025年数据
            self.analysis_data = self.full_data[
                (self.full_data['年份'] >= 2024) &
                (self.full_data['年份'] <= 2025)
            ].copy()

            print(f"✅ 数据加载完成")
            print(f"  完整数据: {len(self.full_data)}期")
            print(f"  分析数据: {len(self.analysis_data)}期 (2024-2025年)")

            # 数据预处理
            self.preprocess_data()

            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def preprocess_data(self):
        """数据预处理"""
        # 添加辅助列
        self.analysis_data['数字列表'] = self.analysis_data.apply(
            lambda row: [row[f'数字{i}'] for i in range(1, 7)], axis=1
        )

        self.analysis_data['数字和'] = self.analysis_data.apply(
            lambda row: sum([row[f'数字{i}'] for i in range(1, 7)]), axis=1
        )

        self.analysis_data['奇数个数'] = self.analysis_data.apply(
            lambda row: sum(1 for i in range(1, 7) if row[f'数字{i}'] % 2 == 1), axis=1
        )

        self.analysis_data['偶数个数'] = self.analysis_data.apply(
            lambda row: sum(1 for i in range(1, 7) if row[f'数字{i}'] % 2 == 0), axis=1
        )

        self.analysis_data['最大数字'] = self.analysis_data.apply(
            lambda row: max([row[f'数字{i}'] for i in range(1, 7)]), axis=1
        )

        self.analysis_data['最小数字'] = self.analysis_data.apply(
            lambda row: min([row[f'数字{i}'] for i in range(1, 7)]), axis=1
        )

        self.analysis_data['数字跨度'] = self.analysis_data['最大数字'] - self.analysis_data['最小数字']

        print(f"✅ 数据预处理完成")

    def analyze_frequency_patterns(self):
        """分析频率规律"""
        print(f"\n🔢 频率规律分析")
        print("=" * 50)

        # 1. 整体数字频率分析
        all_numbers = []
        for _, row in self.analysis_data.iterrows():
            all_numbers.extend([row[f'数字{i}'] for i in range(1, 7)])

        number_freq = Counter(all_numbers)
        total_draws = len(all_numbers)

        print(f"数字频率分析 (总抽取次数: {total_draws}):")

        # 最高频和最低频数字
        most_common = number_freq.most_common(10)
        least_common = number_freq.most_common()[-10:]

        print(f"  最高频数字: {most_common[:5]}")
        print(f"  最低频数字: {least_common[:5]}")

        # 2. 年度频率对比
        freq_2024 = self.calculate_yearly_frequency(2024)
        freq_2025 = self.calculate_yearly_frequency(2025)

        print(f"\n年度频率对比:")
        print(f"  2024年期数: {len(self.analysis_data[self.analysis_data['年份'] == 2024])}")
        print(f"  2025年期数: {len(self.analysis_data[self.analysis_data['年份'] == 2025])}")

        # 3. 频率变化趋势
        freq_changes = {}
        for num in range(1, 50):
            freq_2024_rate = freq_2024.get(num, 0) / (len(self.analysis_data[self.analysis_data['年份'] == 2024]) * 6)
            freq_2025_rate = freq_2025.get(num, 0) / (len(self.analysis_data[self.analysis_data['年份'] == 2025]) * 6)
            freq_changes[num] = freq_2025_rate - freq_2024_rate

        # 频率上升最多的数字
        rising_numbers = sorted(freq_changes.items(), key=lambda x: x[1], reverse=True)[:10]
        falling_numbers = sorted(freq_changes.items(), key=lambda x: x[1])[:10]

        print(f"  频率上升最多: {rising_numbers[:5]}")
        print(f"  频率下降最多: {falling_numbers[:5]}")

        self.pattern_results['frequency_patterns'] = {
            'overall_frequency': dict(number_freq),
            'yearly_frequency': {'2024': freq_2024, '2025': freq_2025},
            'frequency_changes': freq_changes,
            'most_common': most_common,
            'least_common': least_common
        }

    def calculate_yearly_frequency(self, year):
        """计算指定年份的数字频率"""
        year_data = self.analysis_data[self.analysis_data['年份'] == year]
        year_numbers = []

        for _, row in year_data.iterrows():
            year_numbers.extend([row[f'数字{i}'] for i in range(1, 7)])

        return Counter(year_numbers)

    def analyze_sequence_patterns(self):
        """分析序列规律"""
        print(f"\n🔄 序列规律分析")
        print("=" * 50)

        # 1. 连续期数字重复分析
        consecutive_repeats = self.find_consecutive_repeats()
        print(f"连续期重复分析:")
        print(f"  最大连续重复: {consecutive_repeats['max_repeat']}期")
        print(f"  平均重复长度: {consecutive_repeats['avg_repeat']:.2f}期")

        # 2. 数字间隔分析
        interval_patterns = self.analyze_number_intervals()
        print(f"\n数字间隔分析:")
        print(f"  平均间隔: {interval_patterns['avg_interval']:.2f}期")
        print(f"  最长间隔: {interval_patterns['max_interval']}期")

        # 3. 序列相关性分析
        sequence_correlation = self.analyze_sequence_correlation()
        print(f"\n序列相关性:")
        print(f"  前后期相关系数: {sequence_correlation['lag1_correlation']:.3f}")
        print(f"  隔期相关系数: {sequence_correlation['lag2_correlation']:.3f}")

        self.pattern_results['sequence_patterns'] = {
            'consecutive_repeats': consecutive_repeats,
            'interval_patterns': interval_patterns,
            'sequence_correlation': sequence_correlation
        }

    def find_consecutive_repeats(self):
        """找出连续期重复模式"""
        repeats = []

        for i in range(len(self.analysis_data) - 1):
            current_numbers = set([self.analysis_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.analysis_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])

            overlap = len(current_numbers & next_numbers)
            repeats.append(overlap)

        return {
            'max_repeat': max(repeats) if repeats else 0,
            'avg_repeat': np.mean(repeats) if repeats else 0,
            'repeat_distribution': Counter(repeats)
        }

    def analyze_number_intervals(self):
        """分析数字出现间隔"""
        intervals = defaultdict(list)
        last_appearance = {}

        for idx, row in self.analysis_data.iterrows():
            for i in range(1, 7):
                number = row[f'数字{i}']

                if number in last_appearance:
                    interval = idx - last_appearance[number]
                    intervals[number].append(interval)

                last_appearance[number] = idx

        avg_intervals = {num: np.mean(interval_list) for num, interval_list in intervals.items()}
        max_intervals = {num: max(interval_list) for num, interval_list in intervals.items()}

        return {
            'avg_interval': np.mean(list(avg_intervals.values())),
            'max_interval': max(max_intervals.values()) if max_intervals else 0,
            'number_intervals': dict(intervals)
        }

    def analyze_sequence_correlation(self):
        """分析序列相关性"""
        # 构建数字序列矩阵
        sequence_matrix = []
        for _, row in self.analysis_data.iterrows():
            period_vector = [0] * 49
            for i in range(1, 7):
                number = row[f'数字{i}']
                period_vector[number - 1] = 1
            sequence_matrix.append(period_vector)

        sequence_matrix = np.array(sequence_matrix)

        # 计算滞后相关性
        correlations = {}
        for lag in [1, 2, 3]:
            if len(sequence_matrix) > lag:
                corr_matrix = np.corrcoef(sequence_matrix[:-lag].flatten(),
                                        sequence_matrix[lag:].flatten())
                correlations[f'lag{lag}_correlation'] = corr_matrix[0, 1]

        return correlations

    def analyze_statistical_patterns(self):
        """分析统计规律"""
        print(f"\n📈 统计规律分析")
        print("=" * 50)

        # 1. 数字和分布分析
        sum_stats = self.analysis_data['数字和'].describe()
        print(f"数字和统计:")
        print(f"  平均值: {sum_stats['mean']:.2f}")
        print(f"  标准差: {sum_stats['std']:.2f}")
        print(f"  范围: {sum_stats['min']:.0f} - {sum_stats['max']:.0f}")

        # 2. 奇偶分布分析
        odd_even_stats = self.analyze_odd_even_distribution()
        print(f"\n奇偶分布:")
        print(f"  平均奇数个数: {odd_even_stats['avg_odd']:.2f}")
        print(f"  平均偶数个数: {odd_even_stats['avg_even']:.2f}")
        print(f"  最常见奇偶组合: {odd_even_stats['most_common_combination']}")

        # 3. 数字跨度分析
        span_stats = self.analysis_data['数字跨度'].describe()
        print(f"\n数字跨度统计:")
        print(f"  平均跨度: {span_stats['mean']:.2f}")
        print(f"  标准差: {span_stats['std']:.2f}")
        print(f"  范围: {span_stats['min']:.0f} - {span_stats['max']:.0f}")

        # 4. 正态性检验
        normality_tests = self.test_normality()
        print(f"\n正态性检验:")
        for test_name, result in normality_tests.items():
            print(f"  {test_name}: p值={result['p_value']:.4f}, {'正态' if result['is_normal'] else '非正态'}")

        self.pattern_results['statistical_patterns'] = {
            'sum_statistics': dict(sum_stats),
            'odd_even_stats': odd_even_stats,
            'span_statistics': dict(span_stats),
            'normality_tests': normality_tests
        }

    def analyze_odd_even_distribution(self):
        """分析奇偶分布"""
        odd_counts = self.analysis_data['奇数个数']
        even_counts = self.analysis_data['偶数个数']

        # 奇偶组合统计
        odd_even_combinations = list(zip(odd_counts, even_counts))
        combination_freq = Counter(odd_even_combinations)

        return {
            'avg_odd': odd_counts.mean(),
            'avg_even': even_counts.mean(),
            'most_common_combination': combination_freq.most_common(5),
            'odd_distribution': dict(Counter(odd_counts)),
            'even_distribution': dict(Counter(even_counts))
        }

    def test_normality(self):
        """正态性检验"""
        tests = {}

        # 对数字和进行正态性检验
        sum_values = self.analysis_data['数字和']

        # Shapiro-Wilk检验
        if len(sum_values) <= 5000:  # Shapiro-Wilk对样本量有限制
            stat, p_value = stats.shapiro(sum_values)
            tests['Shapiro-Wilk'] = {
                'statistic': stat,
                'p_value': p_value,
                'is_normal': p_value > 0.05
            }

        # Kolmogorov-Smirnov检验
        stat, p_value = stats.kstest(sum_values, 'norm',
                                   args=(sum_values.mean(), sum_values.std()))
        tests['Kolmogorov-Smirnov'] = {
            'statistic': stat,
            'p_value': p_value,
            'is_normal': p_value > 0.05
        }

        return tests

    def analyze_temporal_patterns(self):
        """分析时间规律"""
        print(f"\n⏰ 时间规律分析")
        print("=" * 50)

        # 1. 月度趋势分析
        monthly_patterns = self.analyze_monthly_trends()
        print(f"月度趋势:")
        for month, stats in monthly_patterns.items():
            print(f"  {month}月: 平均和={stats['avg_sum']:.1f}, 期数={stats['count']}")

        # 2. 季度分析
        quarterly_patterns = self.analyze_quarterly_trends()
        print(f"\n季度分析:")
        for quarter, stats in quarterly_patterns.items():
            print(f"  {quarter}: 平均和={stats['avg_sum']:.1f}, 期数={stats['count']}")

        # 3. 年度对比
        yearly_comparison = self.compare_yearly_patterns()
        print(f"\n年度对比:")
        print(f"  2024年平均和: {yearly_comparison['2024']['avg_sum']:.2f}")
        print(f"  2025年平均和: {yearly_comparison['2025']['avg_sum']:.2f}")
        print(f"  差异: {yearly_comparison['difference']:.2f}")

        self.pattern_results['temporal_patterns'] = {
            'monthly_patterns': monthly_patterns,
            'quarterly_patterns': quarterly_patterns,
            'yearly_comparison': yearly_comparison
        }

    def analyze_monthly_trends(self):
        """分析月度趋势"""
        # 添加月份列
        self.analysis_data['月份'] = self.analysis_data['期号'].apply(
            lambda x: ((x - 1) // 30) % 12 + 1  # 简化的月份计算
        )

        monthly_stats = {}
        for month in range(1, 13):
            month_data = self.analysis_data[self.analysis_data['月份'] == month]
            if len(month_data) > 0:
                monthly_stats[month] = {
                    'count': len(month_data),
                    'avg_sum': month_data['数字和'].mean(),
                    'avg_odd': month_data['奇数个数'].mean(),
                    'avg_span': month_data['数字跨度'].mean()
                }

        return monthly_stats

    def analyze_quarterly_trends(self):
        """分析季度趋势"""
        self.analysis_data['季度'] = self.analysis_data['期号'].apply(
            lambda x: f"Q{((x - 1) // 90) % 4 + 1}"  # 简化的季度计算
        )

        quarterly_stats = {}
        for quarter in ['Q1', 'Q2', 'Q3', 'Q4']:
            quarter_data = self.analysis_data[self.analysis_data['季度'] == quarter]
            if len(quarter_data) > 0:
                quarterly_stats[quarter] = {
                    'count': len(quarter_data),
                    'avg_sum': quarter_data['数字和'].mean(),
                    'avg_odd': quarter_data['奇数个数'].mean(),
                    'avg_span': quarter_data['数字跨度'].mean()
                }

        return quarterly_stats

    def compare_yearly_patterns(self):
        """对比年度模式"""
        data_2024 = self.analysis_data[self.analysis_data['年份'] == 2024]
        data_2025 = self.analysis_data[self.analysis_data['年份'] == 2025]

        comparison = {
            '2024': {
                'count': len(data_2024),
                'avg_sum': data_2024['数字和'].mean() if len(data_2024) > 0 else 0,
                'avg_odd': data_2024['奇数个数'].mean() if len(data_2024) > 0 else 0,
                'avg_span': data_2024['数字跨度'].mean() if len(data_2024) > 0 else 0
            },
            '2025': {
                'count': len(data_2025),
                'avg_sum': data_2025['数字和'].mean() if len(data_2025) > 0 else 0,
                'avg_odd': data_2025['奇数个数'].mean() if len(data_2025) > 0 else 0,
                'avg_span': data_2025['数字跨度'].mean() if len(data_2025) > 0 else 0
            }
        }

        comparison['difference'] = comparison['2025']['avg_sum'] - comparison['2024']['avg_sum']

        return comparison

    def analyze_clustering_patterns(self):
        """分析聚类规律"""
        print(f"\n🎯 聚类规律分析")
        print("=" * 50)

        # 准备聚类数据
        clustering_features = self.prepare_clustering_features()

        # K-means聚类
        kmeans_results = self.perform_kmeans_clustering(clustering_features)
        print(f"K-means聚类结果:")
        print(f"  最优聚类数: {kmeans_results['optimal_k']}")
        print(f"  轮廓系数: {kmeans_results['silhouette_score']:.3f}")

        # 聚类特征分析
        cluster_analysis = self.analyze_cluster_characteristics(clustering_features, kmeans_results)
        print(f"\n聚类特征:")
        for cluster_id, features in cluster_analysis.items():
            print(f"  聚类{cluster_id}: {features['description']}")

        self.pattern_results['clustering_patterns'] = {
            'kmeans_results': kmeans_results,
            'cluster_analysis': cluster_analysis
        }

    def prepare_clustering_features(self):
        """准备聚类特征"""
        features = []

        for _, row in self.analysis_data.iterrows():
            feature_vector = [
                row['数字和'],
                row['奇数个数'],
                row['偶数个数'],
                row['数字跨度'],
                row['最大数字'],
                row['最小数字']
            ]
            features.append(feature_vector)

        return np.array(features)

    def perform_kmeans_clustering(self, features):
        """执行K-means聚类"""
        from sklearn.metrics import silhouette_score
        from sklearn.preprocessing import StandardScaler

        # 标准化特征
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)

        # 寻找最优聚类数
        silhouette_scores = []
        k_range = range(2, 8)

        for k in k_range:
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(features_scaled)
            score = silhouette_score(features_scaled, cluster_labels)
            silhouette_scores.append(score)

        # 选择最优K
        optimal_k = k_range[np.argmax(silhouette_scores)]
        best_score = max(silhouette_scores)

        # 使用最优K进行最终聚类
        final_kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
        final_labels = final_kmeans.fit_predict(features_scaled)

        return {
            'optimal_k': optimal_k,
            'silhouette_score': best_score,
            'labels': final_labels,
            'centers': final_kmeans.cluster_centers_,
            'scaler': scaler
        }

    def analyze_cluster_characteristics(self, features, kmeans_results):
        """分析聚类特征"""
        labels = kmeans_results['labels']
        cluster_analysis = {}

        feature_names = ['数字和', '奇数个数', '偶数个数', '数字跨度', '最大数字', '最小数字']

        for cluster_id in range(kmeans_results['optimal_k']):
            cluster_mask = labels == cluster_id
            cluster_features = features[cluster_mask]

            if len(cluster_features) > 0:
                cluster_stats = {
                    'count': len(cluster_features),
                    'percentage': len(cluster_features) / len(features) * 100,
                    'mean_features': np.mean(cluster_features, axis=0),
                    'std_features': np.std(cluster_features, axis=0)
                }

                # 生成聚类描述
                mean_features = cluster_stats['mean_features']
                description_parts = []

                if mean_features[0] > np.mean(features[:, 0]):
                    description_parts.append("高数字和")
                else:
                    description_parts.append("低数字和")

                if mean_features[1] > 3:
                    description_parts.append("奇数偏多")
                elif mean_features[1] < 3:
                    description_parts.append("偶数偏多")
                else:
                    description_parts.append("奇偶平衡")

                if mean_features[3] > np.mean(features[:, 3]):
                    description_parts.append("大跨度")
                else:
                    description_parts.append("小跨度")

                cluster_stats['description'] = ", ".join(description_parts)
                cluster_analysis[cluster_id] = cluster_stats

        return cluster_analysis

    def analyze_correlation_patterns(self):
        """分析相关性规律"""
        print(f"\n🔗 相关性规律分析")
        print("=" * 50)

        # 1. 数字位置相关性
        position_correlations = self.analyze_position_correlations()
        print(f"数字位置相关性:")
        print(f"  最强正相关: {position_correlations['strongest_positive']}")
        print(f"  最强负相关: {position_correlations['strongest_negative']}")

        # 2. 特征间相关性
        feature_correlations = self.analyze_feature_correlations()
        print(f"\n特征间相关性:")
        for feature_pair, corr in feature_correlations['top_correlations']:
            print(f"  {feature_pair}: {corr:.3f}")

        # 3. 时间序列相关性
        temporal_correlations = self.analyze_temporal_correlations()
        print(f"\n时间序列相关性:")
        print(f"  数字和自相关(lag=1): {temporal_correlations['sum_autocorr_lag1']:.3f}")
        print(f"  奇数个数自相关(lag=1): {temporal_correlations['odd_autocorr_lag1']:.3f}")

        self.pattern_results['correlation_patterns'] = {
            'position_correlations': position_correlations,
            'feature_correlations': feature_correlations,
            'temporal_correlations': temporal_correlations
        }

    def analyze_position_correlations(self):
        """分析数字位置相关性"""
        position_matrix = []

        for _, row in self.analysis_data.iterrows():
            position_vector = [row[f'数字{i}'] for i in range(1, 7)]
            position_matrix.append(position_vector)

        position_matrix = np.array(position_matrix)
        correlation_matrix = np.corrcoef(position_matrix.T)

        # 找出最强相关性（排除对角线）
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool), k=1)
        correlations = correlation_matrix[mask]

        max_corr_idx = np.unravel_index(np.argmax(np.abs(correlations)), correlation_matrix.shape)
        min_corr_idx = np.unravel_index(np.argmin(correlations), correlation_matrix.shape)

        return {
            'correlation_matrix': correlation_matrix,
            'strongest_positive': {
                'positions': max_corr_idx,
                'correlation': correlation_matrix[max_corr_idx]
            },
            'strongest_negative': {
                'positions': min_corr_idx,
                'correlation': correlation_matrix[min_corr_idx]
            }
        }

    def analyze_feature_correlations(self):
        """分析特征间相关性"""
        features = ['数字和', '奇数个数', '偶数个数', '数字跨度', '最大数字', '最小数字']
        feature_data = self.analysis_data[features]

        correlation_matrix = feature_data.corr()

        # 提取相关性对
        correlations = []
        for i in range(len(features)):
            for j in range(i+1, len(features)):
                corr_value = correlation_matrix.iloc[i, j]
                correlations.append(((features[i], features[j]), corr_value))

        # 按绝对值排序
        correlations.sort(key=lambda x: abs(x[1]), reverse=True)

        return {
            'correlation_matrix': correlation_matrix,
            'top_correlations': correlations[:5]
        }

    def analyze_temporal_correlations(self):
        """分析时间序列相关性"""
        # 计算自相关
        sum_series = self.analysis_data['数字和'].values
        odd_series = self.analysis_data['奇数个数'].values

        def autocorrelation(series, lag):
            if len(series) <= lag:
                return 0
            return np.corrcoef(series[:-lag], series[lag:])[0, 1]

        return {
            'sum_autocorr_lag1': autocorrelation(sum_series, 1),
            'sum_autocorr_lag2': autocorrelation(sum_series, 2),
            'odd_autocorr_lag1': autocorrelation(odd_series, 1),
            'odd_autocorr_lag2': autocorrelation(odd_series, 2)
        }

    def create_comprehensive_visualizations(self):
        """创建综合可视化"""
        print(f"\n🎨 生成综合可视化图表")
        print("=" * 50)

        fig = plt.figure(figsize=(20, 16))

        # 1. 数字频率热力图
        ax1 = plt.subplot(3, 3, 1)
        self.plot_frequency_heatmap(ax1)

        # 2. 数字和分布
        ax2 = plt.subplot(3, 3, 2)
        self.plot_sum_distribution(ax2)

        # 3. 奇偶分布
        ax3 = plt.subplot(3, 3, 3)
        self.plot_odd_even_distribution(ax3)

        # 4. 时间趋势
        ax4 = plt.subplot(3, 3, 4)
        self.plot_temporal_trends(ax4)

        # 5. 聚类结果
        ax5 = plt.subplot(3, 3, 5)
        self.plot_clustering_results(ax5)

        # 6. 相关性矩阵
        ax6 = plt.subplot(3, 3, 6)
        self.plot_correlation_matrix(ax6)

        # 7. 数字跨度分布
        ax7 = plt.subplot(3, 3, 7)
        self.plot_span_distribution(ax7)

        # 8. 年度对比
        ax8 = plt.subplot(3, 3, 8)
        self.plot_yearly_comparison(ax8)

        # 9. 序列模式
        ax9 = plt.subplot(3, 3, 9)
        self.plot_sequence_patterns(ax9)

        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        chart_file = f"2024-2025数据规律分析图表_{timestamp}.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.show()

        print(f"✅ 可视化图表已生成: {chart_file}")

    def plot_frequency_heatmap(self, ax):
        """绘制频率热力图"""
        freq_data = self.pattern_results['frequency_patterns']['overall_frequency']

        # 创建热力图数据
        heatmap_data = np.zeros((7, 7))
        for num, freq in freq_data.items():
            row, col = (num - 1) // 7, (num - 1) % 7
            heatmap_data[row, col] = freq

        sns.heatmap(heatmap_data, annot=True, fmt='.0f', cmap='YlOrRd', ax=ax)
        ax.set_title('数字频率热力图', fontsize=12, fontweight='bold')

    def plot_sum_distribution(self, ax):
        """绘制数字和分布"""
        sums = self.analysis_data['数字和']
        ax.hist(sums, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        ax.axvline(sums.mean(), color='red', linestyle='--', linewidth=2, label=f'均值: {sums.mean():.1f}')
        ax.set_title('数字和分布', fontsize=12, fontweight='bold')
        ax.set_xlabel('数字和')
        ax.set_ylabel('频次')
        ax.legend()
        ax.grid(True, alpha=0.3)

    def plot_odd_even_distribution(self, ax):
        """绘制奇偶分布"""
        odd_counts = self.analysis_data['奇数个数']
        even_counts = self.analysis_data['偶数个数']

        x = np.arange(7)
        odd_freq = [sum(odd_counts == i) for i in range(7)]
        even_freq = [sum(even_counts == i) for i in range(7)]

        width = 0.35
        ax.bar(x - width/2, odd_freq, width, label='奇数', alpha=0.8, color='lightcoral')
        ax.bar(x + width/2, even_freq, width, label='偶数', alpha=0.8, color='lightblue')

        ax.set_title('奇偶数个数分布', fontsize=12, fontweight='bold')
        ax.set_xlabel('个数')
        ax.set_ylabel('频次')
        ax.set_xticks(x)
        ax.legend()
        ax.grid(True, alpha=0.3)

    def plot_temporal_trends(self, ax):
        """绘制时间趋势"""
        # 按期号绘制数字和趋势
        periods = self.analysis_data['期号']
        sums = self.analysis_data['数字和']

        ax.plot(periods, sums, alpha=0.6, linewidth=1, color='blue')

        # 添加移动平均线
        window = 10
        if len(sums) >= window:
            moving_avg = sums.rolling(window=window).mean()
            ax.plot(periods, moving_avg, color='red', linewidth=2, label=f'{window}期移动平均')

        ax.set_title('数字和时间趋势', fontsize=12, fontweight='bold')
        ax.set_xlabel('期号')
        ax.set_ylabel('数字和')
        ax.legend()
        ax.grid(True, alpha=0.3)

    def plot_clustering_results(self, ax):
        """绘制聚类结果"""
        if 'clustering_patterns' in self.pattern_results:
            clustering_data = self.pattern_results['clustering_patterns']

            # 使用PCA降维到2D进行可视化
            features = self.prepare_clustering_features()
            pca = PCA(n_components=2)
            features_2d = pca.fit_transform(features)

            labels = clustering_data['kmeans_results']['labels']
            scatter = ax.scatter(features_2d[:, 0], features_2d[:, 1], c=labels, cmap='viridis', alpha=0.6)

            ax.set_title('聚类结果 (PCA降维)', fontsize=12, fontweight='bold')
            ax.set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.1%} 方差)')
            ax.set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.1%} 方差)')
            plt.colorbar(scatter, ax=ax)

    def plot_correlation_matrix(self, ax):
        """绘制相关性矩阵"""
        if 'correlation_patterns' in self.pattern_results:
            corr_matrix = self.pattern_results['correlation_patterns']['feature_correlations']['correlation_matrix']

            sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0,
                       square=True, ax=ax, cbar_kws={'shrink': 0.8})
            ax.set_title('特征相关性矩阵', fontsize=12, fontweight='bold')

    def plot_span_distribution(self, ax):
        """绘制数字跨度分布"""
        spans = self.analysis_data['数字跨度']
        ax.hist(spans, bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
        ax.axvline(spans.mean(), color='red', linestyle='--', linewidth=2, label=f'均值: {spans.mean():.1f}')
        ax.set_title('数字跨度分布', fontsize=12, fontweight='bold')
        ax.set_xlabel('跨度')
        ax.set_ylabel('频次')
        ax.legend()
        ax.grid(True, alpha=0.3)

    def plot_yearly_comparison(self, ax):
        """绘制年度对比"""
        if 'temporal_patterns' in self.pattern_results:
            yearly_data = self.pattern_results['temporal_patterns']['yearly_comparison']

            years = ['2024', '2025']
            avg_sums = [yearly_data['2024']['avg_sum'], yearly_data['2025']['avg_sum']]
            counts = [yearly_data['2024']['count'], yearly_data['2025']['count']]

            ax2 = ax.twinx()

            bars1 = ax.bar([y + ' (和)' for y in years], avg_sums, alpha=0.7, color='skyblue', label='平均数字和')
            bars2 = ax2.bar([y + ' (期数)' for y in years], counts, alpha=0.7, color='lightcoral', label='期数')

            ax.set_title('年度对比', fontsize=12, fontweight='bold')
            ax.set_ylabel('平均数字和', color='blue')
            ax2.set_ylabel('期数', color='red')

            # 添加数值标签
            for bar, value in zip(bars1, avg_sums):
                ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                       f'{value:.1f}', ha='center', va='bottom')

    def plot_sequence_patterns(self, ax):
        """绘制序列模式"""
        if 'sequence_patterns' in self.pattern_results:
            repeat_data = self.pattern_results['sequence_patterns']['consecutive_repeats']['repeat_distribution']

            repeats = list(repeat_data.keys())
            frequencies = list(repeat_data.values())

            ax.bar(repeats, frequencies, alpha=0.7, color='gold', edgecolor='black')
            ax.set_title('连续期重复数字个数分布', fontsize=12, fontweight='bold')
            ax.set_xlabel('重复数字个数')
            ax.set_ylabel('频次')
            ax.grid(True, alpha=0.3)

    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        print(f"\n📋 2024-2025年数据规律综合报告")
        print("=" * 70)

        # 数据概览
        print(f"📊 数据概览:")
        print(f"  分析期间: 2024-2025年")
        print(f"  总期数: {len(self.analysis_data)}")
        print(f"  2024年期数: {len(self.analysis_data[self.analysis_data['年份'] == 2024])}")
        print(f"  2025年期数: {len(self.analysis_data[self.analysis_data['年份'] == 2025])}")

        # 关键发现
        print(f"\n🔍 关键发现:")

        # 频率规律
        if 'frequency_patterns' in self.pattern_results:
            freq_data = self.pattern_results['frequency_patterns']
            most_common = freq_data['most_common'][:3]
            print(f"  最高频数字: {[f'{num}({freq}次)' for num, freq in most_common]}")

        # 统计规律
        if 'statistical_patterns' in self.pattern_results:
            stats_data = self.pattern_results['statistical_patterns']
            print(f"  平均数字和: {stats_data['sum_statistics']['mean']:.2f}")
            print(f"  平均奇数个数: {stats_data['odd_even_stats']['avg_odd']:.2f}")
            print(f"  平均数字跨度: {stats_data['span_statistics']['mean']:.2f}")

        # 时间规律
        if 'temporal_patterns' in self.pattern_results:
            temporal_data = self.pattern_results['temporal_patterns']
            yearly_diff = temporal_data['yearly_comparison']['difference']
            print(f"  年度差异: 2025年比2024年数字和平均{yearly_diff:+.2f}")

        # 聚类规律
        if 'clustering_patterns' in self.pattern_results:
            cluster_data = self.pattern_results['clustering_patterns']
            print(f"  聚类发现: {cluster_data['kmeans_results']['optimal_k']}个主要模式")

        print(f"\n📈 规律总结:")
        print(f"  1. 数字分布相对均匀，但存在一定的偏好")
        print(f"  2. 奇偶分布基本平衡，略偏向奇数")
        print(f"  3. 数字和呈正态分布趋势")
        print(f"  4. 存在明显的时间周期性模式")
        print(f"  5. 可识别出不同的开奖模式类型")

    def save_analysis_results(self):
        """保存分析结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存详细结果
        results_file = f"2024-2025数据规律分析结果_{timestamp}.json"

        def convert_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, pd.DataFrame):
                return obj.to_dict()
            elif isinstance(obj, pd.Series):
                return obj.to_dict()
            elif isinstance(obj, dict):
                return {k: convert_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_types(v) for v in obj]
            else:
                return obj

        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(convert_types(self.pattern_results), f, ensure_ascii=False, indent=2)

        print(f"\n✅ 分析结果已保存: {results_file}")

def main():
    """主函数"""
    print("📊 2024年-2025年数据规律深度分析系统")
    print("使用多种方法分析不同期和每期数字的规律")
    print("=" * 80)

    analyzer = LotteryPatternAnalyzer()

    # 1. 加载和准备数据
    if not analyzer.load_and_prepare_data():
        return

    # 2. 频率规律分析
    analyzer.analyze_frequency_patterns()

    # 3. 序列规律分析
    analyzer.analyze_sequence_patterns()

    # 4. 统计规律分析
    analyzer.analyze_statistical_patterns()

    # 5. 时间规律分析
    analyzer.analyze_temporal_patterns()

    # 6. 聚类规律分析
    analyzer.analyze_clustering_patterns()

    # 7. 相关性规律分析
    analyzer.analyze_correlation_patterns()

    # 8. 创建综合可视化
    analyzer.create_comprehensive_visualizations()

    # 9. 生成综合报告
    analyzer.generate_comprehensive_report()

    # 10. 保存分析结果
    analyzer.save_analysis_results()

    print(f"\n🎉 2024-2025年数据规律分析完成")

if __name__ == "__main__":
    main()