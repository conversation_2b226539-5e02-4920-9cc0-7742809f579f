#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
升级版手动输入预测系统 - 集成数据管理
1. 用户输入真实数据自动添加到主数据文件
2. 预测数据自动保存到专门的CSV文件
3. 完整的数据管理和统计功能
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class UpgradedManualPredictionSystem:
    """升级版手动输入预测系统"""

    def __init__(self):
        self.main_data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.prediction_data_file = "prediction_data.csv"
        self.prediction_history_file = "prediction_history.json"

        # 34.3%方法的最佳参数
        self.optimal_params = {
            'high_freq_boost': 1.15,
            'low_freq_penalty': 0.85,
            'rising_trend_boost': 1.10,
            'falling_trend_penalty': 0.90,
            'perturbation': 0.05
        }

        # 数字分类
        self.high_freq_numbers = [5, 15, 3, 40, 30]
        self.low_freq_numbers = [41, 1, 8, 48, 47]
        self.rising_numbers = [30, 39, 4, 8, 22]
        self.falling_numbers = [5, 26, 44, 36, 15]

        # 模型组件
        self.enhanced_markov_prob = None

    def load_data_and_build_model(self):
        """加载数据并构建模型"""
        print("🔧 加载数据并构建34.3%预测模型...")

        try:
            # 加载主数据
            self.main_data = pd.read_csv(self.main_data_file)
            self.main_data = self.main_data.sort_values(['年份', '期号']).reset_index(drop=True)

            print(f"✅ 主数据加载完成: {len(self.main_data)}期")

            # 构建增强马尔可夫模型
            self.build_enhanced_markov_model()

            return True

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def build_enhanced_markov_model(self):
        """构建增强马尔可夫模型"""
        # 使用2023-2024年作为训练数据
        train_data = self.main_data[
            (self.main_data['年份'] >= 2023) &
            (self.main_data['年份'] <= 2024)
        ].copy()

        # 基础马尔可夫转移概率
        transition_count = defaultdict(lambda: defaultdict(int))

        for i in range(len(train_data) - 1):
            current_numbers = set([train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])

            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1

        # 计算基础转移概率
        base_markov_prob = {}
        for curr_num in range(1, 50):
            if curr_num in transition_count:
                total = sum(transition_count[curr_num].values())
                smoothed_total = total + 49
                base_markov_prob[curr_num] = {}

                for next_num in range(1, 50):
                    count = transition_count[curr_num].get(next_num, 0)
                    base_markov_prob[curr_num][next_num] = (count + 1) / smoothed_total
            else:
                base_markov_prob[curr_num] = {
                    next_num: 1/49 for next_num in range(1, 50)
                }

        # 应用增强策略
        self.apply_enhancements(base_markov_prob)

        print("✅ 34.3%增强马尔可夫模型构建完成")

    def apply_enhancements(self, base_markov_prob):
        """应用增强策略"""
        # 计算权重
        frequency_weights = {}
        trend_weights = {}

        for num in range(1, 50):
            if num in self.high_freq_numbers:
                frequency_weights[num] = self.optimal_params['high_freq_boost']
            elif num in self.low_freq_numbers:
                frequency_weights[num] = self.optimal_params['low_freq_penalty']
            else:
                frequency_weights[num] = 1.0

            if num in self.rising_numbers:
                trend_weights[num] = self.optimal_params['rising_trend_boost']
            elif num in self.falling_numbers:
                trend_weights[num] = self.optimal_params['falling_trend_penalty']
            else:
                trend_weights[num] = 1.0

        # 构建增强马尔可夫概率
        self.enhanced_markov_prob = {}
        for curr_num in base_markov_prob:
            self.enhanced_markov_prob[curr_num] = {}
            total_weight = 0

            for next_num, base_prob in base_markov_prob[curr_num].items():
                freq_weight = frequency_weights[next_num]
                trend_weight = trend_weights[next_num]
                combined_weight = freq_weight * trend_weight
                weighted_prob = base_prob * combined_weight

                self.enhanced_markov_prob[curr_num][next_num] = weighted_prob
                total_weight += weighted_prob

            # 归一化
            for next_num in self.enhanced_markov_prob[curr_num]:
                self.enhanced_markov_prob[curr_num][next_num] /= total_weight

    def predict_next_period(self, current_numbers):
        """预测下期数字"""
        if self.enhanced_markov_prob is None:
            raise RuntimeError("模型未初始化")

        # 使用固定随机种子确保可重复性
        np.random.seed(42)

        number_probs = defaultdict(float)
        total_prob = 0.0

        current_set = set(current_numbers)

        for curr_num in current_set:
            if curr_num in self.enhanced_markov_prob:
                for next_num, prob in self.enhanced_markov_prob[curr_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob

        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob

        # 添加随机扰动
        perturbation = self.optimal_params['perturbation']
        for num in number_probs:
            noise = np.random.normal(0, perturbation * number_probs[num])
            number_probs[num] = max(0, number_probs[num] + noise)

        # 选择前2个数字
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            prediction = [num for num, prob in sorted_numbers[:2]]
            confidence = (sorted_numbers[0][1] + sorted_numbers[1][1]) / 2
        else:
            prediction = [1, 2]
            confidence = 0.02

        return prediction, confidence

    def add_real_data_to_main_file(self, year, period, numbers):
        """将真实数据添加到主数据文件"""
        try:
            # 检查数据是否已存在
            existing = self.main_data[
                (self.main_data['年份'] == year) &
                (self.main_data['期号'] == period)
            ]

            if len(existing) > 0:
                print(f"⚠️ {year}年{period}期数据已存在，更新数据")
                # 更新现有数据
                idx = existing.index[0]
                for i, num in enumerate(sorted(numbers), 1):
                    self.main_data.loc[idx, f'数字{i}'] = num
            else:
                # 添加新数据
                new_row = {
                    '年份': year,
                    '期号': period
                }
                for i, num in enumerate(sorted(numbers), 1):
                    new_row[f'数字{i}'] = num

                # 使用pd.concat替代append
                new_df = pd.DataFrame([new_row])
                self.main_data = pd.concat([self.main_data, new_df], ignore_index=True)
                self.main_data = self.main_data.sort_values(['年份', '期号']).reset_index(drop=True)
                print(f"✅ 添加 {year}年{period}期数据到主文件")

            # 保存到文件
            self.main_data.to_csv(self.main_data_file, index=False, encoding='utf-8')

            return True

        except Exception as e:
            print(f"❌ 添加数据到主文件失败: {e}")
            return False

    def verify_previous_prediction(self, current_year, current_period, current_numbers):
        """验证上期预测"""
        try:
            if not os.path.exists(self.prediction_data_file):
                return False

            # 读取预测数据
            prediction_df = pd.read_csv(self.prediction_data_file, encoding='utf-8-sig')

            # 查找对应当期的预测记录
            target_period_name = f"{current_year}年{current_period}期"

            matching_predictions = prediction_df[
                prediction_df['预测期号'] == target_period_name
            ]

            if len(matching_predictions) == 0:
                print(f"📝 没有找到{target_period_name}的预测记录")
                return False

            # 使用最新的预测记录
            prediction_row = matching_predictions.iloc[-1]
            row_index = matching_predictions.index[-1]

            # 检查是否已经验证过
            if not pd.isna(prediction_row['实际数字1']) and prediction_row['实际数字1'] != '':
                print(f"📝 {target_period_name}的预测已经验证过")
                return False

            # 获取预测数字
            predicted_numbers = [prediction_row['预测数字1'], prediction_row['预测数字2']]

            # 计算命中情况
            predicted_set = set(predicted_numbers)
            actual_set = set(current_numbers)
            hit_numbers = predicted_set & actual_set
            hit_count = len(hit_numbers)
            is_hit = hit_count >= 1

            # 更新预测记录
            for i, num in enumerate(current_numbers, 1):
                prediction_df.loc[row_index, f'实际数字{i}'] = num

            prediction_df.loc[row_index, '命中数量'] = hit_count
            prediction_df.loc[row_index, '是否命中'] = '是' if is_hit else '否'
            prediction_df.loc[row_index, '命中数字'] = ','.join(map(str, sorted(hit_numbers))) if hit_numbers else ''

            # 更新备注
            current_note = prediction_df.loc[row_index, '备注']
            if pd.isna(current_note) or current_note == '':
                prediction_df.loc[row_index, '备注'] = '用户验证'
            else:
                prediction_df.loc[row_index, '备注'] = f"{current_note},用户验证"

            # 保存更新后的文件
            prediction_df.to_csv(self.prediction_data_file, index=False, encoding='utf-8-sig')

            # 显示验证结果
            print(f"\n🎯 自动验证上期预测:")
            print(f"预测期号: {target_period_name}")
            print(f"预测数字: {predicted_numbers}")
            print(f"实际开奖: {current_numbers}")
            print(f"命中数字: {sorted(hit_numbers) if hit_numbers else '无'}")
            print(f"命中数量: {hit_count}")
            print(f"命中状态: {'✅ 命中' if is_hit else '❌ 未命中'}")
            print(f"✅ 预测验证结果已更新到CSV文件")

            return True

        except Exception as e:
            print(f"❌ 验证上期预测失败: {e}")
            return False

    def add_prediction_to_csv(self, prediction_data):
        """添加预测到CSV文件"""
        try:
            # 读取现有数据
            if os.path.exists(self.prediction_data_file):
                existing_df = pd.read_csv(self.prediction_data_file, encoding='utf-8-sig')
            else:
                # 创建新文件
                columns = [
                    '预测日期', '预测时间', '当期年份', '当期期号', '预测期号',
                    '当期数字1', '当期数字2', '当期数字3', '当期数字4', '当期数字5', '当期数字6',
                    '预测数字1', '预测数字2', '预测置信度', '预测方法',
                    '实际数字1', '实际数字2', '实际数字3', '实际数字4', '实际数字5', '实际数字6',
                    '命中数量', '是否命中', '命中数字', '备注'
                ]
                existing_df = pd.DataFrame(columns=columns)

            # 准备新记录
            current_time = datetime.now()
            current_numbers = prediction_data.get('current_numbers', [])
            predicted_numbers = prediction_data.get('predicted_numbers', [])
            actual_numbers = prediction_data.get('actual_numbers', [])

            new_record = {
                '预测日期': current_time.strftime('%Y-%m-%d'),
                '预测时间': current_time.strftime('%H:%M:%S'),
                '当期年份': prediction_data.get('current_year', ''),
                '当期期号': prediction_data.get('current_period', ''),
                '预测期号': prediction_data.get('predicted_period', ''),
                '当期数字1': current_numbers[0] if len(current_numbers) > 0 else '',
                '当期数字2': current_numbers[1] if len(current_numbers) > 1 else '',
                '当期数字3': current_numbers[2] if len(current_numbers) > 2 else '',
                '当期数字4': current_numbers[3] if len(current_numbers) > 3 else '',
                '当期数字5': current_numbers[4] if len(current_numbers) > 4 else '',
                '当期数字6': current_numbers[5] if len(current_numbers) > 5 else '',
                '预测数字1': predicted_numbers[0] if len(predicted_numbers) > 0 else '',
                '预测数字2': predicted_numbers[1] if len(predicted_numbers) > 1 else '',
                '预测置信度': f"{prediction_data.get('confidence', 0):.6f}",
                '预测方法': prediction_data.get('method', '34.3%增强马尔可夫'),
                '实际数字1': actual_numbers[0] if len(actual_numbers) > 0 else '',
                '实际数字2': actual_numbers[1] if len(actual_numbers) > 1 else '',
                '实际数字3': actual_numbers[2] if len(actual_numbers) > 2 else '',
                '实际数字4': actual_numbers[3] if len(actual_numbers) > 3 else '',
                '实际数字5': actual_numbers[4] if len(actual_numbers) > 4 else '',
                '实际数字6': actual_numbers[5] if len(actual_numbers) > 5 else '',
                '命中数量': prediction_data.get('hit_count', ''),
                '是否命中': '是' if prediction_data.get('is_hit') else '否' if prediction_data.get('is_hit') is not None else '',
                '命中数字': ','.join(map(str, prediction_data.get('hit_numbers', []))) if prediction_data.get('hit_numbers') else '',
                '备注': prediction_data.get('notes', '用户输入')
            }

            # 添加新记录
            new_df = pd.DataFrame([new_record])
            updated_df = pd.concat([existing_df, new_df], ignore_index=True)

            # 保存到文件
            updated_df.to_csv(self.prediction_data_file, index=False, encoding='utf-8-sig')

            print(f"✅ 预测已保存到CSV文件: {self.prediction_data_file}")
            return True

        except Exception as e:
            print(f"❌ 保存预测到CSV失败: {e}")
            return False

    def input_current_period_data(self):
        """输入当期数据"""
        print("\n📝 请输入当期开奖数据")
        print("=" * 40)

        while True:
            try:
                # 输入期号信息
                year = input("请输入年份 (如: 2025): ").strip()
                period = input("请输入期号 (如: 187): ").strip()

                if not year.isdigit() or not period.isdigit():
                    print("❌ 年份和期号必须是数字，请重新输入")
                    continue

                year = int(year)
                period = int(period)

                # 输入开奖数字
                print("请输入6个开奖数字:")
                print("支持格式: 空格分隔(5 12 23 31 40 45) 或 逗号分隔(5,12,23,31,40,45)")
                numbers_input = input("开奖数字: ").strip()

                # 解析数字 - 支持多种分隔符
                if ',' in numbers_input:
                    # 逗号分隔
                    numbers_str = numbers_input.replace(' ', '').split(',')
                else:
                    # 空格分隔
                    numbers_str = numbers_input.split()

                # 转换为整数并处理前导零
                numbers = []
                for num_str in numbers_str:
                    num_str = num_str.strip()
                    if num_str:  # 非空字符串
                        numbers.append(int(num_str))

                # 验证输入
                if len(numbers) != 6:
                    print("❌ 必须输入6个数字，请重新输入")
                    continue

                if not all(1 <= num <= 49 for num in numbers):
                    print("❌ 数字必须在1-49范围内，请重新输入")
                    continue

                if len(set(numbers)) != 6:
                    print("❌ 数字不能重复，请重新输入")
                    continue

                return {
                    'year': year,
                    'period': period,
                    'numbers': sorted(numbers)
                }

            except ValueError:
                print("❌ 输入格式错误，请重新输入")
            except KeyboardInterrupt:
                print("\n👋 用户取消输入")
                return None

    def make_prediction_and_save(self, current_data):
        """进行预测并保存到所有文件"""
        print(f"\n🎯 基于{current_data['year']}年{current_data['period']}期数据进行预测")
        print("=" * 50)

        current_numbers = current_data['numbers']
        print(f"当期开奖: {current_numbers}")

        # 1. 自动验证上期预测（如果存在）
        self.verify_previous_prediction(current_data['year'], current_data['period'], current_numbers)

        # 2. 添加真实数据到主文件
        self.add_real_data_to_main_file(current_data['year'], current_data['period'], current_numbers)

        # 3. 执行预测
        predicted_numbers, confidence = self.predict_next_period(current_numbers)

        print(f"\n🔮 预测下期:")
        print(f"预测数字: {predicted_numbers}")
        print(f"预测置信度: {confidence:.3f}")

        # 4. 保存预测到CSV文件
        prediction_data = {
            'current_year': current_data['year'],
            'current_period': current_data['period'],
            'predicted_period': f"{current_data['year']}年{current_data['period']+1}期",
            'current_numbers': current_numbers,
            'predicted_numbers': predicted_numbers,
            'confidence': confidence,
            'method': '34.3%增强马尔可夫',
            'notes': '用户输入'
        }

        self.add_prediction_to_csv(prediction_data)

        return prediction_data

    def show_recent_predictions(self):
        """显示最近预测"""
        try:
            if not os.path.exists(self.prediction_data_file):
                print("📊 暂无预测数据")
                return

            df = pd.read_csv(self.prediction_data_file, encoding='utf-8-sig')

            if len(df) == 0:
                print("📊 暂无预测数据")
                return

            print(f"\n📊 预测统计")
            print("=" * 50)

            total_predictions = len(df)
            verified_predictions = df[df['是否命中'].notna() & (df['是否命中'] != '')].copy()
            hits = len(verified_predictions[verified_predictions['是否命中'] == '是'])

            print(f"总预测期数: {total_predictions}")
            print(f"已验证期数: {len(verified_predictions)}")
            print(f"待验证期数: {total_predictions - len(verified_predictions)}")

            if len(verified_predictions) > 0:
                hit_rate = hits / len(verified_predictions)
                print(f"命中期数: {hits}")
                print(f"命中率: {hit_rate:.3f} ({hit_rate:.1%})")

            # 最近5期预测
            recent = df.tail(5)
            print(f"\n最近{len(recent)}期预测:")
            for _, row in recent.iterrows():
                if row['是否命中'] == '是':
                    status = "✅"
                elif row['是否命中'] == '否':
                    status = "❌"
                else:
                    status = "⏳"

                print(f"  {row['预测期号']}: 预测[{row['预测数字1']},{row['预测数字2']}] {status}")

        except Exception as e:
            print(f"❌ 显示预测失败: {e}")

    # ===== 新增：训练/验证与贝叶斯分析工具集 =====
    def _flatten_numbers(self, df):
        cols = [f'数字{i}' for i in range(1, 7)]
        return df[cols].to_numpy().ravel()

    def _dirichlet_freq_probs(self, train_df, alpha=1.0):
        # 基于Dirichlet-多项式后验：pi_k = (count_k + alpha) / (N + 49*alpha)
        counts = np.zeros(50, dtype=float)
        for num in self._flatten_numbers(train_df):
            counts[int(num)] += 1
        counts = counts[1:50]
        probs = (counts + alpha) / (counts.sum() + 49 * alpha)
        return probs  # index 0->号码1

    def _conditional_probs(self, train_df, alpha=1.0):
        # P(next=j | any current contains i) 通过相邻期统计转移；拉普拉斯平滑alpha
        transition = defaultdict(lambda: np.zeros(49, dtype=float))  # i -> [p1..p49]
        train_df = train_df.sort_values(['年份', '期号']).reset_index(drop=True)
        for i in range(len(train_df) - 1):
            curr = [int(train_df.iloc[i][f'数字{k}']) for k in range(1, 7)]
            nxt = [int(train_df.iloc[i+1][f'数字{k}']) for k in range(1, 7)]
            nxt_counts = np.zeros(50, dtype=float)
            for v in nxt:
                nxt_counts[v] += 1
            for c in set(curr):
                transition[c] += nxt_counts[1:50]
        cond = {}
        for i_num in range(1, 50):
            vec = transition[i_num]
            total = vec.sum()
            cond[i_num] = (vec + alpha) / (total + 49 * alpha)
        return cond  # dict[num]->np.array len49

    def _predict_pair(self, prev_nums, model_type, freq_probs, cond_probs=None, mix_lambda=1.0):
        # 返回top-2预测与对应分数
        # model_type: 'freq' or 'cond' or 'mix'
        if model_type == 'freq':
            scores = freq_probs.copy()
        elif model_type == 'cond':
            scores = np.zeros_like(freq_probs)
            safe_cond = cond_probs or {}
            for c in set(prev_nums):
                if 1 <= c <= 49:
                    scores += safe_cond.get(c, np.ones(49)/49)
            if scores.sum() > 0:
                scores = scores / scores.sum()
        else:  # mix
            cond_s = np.zeros_like(freq_probs)
            safe_cond = cond_probs or {}
            for c in set(prev_nums):
                if 1 <= c <= 49:
                    cond_s += safe_cond.get(c, np.ones(49)/49)
            if cond_s.sum() > 0:
                cond_s = cond_s / cond_s.sum()
            scores = mix_lambda * cond_s + (1 - mix_lambda) * freq_probs
        # 选出top-2不同数字
        top_idx = np.argsort(scores)[::-1]
        n1 = int(top_idx[0] + 1)
        n2 = int(top_idx[1] + 1)
        return [n1, n2], (scores[top_idx[0]] + scores[top_idx[1]]) / 2

    def _sample_pairs(self, scores, G=5, rng=None):
        # 从分布scores(长度49，对应1..49)按不放回采样形成G个2元组
        if rng is None:
            rng = np.random.default_rng(42)
        pairs = []
        for _ in range(G):
            # 采样两个不同数字
            nums = rng.choice(np.arange(1, 50), size=2, replace=False, p=scores/np.sum(scores))
            pairs.append(sorted([int(nums[0]), int(nums[1])]))
        return pairs

    def _previous_numbers(self, full_df, idx):
        if idx <= 0: return None
        prev = full_df.iloc[idx-1]
        return [int(prev[f'数字{k}']) for k in range(1, 6+1)]

    def _time_split_2024(self, df_2024):
        # 简单时间切分：前70%训练、后30%验证
        n = len(df_2024)
        cut = int(n * 0.7)
        tr = df_2024.iloc[:cut].copy()
        va = df_2024.iloc[cut:].copy()
        return tr, va

    def _hit_metrics(self, predicted_pair, actual_numbers):
        pred_set = set(predicted_pair)
        act_set = set(actual_numbers)
        hit_nums = pred_set & act_set
        return len(hit_nums), (len(hit_nums) == 2)

    def _wilson_interval(self, k, n, z=1.96):
        if n == 0:
            return (0.0, 0.0)
        phat = k / n
        denom = 1 + z*z/n
        center = (phat + z*z/(2*n)) / denom
        half = (z * np.sqrt((phat*(1-phat) + z*z/(4*n)) / n)) / denom
        return max(0.0, center - half), min(1.0, center + half)

    def _random_pair_full_hit_prob(self):
        # p = C(47,4)/C(49,6)
        from math import comb
        return comb(47,4) / comb(49,6)

    def cross_validate_and_select_model(self, df_2024, alphas=(0.5,1.0,2.0)):
        # 返回(model_type, alpha, mix_lambda)
        tr, va = self._time_split_2024(df_2024)
        best = None
        for a in alphas:
            freq = self._dirichlet_freq_probs(tr, alpha=a)
            cond = self._conditional_probs(tr, alpha=a)
            for model_type in ['freq', 'cond']:
                hits = 0; total = 0
                va_sorted = va.sort_values(['年份','期号']).reset_index(drop=True)
                full_df = pd.concat([tr, va_sorted], ignore_index=True)
                # 遍历va中的每一行，用其前一条（可能在tr或va内）作为prev
                for idx_va in range(len(tr), len(full_df)):
                    row = full_df.iloc[idx_va]
                    prev_nums = self._previous_numbers(full_df, idx_va)
                    if prev_nums is None: continue
                    pred, _ = self._predict_pair(prev_nums, model_type, freq, cond)
                    cols = [f'数字{i}' for i in range(1,7)]
                    actual = [int(v) for v in row[cols].tolist()]
                    hit1, _ = self._hit_metrics(pred, actual)
                    hits += int(hit1 >= 1)
                    total += 1
                hit_rate = hits / total if total>0 else 0
                cand = (hit_rate, model_type, a)
                if best is None or cand[0] > best[0]:
                    best = cand
        # 奥卡姆剃刀：若简单(freq)在最佳的95%以内，则选freq
        if best is None:
            return ('freq', 1.0, 0.0)
        best_rate, best_type, best_alpha = best
        # 评估freq在best_alpha下与cond最佳的差距
        freq_rate = None
        for a in alphas:
            freq = self._dirichlet_freq_probs(tr, alpha=a)
            hits = 0; total = 0
            va_sorted = va.sort_values(['年份','期号']).reset_index(drop=True)
            full_df = pd.concat([tr, va_sorted], ignore_index=True)
            for idx_va in range(len(tr), len(full_df)):
                row = full_df.iloc[idx_va]
                prev_nums = self._previous_numbers(full_df, idx_va)
                if prev_nums is None: continue
                pred, _ = self._predict_pair(prev_nums, 'freq', freq, None)
                cols = [f'数字{i}' for i in range(1,7)]
                actual = [int(v) for v in row[cols].tolist()]
                hit1, _ = self._hit_metrics(pred, actual)
                hits += int(hit1 >= 1)
                total += 1
            rate = hits/total if total>0 else 0
            if freq_rate is None or rate>freq_rate:
                freq_rate = rate; chosen_alpha_freq=a
        if freq_rate is not None and freq_rate >= 0.95*best_rate:
            return ('freq', chosen_alpha_freq, 0.0)
        return (best_type, best_alpha, 1.0)

    def run_train_test_analysis(self, groups_per_period=5):
        # 1) 严格切分
        df = self.main_data.sort_values(['年份','期号']).reset_index(drop=True)
        df_2024 = df[df['年份']==2024].copy()
        df_2025 = df[df['年份']==2025].copy()
        if len(df_2024)==0 or len(df_2025)==0:
            print('❌ 找不到完整的2024或2025数据，无法执行训练/验证');
            return None
        # 2) 交叉验证选择模型
        model_type, alpha, mix_lambda = self.cross_validate_and_select_model(df_2024)
        print(f"🔍 模型选择: {model_type} (alpha={alpha}) [奥卡姆剃刀]")
        # 3) 用2024全量训练参数
        freq = self._dirichlet_freq_probs(df_2024, alpha=alpha)
        cond = self._conditional_probs(df_2024, alpha=alpha)
        # 4) 在2025上预测与验证
        rows = []
        hits_any = 0; total_p = 0
        full_hits_top2 = 0
        total_groups = 0; full_hits_groups = 0
        baseline_p = self._random_pair_full_hit_prob()
        df_all = df.copy().reset_index(drop=True)
        for idx in df_all.index[df_all['年份']==2025]:
            row = df_all.iloc[idx]
            prev_nums = self._previous_numbers(df_all, idx)
            if prev_nums is None: continue
            # top-2预测
            pred, _ = self._predict_pair(prev_nums, model_type, freq, cond, mix_lambda)
            cols = [f'数字{i}' for i in range(1,7)]
            actual = [int(v) for v in row[cols].tolist()]
            hit1, full2 = self._hit_metrics(pred, actual)
            hits_any += int(hit1>=1); full_hits_top2 += int(full2); total_p += 1
            # 构造打分分布用于采样多组
            if model_type=='freq':
                scores = freq.copy()
            elif model_type=='cond':
                scores = np.zeros_like(freq)
                for c in set(prev_nums):
                    scores += cond.get(c, np.ones(49)/49)
                if scores.sum()>0: scores/=scores.sum()
            else:
                cond_s = np.zeros_like(freq)
                for c in set(prev_nums):
                    cond_s += cond.get(c, np.ones(49)/49)
                if cond_s.sum()>0: cond_s/=cond_s.sum()
                scores = mix_lambda*cond_s + (1-mix_lambda)*freq
            pairs = self._sample_pairs(scores, G=groups_per_period)
            hit_pairs = 0
            for pr in pairs:
                _, f2 = self._hit_metrics(pr, actual)
                hit_pairs += int(f2)
            total_groups += groups_per_period
            full_hits_groups += hit_pairs
            rows.append({
                '年份': int(row['年份']), '期号': int(row['期号']),
                '上一期': f"{int(df_all.iloc[idx-1]['年份'])}年{int(df_all.iloc[idx-1]['期号'])}期",
                '预测top2_1': pred[0], '预测top2_2': pred[1], 'top2_是否命中至少1': int(hit1>=1), 'top2_是否2数全中': int(full2),
                'G': groups_per_period, '命中组数_2全中': hit_pairs
            })
        # 5) 统计指标与置信区间
        hit_rate_any = hits_any/total_p if total_p>0 else 0
        full_rate_top2 = full_hits_top2/total_p if total_p>0 else 0
        full_rate_groups = full_hits_groups/total_groups if total_groups>0 else 0
        ci_any = self._wilson_interval(hits_any, total_p)
        ci_full2_top2 = self._wilson_interval(full_hits_top2, total_p)
        ci_full2_groups = self._wilson_interval(full_hits_groups, total_groups)
        # 命中期数清单
        hit_periods_atleast1 = [f"{r['年份']}年{r['期号']}期" for r in rows if r['top2_是否命中至少1']==1]
        hit_periods_full2_top2 = [f"{r['年份']}年{r['期号']}期" for r in rows if r['top2_是否2数全中']==1]
        hit_periods_groups_anyfull2 = [f"{r['年份']}年{r['期号']}期" for r in rows if r['命中组数_2全中']>0]
        # 与随机基线对比（近似独立）
        expected_any_groups = 1 - (1 - baseline_p)**groups_per_period
        uplift = full_rate_groups - baseline_p
        # z与p近似（双侧，正态近似）
        if total_groups>0:
            denom = np.sqrt(max(1e-12, baseline_p*(1-baseline_p)/total_groups))
            z = (full_rate_groups - baseline_p) / denom
            p_approx = 2*(1 - 0.5*(1 + np.math.erf(abs(z)/np.sqrt(2))))
        else:
            z = None
            p_approx = None
        # 保存报告
        os.makedirs('reports', exist_ok=True)
        detail_path = os.path.join('reports', 'validation_2025.csv')
        summary_path = os.path.join('reports', 'summary_2025.json')
        pd.DataFrame(rows).to_csv(detail_path, index=False, encoding='utf-8-sig')
        summary = {
            '模型类型': model_type, 'alpha': alpha,
            '测试期数': total_p,
            '命中期数_至少1': hits_any, '命中率_至少1': hit_rate_any, '命中率_至少1_Wilson95CI': ci_any,
            '命中期数_top2_2全中': full_hits_top2, '命中率_top2_2全中': full_rate_top2, 'top2_2全中_Wilson95CI': ci_full2_top2,
            '总组数': total_groups, '命中组数_2全中': full_hits_groups,
            '组层面命中率_2全中': full_rate_groups, '组层面命中率_2全中_Wilson95CI': ci_full2_groups,
            '随机基线_单组2全中': baseline_p,
            '随机基线_G组至少1全中_近似': expected_any_groups,
            '相对基线提升(组层面2全中)': uplift,
            'z_vs_baseline_groups': z,
            'p_value_vs_baseline_groups_approx': p_approx,
            '命中的期数_top2_至少1': hit_periods_atleast1,
            '命中的期数_top2_2全中': hit_periods_full2_top2,
            '命中的期数_组层面至少一组2全中': hit_periods_groups_anyfull2
        }
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        print("\n📄 验证报告已生成:")
        print(f"- 明细: {detail_path}")
        print(f"- 汇总: {summary_path}")
        print(f"测试期数: {total_p}, 命中(至少1): {hits_any} ({hit_rate_any:.2%})，top2的2全中: {full_hits_top2} ({full_rate_top2:.4%})")
        print(f"多组(G={groups_per_period})2全中: {full_hits_groups}/{total_groups} ({full_rate_groups:.4%})，随机基线≈{baseline_p:.6f}")
        return {'detail_path': detail_path, 'summary_path': summary_path}


    def main_menu(self):
        """主菜单"""
        while True:
            print(f"\n🎯 升级版手动输入预测系统")
            print("=" * 40)
            print("1. 输入当期数据并预测下期 (自动保存)")
            print("2. 查看预测统计")
            print("3. 退出系统")
            print("4. 训练/验证分析并生成报告 (2024训练, 2025测试)")

            choice = input("\n请选择操作 (1-3): ").strip()

            if choice == '1':
                current_data = self.input_current_period_data()
                if current_data:
                    self.make_prediction_and_save(current_data)

            elif choice == '2':
                self.show_recent_predictions()

            elif choice == '3':
                print("👋 感谢使用，再见！")
                break

            elif choice == '4':
                try:
                    gp = input("请输入每期采样组数G(默认5): ").strip()
                    G = int(gp) if gp.isdigit() and int(gp)>0 else 5
                    res = self.run_train_test_analysis(groups_per_period=G)
                    if res:
                        print(f"报告已生成: {res['detail_path']} 和 {res['summary_path']}")
                except Exception as e:
                    print(f"❌ 分析失败: {e}")

            else:
                print("❌ 无效选择，请重新输入")

def main():
    """主函数"""
    print("🎯 升级版手动输入预测系统")
    print("集成数据管理 - 自动保存到主文件和预测CSV")
    print("=" * 60)

    system = UpgradedManualPredictionSystem()

    # 初始化系统
    if not system.load_data_and_build_model():
        print("❌ 系统初始化失败")
        return

    print("✅ 系统初始化完成")
    print("\n💡 功能说明:")
    print("1. 输入真实开奖数据自动添加到主数据文件")
    print("2. 预测数据自动保存到prediction_data.csv")
    print("3. 完整的数据管理和统计功能")

    # 启动主菜单
    system.main_menu()

if __name__ == "__main__":
    main()
