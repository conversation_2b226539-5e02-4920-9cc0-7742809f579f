#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一测试环境
建立标准化的性能评估和对比测试环境
"""

import pandas as pd
import numpy as np
from collections import Counter, defaultdict
import json
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class UnifiedTestingEnvironment:
    """统一测试环境 - 标准化性能评估"""
    
    def __init__(self, data_file='prediction_data.csv'):
        """初始化测试环境"""
        self.data_file = data_file
        self.prediction_data = None
        self.baseline_metrics = {}
        self.test_results = {}
        
    def load_data(self):
        """加载预测数据"""
        try:
            self.prediction_data = pd.read_csv(self.data_file, encoding='utf-8')
            print(f"✅ 成功加载 {len(self.prediction_data)} 条预测记录")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def establish_baseline_metrics(self):
        """建立基线性能指标"""
        print("\n📊 建立基线性能指标...")
        
        valid_data = self.prediction_data.dropna(subset=['是否命中'])
        
        # 1. 命中率指标
        total_predictions = len(valid_data)
        hits = (valid_data['是否命中'] == '是').sum()
        hit_rate = hits / total_predictions if total_predictions > 0 else 0
        
        # 2. 置信度准确性指标
        confidence_data = valid_data.dropna(subset=['预测置信度'])
        
        # 按置信度分组计算实际命中率
        confidence_bins = np.linspace(0.02, 0.04, 6)
        confidence_accuracy = {}
        
        for i in range(len(confidence_bins) - 1):
            lower, upper = confidence_bins[i], confidence_bins[i + 1]
            mask = (confidence_data['预测置信度'] >= lower) & (confidence_data['预测置信度'] < upper)
            subset = confidence_data[mask]
            
            if len(subset) > 0:
                actual_hit_rate = (subset['是否命中'] == '是').mean()
                avg_confidence = subset['预测置信度'].mean()
                confidence_accuracy[f"{lower:.3f}-{upper:.3f}"] = {
                    'predicted_confidence': avg_confidence,
                    'actual_hit_rate': actual_hit_rate,
                    'accuracy_ratio': actual_hit_rate / avg_confidence if avg_confidence > 0 else 0,
                    'sample_size': len(subset)
                }
        
        # 3. 预测多样性指标
        pred_num1_counts = Counter(valid_data['预测数字1'].dropna())
        pred_num2_counts = Counter(valid_data['预测数字2'].dropna())
        
        # 计算熵（多样性指标）
        def calculate_entropy(counts):
            total = sum(counts.values())
            if total == 0:
                return 0
            probs = [count / total for count in counts.values()]
            return -sum(p * np.log2(p) for p in probs if p > 0)
        
        pred_num1_entropy = calculate_entropy(pred_num1_counts)
        pred_num2_entropy = calculate_entropy(pred_num2_counts)
        avg_entropy = (pred_num1_entropy + pred_num2_entropy) / 2
        
        # 计算基尼系数（不平等指标，越低越好）
        def calculate_gini(counts):
            values = list(counts.values())
            if not values:
                return 0
            values.sort()
            n = len(values)
            cumsum = np.cumsum(values)
            return (n + 1 - 2 * sum((n + 1 - i) * y for i, y in enumerate(values))) / (n * sum(values))
        
        pred_num1_gini = calculate_gini(pred_num1_counts)
        pred_num2_gini = calculate_gini(pred_num2_counts)
        avg_gini = (pred_num1_gini + pred_num2_gini) / 2
        
        # 4. 时间序列稳定性指标
        # 按时间窗口计算命中率变化
        window_size = 20
        rolling_hit_rates = []
        
        for i in range(window_size, len(valid_data)):
            window_data = valid_data.iloc[i-window_size:i]
            window_hit_rate = (window_data['是否命中'] == '是').mean()
            rolling_hit_rates.append(window_hit_rate)
        
        hit_rate_volatility = np.std(rolling_hit_rates) if rolling_hit_rates else 0
        
        self.baseline_metrics = {
            'basic_metrics': {
                'total_predictions': total_predictions,
                'hits': hits,
                'hit_rate': hit_rate,
                'avg_confidence': confidence_data['预测置信度'].mean(),
                'confidence_std': confidence_data['预测置信度'].std()
            },
            'confidence_accuracy': confidence_accuracy,
            'diversity_metrics': {
                'pred_num1_entropy': pred_num1_entropy,
                'pred_num2_entropy': pred_num2_entropy,
                'avg_entropy': avg_entropy,
                'pred_num1_gini': pred_num1_gini,
                'pred_num2_gini': pred_num2_gini,
                'avg_gini': avg_gini,
                'max_entropy_possible': np.log2(49)  # 理论最大熵
            },
            'stability_metrics': {
                'hit_rate_volatility': hit_rate_volatility,
                'rolling_window_size': window_size,
                'num_windows': len(rolling_hit_rates)
            }
        }
        
        print(f"   ✅ 基线命中率: {hit_rate:.1%}")
        print(f"   ✅ 平均置信度: {confidence_data['预测置信度'].mean():.4f}")
        print(f"   ✅ 预测多样性熵: {avg_entropy:.3f} (最大{np.log2(49):.3f})")
        print(f"   ✅ 预测不平等度: {avg_gini:.3f} (越低越好)")
        print(f"   ✅ 命中率波动性: {hit_rate_volatility:.3f}")
        
        return self.baseline_metrics
    
    def create_ab_testing_framework(self):
        """创建A/B测试框架"""
        print("\n🧪 创建A/B测试框架...")
        
        # 将数据分为训练集和测试集
        total_records = len(self.prediction_data)
        split_point = int(total_records * 0.7)  # 70%训练，30%测试
        
        train_data = self.prediction_data.iloc[:split_point].copy()
        test_data = self.prediction_data.iloc[split_point:].copy()
        
        ab_framework = {
            'data_split': {
                'total_records': total_records,
                'train_records': len(train_data),
                'test_records': len(test_data),
                'split_ratio': '70:30'
            },
            'test_methodology': {
                'approach': 'Historical Backtesting',
                'control_group': 'Original System',
                'treatment_groups': ['Optimization_1', 'Optimization_2', 'Optimization_3'],
                'evaluation_metrics': [
                    'hit_rate',
                    'confidence_accuracy',
                    'prediction_diversity',
                    'stability_score'
                ]
            },
            'statistical_significance': {
                'confidence_level': 0.95,
                'minimum_sample_size': 30,
                'test_methods': ['t-test', 'chi-square', 'mann-whitney-u']
            }
        }
        
        # 保存训练和测试数据
        train_data.to_csv('train_data.csv', index=False, encoding='utf-8')
        test_data.to_csv('test_data.csv', index=False, encoding='utf-8')
        
        print(f"   ✅ 训练集: {len(train_data)} 条记录")
        print(f"   ✅ 测试集: {len(test_data)} 条记录")
        print(f"   ✅ 数据文件已保存")
        
        return ab_framework, train_data, test_data
    
    def define_evaluation_metrics(self):
        """定义评估指标"""
        print("\n📏 定义标准化评估指标...")
        
        evaluation_metrics = {
            'primary_metrics': {
                'hit_rate': {
                    'description': '预测命中率',
                    'formula': 'hits / total_predictions',
                    'target': '>30%',
                    'weight': 0.4
                },
                'confidence_accuracy': {
                    'description': '置信度准确性',
                    'formula': 'correlation(predicted_confidence, actual_hit_rate)',
                    'target': '>0.7',
                    'weight': 0.3
                },
                'prediction_diversity': {
                    'description': '预测多样性',
                    'formula': 'entropy(prediction_distribution)',
                    'target': '>4.0',
                    'weight': 0.2
                },
                'stability_score': {
                    'description': '预测稳定性',
                    'formula': '1 / (1 + hit_rate_volatility)',
                    'target': '>0.8',
                    'weight': 0.1
                }
            },
            'secondary_metrics': {
                'response_time': {
                    'description': '响应时间',
                    'target': '<100ms'
                },
                'memory_usage': {
                    'description': '内存使用',
                    'target': '<500MB'
                },
                'implementation_complexity': {
                    'description': '实施复杂度',
                    'scale': '1-10 (越低越好)'
                }
            },
            'composite_score': {
                'formula': 'weighted_sum(primary_metrics)',
                'scale': '0-100',
                'interpretation': {
                    '90-100': '优秀',
                    '80-89': '良好',
                    '70-79': '一般',
                    '60-69': '需改进',
                    '<60': '不合格'
                }
            }
        }
        
        print("   ✅ 主要指标: 命中率(40%) + 置信度准确性(30%) + 多样性(20%) + 稳定性(10%)")
        print("   ✅ 次要指标: 响应时间、内存使用、实施复杂度")
        print("   ✅ 综合评分: 0-100分制")
        
        return evaluation_metrics
    
    def create_testing_pipeline(self):
        """创建测试管道"""
        print("\n🔄 创建测试管道...")
        
        testing_pipeline = {
            'stages': {
                'stage_1_preparation': {
                    'name': '数据准备',
                    'tasks': [
                        '加载历史数据',
                        '数据清洗和验证',
                        '训练测试集分割'
                    ],
                    'output': '标准化数据集'
                },
                'stage_2_baseline': {
                    'name': '基线测试',
                    'tasks': [
                        '计算原始系统指标',
                        '建立性能基准',
                        '记录基线结果'
                    ],
                    'output': '基线性能报告'
                },
                'stage_3_optimization': {
                    'name': '优化实施',
                    'tasks': [
                        '逐个实施优化措施',
                        '记录每步改进效果',
                        '保存中间结果'
                    ],
                    'output': '优化后系统'
                },
                'stage_4_evaluation': {
                    'name': '效果评估',
                    'tasks': [
                        '计算优化后指标',
                        '统计显著性检验',
                        '生成对比报告'
                    ],
                    'output': '评估报告'
                },
                'stage_5_validation': {
                    'name': '结果验证',
                    'tasks': [
                        '交叉验证',
                        '鲁棒性测试',
                        '最终确认'
                    ],
                    'output': '验证报告'
                }
            },
            'automation': {
                'automated_stages': ['stage_1', 'stage_2', 'stage_4'],
                'manual_stages': ['stage_3', 'stage_5'],
                'total_runtime': '预计2-3小时'
            }
        }
        
        print("   ✅ 5阶段测试管道已定义")
        print("   ✅ 自动化程度: 60%")
        print("   ✅ 预计运行时间: 2-3小时")
        
        return testing_pipeline
    
    def save_testing_environment(self, filename='unified_testing_environment.json'):
        """保存测试环境配置"""
        ab_framework, _, _ = self.create_ab_testing_framework()
        evaluation_metrics = self.define_evaluation_metrics()
        testing_pipeline = self.create_testing_pipeline()

        # 转换numpy类型为Python原生类型
        def convert_numpy_types(obj):
            if isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(v) for v in obj]
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            else:
                return obj

        environment_config = {
            'version': '1.0',
            'created_at': datetime.now().isoformat(),
            'baseline_metrics': convert_numpy_types(self.baseline_metrics),
            'ab_testing_framework': convert_numpy_types(ab_framework),
            'evaluation_metrics': convert_numpy_types(evaluation_metrics),
            'testing_pipeline': convert_numpy_types(testing_pipeline),
            'data_files': {
                'original': self.data_file,
                'train': 'train_data.csv',
                'test': 'test_data.csv'
            }
        }

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(environment_config, f, ensure_ascii=False, indent=2)
            print(f"✅ 测试环境配置已保存到 {filename}")
            return True
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False
    
    def run_environment_setup(self):
        """运行环境搭建"""
        print("🚀 开始搭建统一测试环境...")
        
        if not self.load_data():
            return False
        
        # 建立基线指标
        self.establish_baseline_metrics()
        
        # 创建A/B测试框架
        ab_framework, train_data, test_data = self.create_ab_testing_framework()
        
        # 定义评估指标
        evaluation_metrics = self.define_evaluation_metrics()
        
        # 创建测试管道
        testing_pipeline = self.create_testing_pipeline()
        
        # 保存环境配置
        self.save_testing_environment()
        
        # 生成环境报告
        self.generate_environment_report()
        
        print("\n✅ 统一测试环境搭建完成！")
        return True
    
    def generate_environment_report(self):
        """生成环境报告"""
        print("\n" + "="*60)
        print("📊 统一测试环境报告")
        print("="*60)
        
        baseline = self.baseline_metrics['basic_metrics']
        diversity = self.baseline_metrics['diversity_metrics']
        stability = self.baseline_metrics['stability_metrics']
        
        print(f"\n📈 基线性能指标:")
        print(f"   命中率: {baseline['hit_rate']:.1%}")
        print(f"   平均置信度: {baseline['avg_confidence']:.4f}")
        print(f"   预测多样性: {diversity['avg_entropy']:.3f}/{diversity['max_entropy_possible']:.3f}")
        print(f"   预测均衡度: {1-diversity['avg_gini']:.3f} (越高越好)")
        print(f"   稳定性评分: {1/(1+stability['hit_rate_volatility']):.3f}")
        
        print(f"\n🧪 测试环境配置:")
        print(f"   数据分割: 70%训练 + 30%测试")
        print(f"   评估指标: 4个主要指标 + 3个次要指标")
        print(f"   测试方法: 历史回测 + 统计检验")
        print(f"   显著性水平: 95%置信度")
        
        print(f"\n🔄 测试管道:")
        print(f"   阶段数: 5个阶段")
        print(f"   自动化程度: 60%")
        print(f"   预计时间: 2-3小时")
        
        print(f"\n📁 输出文件:")
        print(f"   train_data.csv - 训练数据集")
        print(f"   test_data.csv - 测试数据集")
        print(f"   unified_testing_environment.json - 环境配置")
        
        print(f"\n🎯 准备就绪:")
        print(f"   ✅ 基线指标已建立")
        print(f"   ✅ 测试框架已配置")
        print(f"   ✅ 评估标准已定义")
        print(f"   ✅ 可以开始优化实施")

if __name__ == "__main__":
    env = UnifiedTestingEnvironment()
    env.run_environment_setup()
