#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
[5,2]组合问题深度思辨与优化方案
分析循环模式产生原因，设计多种优化策略
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict, Counter
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class CombinationProblemAnalyzer:
    """[5,2]组合问题分析器"""
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.full_data = None
        self.train_data = None
        self.historical_data = None
        
        # 分析结果存储
        self.analysis_results = {}
        self.optimization_strategies = {}
        
    def load_data(self):
        """加载数据"""
        print(f"🧠 [5,2]组合问题深度思辨与优化")
        print("=" * 60)
        
        try:
            self.full_data = pd.read_csv(self.data_file)
            self.full_data = self.full_data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            self.train_data = self.full_data[
                (self.full_data['年份'] >= 2023) & 
                (self.full_data['年份'] <= 2024)
            ].copy()
            
            self.historical_data = self.full_data[
                ((self.full_data['年份'] >= 2023) & (self.full_data['年份'] <= 2024)) |
                ((self.full_data['年份'] == 2025) & (self.full_data['期号'] <= 185))
            ].copy()
            
            print(f"✅ 数据加载完成")
            print(f"  训练集: {len(self.train_data)}期")
            print(f"  历史数据: {len(self.historical_data)}期")
            
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def analyze_52_combination_in_history(self):
        """分析[5,2]组合在历史数据中的真实表现"""
        print(f"\n🔍 [5,2]组合历史真实表现分析")
        print("=" * 50)
        
        # 统计[5,2]组合在历史数据中的实际命中情况
        combination_52_hits = []
        total_periods = 0
        hit_periods = 0
        
        for _, row in self.historical_data.iterrows():
            actual_numbers = set([row[f'数字{j}'] for j in range(1, 7)])
            predicted_set = {5, 2}
            
            hit_count = len(predicted_set & actual_numbers)
            is_hit = hit_count >= 1
            
            combination_52_hits.append({
                'year': row['年份'],
                'period': row['期号'],
                'actual_numbers': list(actual_numbers),
                'hit_count': hit_count,
                'is_hit': is_hit,
                'hit_numbers': list(predicted_set & actual_numbers)
            })
            
            total_periods += 1
            if is_hit:
                hit_periods += 1
        
        hit_rate = hit_periods / total_periods if total_periods > 0 else 0
        
        print(f"[5,2]组合历史表现:")
        print(f"  总期数: {total_periods}")
        print(f"  命中期数: {hit_periods}")
        print(f"  命中率: {hit_rate:.3f} ({hit_rate*100:.1f}%)")
        
        # 分析命中分布
        hit_years = defaultdict(lambda: {'total': 0, 'hits': 0})
        for hit_info in combination_52_hits:
            year = hit_info['year']
            hit_years[year]['total'] += 1
            if hit_info['is_hit']:
                hit_years[year]['hits'] += 1
        
        print(f"\n年度命中分布:")
        for year in sorted(hit_years.keys()):
            year_data = hit_years[year]
            year_rate = year_data['hits'] / year_data['total']
            print(f"  {year}年: {year_data['hits']}/{year_data['total']} = {year_rate:.3f}")
        
        # 分析最近表现
        recent_50 = combination_52_hits[-50:]
        recent_hits = sum(1 for h in recent_50 if h['is_hit'])
        recent_rate = recent_hits / len(recent_50)
        
        print(f"\n最近50期表现:")
        print(f"  命中: {recent_hits}/{len(recent_50)} = {recent_rate:.3f}")
        print(f"  vs 总体: {recent_rate - hit_rate:+.3f}")
        
        self.analysis_results['52_historical'] = {
            'total_periods': total_periods,
            'hit_periods': hit_periods,
            'hit_rate': hit_rate,
            'recent_rate': recent_rate,
            'hit_details': combination_52_hits
        }
        
        return hit_rate
    
    def analyze_markov_bias(self):
        """分析马尔可夫模型对[5,2]的偏好原因"""
        print(f"\n🔬 马尔可夫模型偏好分析")
        print("=" * 50)
        
        # 构建转移概率矩阵
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 分析数字5和2的转移概率
        print(f"数字5的转移概率分析:")
        if 5 in transition_count:
            total_5 = sum(transition_count[5].values())
            top_transitions_5 = sorted(transition_count[5].items(), key=lambda x: x[1], reverse=True)[:10]
            for next_num, count in top_transitions_5:
                prob = count / total_5
                print(f"  5 → {next_num}: {count}次, 概率{prob:.3f}")
        
        print(f"\n数字2的转移概率分析:")
        if 2 in transition_count:
            total_2 = sum(transition_count[2].values())
            top_transitions_2 = sorted(transition_count[2].items(), key=lambda x: x[1], reverse=True)[:10]
            for next_num, count in top_transitions_2:
                prob = count / total_2
                print(f"  2 → {next_num}: {count}次, 概率{prob:.3f}")
        
        # 分析[5,2]互相转移的情况
        prob_5_to_2 = transition_count[5].get(2, 0) / sum(transition_count[5].values()) if 5 in transition_count else 0
        prob_2_to_5 = transition_count[2].get(5, 0) / sum(transition_count[2].values()) if 2 in transition_count else 0
        
        print(f"\n[5,2]互转分析:")
        print(f"  5 → 2 概率: {prob_5_to_2:.3f}")
        print(f"  2 → 5 概率: {prob_2_to_5:.3f}")
        print(f"  互转强度: {(prob_5_to_2 + prob_2_to_5)/2:.3f}")
        
        # 分析数字5和2在训练数据中的整体频率
        all_numbers = []
        for _, row in self.train_data.iterrows():
            all_numbers.extend([row[f'数字{j}'] for j in range(1, 7)])
        
        number_freq = Counter(all_numbers)
        total_numbers = len(all_numbers)
        
        freq_5 = number_freq[5] / total_numbers
        freq_2 = number_freq[2] / total_numbers
        
        print(f"\n训练数据中的频率:")
        print(f"  数字5频率: {number_freq[5]}次 / {total_numbers} = {freq_5:.3f}")
        print(f"  数字2频率: {number_freq[2]}次 / {total_numbers} = {freq_2:.3f}")
        print(f"  [5,2]总频率: {freq_5 + freq_2:.3f}")
        
        self.analysis_results['markov_bias'] = {
            'prob_5_to_2': prob_5_to_2,
            'prob_2_to_5': prob_2_to_5,
            'freq_5': freq_5,
            'freq_2': freq_2,
            'transition_count': dict(transition_count)
        }
    
    def analyze_selection_strategy_bias(self):
        """分析选择策略对[5,2]的偏好"""
        print(f"\n🎯 选择策略偏好分析")
        print("=" * 50)
        
        # 模拟选择策略对不同组合的评分
        test_combinations = [
            [5, 2], [1, 3], [7, 9], [10, 15], [20, 25], 
            [30, 35], [40, 45], [11, 22], [33, 44], [6, 8]
        ]
        
        # 使用最近10期数据评估各组合
        recent_data = self.historical_data.tail(10)
        
        combination_scores = {}
        for combo in test_combinations:
            score = self.evaluate_combination_performance(combo, recent_data)
            combination_scores[str(combo)] = score
        
        print(f"不同组合的选择策略评分:")
        sorted_combos = sorted(combination_scores.items(), key=lambda x: x[1], reverse=True)
        for combo_str, score in sorted_combos:
            print(f"  {combo_str}: {score:.3f}")
        
        # 分析[5,2]的排名
        combo_52_score = combination_scores['[5, 2]']
        better_combos = sum(1 for score in combination_scores.values() if score > combo_52_score)
        rank = better_combos + 1
        
        print(f"\n[5,2]组合排名: {rank}/{len(test_combinations)}")
        print(f"[5,2]评分: {combo_52_score:.3f}")
        
        self.analysis_results['selection_bias'] = {
            'combination_scores': combination_scores,
            'combo_52_rank': rank,
            'combo_52_score': combo_52_score
        }
    
    def evaluate_combination_performance(self, combination, historical_data):
        """评估组合在历史数据上的表现"""
        if len(historical_data) == 0:
            return 0.5
        
        hits = 0
        total = len(historical_data)
        
        for _, row in historical_data.iterrows():
            actual_numbers = [row[f'数字{j}'] for j in range(1, 7)]
            predicted_set = set(combination)
            actual_set = set(actual_numbers)
            
            if len(predicted_set & actual_set) >= 1:
                hits += 1
        
        return hits / total if total > 0 else 0.5
    
    def design_optimization_strategies(self):
        """设计优化策略"""
        print(f"\n🚀 优化策略设计")
        print("=" * 50)
        
        strategies = {
            '反循环机制': self.design_anti_cycle_strategy,
            '多样性强制': self.design_diversity_strategy,
            '动态权重调整': self.design_dynamic_weight_strategy,
            '外部随机注入': self.design_random_injection_strategy,
            '集成多方法': self.design_ensemble_strategy
        }
        
        for strategy_name, strategy_func in strategies.items():
            print(f"\n设计策略: {strategy_name}")
            strategy_details = strategy_func()
            self.optimization_strategies[strategy_name] = strategy_details
            print(f"  核心思路: {strategy_details['core_idea']}")
            print(f"  实现方法: {strategy_details['implementation']}")
            print(f"  预期效果: {strategy_details['expected_effect']}")
    
    def design_anti_cycle_strategy(self):
        """设计反循环机制"""
        return {
            'core_idea': '检测并主动打破预测循环',
            'implementation': '记录最近N期预测，如果重复超过阈值则强制选择不同候选',
            'expected_effect': '避免[5,2]等组合的过度重复',
            'parameters': {
                'memory_length': 5,
                'repeat_threshold': 3,
                'diversity_boost': 0.2
            },
            'code_snippet': '''
def anti_cycle_selection(candidates, recent_predictions, threshold=3):
    # 检查最近预测中的重复模式
    recent_combos = [tuple(sorted(pred)) for pred in recent_predictions[-5:]]
    combo_counts = Counter(recent_combos)
    
    # 如果某组合重复超过阈值，降低其选择概率
    adjusted_scores = []
    for candidate in candidates:
        combo = tuple(sorted(candidate))
        base_score = evaluate_candidate(candidate)
        
        if combo_counts[combo] >= threshold:
            penalty = 0.3 * combo_counts[combo]
            adjusted_score = max(0, base_score - penalty)
        else:
            adjusted_score = base_score
        
        adjusted_scores.append(adjusted_score)
    
    return candidates[np.argmax(adjusted_scores)]
            '''
        }
    
    def design_diversity_strategy(self):
        """设计多样性强制策略"""
        return {
            'core_idea': '强制保持候选预测的多样性',
            'implementation': '在候选生成时增加多样性约束，确保候选之间差异足够大',
            'expected_effect': '减少候选同质化，提高选择策略的有效性',
            'parameters': {
                'min_distance': 10,
                'diversity_weight': 0.3,
                'max_iterations': 100
            },
            'code_snippet': '''
def generate_diverse_candidates(base_method, num_candidates=10):
    candidates = []
    max_attempts = 100
    
    for i in range(num_candidates):
        attempts = 0
        while attempts < max_attempts:
            candidate = base_method(seed=42+i+attempts)
            
            # 检查与已有候选的差异
            is_diverse = True
            for existing in candidates:
                if calculate_distance(candidate, existing) < min_distance:
                    is_diverse = False
                    break
            
            if is_diverse:
                candidates.append(candidate)
                break
            
            attempts += 1
    
    return candidates
            '''
        }
    
    def design_dynamic_weight_strategy(self):
        """设计动态权重调整策略"""
        return {
            'core_idea': '根据预测链长度动态调整选择权重',
            'implementation': '随着预测链延长，增加随机性，减少对历史表现的依赖',
            'expected_effect': '缓解预测链累积误差，保持预测多样性',
            'parameters': {
                'initial_weight': 0.8,
                'decay_rate': 0.05,
                'min_weight': 0.3
            },
            'code_snippet': '''
def dynamic_weight_selection(candidates, scores, chain_length):
    # 计算动态权重
    historical_weight = max(0.3, 0.8 - 0.05 * chain_length)
    random_weight = 1 - historical_weight
    
    # 调整选择概率
    adjusted_scores = []
    for score in scores:
        random_component = np.random.uniform(0, 1)
        adjusted_score = historical_weight * score + random_weight * random_component
        adjusted_scores.append(adjusted_score)
    
    return candidates[np.argmax(adjusted_scores)]
            '''
        }
    
    def design_random_injection_strategy(self):
        """设计外部随机注入策略"""
        return {
            'core_idea': '定期注入完全随机的预测，打破系统性偏好',
            'implementation': '每隔N期强制使用随机预测，或在候选中加入随机选项',
            'expected_effect': '防止系统陷入局部最优，保持探索能力',
            'parameters': {
                'injection_frequency': 5,
                'random_ratio': 0.2,
                'exploration_boost': 0.1
            },
            'code_snippet': '''
def random_injection_selection(candidates, period, injection_freq=5):
    # 每隔N期强制随机选择
    if period % injection_freq == 0:
        return random.choice(candidates)
    
    # 或者在候选中加入随机选项
    random_candidates = generate_random_candidates(2)
    extended_candidates = candidates + random_candidates
    
    # 正常选择流程
    return normal_selection(extended_candidates)
            '''
        }
    
    def design_ensemble_strategy(self):
        """设计集成多方法策略"""
        return {
            'core_idea': '集成多种预测方法，减少单一方法的偏好',
            'implementation': '结合马尔可夫、奇偶平衡、频率分析等多种方法',
            'expected_effect': '平衡不同方法的偏好，提高预测稳定性',
            'parameters': {
                'method_weights': {'markov': 0.4, 'odd_even': 0.3, 'frequency': 0.3},
                'rebalance_frequency': 10,
                'performance_window': 20
            },
            'code_snippet': '''
def ensemble_prediction(prev_numbers, period):
    methods = {
        'markov': markov_prediction(prev_numbers),
        'odd_even': odd_even_prediction(),
        'frequency': frequency_prediction()
    }
    
    # 动态调整权重
    weights = calculate_dynamic_weights(methods, recent_performance)
    
    # 加权选择
    final_candidates = []
    for method, candidates in methods.items():
        weight = weights[method]
        weighted_candidates = [(c, weight) for c in candidates]
        final_candidates.extend(weighted_candidates)
    
    return weighted_selection(final_candidates)
            '''
        }
    
    def test_optimization_strategies(self):
        """测试优化策略效果"""
        print(f"\n🧪 优化策略效果测试")
        print("=" * 50)
        
        # 模拟测试各种策略
        test_periods = 15
        original_predictions = self.simulate_original_method(test_periods)
        
        print(f"原始方法预测结果:")
        self.analyze_prediction_diversity(original_predictions, "原始方法")
        
        # 测试反循环机制
        anti_cycle_predictions = self.simulate_anti_cycle_method(test_periods)
        print(f"\n反循环机制预测结果:")
        self.analyze_prediction_diversity(anti_cycle_predictions, "反循环机制")
        
        # 测试多样性强制
        diversity_predictions = self.simulate_diversity_method(test_periods)
        print(f"\n多样性强制预测结果:")
        self.analyze_prediction_diversity(diversity_predictions, "多样性强制")
    
    def simulate_original_method(self, periods):
        """模拟原始方法"""
        predictions = []
        prev_numbers = {1, 11, 22, 29, 40, 45}  # 185期数据
        
        for period in range(186, 186 + periods):
            # 简化的马尔可夫预测
            if {5, 2}.issubset(prev_numbers) or prev_numbers == {5, 2}:
                prediction = [5, 2]  # 模拟循环
            else:
                prediction = [5, 2]  # 模拟偏好
            
            predictions.append(prediction)
            prev_numbers = set(prediction)
        
        return predictions
    
    def simulate_anti_cycle_method(self, periods):
        """模拟反循环机制"""
        predictions = []
        prev_numbers = {1, 11, 22, 29, 40, 45}
        recent_predictions = []
        
        alternative_combos = [[1, 3], [7, 9], [10, 15], [20, 25], [30, 35]]
        
        for period in range(186, 186 + periods):
            # 检查最近预测中的重复
            if len(recent_predictions) >= 3:
                recent_combos = [tuple(sorted(pred)) for pred in recent_predictions[-3:]]
                if len(set(recent_combos)) == 1:  # 连续3期相同
                    # 强制选择不同的组合
                    prediction = list(np.random.choice(alternative_combos))
                else:
                    prediction = [5, 2]
            else:
                prediction = [5, 2]
            
            predictions.append(prediction)
            recent_predictions.append(prediction)
            prev_numbers = set(prediction)
        
        return predictions
    
    def simulate_diversity_method(self, periods):
        """模拟多样性强制方法"""
        predictions = []
        prev_numbers = {1, 11, 22, 29, 40, 45}
        
        diverse_combos = [[5, 2], [1, 3], [7, 9], [10, 15], [20, 25]]
        
        for period in range(186, 186 + periods):
            # 轮换使用不同组合
            combo_index = (period - 186) % len(diverse_combos)
            prediction = diverse_combos[combo_index]
            
            predictions.append(prediction)
            prev_numbers = set(prediction)
        
        return predictions
    
    def analyze_prediction_diversity(self, predictions, method_name):
        """分析预测多样性"""
        # 统计不同组合的数量
        unique_combos = set(tuple(sorted(pred)) for pred in predictions)
        combo_counts = Counter(tuple(sorted(pred)) for pred in predictions)
        
        print(f"  {method_name}:")
        print(f"    总预测期数: {len(predictions)}")
        print(f"    不同组合数: {len(unique_combos)}")
        print(f"    多样性指数: {len(unique_combos)/len(predictions):.3f}")
        
        # 显示最频繁的组合
        most_common = combo_counts.most_common(3)
        print(f"    最频繁组合: {most_common}")
        
        # 检查连续重复
        max_consecutive = self.find_max_consecutive_repeats(predictions)
        print(f"    最大连续重复: {max_consecutive}期")
    
    def find_max_consecutive_repeats(self, predictions):
        """找到最大连续重复期数"""
        if len(predictions) <= 1:
            return len(predictions)
        
        max_repeat = 1
        current_repeat = 1
        
        for i in range(1, len(predictions)):
            if tuple(sorted(predictions[i])) == tuple(sorted(predictions[i-1])):
                current_repeat += 1
                max_repeat = max(max_repeat, current_repeat)
            else:
                current_repeat = 1
        
        return max_repeat
    
    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        print(f"\n📋 [5,2]组合问题综合分析报告")
        print("=" * 70)
        
        # 问题诊断
        print(f"🔍 问题诊断:")
        print(f"1. 历史表现偏好:")
        if '52_historical' in self.analysis_results:
            hist_rate = self.analysis_results['52_historical']['hit_rate']
            recent_rate = self.analysis_results['52_historical']['recent_rate']
            print(f"   [5,2]历史命中率: {hist_rate:.3f}")
            print(f"   最近50期命中率: {recent_rate:.3f}")
            print(f"   表现趋势: {'上升' if recent_rate > hist_rate else '下降'}")
        
        print(f"\n2. 马尔可夫偏好:")
        if 'markov_bias' in self.analysis_results:
            bias_data = self.analysis_results['markov_bias']
            print(f"   5→2转移概率: {bias_data['prob_5_to_2']:.3f}")
            print(f"   2→5转移概率: {bias_data['prob_2_to_5']:.3f}")
            print(f"   数字5训练频率: {bias_data['freq_5']:.3f}")
            print(f"   数字2训练频率: {bias_data['freq_2']:.3f}")
        
        print(f"\n3. 选择策略偏好:")
        if 'selection_bias' in self.analysis_results:
            selection_data = self.analysis_results['selection_bias']
            print(f"   [5,2]评分排名: {selection_data['combo_52_rank']}/10")
            print(f"   [5,2]评分: {selection_data['combo_52_score']:.3f}")
        
        # 优化建议
        print(f"\n🚀 优化建议:")
        print(f"1. 立即可实施:")
        print(f"   - 实施反循环机制，检测连续重复")
        print(f"   - 增加候选多样性约束")
        print(f"   - 定期注入随机预测")
        
        print(f"\n2. 中期改进:")
        print(f"   - 动态调整选择权重")
        print(f"   - 集成多种预测方法")
        print(f"   - 建立预测质量监控")
        
        print(f"\n3. 长期优化:")
        print(f"   - 重新设计选择策略")
        print(f"   - 引入外部信息源")
        print(f"   - 建立自适应学习机制")

def main():
    """主函数"""
    print("🧠 [5,2]组合问题深度思辨与优化")
    print("=" * 80)
    
    analyzer = CombinationProblemAnalyzer()
    
    # 1. 加载数据
    if not analyzer.load_data():
        return
    
    # 2. 分析[5,2]组合历史表现
    analyzer.analyze_52_combination_in_history()
    
    # 3. 分析马尔可夫模型偏好
    analyzer.analyze_markov_bias()
    
    # 4. 分析选择策略偏好
    analyzer.analyze_selection_strategy_bias()
    
    # 5. 设计优化策略
    analyzer.design_optimization_strategies()
    
    # 6. 测试优化策略
    analyzer.test_optimization_strategies()
    
    # 7. 生成综合报告
    analyzer.generate_comprehensive_report()
    
    print(f"\n🎉 [5,2]组合问题分析完成")

if __name__ == "__main__":
    main()
