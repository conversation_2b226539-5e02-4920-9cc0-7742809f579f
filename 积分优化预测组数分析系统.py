#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
积分优化预测组数分析系统
基于积分规则：2数命中+600分，没命中-20分
计算最优预测组数以达到最大期望积分
"""

import pandas as pd
import numpy as np
from collections import Counter
import matplotlib.pyplot as plt
import seaborn as sns
from math import comb
from scipy.optimize import minimize_scalar
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ScoreOptimizationSystem:
    """积分优化预测组数分析系统"""
    
    def __init__(self):
        """初始化系统"""
        self.hit_score = 600      # 2数命中得分
        self.miss_penalty = -20   # 没命中扣分
        self.total_numbers = 49   # 总数字数量
        self.draw_numbers = 6     # 每期开奖数量
        
        # 基于之前分析的实际命中率
        self.empirical_hit_rate = 0.0128  # 实际2数全命中率约1.28%
        
    def calculate_theoretical_probabilities(self, num_groups):
        """计算理论概率"""
        # 单组2数全命中的理论概率
        single_group_hit_prob = comb(2, 2) * comb(47, 4) / comb(49, 6)
        
        # 多组预测的概率计算
        # 至少一组命中的概率 = 1 - 所有组都不命中的概率
        prob_no_hit = (1 - single_group_hit_prob) ** num_groups
        prob_at_least_one_hit = 1 - prob_no_hit
        
        # 期望命中组数
        expected_hits = num_groups * single_group_hit_prob
        
        return {
            'single_hit_prob': single_group_hit_prob,
            'prob_at_least_one_hit': prob_at_least_one_hit,
            'prob_no_hit': prob_no_hit,
            'expected_hits': expected_hits
        }
    
    def calculate_empirical_probabilities(self, num_groups):
        """基于实际数据计算概率"""
        # 使用实际观测到的命中率
        single_group_hit_prob = self.empirical_hit_rate
        
        # 多组预测的概率计算
        prob_no_hit = (1 - single_group_hit_prob) ** num_groups
        prob_at_least_one_hit = 1 - prob_no_hit
        expected_hits = num_groups * single_group_hit_prob
        
        return {
            'single_hit_prob': single_group_hit_prob,
            'prob_at_least_one_hit': prob_at_least_one_hit,
            'prob_no_hit': prob_no_hit,
            'expected_hits': expected_hits
        }
    
    def calculate_expected_score(self, num_groups, use_empirical=True):
        """计算期望积分"""
        if use_empirical:
            probs = self.calculate_empirical_probabilities(num_groups)
        else:
            probs = self.calculate_theoretical_probabilities(num_groups)
        
        # 期望积分 = 期望命中数 × 命中得分 + (总组数 - 期望命中数) × 扣分
        expected_hits = probs['expected_hits']
        expected_misses = num_groups - expected_hits
        
        expected_score = expected_hits * self.hit_score + expected_misses * self.miss_penalty
        
        return expected_score, probs
    
    def find_optimal_groups(self, max_groups=200):
        """寻找最优预测组数"""
        print("🎯 寻找最优预测组数")
        print("=" * 50)
        print(f"积分规则: 命中+{self.hit_score}分, 没命中{self.miss_penalty}分")
        
        # 计算不同组数的期望积分
        group_range = range(1, max_groups + 1)
        theoretical_scores = []
        empirical_scores = []
        
        for num_groups in group_range:
            # 理论期望积分
            theo_score, _ = self.calculate_expected_score(num_groups, use_empirical=False)
            theoretical_scores.append(theo_score)
            
            # 实际期望积分
            emp_score, _ = self.calculate_expected_score(num_groups, use_empirical=True)
            empirical_scores.append(emp_score)
        
        # 找到最优点
        theo_optimal_idx = np.argmax(theoretical_scores)
        emp_optimal_idx = np.argmax(empirical_scores)
        
        theo_optimal_groups = group_range[theo_optimal_idx]
        emp_optimal_groups = group_range[emp_optimal_idx]
        
        theo_optimal_score = theoretical_scores[theo_optimal_idx]
        emp_optimal_score = empirical_scores[emp_optimal_idx]
        
        print(f"\n📊 理论最优结果:")
        print(f"   最优组数: {theo_optimal_groups}")
        print(f"   最大期望积分: {theo_optimal_score:.2f}")
        
        print(f"\n📊 实际最优结果:")
        print(f"   最优组数: {emp_optimal_groups}")
        print(f"   最大期望积分: {emp_optimal_score:.2f}")
        
        # 详细分析最优点
        self._analyze_optimal_point(theo_optimal_groups, use_empirical=False, label="理论")
        self._analyze_optimal_point(emp_optimal_groups, use_empirical=True, label="实际")
        
        return {
            'theoretical': {
                'optimal_groups': theo_optimal_groups,
                'optimal_score': theo_optimal_score,
                'all_scores': theoretical_scores
            },
            'empirical': {
                'optimal_groups': emp_optimal_groups,
                'optimal_score': emp_optimal_score,
                'all_scores': empirical_scores
            },
            'group_range': list(group_range)
        }
    
    def _analyze_optimal_point(self, optimal_groups, use_empirical=True, label=""):
        """分析最优点的详细信息"""
        expected_score, probs = self.calculate_expected_score(optimal_groups, use_empirical)
        
        print(f"\n🔍 {label}最优点详细分析 (组数={optimal_groups}):")
        print(f"   单组命中概率: {probs['single_hit_prob']:.6f} ({probs['single_hit_prob']*100:.4f}%)")
        print(f"   至少一组命中概率: {probs['prob_at_least_one_hit']:.6f} ({probs['prob_at_least_one_hit']*100:.2f}%)")
        print(f"   期望命中组数: {probs['expected_hits']:.4f}")
        print(f"   期望积分: {expected_score:.2f}")
        
        # 计算盈亏平衡点
        break_even_hit_rate = abs(self.miss_penalty) / (self.hit_score + abs(self.miss_penalty))
        print(f"   盈亏平衡命中率: {break_even_hit_rate:.6f} ({break_even_hit_rate*100:.4f}%)")
        
        if probs['single_hit_prob'] > break_even_hit_rate:
            print(f"   ✅ 命中率高于盈亏平衡点，预测有利")
        else:
            print(f"   ❌ 命中率低于盈亏平衡点，预测不利")
    
    def sensitivity_analysis(self, optimal_groups_range):
        """敏感性分析"""
        print(f"\n📈 敏感性分析")
        print("=" * 50)
        
        # 分析最优组数附近的表现
        analysis_range = range(max(1, optimal_groups_range - 20), 
                              optimal_groups_range + 21)
        
        print(f"分析范围: {min(analysis_range)} - {max(analysis_range)} 组")
        print(f"\n组数 | 期望积分 | 命中概率 | 期望命中数")
        print("-" * 45)
        
        for num_groups in analysis_range:
            expected_score, probs = self.calculate_expected_score(num_groups, use_empirical=True)
            print(f"{num_groups:4d} | {expected_score:8.2f} | {probs['prob_at_least_one_hit']:8.4f} | {probs['expected_hits']:10.4f}")
    
    def risk_analysis(self, num_groups):
        """风险分析"""
        print(f"\n⚠️ 风险分析 (预测{num_groups}组)")
        print("=" * 50)
        
        expected_score, probs = self.calculate_expected_score(num_groups, use_empirical=True)
        
        # 计算不同情况下的积分
        scenarios = {
            '全部不中': num_groups * self.miss_penalty,
            '命中1组': 1 * self.hit_score + (num_groups - 1) * self.miss_penalty,
            '命中2组': 2 * self.hit_score + (num_groups - 2) * self.miss_penalty,
            '期望情况': expected_score
        }
        
        print("可能情况分析:")
        for scenario, score in scenarios.items():
            if scenario == '期望情况':
                print(f"   {scenario}: {score:.2f}分")
            else:
                print(f"   {scenario}: {score}分")
        
        # 风险指标
        worst_case = scenarios['全部不中']
        best_case_1_hit = scenarios['命中1组']
        
        print(f"\n风险指标:")
        print(f"   最坏情况: {worst_case}分")
        print(f"   命中1组可获得: {best_case_1_hit}分")
        print(f"   风险收益比: {abs(worst_case) / self.hit_score:.2f}")
    
    def generate_visualization(self, optimization_results):
        """生成可视化图表"""
        print(f"\n📊 生成可视化分析")
        print("=" * 50)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('积分优化预测组数分析', fontsize=16, fontweight='bold')
        
        group_range = optimization_results['group_range']
        theo_scores = optimization_results['theoretical']['all_scores']
        emp_scores = optimization_results['empirical']['all_scores']
        
        # 1. 期望积分曲线
        axes[0, 0].plot(group_range, theo_scores, 'b-', label='理论期望积分', alpha=0.7)
        axes[0, 0].plot(group_range, emp_scores, 'r-', label='实际期望积分', alpha=0.7)
        
        # 标记最优点
        theo_opt = optimization_results['theoretical']['optimal_groups']
        emp_opt = optimization_results['empirical']['optimal_groups']
        
        axes[0, 0].scatter([theo_opt], [optimization_results['theoretical']['optimal_score']], 
                          color='blue', s=100, zorder=5, label=f'理论最优({theo_opt}组)')
        axes[0, 0].scatter([emp_opt], [optimization_results['empirical']['optimal_score']], 
                          color='red', s=100, zorder=5, label=f'实际最优({emp_opt}组)')
        
        axes[0, 0].axhline(y=0, color='black', linestyle='--', alpha=0.5, label='盈亏平衡线')
        axes[0, 0].set_title('期望积分 vs 预测组数')
        axes[0, 0].set_xlabel('预测组数')
        axes[0, 0].set_ylabel('期望积分')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 命中概率曲线
        hit_probs = []
        for num_groups in group_range:
            _, probs = self.calculate_expected_score(num_groups, use_empirical=True)
            hit_probs.append(probs['prob_at_least_one_hit'])
        
        axes[0, 1].plot(group_range, hit_probs, 'g-', linewidth=2)
        axes[0, 1].set_title('至少一组命中概率 vs 预测组数')
        axes[0, 1].set_xlabel('预测组数')
        axes[0, 1].set_ylabel('至少一组命中概率')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 期望命中数曲线
        expected_hits = []
        for num_groups in group_range:
            _, probs = self.calculate_expected_score(num_groups, use_empirical=True)
            expected_hits.append(probs['expected_hits'])
        
        axes[1, 0].plot(group_range, expected_hits, 'm-', linewidth=2)
        axes[1, 0].set_title('期望命中组数 vs 预测组数')
        axes[1, 0].set_xlabel('预测组数')
        axes[1, 0].set_ylabel('期望命中组数')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 风险收益分析
        risk_ratios = []
        for num_groups in group_range:
            worst_case = num_groups * self.miss_penalty
            risk_ratio = abs(worst_case) / self.hit_score
            risk_ratios.append(risk_ratio)
        
        axes[1, 1].plot(group_range, risk_ratios, 'orange', linewidth=2)
        axes[1, 1].set_title('风险收益比 vs 预测组数')
        axes[1, 1].set_xlabel('预测组数')
        axes[1, 1].set_ylabel('风险收益比')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        filename = '积分优化预测组数分析图表.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"✅ 可视化图表已保存: {filename}")
        
        plt.show()
        return filename
    
    def run_complete_analysis(self):
        """运行完整分析"""
        print("🎯 积分优化预测组数分析系统")
        print("=" * 60)
        print(f"积分规则: 2数命中+{self.hit_score}分, 没命中{self.miss_penalty}分")
        print(f"基于实际命中率: {self.empirical_hit_rate:.4f} ({self.empirical_hit_rate*100:.2f}%)")
        
        # 1. 寻找最优组数
        optimization_results = self.find_optimal_groups()
        
        # 2. 敏感性分析
        emp_optimal = optimization_results['empirical']['optimal_groups']
        self.sensitivity_analysis(emp_optimal)
        
        # 3. 风险分析
        self.risk_analysis(emp_optimal)
        
        # 4. 生成可视化
        chart_file = self.generate_visualization(optimization_results)
        
        # 5. 总结建议
        self._generate_recommendations(optimization_results)
        
        return optimization_results
    
    def _generate_recommendations(self, results):
        """生成建议"""
        print(f"\n🚀 优化建议")
        print("=" * 50)
        
        emp_optimal = results['empirical']['optimal_groups']
        emp_score = results['empirical']['optimal_score']
        
        print(f"📊 基于实际数据的建议:")
        print(f"   推荐预测组数: {emp_optimal} 组")
        print(f"   期望积分: {emp_score:.2f} 分")
        
        if emp_score > 0:
            print(f"   ✅ 期望积分为正，长期来看有利可图")
        else:
            print(f"   ❌ 期望积分为负，长期来看会亏损")
        
        # 保守建议
        conservative_groups = max(1, emp_optimal // 2)
        conservative_score, _ = self.calculate_expected_score(conservative_groups, use_empirical=True)
        
        print(f"\n🛡️ 保守策略建议:")
        print(f"   保守预测组数: {conservative_groups} 组")
        print(f"   保守期望积分: {conservative_score:.2f} 分")
        
        # 激进建议
        aggressive_groups = min(200, emp_optimal * 2)
        aggressive_score, _ = self.calculate_expected_score(aggressive_groups, use_empirical=True)
        
        print(f"\n🚀 激进策略建议:")
        print(f"   激进预测组数: {aggressive_groups} 组")
        print(f"   激进期望积分: {aggressive_score:.2f} 分")

def main():
    """主函数"""
    print("🎯 积分优化预测组数分析系统")
    print("基于积分规则：2数命中+600分，没命中-20分")
    print("=" * 60)
    
    # 创建分析系统
    optimizer = ScoreOptimizationSystem()
    
    # 运行完整分析
    results = optimizer.run_complete_analysis()
    
    print("\n🎉 分析完成！")
    print("📊 请查看生成的可视化图表")

if __name__ == "__main__":
    main()
