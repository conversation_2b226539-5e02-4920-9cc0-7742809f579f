#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实评分系统更新prediction_data.csv文件
Update prediction_data.csv with real scoring system results

基于训练好的评分模型计算真实评分
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import pickle
from hit_rate_scoring_system import HitRateScoringSystem

def load_scoring_system():
    """加载评分系统"""
    print("🎯 加载评分系统...")
    
    scoring_model_file = "scoring_model.pkl"
    
    if os.path.exists(scoring_model_file):
        print("📂 加载已训练的评分模型...")
        with open(scoring_model_file, 'rb') as f:
            scoring_data = pickle.load(f)
        
        scoring_system = HitRateScoringSystem()
        scoring_system.model = scoring_data['model']
        scoring_system.features = scoring_data['features']
        scoring_system.score_thresholds = scoring_data['score_thresholds']
        scoring_system.validation_results = scoring_data['validation_results']
        
        print("✅ 评分系统加载完成")
        return scoring_system
    else:
        print("❌ 评分模型文件不存在，请先运行评分系统训练")
        return None

def load_validation_results():
    """加载验证结果数据"""
    print("📊 加载测试集验证结果...")
    
    validation_file = 'comprehensive_validation_results_20250715_203038.csv'
    if not os.path.exists(validation_file):
        print(f"❌ 验证结果文件不存在: {validation_file}")
        return None
    
    df = pd.read_csv(validation_file, encoding='utf-8-sig')
    print(f"✅ 验证结果加载完成: {len(df)}期数据")
    
    return df

def calculate_real_scores(validation_df, scoring_system):
    """计算真实评分"""
    print("🔧 计算真实评分...")
    
    scored_data = []
    
    for _, row in validation_df.iterrows():
        # 准备评分数据
        prediction_data = {
            'pred_num1': row['预测数字1'],
            'pred_num2': row['预测数字2'],
            'period': row['期号'],
            'original_confidence': row['原始置信度'],
            'optimized_confidence': row['优化置信度'],
            'accuracy_factor': row['准确率因子'],
            'calibration_factor': row['校准因子'],
            'stability_factor': row['稳定性因子'],
            'trend_factor': row['趋势因子'],
            'composite_factor': row['复合因子']
        }
        
        # 计算评分
        try:
            score_result = scoring_system.calculate_prediction_score(prediction_data)
        except Exception as e:
            print(f"⚠️ 第{row['期号']}期评分计算失败: {e}")
            score_result = {
                'score': 35.0,
                'probability': 0.35,
                'grade': 'C (评分失败)',
                'recommendation': '无法评分'
            }
        
        # 解析命中数字
        hit_numbers_str = row['命中数字']
        if hit_numbers_str == '[]':
            hit_numbers = []
        else:
            hit_numbers_str = hit_numbers_str.strip('[]')
            if hit_numbers_str:
                hit_numbers = [int(x.strip()) for x in hit_numbers_str.split(',') if x.strip()]
            else:
                hit_numbers = []
        
        # 构建完整记录
        scored_record = {
            '预测日期': '2025-07-15',
            '预测时间': '20:30:00',
            '当期年份': 2025,
            '当期期号': row['期号'] - 1,
            '预测期号': f"2025年{row['期号']}期",
            
            # 当期数字（占位符）
            '当期数字1': '',
            '当期数字2': '',
            '当期数字3': '',
            '当期数字4': '',
            '当期数字5': '',
            '当期数字6': '',
            
            # 预测数字
            '预测数字1': row['预测数字1'],
            '预测数字2': row['预测数字2'],
            '预测置信度': f"{row['原始置信度']:.6f}",
            '预测方法': '34.3%增强马尔可夫',
            
            # 真实评分信息
            '预测评分': f"{score_result['score']:.1f}",
            '评分等级': score_result['grade'],
            '评分建议': score_result['recommendation'],
            '评分概率': f"{score_result['probability']:.3f}",
            
            # 实际结果
            '实际数字1': row['实际数字1'],
            '实际数字2': row['实际数字2'],
            '实际数字3': row['实际数字3'],
            '实际数字4': row['实际数字4'],
            '实际数字5': row['实际数字5'],
            '实际数字6': row['实际数字6'],
            
            # 命中情况
            '命中数量': row['命中数量'],
            '是否命中': row['是否命中'],
            '命中数字': ','.join(map(str, hit_numbers)) if hit_numbers else '',
            '备注': '测试集验证+真实评分'
        }
        
        scored_data.append(scored_record)
        
        # 显示进度
        if row['期号'] % 20 == 0:
            print(f"  评分进度: 第{row['期号']}期 (评分: {score_result['score']:.1f}分)")
    
    print(f"✅ 真实评分计算完成: {len(scored_data)}期")
    return scored_data

def update_prediction_data_with_real_scores(scored_data):
    """使用真实评分更新prediction_data.csv文件"""
    print("💾 更新prediction_data.csv文件（真实评分）...")
    
    # 创建DataFrame
    scored_df = pd.DataFrame(scored_data)
    
    # 确保列顺序正确
    columns_order = [
        '预测日期', '预测时间', '当期年份', '当期期号', '预测期号',
        '当期数字1', '当期数字2', '当期数字3', '当期数字4', '当期数字5', '当期数字6',
        '预测数字1', '预测数字2', '预测置信度', '预测方法',
        '预测评分', '评分等级', '评分建议', '评分概率',
        '实际数字1', '实际数字2', '实际数字3', '实际数字4', '实际数字5', '实际数字6',
        '命中数量', '是否命中', '命中数字', '备注'
    ]
    
    # 重新排列列顺序
    scored_df = scored_df[columns_order]
    
    # 备份当前文件
    if os.path.exists('prediction_data.csv'):
        backup_filename = f'prediction_data_backup_real_scores_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        os.rename('prediction_data.csv', backup_filename)
        print(f"📁 原文件已备份为: {backup_filename}")
    
    # 保存新文件
    scored_df.to_csv('prediction_data.csv', index=False, encoding='utf-8-sig')
    
    print(f"✅ prediction_data.csv文件更新完成（真实评分）")
    print(f"   总记录数: {len(scored_df)}")
    print(f"   期号范围: 第{scored_df['当期期号'].min()}-{scored_df['当期期号'].max()}期")
    
    return scored_df

def generate_real_score_summary(scored_df):
    """生成真实评分摘要"""
    print("\n📊 真实评分摘要统计")
    print("="*50)
    
    # 基本统计
    total_records = len(scored_df)
    hit_records = len(scored_df[scored_df['是否命中'] == '是'])
    hit_rate = hit_records / total_records if total_records > 0 else 0
    
    print(f"总记录数: {total_records}")
    print(f"命中记录: {hit_records}")
    print(f"命中率: {hit_rate:.1%}")
    
    # 评分统计
    scores = pd.to_numeric(scored_df['预测评分'], errors='coerce')
    print(f"\n真实评分统计:")
    print(f"  平均评分: {scores.mean():.1f}分")
    print(f"  最高评分: {scores.max():.1f}分")
    print(f"  最低评分: {scores.min():.1f}分")
    print(f"  评分标准差: {scores.std():.1f}")
    
    # 评分分布
    high_scores = len(scores[scores >= 70])
    medium_high_scores = len(scores[(scores >= 60) & (scores < 70)])
    medium_scores = len(scores[(scores >= 50) & (scores < 60)])
    low_scores = len(scores[scores < 50])
    
    print(f"\n真实评分分布:")
    print(f"  极高评分(≥70分): {high_scores}期 ({high_scores/total_records:.1%})")
    print(f"  高评分(60-69分): {medium_high_scores}期 ({medium_high_scores/total_records:.1%})")
    print(f"  中评分(50-59分): {medium_scores}期 ({medium_scores/total_records:.1%})")
    print(f"  低评分(<50分): {low_scores}期 ({low_scores/total_records:.1%})")
    
    # 评分与命中率关系分析
    print(f"\n评分与命中率关系:")
    
    if high_scores > 0:
        high_score_df = scored_df[pd.to_numeric(scored_df['预测评分'], errors='coerce') >= 70]
        high_score_hits = len(high_score_df[high_score_df['是否命中'] == '是'])
        high_score_hit_rate = high_score_hits / len(high_score_df)
        print(f"  极高评分(≥70分): {high_score_hit_rate:.1%} ({high_score_hits}/{len(high_score_df)})")
    
    if medium_high_scores > 0:
        medium_high_df = scored_df[
            (pd.to_numeric(scored_df['预测评分'], errors='coerce') >= 60) & 
            (pd.to_numeric(scored_df['预测评分'], errors='coerce') < 70)
        ]
        medium_high_hits = len(medium_high_df[medium_high_df['是否命中'] == '是'])
        medium_high_hit_rate = medium_high_hits / len(medium_high_df)
        print(f"  高评分(60-69分): {medium_high_hit_rate:.1%} ({medium_high_hits}/{len(medium_high_df)})")
    
    if medium_scores > 0:
        medium_df = scored_df[
            (pd.to_numeric(scored_df['预测评分'], errors='coerce') >= 50) & 
            (pd.to_numeric(scored_df['预测评分'], errors='coerce') < 60)
        ]
        medium_hits = len(medium_df[medium_df['是否命中'] == '是'])
        medium_hit_rate = medium_hits / len(medium_df)
        print(f"  中评分(50-59分): {medium_hit_rate:.1%} ({medium_hits}/{len(medium_df)})")
    
    if low_scores > 0:
        low_score_df = scored_df[pd.to_numeric(scored_df['预测评分'], errors='coerce') < 50]
        low_score_hits = len(low_score_df[low_score_df['是否命中'] == '是'])
        low_score_hit_rate = low_score_hits / len(low_score_df)
        print(f"  低评分(<50分): {low_score_hit_rate:.1%} ({low_score_hits}/{len(low_score_df)})")
    
    # 显示一些高评分的例子
    if high_scores > 0 or medium_high_scores > 0:
        print(f"\n高评分预测示例:")
        high_score_examples = scored_df[pd.to_numeric(scored_df['预测评分'], errors='coerce') >= 60].head(5)
        for _, row in high_score_examples.iterrows():
            hit_status = "✅" if row['是否命中'] == '是' else "❌"
            print(f"  {row['预测期号']}: [{row['预测数字1']},{row['预测数字2']}] "
                  f"{row['预测评分']}分 {hit_status}")

def main():
    """主函数"""
    print("🔄 使用真实评分系统更新prediction_data.csv文件")
    print("="*70)
    
    try:
        # 1. 加载评分系统
        scoring_system = load_scoring_system()
        if scoring_system is None:
            return
        
        # 2. 加载验证结果
        validation_df = load_validation_results()
        if validation_df is None:
            return
        
        # 3. 计算真实评分
        scored_data = calculate_real_scores(validation_df, scoring_system)
        
        # 4. 更新CSV文件
        scored_df = update_prediction_data_with_real_scores(scored_data)
        
        # 5. 生成摘要统计
        generate_real_score_summary(scored_df)
        
        print(f"\n✅ 真实评分更新完成！")
        print(f"prediction_data.csv文件已更新，包含:")
        print(f"  - 第2-194期的完整预测数据")
        print(f"  - 基于机器学习模型的真实评分")
        print(f"  - 完整的命中验证结果")
        print(f"  - 评分与命中率关系分析")
        
    except Exception as e:
        print(f"❌ 更新过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
