#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态权重自主预测系统
基于严格验证动态权重系统，提供可自行运行的预测功能

核心功能：
1. 输入当期开奖数据，自动预测下期
2. 动态权重实时更新和调整
3. 预测结果自动保存到CSV文件
4. 完整的预测历史管理
5. 性能统计和分析功能
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
from collections import defaultdict, Counter, deque
import warnings
warnings.filterwarnings('ignore')

class DynamicWeightPredictionSystem:
    """动态权重自主预测系统"""
    
    def __init__(self):
        self.main_data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.prediction_data_file = "dynamic_weight_predictions.csv"
        self.system_state_file = "dynamic_weight_state.json"
        
        # 动态权重配置
        self.config = {
            'weight_detection': {
                'window_size': 15,          # 检测窗口大小
                'update_frequency': 5,      # 更新频率
                'min_appearance': 2,        # 最小出现次数
                'boost_factor': 1.3,        # 权重提升因子
                'decay_factor': 0.98,       # 权重衰减因子
                'regularization': 0.05      # 正则化强度
            },
            'prediction': {
                'base_confidence': 0.2,     # 基础置信度
                'hot_boost': 1.5,           # 热门数字加成
                'diversity_factor': 0.3,    # 多样性因子
                'min_distance': 6,          # 最小距离
                'exploration_rate': 0.25    # 探索率
            }
        }
        
        # 系统状态
        self.main_data = None
        self.base_weights = defaultdict(float)      # 基础权重
        self.dynamic_weights = defaultdict(float)   # 动态权重
        self.trend_weights = defaultdict(float)     # 趋势权重
        self.weight_history = deque(maxlen=50)      # 权重历史
        self.prediction_history = deque(maxlen=20)  # 预测历史
        self.system_initialized = False
        
    def initialize_system(self):
        """初始化系统"""
        print("🔧 初始化动态权重预测系统...")
        
        try:
            # 1. 加载主数据文件
            if not self.load_main_data():
                return False
            
            # 2. 初始化基础权重
            if not self.initialize_base_weights():
                return False
            
            # 3. 加载系统状态
            self.load_system_state()
            
            self.system_initialized = True
            print("✅ 系统初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            return False
    
    def load_main_data(self):
        """加载主数据文件"""
        try:
            if not os.path.exists(self.main_data_file):
                print(f"❌ 主数据文件不存在: {self.main_data_file}")
                return False
            
            self.main_data = pd.read_csv(self.main_data_file, encoding='utf-8')
            self.main_data = self.main_data.dropna().sort_values(['年份', '期号'])
            
            print(f"   主数据加载完成: {len(self.main_data)} 期")
            return True
            
        except Exception as e:
            print(f"❌ 主数据加载失败: {e}")
            return False
    
    def initialize_base_weights(self):
        """初始化基础权重"""
        try:
            # 使用最近100期数据初始化基础权重
            recent_data = self.main_data.tail(100)
            
            all_numbers = []
            for _, row in recent_data.iterrows():
                numbers = [int(row[f'数字{i}']) for i in range(1, 7)]
                all_numbers.extend(numbers)
            
            # 计算基础频率
            number_counts = Counter(all_numbers)
            total_count = len(all_numbers)
            
            # 初始化权重
            for num in range(1, 50):
                base_freq = number_counts[num] / total_count if total_count > 0 else 0
                # 应用正则化
                regularization = self.config['weight_detection']['regularization']
                self.base_weights[num] = base_freq * (1 - regularization) + regularization / 49
                self.dynamic_weights[num] = self.base_weights[num]
                self.trend_weights[num] = 0.0
            
            print(f"   基础权重初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ 基础权重初始化失败: {e}")
            return False
    
    def load_system_state(self):
        """加载系统状态"""
        try:
            if os.path.exists(self.system_state_file):
                with open(self.system_state_file, 'r', encoding='utf-8') as f:
                    state = json.load(f)
                
                # 恢复权重
                if 'dynamic_weights' in state:
                    for num_str, weight in state['dynamic_weights'].items():
                        self.dynamic_weights[int(num_str)] = weight
                
                if 'trend_weights' in state:
                    for num_str, weight in state['trend_weights'].items():
                        self.trend_weights[int(num_str)] = weight
                
                # 恢复历史
                if 'prediction_history' in state:
                    self.prediction_history = deque(
                        [tuple(combo) for combo in state['prediction_history']], 
                        maxlen=20
                    )
                
                print("   系统状态加载完成")
            
        except Exception as e:
            print(f"⚠️ 系统状态加载失败: {e}")
    
    def save_system_state(self):
        """保存系统状态"""
        try:
            state = {
                'dynamic_weights': {str(k): v for k, v in self.dynamic_weights.items()},
                'trend_weights': {str(k): v for k, v in self.trend_weights.items()},
                'prediction_history': list(self.prediction_history),
                'last_update': datetime.now().isoformat()
            }
            
            with open(self.system_state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            print(f"⚠️ 系统状态保存失败: {e}")
    
    def update_dynamic_weights(self, current_period):
        """更新动态权重"""
        update_freq = self.config['weight_detection']['update_frequency']
        
        # 只在指定频率更新权重
        if current_period % update_freq != 0:
            return False
        
        print(f"   🔄 更新动态权重 - 第{current_period}期")
        
        try:
            window_size = self.config['weight_detection']['window_size']
            min_appearance = self.config['weight_detection']['min_appearance']
            boost_factor = self.config['weight_detection']['boost_factor']
            decay_factor = self.config['weight_detection']['decay_factor']
            
            # 获取最近window_size期的数据
            recent_data = self.main_data.tail(window_size)
            
            # 提取最近期数的数字
            recent_numbers = []
            for _, row in recent_data.iterrows():
                numbers = [int(row[f'数字{i}']) for i in range(1, 7)]
                recent_numbers.extend(numbers)
            
            if len(recent_numbers) == 0:
                return False
            
            # 统计最近频率
            recent_counts = Counter(recent_numbers)
            total_recent = len(recent_numbers)
            
            # 识别热门数字
            hot_numbers = []
            for num in range(1, 50):
                if recent_counts[num] >= min_appearance:
                    recent_freq = recent_counts[num] / total_recent
                    base_freq = self.base_weights[num]
                    
                    if base_freq > 0:
                        weight_ratio = recent_freq / base_freq
                        if weight_ratio > 1.2:
                            hot_numbers.append(num)
            
            # 更新动态权重
            for num in range(1, 50):
                if num in hot_numbers:
                    # 热门数字权重提升
                    self.dynamic_weights[num] *= boost_factor
                    self.trend_weights[num] = min(1.0, self.trend_weights[num] + 0.15)
                else:
                    # 其他数字权重衰减
                    self.dynamic_weights[num] *= decay_factor
                    self.trend_weights[num] *= 0.95
            
            # 记录权重更新历史
            self.weight_history.append({
                'period': current_period,
                'hot_numbers': hot_numbers,
                'hot_count': len(hot_numbers)
            })
            
            print(f"     🔥 识别热门数字: {hot_numbers[:5]}{'...' if len(hot_numbers) > 5 else ''} (共{len(hot_numbers)}个)")
            
            return True
            
        except Exception as e:
            print(f"❌ 权重更新失败: {e}")
            return False
    
    def predict_next_period(self, current_period):
        """预测下期数字"""
        try:
            # 更新动态权重
            self.update_dynamic_weights(current_period)
            
            # 计算综合权重分数
            combined_scores = {}
            hot_boost = self.config['prediction']['hot_boost']
            
            for num in range(1, 50):
                # 基础动态权重
                base_score = self.dynamic_weights[num]
                
                # 趋势加成
                trend_score = self.trend_weights[num]
                if trend_score > 0.1:
                    base_score *= (1 + trend_score * 0.5)
                
                # 添加适度随机性
                exploration_rate = self.config['prediction']['exploration_rate']
                if np.random.random() < exploration_rate:
                    random_factor = np.random.uniform(0.8, 1.3)
                    base_score *= random_factor
                
                combined_scores[num] = base_score
            
            # 应用多样性约束
            combined_scores = self.apply_diversity_constraint(combined_scores)
            
            # 选择得分最高的两个数字
            sorted_scores = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
            
            selected = [sorted_scores[0][0]]
            min_distance = self.config['prediction']['min_distance']
            
            # 选择第二个数字，确保距离
            for num, score in sorted_scores[1:]:
                if abs(num - selected[0]) >= min_distance:
                    selected.append(num)
                    break
            
            if len(selected) < 2:
                selected.append(sorted_scores[1][0])
            
            # 计算置信度
            confidence = np.mean([combined_scores[num] for num in selected])
            confidence = min(0.8, max(0.1, confidence * 20))
            
            # 记录预测历史
            prediction_combo = tuple(sorted(selected))
            self.prediction_history.append(prediction_combo)
            
            return {
                'numbers': sorted(selected),
                'confidence': confidence,
                'method': 'Dynamic_Weight_Prediction',
                'scores': {num: combined_scores[num] for num in selected}
            }
            
        except Exception as e:
            print(f"❌ 预测失败: {e}")
            return self.fallback_prediction()
    
    def apply_diversity_constraint(self, scores):
        """应用多样性约束"""
        diversity_factor = self.config['prediction']['diversity_factor']
        
        # 对最近使用过的数字进行惩罚
        if len(self.prediction_history) >= 3:
            recent_numbers = set()
            for combo in list(self.prediction_history)[-3:]:
                recent_numbers.update(combo)
            
            for num in recent_numbers:
                if num in scores:
                    scores[num] *= (1 - diversity_factor)
        
        return scores
    
    def fallback_prediction(self):
        """备用预测方法"""
        sorted_weights = sorted(self.base_weights.items(), key=lambda x: x[1], reverse=True)
        candidates = [num for num, weight in sorted_weights[:20]]
        selected = sorted(np.random.choice(candidates, 2, replace=False))
        
        return {
            'numbers': selected,
            'confidence': 0.15,
            'method': 'Fallback_BaseWeight',
            'scores': {}
        }
    
    def input_current_period_data(self):
        """输入当期数据"""
        print("\n📝 输入当期开奖数据")
        print("=" * 40)
        
        while True:
            try:
                # 输入期号信息
                year = input("请输入年份 (如: 2025): ").strip()
                period = input("请输入期号 (如: 205): ").strip()
                
                if not year.isdigit() or not period.isdigit():
                    print("❌ 年份和期号必须是数字，请重新输入")
                    continue
                
                year = int(year)
                period = int(period)
                
                # 输入开奖数字
                print("请输入6个开奖数字:")
                print("支持格式: 空格分隔(5 12 23 31 40 45) 或 逗号分隔(5,12,23,31,40,45)")
                numbers_input = input("开奖数字: ").strip()
                
                # 解析数字
                if ',' in numbers_input:
                    numbers_str = numbers_input.replace(' ', '').split(',')
                else:
                    numbers_str = numbers_input.split()
                
                # 转换为整数
                numbers = []
                for num_str in numbers_str:
                    num_str = num_str.strip()
                    if num_str:
                        numbers.append(int(num_str))
                
                # 验证输入
                if len(numbers) != 6:
                    print("❌ 必须输入6个数字，请重新输入")
                    continue
                
                if not all(1 <= num <= 49 for num in numbers):
                    print("❌ 数字必须在1-49范围内，请重新输入")
                    continue
                
                if len(set(numbers)) != 6:
                    print("❌ 数字不能重复，请重新输入")
                    continue
                
                return {
                    'year': year,
                    'period': period,
                    'numbers': sorted(numbers)
                }
                
            except ValueError:
                print("❌ 输入格式错误，请重新输入")
            except KeyboardInterrupt:
                print("\n👋 用户取消输入")
                return None
    
    def add_data_to_main_file(self, year, period, numbers):
        """将数据添加到主数据文件"""
        try:
            # 检查数据是否已存在
            existing = self.main_data[
                (self.main_data['年份'] == year) & 
                (self.main_data['期号'] == period)
            ]
            
            if not existing.empty:
                print(f"   数据已存在: {year}年{period}期")
                return True
            
            # 创建新数据行
            new_row = {
                '年份': year,
                '期号': period,
                '数字1': numbers[0],
                '数字2': numbers[1],
                '数字3': numbers[2],
                '数字4': numbers[3],
                '数字5': numbers[4],
                '数字6': numbers[5]
            }
            
            # 添加到DataFrame
            new_df = pd.DataFrame([new_row])
            self.main_data = pd.concat([self.main_data, new_df], ignore_index=True)
            self.main_data = self.main_data.sort_values(['年份', '期号'])
            
            # 保存到文件
            self.main_data.to_csv(self.main_data_file, index=False, encoding='utf-8')
            
            print(f"   ✅ 数据已添加: {year}年{period}期")
            return True
            
        except Exception as e:
            print(f"❌ 添加数据失败: {e}")
            return False
    
    def save_prediction_to_csv(self, current_data, prediction_data):
        """保存预测到CSV文件"""
        try:
            # 准备预测数据
            prediction_record = {
                '预测时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                '基于年份': current_data['year'],
                '基于期号': current_data['period'],
                '基于数字': str(current_data['numbers']),
                '预测年份': current_data['year'],
                '预测期号': current_data['period'] + 1,
                '预测数字1': prediction_data['numbers'][0],
                '预测数字2': prediction_data['numbers'][1],
                '预测组合': str(prediction_data['numbers']),
                '预测置信度': round(prediction_data['confidence'], 4),
                '预测方法': prediction_data['method'],
                '预测得分': str(prediction_data.get('scores', {})),
                '实际数字1': '',
                '实际数字2': '',
                '实际数字3': '',
                '实际数字4': '',
                '实际数字5': '',
                '实际数字6': '',
                '实际组合': '',
                '命中数量': '',
                '是否命中': '',
                '命中率': '',
                '验证状态': '待验证'
            }
            
            # 读取现有数据或创建新文件
            if os.path.exists(self.prediction_data_file):
                existing_df = pd.read_csv(self.prediction_data_file, encoding='utf-8')
                new_df = pd.DataFrame([prediction_record])
                combined_df = pd.concat([existing_df, new_df], ignore_index=True)
            else:
                combined_df = pd.DataFrame([prediction_record])
            
            # 保存到文件
            combined_df.to_csv(self.prediction_data_file, index=False, encoding='utf-8')
            
            print(f"   ✅ 预测已保存到: {self.prediction_data_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存预测失败: {e}")
            return False

    def make_prediction_and_save(self, current_data):
        """进行预测并保存"""
        print(f"\n🎯 基于{current_data['year']}年{current_data['period']}期数据进行预测")
        print("=" * 60)

        # 1. 添加当期数据到主文件
        self.add_data_to_main_file(current_data['year'], current_data['period'], current_data['numbers'])

        # 2. 进行预测
        prediction_data = self.predict_next_period(current_data['period'])

        # 3. 显示预测结果
        print(f"\n📊 预测结果:")
        print(f"   预测期号: {current_data['year']}年{current_data['period'] + 1}期")
        print(f"   预测数字: {prediction_data['numbers']}")
        print(f"   预测置信度: {prediction_data['confidence']:.1%}")
        print(f"   预测方法: {prediction_data['method']}")

        # 4. 保存预测到CSV
        self.save_prediction_to_csv(current_data, prediction_data)

        # 5. 保存系统状态
        self.save_system_state()

        print(f"\n✅ 预测完成并已保存")
        return prediction_data

    def show_recent_predictions(self):
        """显示最近预测"""
        try:
            if not os.path.exists(self.prediction_data_file):
                print("📊 暂无预测数据")
                return

            df = pd.read_csv(self.prediction_data_file, encoding='utf-8')

            if df.empty:
                print("📊 暂无预测数据")
                return

            print(f"\n📊 最近预测记录 (共{len(df)}条)")
            print("=" * 80)

            # 显示最近10条记录
            recent_df = df.tail(10)

            for _, row in recent_df.iterrows():
                print(f"预测时间: {row['预测时间']}")
                print(f"基于数据: {row['基于年份']}年{row['基于期号']}期 {row['基于数字']}")
                print(f"预测结果: {row['预测年份']}年{row['预测期号']}期 {row['预测组合']}")
                print(f"置信度: {row['预测置信度']:.1%} | 方法: {row['预测方法']}")
                print(f"验证状态: {row['验证状态']}")
                print("-" * 60)

            # 统计信息
            total_predictions = len(df)
            verified_predictions = len(df[df['验证状态'] != '待验证'])

            if verified_predictions > 0:
                hit_predictions = len(df[df['是否命中'] == '是'])
                hit_rate = hit_predictions / verified_predictions
                print(f"\n📈 预测统计:")
                print(f"   总预测数: {total_predictions}")
                print(f"   已验证数: {verified_predictions}")
                print(f"   命中数: {hit_predictions}")
                print(f"   命中率: {hit_rate:.1%}")

        except Exception as e:
            print(f"❌ 显示预测记录失败: {e}")

    def verify_prediction(self, prediction_year, prediction_period, actual_numbers):
        """验证预测结果"""
        try:
            if not os.path.exists(self.prediction_data_file):
                print("❌ 预测数据文件不存在")
                return False

            df = pd.read_csv(self.prediction_data_file, encoding='utf-8')

            # 查找对应的预测记录
            mask = (df['预测年份'] == prediction_year) & (df['预测期号'] == prediction_period)
            matching_rows = df[mask]

            if matching_rows.empty:
                print(f"❌ 未找到{prediction_year}年{prediction_period}期的预测记录")
                return False

            # 更新验证结果
            for idx in matching_rows.index:
                predicted_numbers = eval(df.loc[idx, '预测组合'])

                # 计算命中情况
                pred_set = set(predicted_numbers)
                actual_set = set(actual_numbers)
                hit_count = len(pred_set & actual_set)

                # 更新记录
                df.loc[idx, '实际数字1'] = actual_numbers[0]
                df.loc[idx, '实际数字2'] = actual_numbers[1]
                df.loc[idx, '实际数字3'] = actual_numbers[2]
                df.loc[idx, '实际数字4'] = actual_numbers[3]
                df.loc[idx, '实际数字5'] = actual_numbers[4]
                df.loc[idx, '实际数字6'] = actual_numbers[5]
                df.loc[idx, '实际组合'] = str(actual_numbers)
                df.loc[idx, '命中数量'] = hit_count
                df.loc[idx, '是否命中'] = '是' if hit_count > 0 else '否'
                df.loc[idx, '命中率'] = hit_count / 2.0
                df.loc[idx, '验证状态'] = '已验证'

            # 保存更新后的数据
            df.to_csv(self.prediction_data_file, index=False, encoding='utf-8')

            # 显示验证结果
            hit_count = len(pred_set & actual_set)
            status = "✅ 命中" if hit_count > 0 else "❌ 未命中"

            print(f"\n🔍 预测验证结果:")
            print(f"   预测期号: {prediction_year}年{prediction_period}期")
            print(f"   预测数字: {predicted_numbers}")
            print(f"   实际数字: {actual_numbers}")
            print(f"   命中数量: {hit_count}")
            print(f"   验证结果: {status}")

            return True

        except Exception as e:
            print(f"❌ 验证预测失败: {e}")
            return False

    def show_system_status(self):
        """显示系统状态"""
        print(f"\n🔧 系统状态信息")
        print("=" * 50)

        # 基本信息
        print(f"系统初始化: {'✅ 已完成' if self.system_initialized else '❌ 未完成'}")
        print(f"主数据文件: {self.main_data_file}")
        print(f"预测数据文件: {self.prediction_data_file}")
        print(f"系统状态文件: {self.system_state_file}")

        if self.main_data is not None:
            print(f"主数据期数: {len(self.main_data)} 期")
            latest_data = self.main_data.tail(1).iloc[0]
            print(f"最新数据: {int(latest_data['年份'])}年{int(latest_data['期号'])}期")

        # 权重信息
        if self.dynamic_weights:
            weight_values = list(self.dynamic_weights.values())
            print(f"权重范围: {min(weight_values):.4f} - {max(weight_values):.4f}")
            print(f"权重更新: {len(self.weight_history)} 次")

        # 预测历史
        print(f"预测历史: {len(self.prediction_history)} 条")

        # 文件状态
        files_status = []
        for file_path in [self.main_data_file, self.prediction_data_file, self.system_state_file]:
            status = "✅ 存在" if os.path.exists(file_path) else "❌ 不存在"
            files_status.append(f"   {os.path.basename(file_path)}: {status}")

        print("文件状态:")
        for status in files_status:
            print(status)

    def main_menu(self):
        """主菜单"""
        while True:
            print(f"\n🎯 动态权重自主预测系统")
            print("=" * 50)
            print("1. 输入当期数据并预测下期")
            print("2. 查看预测历史")
            print("3. 验证预测结果")
            print("4. 查看系统状态")
            print("5. 退出系统")

            choice = input("\n请选择操作 (1-5): ").strip()

            if choice == '1':
                current_data = self.input_current_period_data()
                if current_data:
                    self.make_prediction_and_save(current_data)

            elif choice == '2':
                self.show_recent_predictions()

            elif choice == '3':
                self.verify_prediction_menu()

            elif choice == '4':
                self.show_system_status()

            elif choice == '5':
                print("👋 感谢使用，再见！")
                break

            else:
                print("❌ 无效选择，请重新输入")

    def verify_prediction_menu(self):
        """验证预测菜单"""
        print("\n🔍 验证预测结果")
        print("=" * 40)

        try:
            # 输入要验证的期号
            year = input("请输入要验证的年份 (如: 2025): ").strip()
            period = input("请输入要验证的期号 (如: 206): ").strip()

            if not year.isdigit() or not period.isdigit():
                print("❌ 年份和期号必须是数字")
                return

            year = int(year)
            period = int(period)

            # 输入实际开奖数字
            print("请输入实际开奖的6个数字:")
            numbers_input = input("开奖数字: ").strip()

            # 解析数字
            if ',' in numbers_input:
                numbers_str = numbers_input.replace(' ', '').split(',')
            else:
                numbers_str = numbers_input.split()

            numbers = [int(num_str.strip()) for num_str in numbers_str if num_str.strip()]

            # 验证输入
            if len(numbers) != 6:
                print("❌ 必须输入6个数字")
                return

            if not all(1 <= num <= 49 for num in numbers):
                print("❌ 数字必须在1-49范围内")
                return

            if len(set(numbers)) != 6:
                print("❌ 数字不能重复")
                return

            # 执行验证
            self.verify_prediction(year, period, sorted(numbers))

        except ValueError:
            print("❌ 输入格式错误")
        except Exception as e:
            print(f"❌ 验证失败: {e}")

def main():
    """主函数"""
    print("🎯 动态权重自主预测系统")
    print("基于严格验证的动态权重调整算法")
    print("=" * 70)

    system = DynamicWeightPredictionSystem()

    # 初始化系统
    if not system.initialize_system():
        print("❌ 系统初始化失败")
        return

    print("\n💡 功能说明:")
    print("1. 输入当期开奖数据，自动预测下期数字")
    print("2. 动态权重实时更新和调整")
    print("3. 预测结果自动保存到CSV文件")
    print("4. 完整的预测历史管理和验证功能")
    print("5. 系统状态监控和统计分析")

    # 启动主菜单
    system.main_menu()

if __name__ == "__main__":
    main()
