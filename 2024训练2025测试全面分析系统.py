#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2024年训练集 vs 2025年测试集全面分析系统
分析四种预测方案的准确命中率和命中期数统计
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import json
from datetime import datetime
from collections import defaultdict

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ComprehensivePredictionAnalyzer:
    """全面预测分析器"""
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.prediction_file = "生产优化2025年181-200期预测结果.csv"
        
        self.train_data = None
        self.test_data = None
        self.prediction_data = None
        self.analysis_results = {}
        
    def load_and_split_data(self):
        """加载数据并按年份分割"""
        try:
            # 加载主数据文件
            data = pd.read_csv(self.data_file)
            print(f"✅ 主数据加载成功: {len(data)}期")
            
            # 按年份分割数据
            self.train_data = data[data['年份'] == 2024].copy()
            self.test_data = data[data['年份'] == 2025].copy()
            
            # 筛选2025年1-185期作为测试集
            self.test_data = self.test_data[self.test_data['期号'] <= 185].copy()
            
            print(f"✅ 数据分割完成:")
            print(f"  训练集(2024年): {len(self.train_data)}期")
            print(f"  测试集(2025年1-185期): {len(self.test_data)}期")
            
            # 加载预测数据
            try:
                self.prediction_data = pd.read_csv(self.prediction_file)
                print(f"✅ 预测数据加载成功: {len(self.prediction_data)}期")
            except Exception as e:
                print(f"⚠️ 预测数据加载失败: {e}")
                self.prediction_data = None
            
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def build_markov_baseline_model(self):
        """基于2024年训练数据构建马尔可夫基线模型"""
        print(f"\n🔧 构建马尔可夫基线模型")
        print("=" * 50)
        
        transition_count = defaultdict(lambda: defaultdict(int))
        
        # 使用2024年数据构建转移矩阵
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 计算转移概率
        transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                transition_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        print(f"✅ 马尔可夫模型构建完成")
        print(f"  状态数量: {len(transition_prob)}")
        print(f"  训练样本: {len(self.train_data)}期")
        
        return transition_prob
    
    def generate_baseline_predictions(self, transition_prob):
        """生成基线预测"""
        print(f"\n🔮 生成基线预测")
        print("=" * 50)
        
        baseline_predictions = []
        
        # 使用2024年最后一期作为初始状态
        last_2024_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
        
        for idx, test_row in self.test_data.iterrows():
            period = test_row['期号']
            
            # 获取前一期数字作为当前状态
            if idx == self.test_data.index[0]:
                # 第一期使用2024年最后一期
                prev_numbers = last_2024_numbers
            else:
                prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                prev_numbers = set([self.test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            # 马尔可夫预测
            predicted_numbers, confidence = self._markov_prediction(prev_numbers, transition_prob)
            
            baseline_predictions.append({
                'period': period,
                'predicted_numbers': predicted_numbers,
                'confidence': confidence,
                'actual_numbers': [test_row[f'数字{j}'] for j in range(1, 7)]
            })
        
        print(f"✅ 基线预测生成完成: {len(baseline_predictions)}期")
        return baseline_predictions
    
    def _markov_prediction(self, previous_numbers, transition_prob):
        """马尔可夫预测方法"""
        number_probs = defaultdict(float)
        total_prob = 0.0
        coverage_count = 0
        
        for prev_num in previous_numbers:
            if prev_num in transition_prob:
                coverage_count += 1
                for next_num, prob in transition_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        # 添加随机扰动
        perturbation = 0.08
        for num in number_probs:
            noise = np.random.normal(0, perturbation * number_probs[num])
            number_probs[num] = max(0, number_probs[num] + noise)
        
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            predicted_numbers = [num for num, prob in sorted_numbers[:2]]
            
            # 置信度计算
            top_2_probs = [prob for num, prob in sorted_numbers[:2]]
            base_confidence = np.mean(top_2_probs)
            coverage_boost = (coverage_count / len(previous_numbers)) * 0.01
            enhanced_confidence = base_confidence + coverage_boost
            
            return predicted_numbers, enhanced_confidence
        else:
            return [1, 2], 0.01
    
    def analyze_four_prediction_schemes(self):
        """分析四种预测方案"""
        print(f"\n📊 分析四种预测方案")
        print("=" * 50)
        
        # 构建基线模型
        transition_prob = self.build_markov_baseline_model()
        baseline_predictions = self.generate_baseline_predictions(transition_prob)
        
        # 准备分析结果
        schemes_analysis = {}
        
        # 1. 分析基线预测方案
        schemes_analysis['马尔可夫基线'] = self._analyze_baseline_scheme(baseline_predictions)
        
        # 2. 分析预测文件中的四种方案（如果有的话）
        if self.prediction_data is not None:
            schemes_analysis.update(self._analyze_prediction_file_schemes())
        
        # 3. 生成对比分析
        self._generate_comparison_analysis(schemes_analysis)
        
        self.analysis_results['schemes_analysis'] = schemes_analysis
        return schemes_analysis
    
    def _analyze_baseline_scheme(self, baseline_predictions):
        """分析基线预测方案"""
        total_periods = len(baseline_predictions)
        hit_periods = 0
        hit_details = []
        
        for pred in baseline_predictions:
            predicted_set = set(pred['predicted_numbers'])
            actual_set = set(pred['actual_numbers'])
            hit_count = len(predicted_set & actual_set)
            is_hit = hit_count >= 1
            
            if is_hit:
                hit_periods += 1
            
            hit_details.append({
                'period': pred['period'],
                'predicted': pred['predicted_numbers'],
                'actual': pred['actual_numbers'],
                'hit_count': hit_count,
                'is_hit': is_hit,
                'confidence': pred['confidence']
            })
        
        hit_rate = hit_periods / total_periods if total_periods > 0 else 0
        
        return {
            'scheme_name': '马尔可夫基线',
            'total_periods': total_periods,
            'hit_periods': hit_periods,
            'hit_rate': hit_rate,
            'hit_details': hit_details
        }
    
    def _analyze_prediction_file_schemes(self):
        """分析预测文件中的四种方案"""
        schemes = {}
        
        # 合并预测数据和测试数据
        merged_data = pd.merge(
            self.prediction_data,
            self.test_data,
            left_on=['年份', '期号'],
            right_on=['年份', '期号'],
            how='inner'
        )
        
        if len(merged_data) == 0:
            print("⚠️ 预测数据与测试数据无法匹配")
            return schemes
        
        print(f"  预测数据匹配期数: {len(merged_data)}期")
        
        # 定义四种预测方案
        prediction_schemes = {
            '预测数字1': ('预测数字1', '预测数字2'),
            '预测数字2': ('预测数字1', '预测数字2'),  # 同一组预测
            '基础预测1': ('基础预测1', '基础预测2'),
            '基础预测2': ('基础预测1', '基础预测2')   # 同一组基础预测
        }
        
        for scheme_name, (col1, col2) in prediction_schemes.items():
            total_periods = len(merged_data)
            hit_periods = 0
            hit_details = []
            
            for _, row in merged_data.iterrows():
                predicted_numbers = [row[col1], row[col2]]
                actual_numbers = [row[f'数字{j}'] for j in range(1, 7)]
                
                predicted_set = set(predicted_numbers)
                actual_set = set(actual_numbers)
                hit_count = len(predicted_set & actual_set)
                is_hit = hit_count >= 1
                
                if is_hit:
                    hit_periods += 1
                
                hit_details.append({
                    'period': row['期号'],
                    'predicted': predicted_numbers,
                    'actual': actual_numbers,
                    'hit_count': hit_count,
                    'is_hit': is_hit,
                    'confidence': row.get('最终置信度', 0.5)
                })
            
            hit_rate = hit_periods / total_periods if total_periods > 0 else 0
            
            schemes[scheme_name] = {
                'scheme_name': scheme_name,
                'total_periods': total_periods,
                'hit_periods': hit_periods,
                'hit_rate': hit_rate,
                'hit_details': hit_details
            }
        
        return schemes
    
    def _generate_comparison_analysis(self, schemes_analysis):
        """生成对比分析"""
        print(f"\n📈 预测方案性能对比")
        print("=" * 60)
        
        print(f"{'方案名称':<15} {'总期数':<8} {'命中期数':<8} {'命中率':<10} {'性能等级'}")
        print("-" * 65)
        
        # 按命中率排序
        sorted_schemes = sorted(schemes_analysis.items(), 
                               key=lambda x: x[1]['hit_rate'], reverse=True)
        
        for scheme_name, result in sorted_schemes:
            hit_rate = result['hit_rate']
            total_periods = result['total_periods']
            hit_periods = result['hit_periods']
            
            # 性能等级评定
            if hit_rate >= 0.5:
                level = "⭐⭐⭐⭐⭐"
            elif hit_rate >= 0.4:
                level = "⭐⭐⭐⭐"
            elif hit_rate >= 0.3:
                level = "⭐⭐⭐"
            elif hit_rate >= 0.2:
                level = "⭐⭐"
            else:
                level = "⭐"
            
            print(f"{scheme_name:<15} {total_periods:<8} {hit_periods:<8} {hit_rate:.3f}      {level}")
    
    def statistical_significance_analysis(self):
        """统计显著性分析"""
        print(f"\n📊 统计显著性分析")
        print("=" * 50)
        
        if 'schemes_analysis' not in self.analysis_results:
            print("⚠️ 需要先进行预测方案分析")
            return
        
        schemes = self.analysis_results['schemes_analysis']
        baseline_rate = 2/49 * 2  # 理论随机基线
        
        print(f"基线概率: {baseline_rate:.4f} ({baseline_rate*100:.2f}%)")
        print(f"")
        print(f"{'方案名称':<15} {'命中率':<10} {'p值':<10} {'显著性':<8} {'vs基线'}")
        print("-" * 55)
        
        for scheme_name, result in schemes.items():
            n = result['total_periods']
            k = result['hit_periods']
            observed_rate = result['hit_rate']
            
            if n > 0:
                # 二项检验
                try:
                    p_value = stats.binom_test(k, n, baseline_rate, alternative='two-sided')
                except AttributeError:
                    # 新版本scipy
                    test_result = stats.binomtest(k, n, baseline_rate, alternative='two-sided')
                    p_value = test_result.pvalue
                
                is_significant = p_value < 0.05
                significance = "显著" if is_significant else "不显著"
                vs_baseline = f"{observed_rate - baseline_rate:+.3f}"
                
                print(f"{scheme_name:<15} {observed_rate:.3f}      {p_value:.4f}     {significance:<8} {vs_baseline}")
    
    def generate_detailed_report(self):
        """生成详细报告"""
        print(f"\n📋 详细分析报告")
        print("=" * 60)
        
        if 'schemes_analysis' not in self.analysis_results:
            print("⚠️ 需要先进行预测方案分析")
            return
        
        schemes = self.analysis_results['schemes_analysis']
        
        # 找出最佳方案
        best_scheme = max(schemes.items(), key=lambda x: x[1]['hit_rate'])
        best_name, best_result = best_scheme
        
        print(f"🏆 最佳预测方案: {best_name}")
        print(f"  命中率: {best_result['hit_rate']:.3f} ({best_result['hit_rate']*100:.1f}%)")
        print(f"  命中期数: {best_result['hit_periods']}/{best_result['total_periods']}")
        
        # 显示前10期详细结果
        print(f"\n📝 {best_name} 前10期详细结果:")
        print(f"{'期号':<6} {'预测':<12} {'实际':<25} {'命中':<6} {'命中数'}")
        print("-" * 60)
        
        for i, detail in enumerate(best_result['hit_details'][:10]):
            period = detail['period']
            predicted = str(detail['predicted'])
            actual = str(detail['actual'])
            hit_status = "✅" if detail['is_hit'] else "❌"
            hit_count = detail['hit_count']
            
            print(f"{period:<6} {predicted:<12} {actual:<25} {hit_status:<6} {hit_count}")
        
        if len(best_result['hit_details']) > 10:
            print(f"... (共{len(best_result['hit_details'])}期)")
    
    def create_visualization(self):
        """创建可视化图表"""
        print(f"\n🎨 生成可视化图表")
        print("=" * 50)
        
        if 'schemes_analysis' not in self.analysis_results:
            print("⚠️ 需要先进行预测方案分析")
            return
        
        schemes = self.analysis_results['schemes_analysis']
        
        # 创建对比图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 命中率对比
        scheme_names = list(schemes.keys())
        hit_rates = [schemes[name]['hit_rate'] for name in scheme_names]
        
        colors = plt.cm.Set3(np.linspace(0, 1, len(scheme_names)))
        bars1 = ax1.bar(scheme_names, hit_rates, color=colors, alpha=0.8, edgecolor='black')
        
        # 添加基线
        baseline_rate = 2/49 * 2
        ax1.axhline(y=baseline_rate, color='red', linestyle='--', linewidth=2, label=f'理论基线({baseline_rate:.3f})')
        ax1.axhline(y=0.292, color='blue', linestyle='--', linewidth=2, label='29.2%基线')
        
        for bar, rate in zip(bars1, hit_rates):
            ax1.text(bar.get_x() + bar.get_width()/2., rate + 0.01,
                    f'{rate:.3f}', ha='center', va='bottom', fontweight='bold')
        
        ax1.set_title('预测方案命中率对比', fontsize=14, fontweight='bold')
        ax1.set_ylabel('命中率', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
        
        # 2. 命中期数对比
        hit_periods = [schemes[name]['hit_periods'] for name in scheme_names]
        total_periods = [schemes[name]['total_periods'] for name in scheme_names]
        
        bars2 = ax2.bar(scheme_names, hit_periods, color=colors, alpha=0.8, edgecolor='black')
        
        for bar, hit, total in zip(bars2, hit_periods, total_periods):
            ax2.text(bar.get_x() + bar.get_width()/2., hit + 0.5,
                    f'{hit}/{total}', ha='center', va='bottom', fontweight='bold')
        
        ax2.set_title('命中期数统计', fontsize=14, fontweight='bold')
        ax2.set_ylabel('命中期数', fontsize=12)
        ax2.grid(True, alpha=0.3)
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
        
        # 3. 与基线对比
        baseline_comparison = [rate - baseline_rate for rate in hit_rates]
        colors3 = ['green' if x > 0 else 'red' for x in baseline_comparison]
        
        bars3 = ax3.bar(scheme_names, baseline_comparison, color=colors3, alpha=0.8, edgecolor='black')
        ax3.axhline(y=0, color='black', linestyle='-', linewidth=1)
        
        for bar, diff in zip(bars3, baseline_comparison):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., 
                    height + (0.005 if height > 0 else -0.015),
                    f'{diff:+.3f}', ha='center', 
                    va='bottom' if height > 0 else 'top', fontweight='bold')
        
        ax3.set_title('相对理论基线的性能差异', fontsize=14, fontweight='bold')
        ax3.set_ylabel('性能差异', fontsize=12)
        ax3.grid(True, alpha=0.3)
        plt.setp(ax3.xaxis.get_majorticklabels(), rotation=45)
        
        # 4. 累积命中率趋势（以最佳方案为例）
        best_scheme_name = max(schemes.keys(), key=lambda x: schemes[x]['hit_rate'])
        best_details = schemes[best_scheme_name]['hit_details']
        
        periods = [detail['period'] for detail in best_details[:50]]  # 前50期
        cumulative_hits = np.cumsum([detail['is_hit'] for detail in best_details[:50]])
        cumulative_rates = cumulative_hits / np.arange(1, len(cumulative_hits) + 1)
        
        ax4.plot(periods, cumulative_rates, marker='o', linewidth=2, markersize=4, 
                label=f'{best_scheme_name}累积命中率')
        ax4.axhline(y=baseline_rate, color='red', linestyle='--', linewidth=2, label='理论基线')
        ax4.axhline(y=0.292, color='blue', linestyle='--', linewidth=2, label='29.2%基线')
        
        ax4.set_title(f'{best_scheme_name} 累积命中率趋势', fontsize=14, fontweight='bold')
        ax4.set_xlabel('期号', fontsize=12)
        ax4.set_ylabel('累积命中率', fontsize=12)
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('2024训练2025测试全面分析图.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✅ 可视化图表已生成: 2024训练2025测试全面分析图.png")
    
    def save_analysis_results(self):
        """保存分析结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存到JSON文件
        results_file = f"2024训练2025测试分析结果_{timestamp}.json"
        
        # 处理numpy类型
        def convert_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_types(v) for v in obj]
            else:
                return obj
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(convert_types(self.analysis_results), f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 分析结果已保存: {results_file}")

def main():
    """主函数"""
    print("🔍 2024年训练集 vs 2025年测试集全面分析")
    print("分析四种预测方案的准确命中率和命中期数统计")
    print("=" * 80)
    
    analyzer = ComprehensivePredictionAnalyzer()
    
    # 1. 加载和分割数据
    if not analyzer.load_and_split_data():
        return
    
    # 2. 分析四种预测方案
    analyzer.analyze_four_prediction_schemes()
    
    # 3. 统计显著性分析
    analyzer.statistical_significance_analysis()
    
    # 4. 生成详细报告
    analyzer.generate_detailed_report()
    
    # 5. 创建可视化图表
    analyzer.create_visualization()
    
    # 6. 保存分析结果
    analyzer.save_analysis_results()
    
    print(f"\n🎉 全面分析完成")

if __name__ == "__main__":
    main()
