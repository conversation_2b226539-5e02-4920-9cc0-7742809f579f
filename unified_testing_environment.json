{"version": "1.0", "created_at": "2025-07-22T20:52:57.046691", "baseline_metrics": {"basic_metrics": {"total_predictions": 200, "hits": 58, "hit_rate": 0.29, "avg_confidence": 0.02963858, "confidence_std": 0.0018231698260648584}, "confidence_accuracy": {"0.024-0.028": {"predicted_confidence": 0.027317605263157893, "actual_hit_rate": 0.3684210526315789, "accuracy_ratio": 13.486579408497894, "sample_size": 38}, "0.028-0.032": {"predicted_confidence": 0.029756429577464792, "actual_hit_rate": 0.2746478873239437, "accuracy_ratio": 9.229866997616565, "sample_size": 142}, "0.032-0.036": {"predicted_confidence": 0.03285733333333333, "actual_hit_rate": 0.2777777777777778, "accuracy_ratio": 8.45405727116558, "sample_size": 18}, "0.036-0.040": {"predicted_confidence": 0.036401, "actual_hit_rate": 0.0, "accuracy_ratio": 0.0, "sample_size": 2}}, "diversity_metrics": {"pred_num1_entropy": 4.039443074384109, "pred_num2_entropy": 4.310275538521834, "avg_entropy": 4.174859306452971, "pred_num1_gini": -0.5480357142857143, "pred_num2_gini": -0.55765625, "avg_gini": -0.5528459821428571, "max_entropy_possible": 5.614709844115208}, "stability_metrics": {"hit_rate_volatility": 0.07243291915819615, "rolling_window_size": 20, "num_windows": 180}}, "ab_testing_framework": {"data_split": {"total_records": 202, "train_records": 141, "test_records": 61, "split_ratio": "70:30"}, "test_methodology": {"approach": "Historical Backtesting", "control_group": "Original System", "treatment_groups": ["Optimization_1", "Optimization_2", "Optimization_3"], "evaluation_metrics": ["hit_rate", "confidence_accuracy", "prediction_diversity", "stability_score"]}, "statistical_significance": {"confidence_level": 0.95, "minimum_sample_size": 30, "test_methods": ["t-test", "chi-square", "mann-whitney-u"]}}, "evaluation_metrics": {"primary_metrics": {"hit_rate": {"description": "预测命中率", "formula": "hits / total_predictions", "target": ">30%", "weight": 0.4}, "confidence_accuracy": {"description": "置信度准确性", "formula": "correlation(predicted_confidence, actual_hit_rate)", "target": ">0.7", "weight": 0.3}, "prediction_diversity": {"description": "预测多样性", "formula": "entropy(prediction_distribution)", "target": ">4.0", "weight": 0.2}, "stability_score": {"description": "预测稳定性", "formula": "1 / (1 + hit_rate_volatility)", "target": ">0.8", "weight": 0.1}}, "secondary_metrics": {"response_time": {"description": "响应时间", "target": "<100ms"}, "memory_usage": {"description": "内存使用", "target": "<500MB"}, "implementation_complexity": {"description": "实施复杂度", "scale": "1-10 (越低越好)"}}, "composite_score": {"formula": "weighted_sum(primary_metrics)", "scale": "0-100", "interpretation": {"90-100": "优秀", "80-89": "良好", "70-79": "一般", "60-69": "需改进", "<60": "不合格"}}}, "testing_pipeline": {"stages": {"stage_1_preparation": {"name": "数据准备", "tasks": ["加载历史数据", "数据清洗和验证", "训练测试集分割"], "output": "标准化数据集"}, "stage_2_baseline": {"name": "基线测试", "tasks": ["计算原始系统指标", "建立性能基准", "记录基线结果"], "output": "基线性能报告"}, "stage_3_optimization": {"name": "优化实施", "tasks": ["逐个实施优化措施", "记录每步改进效果", "保存中间结果"], "output": "优化后系统"}, "stage_4_evaluation": {"name": "效果评估", "tasks": ["计算优化后指标", "统计显著性检验", "生成对比报告"], "output": "评估报告"}, "stage_5_validation": {"name": "结果验证", "tasks": ["交叉验证", "鲁棒性测试", "最终确认"], "output": "验证报告"}}, "automation": {"automated_stages": ["stage_1", "stage_2", "stage_4"], "manual_stages": ["stage_3", "stage_5"], "total_runtime": "预计2-3小时"}}, "data_files": {"original": "prediction_data.csv", "train": "train_data.csv", "test": "test_data.csv"}}