#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
波动性指标计算器
检测彩票号码分布的剧烈波动，识别模式失效信号
为状态评估器提供市场波动性评估
"""

import pandas as pd
import numpy as np
import json
from collections import defaultdict, Counter
from datetime import datetime
from scipy import stats
from scipy.stats import entropy
import warnings
warnings.filterwarnings('ignore')

class VolatilityIndicator:
    """
    波动性指标计算器
    检测彩票号码分布的异常波动
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.data = None
        self.train_data = None
        self.test_data = None
        
        # 波动性分析配置
        self.config = {
            'volatility_window': 30,     # 波动性分析窗口
            'baseline_window': 90,       # 基线计算窗口
            'entropy_threshold': 0.1,    # 熵变化阈值
            'distribution_threshold': 0.05, # 分布变化阈值
            'pattern_threshold': 0.15,   # 模式变化阈值
            'anomaly_threshold': 2.0     # 异常检测阈值（标准差倍数）
        }
        
        # 波动性指标历史
        self.volatility_history = []
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            # 使用更大范围的数据进行波动性分析
            self.train_data = self.data[(self.data['年份'] >= 2021) & (self.data['年份'] < 2025)].copy()
            self.test_data = self.data[(self.data['年份'] == 2025) & (self.data['期号'] <= 182)].copy()
            
            print(f"✅ 数据加载成功")
            print(f"  历史数据: {len(self.train_data)}期 (2021-2024年)")
            print(f"  测试数据: {len(self.test_data)}期 (2025年)")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def calculate_number_distribution_entropy(self, data_window):
        """计算数字分布熵"""
        all_numbers = []
        for _, row in data_window.iterrows():
            numbers = [row[f'数字{j}'] for j in range(1, 7)]
            all_numbers.extend(numbers)
        
        # 计算数字频率分布
        number_counts = Counter(all_numbers)
        total_count = len(all_numbers)
        
        # 计算概率分布
        probabilities = [count / total_count for count in number_counts.values()]
        
        # 计算熵
        distribution_entropy = entropy(probabilities, base=2)
        
        return distribution_entropy, number_counts
    
    def calculate_range_distribution_volatility(self, data_window):
        """计算数字范围分布波动性"""
        range_distributions = []
        
        for _, row in data_window.iterrows():
            numbers = [row[f'数字{j}'] for j in range(1, 7)]
            
            # 计算范围分布
            small_count = sum(1 for n in numbers if 1 <= n <= 16)
            medium_count = sum(1 for n in numbers if 17 <= n <= 33)
            large_count = sum(1 for n in numbers if 34 <= n <= 49)
            
            range_distributions.append([small_count, medium_count, large_count])
        
        # 计算各范围的标准差
        range_array = np.array(range_distributions)
        range_volatility = np.mean(np.std(range_array, axis=0))
        
        return range_volatility, range_distributions
    
    def calculate_sum_volatility(self, data_window):
        """计算数字和的波动性"""
        sums = []
        
        for _, row in data_window.iterrows():
            numbers = [row[f'数字{j}'] for j in range(1, 7)]
            period_sum = sum(numbers)
            sums.append(period_sum)
        
        sum_volatility = np.std(sums)
        mean_sum = np.mean(sums)
        
        return sum_volatility, mean_sum, sums
    
    def calculate_gap_pattern_volatility(self, data_window):
        """计算间隔模式波动性"""
        gap_patterns = []
        
        for _, row in data_window.iterrows():
            numbers = sorted([row[f'数字{j}'] for j in range(1, 7)])
            gaps = [numbers[i+1] - numbers[i] for i in range(len(numbers)-1)]
            
            # 计算间隔特征
            avg_gap = np.mean(gaps)
            max_gap = max(gaps)
            min_gap = min(gaps)
            gap_std = np.std(gaps)
            
            gap_patterns.append([avg_gap, max_gap, min_gap, gap_std])
        
        # 计算间隔模式的波动性
        gap_array = np.array(gap_patterns)
        gap_volatility = np.mean(np.std(gap_array, axis=0))
        
        return gap_volatility, gap_patterns
    
    def calculate_consecutive_pattern_volatility(self, data_window):
        """计算连续性模式波动性"""
        consecutive_patterns = []
        
        for _, row in data_window.iterrows():
            numbers = sorted([row[f'数字{j}'] for j in range(1, 7)])
            
            # 计算连续性特征
            consecutive_count = 0
            for i in range(len(numbers) - 1):
                if numbers[i+1] - numbers[i] == 1:
                    consecutive_count += 1
            
            # 计算最大连续长度
            max_consecutive = 1
            current_consecutive = 1
            for i in range(len(numbers) - 1):
                if numbers[i+1] - numbers[i] == 1:
                    current_consecutive += 1
                    max_consecutive = max(max_consecutive, current_consecutive)
                else:
                    current_consecutive = 1
            
            consecutive_patterns.append([consecutive_count, max_consecutive])
        
        # 计算连续性模式的波动性
        consecutive_array = np.array(consecutive_patterns)
        consecutive_volatility = np.mean(np.std(consecutive_array, axis=0))
        
        return consecutive_volatility, consecutive_patterns
    
    def calculate_comprehensive_volatility(self, current_period_idx):
        """计算综合波动性指标"""
        window_size = self.config['volatility_window']
        baseline_size = self.config['baseline_window']
        
        # 合并所有数据
        all_data = pd.concat([self.train_data, self.test_data], ignore_index=True)
        
        if current_period_idx < baseline_size + window_size:
            return 0.5  # 数据不足时返回中性分数
        
        # 当前窗口
        current_window = all_data.iloc[current_period_idx - window_size:current_period_idx]
        
        # 基线窗口
        baseline_window = all_data.iloc[
            current_period_idx - baseline_size - window_size:
            current_period_idx - window_size
        ]
        
        volatility_indicators = {}
        
        # 1. 数字分布熵波动性
        current_entropy, current_dist = self.calculate_number_distribution_entropy(current_window)
        baseline_entropy, baseline_dist = self.calculate_number_distribution_entropy(baseline_window)
        
        entropy_change = abs(current_entropy - baseline_entropy) / baseline_entropy
        volatility_indicators['entropy_volatility'] = entropy_change
        
        # 2. 范围分布波动性
        current_range_vol, _ = self.calculate_range_distribution_volatility(current_window)
        baseline_range_vol, _ = self.calculate_range_distribution_volatility(baseline_window)
        
        range_volatility_change = abs(current_range_vol - baseline_range_vol) / baseline_range_vol
        volatility_indicators['range_volatility'] = range_volatility_change
        
        # 3. 数字和波动性
        current_sum_vol, current_mean_sum, _ = self.calculate_sum_volatility(current_window)
        baseline_sum_vol, baseline_mean_sum, _ = self.calculate_sum_volatility(baseline_window)
        
        sum_volatility_change = abs(current_sum_vol - baseline_sum_vol) / baseline_sum_vol
        sum_mean_change = abs(current_mean_sum - baseline_mean_sum) / baseline_mean_sum
        volatility_indicators['sum_volatility'] = (sum_volatility_change + sum_mean_change) / 2
        
        # 4. 间隔模式波动性
        current_gap_vol, _ = self.calculate_gap_pattern_volatility(current_window)
        baseline_gap_vol, _ = self.calculate_gap_pattern_volatility(baseline_window)
        
        gap_volatility_change = abs(current_gap_vol - baseline_gap_vol) / baseline_gap_vol
        volatility_indicators['gap_volatility'] = gap_volatility_change
        
        # 5. 连续性模式波动性
        current_cons_vol, _ = self.calculate_consecutive_pattern_volatility(current_window)
        baseline_cons_vol, _ = self.calculate_consecutive_pattern_volatility(baseline_window)
        
        consecutive_volatility_change = abs(current_cons_vol - baseline_cons_vol) / baseline_cons_vol
        volatility_indicators['consecutive_volatility'] = consecutive_volatility_change
        
        # 综合波动性评分
        weights = {
            'entropy_volatility': 0.3,
            'range_volatility': 0.2,
            'sum_volatility': 0.2,
            'gap_volatility': 0.15,
            'consecutive_volatility': 0.15
        }
        
        weighted_volatility = sum(
            weights[key] * value for key, value in volatility_indicators.items()
        )
        
        # 转换为0-1评分（低波动性得高分）
        volatility_score = max(0, min(1, 1 - weighted_volatility))
        
        return {
            'volatility_score': volatility_score,
            'weighted_volatility': weighted_volatility,
            'individual_indicators': volatility_indicators,
            'current_entropy': current_entropy,
            'baseline_entropy': baseline_entropy
        }
    
    def run_volatility_analysis(self):
        """运行波动性分析"""
        print(f"\n📊 运行波动性分析")
        print("=" * 60)
        
        # 合并所有数据
        all_data = pd.concat([self.train_data, self.test_data], ignore_index=True)
        
        baseline_size = self.config['baseline_window']
        window_size = self.config['volatility_window']
        
        for i in range(baseline_size + window_size, len(all_data), 5):  # 每5期分析一次
            volatility_result = self.calculate_comprehensive_volatility(i)
            
            if volatility_result:
                period_info = {
                    'period_idx': i,
                    'year': all_data.iloc[i-1]['年份'],
                    'period': all_data.iloc[i-1]['期号']
                }
                
                volatility_result.update(period_info)
                self.volatility_history.append(volatility_result)
        
        print(f"✅ 波动性分析完成，共{len(self.volatility_history)}个分析点")
        
        return self.volatility_history
    
    def analyze_volatility_patterns(self):
        """分析波动性模式"""
        print(f"\n🔍 分析波动性模式")
        print("-" * 40)
        
        if not self.volatility_history:
            print("❌ 无波动性历史数据")
            return None
        
        volatility_scores = [record['volatility_score'] for record in self.volatility_history]
        weighted_volatilities = [record['weighted_volatility'] for record in self.volatility_history]
        
        # 基本统计
        mean_score = np.mean(volatility_scores)
        std_score = np.std(volatility_scores)
        
        print(f"  平均波动性评分: {mean_score:.3f}")
        print(f"  波动性评分标准差: {std_score:.3f}")
        
        # 高波动性期间识别
        high_volatility_threshold = mean_score - std_score
        high_volatility_periods = [
            record for record in self.volatility_history
            if record['volatility_score'] <= high_volatility_threshold
        ]
        
        print(f"  高波动性期间: {len(high_volatility_periods)} ({len(high_volatility_periods)/len(self.volatility_history):.1%})")
        
        # 波动性趋势
        x = np.arange(len(volatility_scores))
        slope, _, r_value, p_value, _ = stats.linregress(x, volatility_scores)
        
        print(f"\n  波动性趋势:")
        print(f"    趋势斜率: {slope:.6f}")
        print(f"    相关系数: {r_value:.3f}")
        print(f"    趋势显著性: {'是' if p_value < 0.05 else '否'}")
        
        # 异常波动检测
        anomaly_threshold = mean_score - 2 * std_score
        anomaly_periods = [
            record for record in self.volatility_history
            if record['volatility_score'] <= anomaly_threshold
        ]
        
        print(f"\n  异常波动期间: {len(anomaly_periods)}")
        if anomaly_periods:
            print(f"    异常期间详情:")
            for period in anomaly_periods[:5]:  # 显示前5个
                print(f"      {period['year']}年{period['period']}期: 评分{period['volatility_score']:.3f}")
        
        return {
            'mean_volatility_score': mean_score,
            'std_volatility_score': std_score,
            'high_volatility_count': len(high_volatility_periods),
            'anomaly_count': len(anomaly_periods),
            'volatility_trend_slope': slope,
            'volatility_trend_r': r_value,
            'volatility_trend_p': p_value
        }
    
    def get_current_volatility_score(self, current_period_idx=None):
        """获取当前波动性评分"""
        if not self.volatility_history:
            return 0.5  # 默认中性评分
        
        if current_period_idx is None:
            return self.volatility_history[-1]['volatility_score']
        else:
            # 找到最接近的分析点
            for record in reversed(self.volatility_history):
                if record['period_idx'] <= current_period_idx:
                    return record['volatility_score']
            return 0.5

def main():
    """主函数"""
    print("🎯 波动性指标计算器")
    print("检测彩票号码分布的剧烈波动")
    print("=" * 70)
    
    # 初始化计算器
    calculator = VolatilityIndicator()
    
    # 1. 加载数据
    if not calculator.load_data():
        return
    
    # 2. 运行波动性分析
    volatility_history = calculator.run_volatility_analysis()
    
    # 3. 分析波动性模式
    volatility_patterns = calculator.analyze_volatility_patterns()
    
    # 4. 保存结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"波动性指标分析结果_{timestamp}.json"
    
    # 处理numpy类型
    def convert_numpy_types(obj):
        if isinstance(obj, dict):
            return {k: convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(v) for v in obj]
        elif hasattr(obj, 'item'):
            return obj.item()
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, (np.integer, int)):
            return int(obj)
        elif isinstance(obj, (np.floating, float)):
            return float(obj)
        else:
            return obj
    
    results = {
        'config': calculator.config,
        'volatility_history': convert_numpy_types(volatility_history[:10]),  # 保存前10个分析点
        'volatility_patterns': convert_numpy_types(volatility_patterns),
        'analysis_timestamp': datetime.now().isoformat()
    }
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 波动性指标分析结果已保存: {results_file}")
    
    # 5. 总结
    current_volatility = calculator.get_current_volatility_score()
    
    print(f"\n🎉 波动性指标分析完成")
    print("=" * 50)
    print(f"✅ 波动性分析点: {len(volatility_history)}")
    print(f"✅ 当前波动性评分: {current_volatility:.3f}")
    
    if volatility_patterns:
        print(f"✅ 平均波动性: {volatility_patterns['mean_volatility_score']:.3f}")
        print(f"✅ 高波动期间: {volatility_patterns['high_volatility_count']}")
        print(f"✅ 异常波动期间: {volatility_patterns['anomaly_count']}")
    
    print(f"\n💡 波动性评估:")
    if current_volatility >= 0.7:
        print(f"  🟢 低波动性：模式稳定，适合投注")
    elif current_volatility >= 0.4:
        print(f"  🟡 中等波动性：模式一般，谨慎投注")
    else:
        print(f"  🔴 高波动性：模式失效，建议跳过")

if __name__ == "__main__":
    main()
