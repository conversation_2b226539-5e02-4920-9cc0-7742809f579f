{"cleanup_summary": {"total_files_analyzed": 79, "files_to_keep": 19, "files_to_delete": 60, "cleanup_date": "2025-07-13T04:33:29.111440"}, "keep_files": {"core_algorithms": [{"file": "2025年180期正确预测.py", "reason": "待进一步分析的算法文件"}, {"file": "2025年181-200期预测系统.py", "reason": "待进一步分析的算法文件"}, {"file": "优化后的最佳预测系统.py", "reason": "待进一步分析的算法文件"}, {"file": "全面预测方式验证实验.py", "reason": "待进一步分析的算法文件"}, {"file": "平衡优化的预测系统.py", "reason": "待进一步分析的算法文件"}, {"file": "最终优化预测系统.py", "reason": "待进一步分析的算法文件"}, {"file": "正确的单期预测系统.py", "reason": "单期预测算法，29.2%准确率，A级可信度"}, {"file": "生产级马尔可夫预测系统.py", "reason": "待进一步分析的算法文件"}, {"file": "预测重复问题深度思辨分析.py", "reason": "待进一步分析的算法文件"}, {"file": "两阶段预测系统.py", "reason": "待进一步分析的算法文件"}, {"file": "改进两阶段预测系统.py", "reason": "待进一步分析的算法文件"}, {"file": "简化两阶段预测系统.py", "reason": "待进一步分析的算法文件"}], "validation_reports": [{"file": "29.2%基线来源验证与一致性分析报告.md", "reason": "基线来源验证报告，确认A级可信度"}, {"file": "全面预测方式验证实验报告.md", "reason": "核心验证报告，包含统计显著性检验"}, {"file": "全面预测验证实验结果_20250713_040649.json", "reason": "最终验证实验结果，包含统计检验"}], "data_files": [{"file": "lottery_data_clean_no_special.csv", "reason": "主数据文件，1638期真实数据，A级可信度"}], "documentation": [{"file": "README.md", "reason": "项目说明文档"}, {"file": "基准性能修正说明.md", "reason": "项目说明文档"}, {"file": "README.md", "reason": "项目说明文档"}]}, "delete_files": {"failed_methods": [{"file": "连续预测的可能性与局限性分析.py", "reason": "连续预测算法，20.8%准确率，存在严重缺陷"}, {"file": "逐期预测vs连续预测对比实验.py", "reason": "连续预测算法，20.8%准确率，存在严重缺陷"}, {"file": "逐期vs连续预测对比结果_20250713_042601.json", "reason": "连续预测实验结果，方法已被证实有缺陷"}], "temporary_files": [{"file": "lottery_data_2021_2025_integrated.csv", "reason": "临时或重复的数据文件"}, {"file": "lottery_data_2023_2025.csv", "reason": "临时或重复的数据文件"}, {"file": "lottery_data_clean.csv", "reason": "临时或重复的数据文件"}], "duplicate_files": [{"file": "2025年180期最终预测报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "2025年181-200期预测分析报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "2025年181-200期预测结果报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "2数预测优化最终报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "历史预测文件深度思辨分析报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "最终优化系统成果报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "最终综合分析与建议报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "深度思辨分析综合报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "特码验证与两阶段预测分析完整报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "状态评估器系统综合报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "生产系统优化与随机基准验证最终报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "生产系统配置优化报告_20250712_215735.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "精英特征工程深度思辨分析报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "训练数据区间调节实验分析报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "训练数据区间调节实验最终报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "重复数值机制思辨分析报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "重复机制融合可行性_深度思辨报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "项目清理与生产部署完成报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "预测数字范围差异_分析报告.md", "reason": "中间验证报告，已被最终报告替代"}, {"file": "2025年181-200期预测结果.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "2025年181-200期预测结果_20250713_034532.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "ROI商业价值分析结果_20250713_024956.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "优化数据策略2025年181-200期预测结果.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "优化预测系统结果_20250713_032629.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "历史表现回溯结果_20250713_030952.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "完整状态评估器结果_20250713_031510.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "平衡优化系统结果_20250713_032748.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "最佳方法影响分析结果_20250713_032502.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "最终优化系统结果_20250713_034003.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "最终优化系统结果_20250713_034013.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "模式稳定性分析结果_20250713_031139.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "正确单期预测结果_20250713_035244.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "波动性指标分析结果_20250713_031320.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "滚动窗口交叉验证结果_20250713_024641.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "理论基准计算与验证结果_20250713_024433.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "理论基准计算与验证结果_20250713_024510.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "生产优化2025年181-200期预测结果.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "生产级马尔可夫2025年181-200期预测结果.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "训练数据区间调节实验结果_20250712_214212.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "训练数据区间调节实验结果_20250712_214342.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "错误归因分析结果_20250713_024820.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "随机基准方法对比验证结果_20250712_215947.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "两阶段预测验证结果_20250712_213026.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "两阶段预测验证结果_20250712_213049.json", "reason": "中间实验结果，已被最终结果替代"}, {"file": "1181-1200期最佳方法预测结果.md", "reason": "过程文档，已被最终文档替代"}, {"file": "1181-1200期预测快速查看表.md", "reason": "过程文档，已被最终文档替代"}, {"file": "1181-1200期预测结果总结.md", "reason": "过程文档，已被最终文档替代"}, {"file": "181-200期预测结果总结.md", "reason": "过程文档，已被最终文档替代"}, {"file": "2025年181-200期2数预测结果表.md", "reason": "过程文档，已被最终文档替代"}, {"file": "2025年第181-200期预测结果表.md", "reason": "过程文档，已被最终文档替代"}, {"file": "2数字性能差异深度思辨分析.md", "reason": "过程文档，已被最终文档替代"}, {"file": "改进方法181-200期预测结果.md", "reason": "过程文档，已被最终文档替代"}, {"file": "最佳方法181-200期预测结果.md", "reason": "过程文档，已被最终文档替代"}, {"file": "SCIENTIFIC_METHODOLOGY_AND_LIMITATIONS.md", "reason": "过程文档，已被最终文档替代"}], "error_files": []}, "optimization_rationale": {"performance_based": "基于验证实验结果，保留29.2%+准确率的方法", "credibility_based": "基于A级可信度评估，删除D级方法", "statistical_based": "基于McNemar检验p=0.0455的统计显著性", "practical_based": "基于实际使用价值和维护成本"}}