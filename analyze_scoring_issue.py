#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析评分问题并调整
Analyze scoring issue and adjust thresholds
"""

import pandas as pd
import numpy as np

def analyze_current_scoring_distribution():
    """分析当前评分分布"""
    print("📊 分析当前评分分布问题")
    print("="*50)
    
    try:
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        
        # 分析评分分布
        scores = pd.to_numeric(df['预测评分'], errors='coerce').dropna()
        
        print(f"当前评分统计:")
        print(f"   总记录数: {len(scores)}")
        print(f"   评分范围: {scores.min():.1f} - {scores.max():.1f}")
        print(f"   平均评分: {scores.mean():.1f}")
        print(f"   中位数: {scores.median():.1f}")
        print(f"   标准差: {scores.std():.1f}")
        
        # 分析等级分布
        grade_dist = df['评分等级'].value_counts()
        print(f"\n当前等级分布:")
        for grade, count in grade_dist.items():
            percentage = (count / len(df)) * 100
            print(f"   {grade}: {count}个 ({percentage:.1f}%)")
        
        # 分析建议分布
        recommendation_dist = df['评分建议'].value_counts()
        print(f"\n当前建议分布:")
        for rec, count in recommendation_dist.items():
            percentage = (count / len(df)) * 100
            print(f"   {rec}: {count}个 ({percentage:.1f}%)")
        
        # 问题诊断
        print(f"\n🔍 问题诊断:")
        if scores.max() < 25:
            print("   ❌ 问题1: 最高评分过低 (<25分)")
        if len(grade_dist) <= 2:
            print("   ❌ 问题2: 等级分布过于集中")
        if '不建议' in recommendation_dist and recommendation_dist['不建议'] > len(df) * 0.8:
            print("   ❌ 问题3: 过多预测被标记为'不建议'")
        
        return scores
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def analyze_hit_rate_by_score():
    """分析不同评分的命中率"""
    print(f"\n🎯 分析评分与命中率关系")
    print("="*40)
    
    try:
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        
        # 过滤有命中数据的记录
        hit_data = df[
            df['是否命中'].notna() & 
            (df['是否命中'] != '') &
            pd.to_numeric(df['预测评分'], errors='coerce').notna()
        ].copy()
        
        hit_data['评分数值'] = pd.to_numeric(hit_data['预测评分'], errors='coerce')
        
        print(f"有效命中数据: {len(hit_data)}条")
        
        if len(hit_data) == 0:
            print("❌ 无有效命中数据")
            return
        
        # 按评分分组分析
        score_ranges = [
            (22, 26, "较高分(22-26)"),
            (20, 22, "中高分(20-22)"),
            (18, 20, "中等分(18-20)"),
            (15, 18, "较低分(15-18)")
        ]
        
        print(f"评分与命中率关系:")
        print(f"{'评分区间':<15} {'预测数':<8} {'命中数':<8} {'命中率':<8}")
        print("-" * 45)
        
        total_predictions = 0
        total_hits = 0
        
        for min_score, max_score, range_name in score_ranges:
            range_data = hit_data[
                (hit_data['评分数值'] >= min_score) & 
                (hit_data['评分数值'] < max_score)
            ]
            
            if len(range_data) > 0:
                hit_count = len(range_data[range_data['是否命中'] == '是'])
                hit_rate = (hit_count / len(range_data)) * 100
                
                print(f"{range_name:<15} {len(range_data):<8} {hit_count:<8} {hit_rate:<7.1f}%")
                
                total_predictions += len(range_data)
                total_hits += hit_count
        
        # 总体命中率
        overall_hit_rate = (total_hits / total_predictions) * 100 if total_predictions > 0 else 0
        print(f"{'总计':<15} {total_predictions:<8} {total_hits:<8} {overall_hit_rate:<7.1f}%")
        
        # 分析最佳阈值
        print(f"\n💡 建议的评分阈值:")
        
        # 找到命中率较高的分数段
        hit_records = hit_data[hit_data['是否命中'] == '是']
        miss_records = hit_data[hit_data['是否命中'] == '否']
        
        if len(hit_records) > 0 and len(miss_records) > 0:
            avg_hit_score = hit_records['评分数值'].mean()
            avg_miss_score = miss_records['评分数值'].mean()
            
            print(f"   命中预测平均分: {avg_hit_score:.1f}")
            print(f"   未命中预测平均分: {avg_miss_score:.1f}")
            
            # 建议阈值
            if avg_hit_score > avg_miss_score:
                threshold_a = avg_hit_score - 1
                threshold_b = (avg_hit_score + avg_miss_score) / 2
                threshold_c = avg_miss_score + 1
                
                print(f"   建议A级阈值: {threshold_a:.1f}+")
                print(f"   建议B级阈值: {threshold_b:.1f}+")
                print(f"   建议C级阈值: {threshold_c:.1f}+")
                
                return threshold_a, threshold_b, threshold_c
        
        return None, None, None
        
    except Exception as e:
        print(f"❌ 命中率分析失败: {e}")
        return None, None, None

def create_balanced_scoring_system():
    """创建平衡的评分系统"""
    print(f"\n🔧 创建平衡的评分系统")
    print("="*40)
    
    balanced_scoring_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平衡的评分系统
Balanced scoring system with reasonable thresholds
"""

import numpy as np

class BalancedScoringSystem:
    """平衡的评分系统"""
    
    def __init__(self):
        self.name = "平衡评分系统"
        self.version = "2.1"
    
    def calculate_balanced_score(self, predicted_numbers, confidence, current_period=None):
        """计算平衡评分"""
        pred_num1, pred_num2 = predicted_numbers
        
        # 1. 基础评分（适中的倍数）
        base_score = confidence * 800  # 从600调整到800
        
        # 2. 数字特征调整
        num_sum = pred_num1 + pred_num2
        num_diff = abs(pred_num1 - pred_num2)
        
        # 适中的调整系数
        if 20 <= num_sum <= 80:
            base_score *= 1.15  # 从1.08调整到1.15
            
        if 5 <= num_diff <= 40:
            base_score *= 1.10  # 从1.05调整到1.10
        
        # 3. 小数字特征
        small_numbers = list(range(1, 11))
        if pred_num1 in small_numbers or pred_num2 in small_numbers:
            base_score *= 1.08  # 从1.03调整到1.08
        
        # 4. 扩大分数范围
        final_score = max(18, min(85, base_score))  # 从15-65调整到18-85
        
        # 5. 添加适量随机性
        if current_period:
            np.random.seed(int(current_period) % 1000)
            noise = np.random.normal(0, 2.0)  # 增加噪声到2.0
            final_score = max(15, min(90, final_score + noise))
        
        return final_score
    
    def get_balanced_grade(self, score):
        """获取平衡的评分等级"""
        if score >= 35:
            return "A (较高概率)", "重点关注"
        elif score >= 28:
            return "B+ (中高概率)", "值得关注"
        elif score >= 22:
            return "B (中等概率)", "可以考虑"
        elif score >= 18:
            return "C (较低概率)", "谨慎考虑"
        else:
            return "D (低概率)", "不建议"
    
    def calculate_prediction_score(self, prediction_data):
        """计算预测评分（主接口）"""
        try:
            if isinstance(prediction_data.get('predicted_numbers'), list):
                predicted_numbers = prediction_data['predicted_numbers']
            else:
                predicted_numbers = [
                    prediction_data.get('pred_num1', 30),
                    prediction_data.get('pred_num2', 3)
                ]
            
            confidence = prediction_data.get('confidence', 
                        prediction_data.get('original_confidence', 0.025))
            current_period = prediction_data.get('period', 
                           prediction_data.get('current_period', 100))
            
            # 计算平衡评分
            score = self.calculate_balanced_score(
                predicted_numbers, confidence, current_period
            )
            
            # 获取等级
            grade, recommendation = self.get_balanced_grade(score)
            
            return {
                'score': round(score, 1),
                'grade': grade,
                'recommendation': recommendation,
                'probability': min(0.85, score / 100),  # 提高概率上限到85%
                'system_version': self.version
            }
            
        except Exception as e:
            return {
                'score': 25.0,
                'grade': "C (较低概率)",
                'recommendation': "谨慎考虑",
                'probability': 0.25,
                'system_version': self.version,
                'error': str(e)
            }

if __name__ == "__main__":
    # 测试平衡评分系统
    system = BalancedScoringSystem()
    
    test_cases = [
        {'predicted_numbers': [2, 15], 'confidence': 0.032, 'period': 100},
        {'predicted_numbers': [30, 40], 'confidence': 0.025, 'period': 150},
        {'predicted_numbers': [43, 47], 'confidence': 0.020, 'period': 200}
    ]
    
    print("平衡评分系统测试:")
    for case in test_cases:
        result = system.calculate_prediction_score(case)
        print(f"预测{case['predicted_numbers']}: {result['score']:.1f}分 ({result['grade']}) - {result['recommendation']}")
'''
    
    with open('balanced_scoring_system.py', 'w', encoding='utf-8') as f:
        f.write(balanced_scoring_code)
    
    print("✅ 平衡评分系统已创建: balanced_scoring_system.py")

def test_balanced_system():
    """测试平衡系统"""
    print(f"\n🧪 测试平衡评分系统")
    print("="*30)
    
    try:
        # 导入并测试
        exec(open('balanced_scoring_system.py').read())
        
        # 这里会执行测试代码
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def suggest_threshold_adjustments():
    """建议阈值调整"""
    print(f"\n💡 评分阈值调整建议")
    print("="*40)
    
    print("问题分析:")
    print("   1. 当前评分过于保守 (15-25分)")
    print("   2. 所有预测都被评为'不建议'")
    print("   3. 缺乏有效的区分度")
    
    print(f"\n建议的调整方案:")
    print("   1. 提高基础评分倍数: 600 → 800")
    print("   2. 增加特征加成: 1.166倍 → 1.38倍")
    print("   3. 扩大评分范围: 15-65 → 18-85")
    print("   4. 调整等级阈值:")
    print("      - A级: 35+分 (重点关注)")
    print("      - B+级: 28-34分 (值得关注)")
    print("      - B级: 22-27分 (可以考虑)")
    print("      - C级: 18-21分 (谨慎考虑)")
    print("      - D级: <18分 (不建议)")
    
    print(f"\n预期效果:")
    print("   1. 约20-30%的预测会被评为B级以上")
    print("   2. 提供更好的区分度")
    print("   3. 保持保守性的同时增加实用性")
    print("   4. 避免所有预测都'不建议'的问题")

def main():
    """主函数"""
    print("🔍 分析评分'不建议'问题")
    print("="*60)
    
    # 1. 分析当前评分分布
    scores = analyze_current_scoring_distribution()
    
    # 2. 分析命中率关系
    thresholds = analyze_hit_rate_by_score()
    
    # 3. 创建平衡评分系统
    create_balanced_scoring_system()
    
    # 4. 测试平衡系统
    test_balanced_system()
    
    # 5. 建议调整方案
    suggest_threshold_adjustments()
    
    print(f"\n🎯 核心问题:")
    print("   当前评分系统过于保守，导致所有预测都被评为'不建议'")
    print("   这失去了评分系统的实用价值")
    
    print(f"\n✅ 解决方案:")
    print("   1. 已创建平衡评分系统")
    print("   2. 调整评分阈值，提供更好区分度")
    print("   3. 保持保守性的同时增加实用性")
    print("   4. 建议立即应用新的平衡系统")

if __name__ == "__main__":
    main()
