#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据修复脚本
修复错误生成的模拟数据，确保数据的真实性和准确性
"""

import pandas as pd
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class DataFixScript:
    """数据修复脚本"""
    
    def __init__(self):
        self.main_data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.prediction_data_file = "prediction_data_final_production.csv"
        self.backup_suffix = f"_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def backup_files(self):
        """备份原始文件"""
        print("📁 备份原始文件...")
        
        try:
            # 备份主数据文件
            if os.path.exists(self.main_data_file):
                backup_main = self.main_data_file.replace('.csv', f'{self.backup_suffix}.csv')
                import shutil
                shutil.copy2(self.main_data_file, backup_main)
                print(f"✅ 主数据文件已备份到: {backup_main}")
            
            # 备份预测数据文件
            if os.path.exists(self.prediction_data_file):
                backup_pred = self.prediction_data_file.replace('.csv', f'{self.backup_suffix}.csv')
                import shutil
                shutil.copy2(self.prediction_data_file, backup_pred)
                print(f"✅ 预测数据文件已备份到: {backup_pred}")
            
            return True
            
        except Exception as e:
            print(f"❌ 备份文件失败: {e}")
            return False
    
    def analyze_data_status(self):
        """分析当前数据状态"""
        print("\n🔍 分析当前数据状态...")
        
        try:
            # 分析主数据文件
            main_data = pd.read_csv(self.main_data_file, encoding='utf-8')
            last_main_row = main_data.iloc[-1]
            main_last_period = int(last_main_row['期号'])
            
            print(f"📊 主数据文件状态:")
            print(f"   总记录数: {len(main_data)}")
            print(f"   最新期号: {int(last_main_row['年份'])}年{main_last_period}期")
            print(f"   最新开奖: [{int(last_main_row['数字1'])}, {int(last_main_row['数字2'])}, {int(last_main_row['数字3'])}, {int(last_main_row['数字4'])}, {int(last_main_row['数字5'])}, {int(last_main_row['数字6'])}]")
            
            # 分析预测数据文件
            pred_data = pd.read_csv(self.prediction_data_file, encoding='utf-8')
            pred_data = pred_data.dropna(subset=['当期期号'])  # 移除空行
            
            print(f"\n📊 预测数据文件状态:")
            print(f"   总记录数: {len(pred_data)}")
            
            # 找出超出主数据范围的预测记录
            invalid_predictions = pred_data[pred_data['当期期号'] > main_last_period]
            
            if len(invalid_predictions) > 0:
                print(f"   ⚠️ 发现 {len(invalid_predictions)} 条无效预测记录:")
                for _, row in invalid_predictions.iterrows():
                    period = int(row['当期期号']) if pd.notna(row['当期期号']) else 'N/A'
                    pred_period = row.get('预测期号', 'N/A')
                    print(f"     期号{period} → {pred_period}")
            else:
                print(f"   ✅ 所有预测记录都在有效范围内")
            
            return {
                'main_last_period': main_last_period,
                'invalid_predictions': invalid_predictions,
                'total_predictions': len(pred_data)
            }
            
        except Exception as e:
            print(f"❌ 数据分析失败: {e}")
            return None
    
    def fix_prediction_data(self, analysis_result):
        """修复预测数据"""
        print(f"\n🔧 修复预测数据...")
        
        try:
            # 加载预测数据
            pred_data = pd.read_csv(self.prediction_data_file, encoding='utf-8')
            original_count = len(pred_data.dropna(subset=['当期期号']))
            
            # 移除无效的预测记录
            main_last_period = analysis_result['main_last_period']
            
            # 保留有效的预测记录（当期期号 <= 最新主数据期号）
            valid_mask = (pred_data['当期期号'] <= main_last_period) | pd.isna(pred_data['当期期号'])
            fixed_pred_data = pred_data[valid_mask].copy()
            
            # 移除完全空白的行
            fixed_pred_data = fixed_pred_data.dropna(how='all')
            
            # 修复203期的预测记录（如果存在问题）
            period_203_mask = fixed_pred_data['当期期号'] == 203
            if period_203_mask.any():
                # 确保203期的预测记录是预测204期的
                fixed_pred_data.loc[period_203_mask, '预测期号'] = '2025年204期'
                print(f"   ✅ 修复了203期的预测记录")
            
            # 保存修复后的数据
            fixed_pred_data.to_csv(self.prediction_data_file, index=False, encoding='utf-8')
            
            fixed_count = len(fixed_pred_data.dropna(subset=['当期期号']))
            removed_count = original_count - fixed_count
            
            print(f"   ✅ 预测数据修复完成")
            print(f"   原始记录数: {original_count}")
            print(f"   修复后记录数: {fixed_count}")
            print(f"   移除无效记录: {removed_count}")
            
            return True
            
        except Exception as e:
            print(f"❌ 预测数据修复失败: {e}")
            return False
    
    def clean_main_data(self):
        """清理主数据文件（移除可能的重复或错误数据）"""
        print(f"\n🧹 清理主数据文件...")
        
        try:
            # 加载主数据
            main_data = pd.read_csv(self.main_data_file, encoding='utf-8')
            original_count = len(main_data)
            
            # 移除重复记录
            main_data = main_data.drop_duplicates(subset=['年份', '期号'], keep='first')
            
            # 确保期号连续性（移除可能的错误数据）
            main_data = main_data.sort_values(['年份', '期号'])
            
            # 检查是否有超出203期的数据
            invalid_main = main_data[main_data['期号'] > 203]
            if len(invalid_main) > 0:
                print(f"   ⚠️ 发现 {len(invalid_main)} 条超出203期的主数据记录，将被移除")
                main_data = main_data[main_data['期号'] <= 203]
            
            # 保存清理后的数据
            main_data.to_csv(self.main_data_file, index=False, encoding='utf-8')
            
            cleaned_count = len(main_data)
            removed_count = original_count - cleaned_count
            
            print(f"   ✅ 主数据清理完成")
            print(f"   原始记录数: {original_count}")
            print(f"   清理后记录数: {cleaned_count}")
            print(f"   移除记录数: {removed_count}")
            
            return True
            
        except Exception as e:
            print(f"❌ 主数据清理失败: {e}")
            return False
    
    def verify_data_integrity(self):
        """验证数据完整性"""
        print(f"\n✅ 验证数据完整性...")
        
        try:
            # 验证主数据
            main_data = pd.read_csv(self.main_data_file, encoding='utf-8')
            last_main_row = main_data.iloc[-1]
            main_last_period = int(last_main_row['期号'])
            
            print(f"📊 主数据验证:")
            print(f"   最新期号: {int(last_main_row['年份'])}年{main_last_period}期")
            print(f"   数据完整性: {'✅ 正常' if main_last_period == 203 else '❌ 异常'}")
            
            # 验证预测数据
            pred_data = pd.read_csv(self.prediction_data_file, encoding='utf-8')
            pred_data = pred_data.dropna(subset=['当期期号'])
            
            max_pred_period = pred_data['当期期号'].max()
            invalid_preds = pred_data[pred_data['当期期号'] > main_last_period]
            
            print(f"\n📊 预测数据验证:")
            print(f"   总预测记录: {len(pred_data)}")
            print(f"   最大预测期号: {int(max_pred_period) if pd.notna(max_pred_period) else 'N/A'}")
            print(f"   无效记录数: {len(invalid_preds)}")
            print(f"   数据一致性: {'✅ 正常' if len(invalid_preds) == 0 else '❌ 仍有问题'}")
            
            # 显示最近几期的状态
            print(f"\n📋 最近期号状态:")
            recent_periods = [200, 201, 202, 203]
            for period in recent_periods:
                main_exists = len(main_data[main_data['期号'] == period]) > 0
                pred_exists = len(pred_data[pred_data['当期期号'] == period]) > 0
                
                status = "✅" if main_exists else "❌"
                pred_status = "✅" if pred_exists else "❌"
                print(f"   {period}期: 主数据{status} 预测数据{pred_status}")
            
            return len(invalid_preds) == 0
            
        except Exception as e:
            print(f"❌ 数据验证失败: {e}")
            return False
    
    def run_data_fix(self):
        """运行数据修复"""
        print("🔧 开始数据修复...")
        print("=" * 50)
        
        # 1. 备份文件
        if not self.backup_files():
            print("❌ 备份失败，停止修复")
            return False
        
        # 2. 分析数据状态
        analysis_result = self.analyze_data_status()
        if not analysis_result:
            print("❌ 数据分析失败，停止修复")
            return False
        
        # 3. 清理主数据
        if not self.clean_main_data():
            print("❌ 主数据清理失败")
            return False
        
        # 4. 修复预测数据
        if not self.fix_prediction_data(analysis_result):
            print("❌ 预测数据修复失败")
            return False
        
        # 5. 验证数据完整性
        if not self.verify_data_integrity():
            print("⚠️ 数据验证发现问题，请检查")
        
        print("\n✅ 数据修复完成！")
        print("=" * 50)
        print("📋 修复总结:")
        print("✅ 移除了超出203期的无效数据")
        print("✅ 确保了数据的真实性和准确性")
        print("✅ 备份了原始文件以防需要恢复")
        print("✅ 验证了数据完整性")
        
        return True

def main():
    """主函数"""
    print("🔧 数据修复脚本")
    print("修复错误生成的模拟数据，确保数据真实性")
    print("=" * 60)
    
    fixer = DataFixScript()
    
    # 确认执行修复
    print("⚠️ 注意：此操作将修复数据文件，原始文件将被备份")
    confirm = input("确认执行数据修复? (y/n): ").strip().lower()
    
    if confirm != 'y':
        print("❌ 修复已取消")
        return
    
    # 执行修复
    success = fixer.run_data_fix()
    
    if success:
        print("\n🎉 数据修复成功完成！")
        print("现在数据已恢复到真实状态，最新期号为203期")
    else:
        print("\n❌ 数据修复失败，请检查错误信息")

if __name__ == "__main__":
    main()
