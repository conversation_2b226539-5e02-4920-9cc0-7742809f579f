#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Predictor for Lottery Prediction System
Generates predictions for future periods using trained models
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
import joblib
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')


class LotteryPredictor:
    """
    Lottery predictor for generating future predictions
    Uses ensemble of trained models for robust predictions
    """
    
    def __init__(self, models_dir: str = 'models', results_dir: str = 'results'):
        self.models_dir = models_dir
        self.results_dir = results_dir
        self.models = {}
        self.scalers = {}
        self.feature_columns = []
        
        # Create results directory
        os.makedirs(results_dir, exist_ok=True)
    
    def load_models_and_scalers(self) -> bool:
        """
        Load all trained models and scalers
        """
        print("=== 加载训练好的模型 ===")
        
        if not os.path.exists(self.models_dir):
            print(f"模型目录不存在: {self.models_dir}")
            return False
        
        # Load models
        model_files = [f for f in os.listdir(self.models_dir) 
                      if f.endswith('.joblib') and not f.startswith('scaler_')]
        
        if not model_files:
            print("没有找到训练好的模型")
            return False
        
        for model_file in model_files:
            model_path = os.path.join(self.models_dir, model_file)
            model_key = model_file.replace('.joblib', '')
            
            try:
                model = joblib.load(model_path)
                self.models[model_key] = model
                
                # Load corresponding scaler if exists
                scaler_file = f"scaler_{model_file}"
                scaler_path = os.path.join(self.models_dir, scaler_file)
                if os.path.exists(scaler_path):
                    scaler = joblib.load(scaler_path)
                    self.scalers[model_key] = scaler
                
                print(f"  加载模型: {model_key}")
                
            except Exception as e:
                print(f"  加载模型失败 {model_key}: {e}")
        
        print(f"成功加载 {len(self.models)} 个模型")
        return len(self.models) > 0
    
    def prepare_prediction_features(self, data: pd.DataFrame, 
                                  feature_columns: List[str]) -> pd.DataFrame:
        """
        Prepare features for prediction based on latest available data
        """
        # Use the last few periods to create features for next prediction
        latest_data = data.tail(20).copy()  # Use last 20 periods for feature calculation
        
        # Create basic features
        main_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        # Basic statistics
        latest_data['和值'] = latest_data[main_cols].sum(axis=1)
        latest_data['平均值'] = latest_data[main_cols].mean(axis=1)
        latest_data['极差'] = latest_data[main_cols].max(axis=1) - latest_data[main_cols].min(axis=1)
        latest_data['方差'] = latest_data[main_cols].var(axis=1)
        latest_data['标准差'] = latest_data[main_cols].std(axis=1)
        
        # Odd/Even counts
        odd_count = latest_data[main_cols].apply(lambda x: sum(num % 2 for num in x), axis=1)
        latest_data['奇数个数'] = odd_count
        latest_data['偶数个数'] = 6 - odd_count
        
        # Big/Small counts
        big_count = latest_data[main_cols].apply(lambda x: sum(num > 25 for num in x), axis=1)
        latest_data['大数个数'] = big_count
        latest_data['小数个数'] = 6 - big_count
        
        # Time series features
        window_sizes = [3, 5, 10]
        for window in window_sizes:
            latest_data[f'和值_mean_{window}'] = latest_data['和值'].rolling(window=window).mean()
            latest_data[f'和值_std_{window}'] = latest_data['和值'].rolling(window=window).std()
            latest_data[f'和值_min_{window}'] = latest_data['和值'].rolling(window=window).min()
            latest_data[f'和值_max_{window}'] = latest_data['和值'].rolling(window=window).max()
            latest_data[f'奇数个数_mean_{window}'] = latest_data['奇数个数'].rolling(window=window).mean()
            latest_data[f'大数个数_mean_{window}'] = latest_data['大数个数'].rolling(window=window).mean()
        
        # Lag features
        for lag in [1, 2, 3]:
            for col in main_cols:
                latest_data[f'{col}_lag_{lag}'] = latest_data[col].shift(lag)
        
        for lag in [1, 2]:
            latest_data[f'和值_lag_{lag}'] = latest_data['和值'].shift(lag)
            latest_data[f'奇数个数_lag_{lag}'] = latest_data['奇数个数'].shift(lag)
            latest_data[f'大数个数_lag_{lag}'] = latest_data['大数个数'].shift(lag)
        
        # Get the last row with all features
        prediction_features = latest_data.iloc[-1:][feature_columns].fillna(0)
        
        return prediction_features
    
    def predict_single_number(self, features: pd.DataFrame, target_col: str) -> Dict:
        """
        Predict a single number using all available models for that target
        """
        predictions = []
        model_names = []
        
        for model_key, model in self.models.items():
            if target_col in model_key:
                model_name = model_key.replace(f"_{target_col}", "")
                
                # Apply scaling if needed
                if model_key in self.scalers:
                    scaler = self.scalers[model_key]
                    features_scaled = scaler.transform(features)
                    pred = model.predict(features_scaled)[0]
                else:
                    pred = model.predict(features)[0]
                
                # Clip to valid range
                pred = np.clip(np.round(pred), 1, 49).astype(int)
                predictions.append(pred)
                model_names.append(model_name)
        
        if not predictions:
            return {'prediction': np.random.randint(1, 50), 'confidence': 0.0, 'models_used': []}
        
        # Ensemble prediction (majority vote or average)
        if len(predictions) == 1:
            final_prediction = predictions[0]
            confidence = 0.5  # Low confidence with single model
        else:
            # Use median as robust ensemble method
            final_prediction = int(np.median(predictions))
            
            # Calculate confidence based on agreement
            agreement = np.mean([abs(p - final_prediction) <= 2 for p in predictions])
            confidence = agreement
        
        return {
            'prediction': final_prediction,
            'confidence': confidence,
            'individual_predictions': predictions,
            'models_used': model_names
        }
    
    def predict_lottery_numbers(self, data: pd.DataFrame, 
                              feature_columns: List[str],
                              target_period: int = None) -> Dict:
        """
        Predict all 6 lottery numbers for the next period
        """
        print(f"=== 预测彩票号码 ===")
        
        if target_period is None:
            target_period = data['期号'].max() + 1
        
        print(f"预测期号: {target_period}")
        
        # Prepare features
        features = self.prepare_prediction_features(data, feature_columns)
        
        # Predict each number
        target_columns = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        predictions = {}
        all_numbers = []
        confidences = []
        
        for target_col in target_columns:
            result = self.predict_single_number(features, target_col)
            predictions[target_col] = result
            all_numbers.append(result['prediction'])
            confidences.append(result['confidence'])
            
            print(f"  {target_col}: {result['prediction']} "
                  f"(置信度: {result['confidence']:.3f})")
        
        # Ensure no duplicates (lottery numbers should be unique)
        unique_numbers = self._ensure_unique_numbers(all_numbers)
        
        # Calculate overall statistics
        sum_value = sum(unique_numbers)
        avg_confidence = np.mean(confidences)
        
        # Count odd/even and big/small
        odd_count = sum(1 for num in unique_numbers if num % 2 == 1)
        big_count = sum(1 for num in unique_numbers if num > 25)
        
        final_result = {
            'period': target_period,
            'numbers': sorted(unique_numbers),
            'sum': sum_value,
            'average': sum_value / 6,
            'odd_count': odd_count,
            'even_count': 6 - odd_count,
            'big_count': big_count,
            'small_count': 6 - big_count,
            'avg_confidence': avg_confidence,
            'individual_predictions': predictions
        }
        
        print(f"\n预测结果: {sorted(unique_numbers)}")
        print(f"和值: {sum_value}, 奇数: {odd_count}, 大数: {big_count}")
        print(f"平均置信度: {avg_confidence:.3f}")
        
        return final_result
    
    def _ensure_unique_numbers(self, numbers: List[int]) -> List[int]:
        """
        Ensure all predicted numbers are unique
        """
        unique_numbers = []
        used_numbers = set()
        
        for num in numbers:
            if num not in used_numbers:
                unique_numbers.append(num)
                used_numbers.add(num)
            else:
                # Find a nearby unused number
                for offset in range(1, 25):  # Try both directions
                    for candidate in [num + offset, num - offset]:
                        if 1 <= candidate <= 49 and candidate not in used_numbers:
                            unique_numbers.append(candidate)
                            used_numbers.add(candidate)
                            break
                    if len(unique_numbers) == len(numbers):
                        break
                
                # If still not found, use random
                if len(unique_numbers) < len(numbers):
                    available = [i for i in range(1, 50) if i not in used_numbers]
                    if available:
                        candidate = np.random.choice(available)
                        unique_numbers.append(candidate)
                        used_numbers.add(candidate)
        
        return unique_numbers
    
    def predict_multiple_periods(self, data: pd.DataFrame, 
                               feature_columns: List[str],
                               num_periods: int = 5) -> Dict:
        """
        Predict multiple future periods
        """
        print(f"=== 预测未来 {num_periods} 期 ===")
        
        all_predictions = {}
        current_data = data.copy()
        
        for i in range(num_periods):
            next_period = current_data['期号'].max() + 1
            
            # Predict next period
            prediction = self.predict_lottery_numbers(current_data, feature_columns, next_period)
            all_predictions[next_period] = prediction
            
            # Add prediction to data for next iteration (optional)
            # This allows the model to use its own predictions for subsequent periods
            # Comment out if you prefer independent predictions
            """
            new_row = {
                '期号': next_period,
                '数字1': prediction['numbers'][0],
                '数字2': prediction['numbers'][1],
                '数字3': prediction['numbers'][2],
                '数字4': prediction['numbers'][3],
                '数字5': prediction['numbers'][4],
                '数字6': prediction['numbers'][5],
                '特码': 0  # Placeholder
            }
            current_data = pd.concat([current_data, pd.DataFrame([new_row])], ignore_index=True)
            """
        
        return all_predictions
    
    def save_predictions(self, predictions: Dict, filename: str = None) -> str:
        """
        Save predictions to CSV file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"predictions_{timestamp}.csv"
        
        filepath = os.path.join(self.results_dir, filename)
        
        # Convert predictions to DataFrame
        rows = []
        for period, pred in predictions.items():
            if isinstance(pred, dict) and 'numbers' in pred:
                row = {
                    '期号': period,
                    '数字1': pred['numbers'][0],
                    '数字2': pred['numbers'][1],
                    '数字3': pred['numbers'][2],
                    '数字4': pred['numbers'][3],
                    '数字5': pred['numbers'][4],
                    '数字6': pred['numbers'][5],
                    '和值': pred['sum'],
                    '奇数个数': pred['odd_count'],
                    '大数个数': pred['big_count'],
                    '平均置信度': pred['avg_confidence']
                }
                rows.append(row)
        
        if rows:
            df = pd.DataFrame(rows)
            df.to_csv(filepath, index=False, encoding='utf-8-sig')
            print(f"预测结果已保存到: {filepath}")
            return filepath
        else:
            print("没有有效的预测结果可保存")
            return ""


if __name__ == "__main__":
    # Test the predictor
    print("预测器测试完成")
