#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
命中率评分系统设计
Hit Rate Scoring System Design

基于历史数据分析设计一个预测命中率的评分系统

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, roc_auc_score, roc_curve
import json

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class HitRateScoringSystem:
    """命中率评分系统"""

    def __init__(self):
        """初始化评分系统"""
        self.data = None
        self.features = None
        self.model = None
        self.score_thresholds = {}
        self.validation_results = {}

        print("🎯 命中率评分系统设计")
        print("="*50)

    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("📊 加载历史数据")
        print("="*20)

        # 加载验证数据
        df = pd.read_csv('comprehensive_validation_results_20250715_203038.csv')

        # 数据预处理
        processed_data = []
        for _, row in df.iterrows():
            # 解析命中数字
            hit_numbers_str = row['命中数字']
            if hit_numbers_str == '[]':
                hit_numbers = []
            else:
                hit_numbers_str = hit_numbers_str.strip('[]')
                if hit_numbers_str:
                    hit_numbers = [int(x.strip()) for x in hit_numbers_str.split(',') if x.strip()]
                else:
                    hit_numbers = []

            processed_data.append({
                'period': row['期号'],
                'pred_num1': row['预测数字1'],
                'pred_num2': row['预测数字2'],
                'actual_nums': [row['实际数字1'], row['实际数字2'], row['实际数字3'],
                               row['实际数字4'], row['实际数字5'], row['实际数字6']],
                'hit_numbers': hit_numbers,
                'hit_count': row['命中数量'],
                'is_hit': row['是否命中'] == '是',
                'original_confidence': row['原始置信度'],
                'optimized_confidence': row['优化置信度'],
                'accuracy_factor': row['准确率因子'],
                'calibration_factor': row['校准因子'],
                'stability_factor': row['稳定性因子'],
                'trend_factor': row['趋势因子'],
                'composite_factor': row['复合因子']
            })

        self.data = processed_data

        print(f"数据准备完成:")
        print(f"  总期数: {len(processed_data)}")
        print(f"  命中期数: {sum(1 for d in processed_data if d['is_hit'])}")
        print(f"  基础命中率: {np.mean([d['is_hit'] for d in processed_data]):.1%}")

        return processed_data

    def extract_scoring_features(self):
        """提取评分特征"""
        print("\n🔧 提取评分特征")
        print("="*30)

        features_data = []

        for i, data_point in enumerate(self.data):
            # 基础特征
            features = {
                'period': data_point['period'],
                'pred_num1': data_point['pred_num1'],
                'pred_num2': data_point['pred_num2'],
                'number_gap': abs(data_point['pred_num1'] - data_point['pred_num2']),
                'number_sum': data_point['pred_num1'] + data_point['pred_num2'],
                'number_avg': (data_point['pred_num1'] + data_point['pred_num2']) / 2,
                'original_confidence': data_point['original_confidence'],
                'optimized_confidence': data_point['optimized_confidence'],
                'confidence_improvement': data_point['optimized_confidence'] - data_point['original_confidence'],
                'accuracy_factor': data_point['accuracy_factor'],
                'calibration_factor': data_point['calibration_factor'],
                'stability_factor': data_point['stability_factor'],
                'trend_factor': data_point['trend_factor'],
                'composite_factor': data_point['composite_factor']
            }

            # 历史表现特征（基于前面的数据）
            if i >= 10:  # 至少需要10期历史数据
                recent_data = self.data[max(0, i-20):i]  # 最近20期

                features['recent_hit_rate'] = np.mean([d['is_hit'] for d in recent_data])
                features['recent_avg_confidence'] = np.mean([d['optimized_confidence'] for d in recent_data])
                features['recent_confidence_trend'] = np.polyfit(range(len(recent_data)),
                                                               [d['optimized_confidence'] for d in recent_data], 1)[0]

                # 数字历史表现
                pred_nums = {data_point['pred_num1'], data_point['pred_num2']}
                num_history = []
                for hist_data in recent_data:
                    hist_nums = {hist_data['pred_num1'], hist_data['pred_num2']}
                    if pred_nums.intersection(hist_nums):
                        num_history.append(hist_data['is_hit'])

                features['number_historical_performance'] = np.mean(num_history) if num_history else 0.35

                # 组合历史表现
                current_combo = tuple(sorted([data_point['pred_num1'], data_point['pred_num2']]))
                combo_history = []
                for hist_data in recent_data:
                    hist_combo = tuple(sorted([hist_data['pred_num1'], hist_data['pred_num2']]))
                    if current_combo == hist_combo:
                        combo_history.append(hist_data['is_hit'])

                features['combo_historical_performance'] = np.mean(combo_history) if combo_history else 0.35
            else:
                # 早期数据使用默认值
                features['recent_hit_rate'] = 0.35
                features['recent_avg_confidence'] = data_point['optimized_confidence']
                features['recent_confidence_trend'] = 0
                features['number_historical_performance'] = 0.35
                features['combo_historical_performance'] = 0.35

            # 时间特征
            features['period_normalized'] = data_point['period'] / 200  # 归一化期数
            features['is_early_period'] = 1 if data_point['period'] < 100 else 0

            # 目标变量
            features['is_hit'] = data_point['is_hit']

            features_data.append(features)

        self.features = pd.DataFrame(features_data)

        print(f"特征提取完成:")
        print(f"  特征数量: {len(self.features.columns) - 1}")  # 减去目标变量
        print(f"  样本数量: {len(self.features)}")

        # 显示特征重要性预览
        feature_cols = [col for col in self.features.columns if col != 'is_hit']
        print(f"  主要特征: {feature_cols[:5]}...")

        return self.features

    def train_scoring_model(self):
        """训练评分模型"""
        print("\n🤖 训练评分模型")
        print("="*30)

        # 准备训练数据
        feature_cols = [col for col in self.features.columns if col not in ['is_hit', 'period']]
        X = self.features[feature_cols]
        y = self.features['is_hit']

        # 时间序列分割（前80%训练，后20%验证）
        split_point = int(len(X) * 0.8)
        X_train, X_test = X[:split_point], X[split_point:]
        y_train, y_test = y[:split_point], y[split_point:]

        print(f"数据分割:")
        print(f"  训练集: {len(X_train)} 样本")
        print(f"  测试集: {len(X_test)} 样本")
        print(f"  训练集命中率: {y_train.mean():.1%}")
        print(f"  测试集命中率: {y_test.mean():.1%}")

        # 训练随机森林模型
        self.model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42
        )

        self.model.fit(X_train, y_train)

        # 预测概率
        train_probs = self.model.predict_proba(X_train)[:, 1]
        test_probs = self.model.predict_proba(X_test)[:, 1]

        # 评估模型
        train_auc = roc_auc_score(y_train, train_probs)
        test_auc = roc_auc_score(y_test, test_probs)

        print(f"\n模型性能:")
        print(f"  训练集AUC: {train_auc:.3f}")
        print(f"  测试集AUC: {test_auc:.3f}")

        # 特征重要性
        feature_importance = pd.DataFrame({
            'feature': feature_cols,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)

        print(f"\n特征重要性 (Top 10):")
        for _, row in feature_importance.head(10).iterrows():
            print(f"  {row['feature']}: {row['importance']:.3f}")

        # 保存验证结果
        self.validation_results = {
            'train_auc': train_auc,
            'test_auc': test_auc,
            'feature_importance': feature_importance.to_dict('records'),
            'train_probs': train_probs.tolist(),
            'test_probs': test_probs.tolist(),
            'y_train': y_train.tolist(),
            'y_test': y_test.tolist()
        }

        return self.model

    def design_scoring_thresholds(self):
        """设计评分阈值"""
        print("\n📊 设计评分阈值")
        print("="*30)

        # 获取所有预测概率
        all_probs = np.concatenate([self.validation_results['train_probs'],
                                   self.validation_results['test_probs']])
        all_labels = np.concatenate([self.validation_results['y_train'],
                                    self.validation_results['y_test']])

        # 按概率分组分析
        prob_ranges = [
            (0.0, 0.2, "极低风险"),
            (0.2, 0.3, "低风险"),
            (0.3, 0.4, "中低风险"),
            (0.4, 0.5, "中等风险"),
            (0.5, 0.6, "中高风险"),
            (0.6, 0.7, "高风险"),
            (0.7, 0.8, "极高风险"),
            (0.8, 1.0, "超高风险")
        ]

        threshold_analysis = []

        for low, high, label in prob_ranges:
            mask = (all_probs >= low) & (all_probs < high)
            if np.sum(mask) > 0:
                group_probs = all_probs[mask]
                group_labels = all_labels[mask]

                analysis = {
                    'range': f"{low:.1f}-{high:.1f}",
                    'label': label,
                    'count': np.sum(mask),
                    'avg_prob': np.mean(group_probs),
                    'actual_hit_rate': np.mean(group_labels),
                    'calibration_error': abs(np.mean(group_probs) - np.mean(group_labels))
                }

                threshold_analysis.append(analysis)

        # 设计评分系统
        scoring_system = {
            'score_ranges': [
                {'score_range': '0-20', 'description': '极低命中概率', 'expected_hit_rate': '10-20%', 'recommendation': '不建议'},
                {'score_range': '21-40', 'description': '低命中概率', 'expected_hit_rate': '20-30%', 'recommendation': '谨慎考虑'},
                {'score_range': '41-60', 'description': '中等命中概率', 'expected_hit_rate': '30-40%', 'recommendation': '可以考虑'},
                {'score_range': '61-80', 'description': '较高命中概率', 'expected_hit_rate': '40-50%', 'recommendation': '值得关注'},
                {'score_range': '81-100', 'description': '高命中概率', 'expected_hit_rate': '50%+', 'recommendation': '重点关注'}
            ],
            'threshold_analysis': threshold_analysis
        }

        # 找出70%以上评分对应的实际命中率
        high_score_mask = all_probs >= 0.7
        if np.sum(high_score_mask) > 0:
            high_score_hit_rate = np.mean(all_labels[high_score_mask])
            high_score_count = np.sum(high_score_mask)

            print(f"高评分(≥70%)分析:")
            print(f"  样本数量: {high_score_count}")
            print(f"  实际命中率: {high_score_hit_rate:.1%}")
            print(f"  占总样本比例: {high_score_count/len(all_probs):.1%}")
        else:
            print(f"⚠️ 没有评分≥70%的样本")

        print(f"\n评分区间分析:")
        for analysis in threshold_analysis:
            print(f"  {analysis['range']}: {analysis['count']}样本, "
                  f"预测{analysis['avg_prob']:.1%}, 实际{analysis['actual_hit_rate']:.1%}, "
                  f"误差{analysis['calibration_error']:.3f}")

        self.score_thresholds = scoring_system
        return scoring_system

    def calculate_prediction_score(self, prediction_data):
        """计算预测评分"""
        # 提取特征
        features = {
            'pred_num1': prediction_data['pred_num1'],
            'pred_num2': prediction_data['pred_num2'],
            'number_gap': abs(prediction_data['pred_num1'] - prediction_data['pred_num2']),
            'number_sum': prediction_data['pred_num1'] + prediction_data['pred_num2'],
            'number_avg': (prediction_data['pred_num1'] + prediction_data['pred_num2']) / 2,
            'original_confidence': prediction_data.get('original_confidence', 0.03),
            'optimized_confidence': prediction_data.get('optimized_confidence', 0.12),
            'confidence_improvement': prediction_data.get('optimized_confidence', 0.12) - prediction_data.get('original_confidence', 0.03),
            'accuracy_factor': prediction_data.get('accuracy_factor', 1.3),
            'calibration_factor': prediction_data.get('calibration_factor', 0.82),
            'stability_factor': prediction_data.get('stability_factor', 1.03),
            'trend_factor': prediction_data.get('trend_factor', 0.99),
            'composite_factor': prediction_data.get('composite_factor', 1.07),
            'recent_hit_rate': prediction_data.get('recent_hit_rate', 0.35),
            'recent_avg_confidence': prediction_data.get('recent_avg_confidence', 0.12),
            'recent_confidence_trend': prediction_data.get('recent_confidence_trend', 0),
            'number_historical_performance': prediction_data.get('number_historical_performance', 0.35),
            'combo_historical_performance': prediction_data.get('combo_historical_performance', 0.35),
            'period_normalized': prediction_data.get('period', 195) / 200,
            'is_early_period': 0 if prediction_data.get('period', 195) >= 100 else 1
        }

        # 转换为DataFrame
        feature_cols = [col for col in self.features.columns if col not in ['is_hit', 'period']]
        X = pd.DataFrame([features])[feature_cols]

        # 预测概率
        hit_probability = self.model.predict_proba(X)[0, 1]

        # 转换为0-100评分
        score = min(100, max(0, hit_probability * 100))

        # 确定评分等级
        if score >= 81:
            grade = "A+ (高命中概率)"
            recommendation = "重点关注"
        elif score >= 61:
            grade = "A (较高命中概率)"
            recommendation = "值得关注"
        elif score >= 41:
            grade = "B (中等命中概率)"
            recommendation = "可以考虑"
        elif score >= 21:
            grade = "C (低命中概率)"
            recommendation = "谨慎考虑"
        else:
            grade = "D (极低命中概率)"
            recommendation = "不建议"

        return {
            'score': score,
            'probability': hit_probability,
            'grade': grade,
            'recommendation': recommendation,
            'features_used': features
        }

    def create_scoring_visualization(self):
        """创建评分系统可视化"""
        print("\n📊 创建评分系统可视化")
        print("="*30)

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('命中率评分系统分析', fontsize=16, fontweight='bold')

        # 1. ROC曲线
        ax1 = axes[0, 0]

        all_probs = np.concatenate([self.validation_results['train_probs'],
                                   self.validation_results['test_probs']])
        all_labels = np.concatenate([self.validation_results['y_train'],
                                    self.validation_results['y_test']])

        fpr, tpr, _ = roc_curve(all_labels, all_probs)
        auc_score = roc_auc_score(all_labels, all_probs)

        ax1.plot(fpr, tpr, linewidth=2, label=f'ROC曲线 (AUC = {auc_score:.3f})')
        ax1.plot([0, 1], [0, 1], 'k--', alpha=0.5, label='随机预测')
        ax1.set_xlabel('假正率')
        ax1.set_ylabel('真正率')
        ax1.set_title('ROC曲线')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 概率校准图
        ax2 = axes[0, 1]

        # 按概率分组
        prob_bins = np.linspace(0, 1, 11)
        bin_centers = (prob_bins[:-1] + prob_bins[1:]) / 2
        bin_hit_rates = []

        for i in range(len(prob_bins) - 1):
            mask = (all_probs >= prob_bins[i]) & (all_probs < prob_bins[i+1])
            if np.sum(mask) > 0:
                bin_hit_rates.append(np.mean(all_labels[mask]))
            else:
                bin_hit_rates.append(0)

        ax2.plot(bin_centers, bin_hit_rates, 'bo-', label='实际命中率')
        ax2.plot([0, 1], [0, 1], 'r--', label='完美校准')
        ax2.set_xlabel('预测概率')
        ax2.set_ylabel('实际命中率')
        ax2.set_title('概率校准图')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 评分分布
        ax3 = axes[1, 0]

        scores = all_probs * 100

        ax3.hist(scores, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax3.axvline(70, color='red', linestyle='--', linewidth=2, label='70分阈值')
        ax3.set_xlabel('评分')
        ax3.set_ylabel('频次')
        ax3.set_title('评分分布')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 特征重要性
        ax4 = axes[1, 1]

        importance_df = pd.DataFrame(self.validation_results['feature_importance'])
        top_features = importance_df.head(8)

        bars = ax4.barh(range(len(top_features)), top_features['importance'])
        ax4.set_yticks(range(len(top_features)))
        ax4.set_yticklabels(top_features['feature'], fontsize=9)
        ax4.set_xlabel('重要性')
        ax4.set_title('特征重要性 (Top 8)')
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'hit_rate_scoring_system_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')

        print(f"评分系统图表已保存: {filename}")
        return filename

    def generate_scoring_report(self, chart_filename):
        """生成评分系统报告"""
        print("\n📋 生成评分系统报告")
        print("="*30)

        # 分析高评分样本
        all_probs = np.concatenate([self.validation_results['train_probs'],
                                   self.validation_results['test_probs']])
        all_labels = np.concatenate([self.validation_results['y_train'],
                                    self.validation_results['y_test']])

        # 不同评分阈值的分析
        thresholds = [0.5, 0.6, 0.7, 0.8, 0.9]
        threshold_analysis = []

        for threshold in thresholds:
            mask = all_probs >= threshold
            if np.sum(mask) > 0:
                analysis = {
                    'threshold': threshold,
                    'score_threshold': threshold * 100,
                    'sample_count': np.sum(mask),
                    'sample_percentage': np.sum(mask) / len(all_probs) * 100,
                    'actual_hit_rate': np.mean(all_labels[mask]),
                    'avg_predicted_prob': np.mean(all_probs[mask])
                }
                threshold_analysis.append(analysis)

        # 创建详细报告
        report = {
            'system_info': {
                'creation_date': datetime.now().isoformat(),
                'model_type': 'RandomForestClassifier',
                'training_samples': len(self.validation_results['y_train']),
                'test_samples': len(self.validation_results['y_test']),
                'total_features': len([col for col in self.features.columns if col not in ['is_hit', 'period']]),
                'base_hit_rate': np.mean(all_labels)
            },
            'model_performance': {
                'train_auc': self.validation_results['train_auc'],
                'test_auc': self.validation_results['test_auc'],
                'overall_auc': roc_auc_score(all_labels, all_probs)
            },
            'scoring_system': self.score_thresholds,
            'threshold_analysis': threshold_analysis,
            'feature_importance': self.validation_results['feature_importance'][:10],
            'generated_files': {
                'visualization': chart_filename
            }
        }

        # 保存JSON报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_filename = f'scoring_system_report_{timestamp}.json'

        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)

        # 生成文本报告
        text_report = self._generate_text_report(report)
        text_filename = f'scoring_system_summary_{timestamp}.txt'

        with open(text_filename, 'w', encoding='utf-8') as f:
            f.write(text_report)

        print(f"详细报告已保存: {json_filename}")
        print(f"摘要报告已保存: {text_filename}")

        return json_filename, text_filename

    def _generate_text_report(self, report):
        """生成文本报告"""
        text = f"""
命中率评分系统设计报告
{'='*60}

系统信息:
{'='*30}
创建日期: {report['system_info']['creation_date'][:19]}
模型类型: {report['system_info']['model_type']}
训练样本: {report['system_info']['training_samples']}
测试样本: {report['system_info']['test_samples']}
特征数量: {report['system_info']['total_features']}
基础命中率: {report['system_info']['base_hit_rate']:.1%}

模型性能:
{'='*30}
训练集AUC: {report['model_performance']['train_auc']:.3f}
测试集AUC: {report['model_performance']['test_auc']:.3f}
整体AUC: {report['model_performance']['overall_auc']:.3f}

评分系统设计:
{'='*30}"""

        for score_range in report['scoring_system']['score_ranges']:
            text += f"""
{score_range['score_range']}分: {score_range['description']}
  预期命中率: {score_range['expected_hit_rate']}
  建议: {score_range['recommendation']}"""

        text += f"""

不同评分阈值分析:
{'='*30}"""

        for analysis in report['threshold_analysis']:
            text += f"""
评分≥{analysis['score_threshold']:.0f}分:
  样本数量: {analysis['sample_count']} ({analysis['sample_percentage']:.1f}%)
  实际命中率: {analysis['actual_hit_rate']:.1%}
  平均预测概率: {analysis['avg_predicted_prob']:.1%}"""

        text += f"""

重要特征 (Top 10):
{'='*30}"""

        for feature in report['feature_importance']:
            text += f"\n{feature['feature']}: {feature['importance']:.3f}"

        # 关键发现
        high_score_analysis = next((a for a in report['threshold_analysis'] if a['score_threshold'] >= 70), None)

        text += f"""

关键发现:
{'='*30}"""

        if high_score_analysis:
            text += f"""
✅ 评分≥70分的预测:
  - 占总预测的 {high_score_analysis['sample_percentage']:.1f}%
  - 实际命中率达到 {high_score_analysis['actual_hit_rate']:.1%}
  - 相比基础命中率提升 {(high_score_analysis['actual_hit_rate'] - report['system_info']['base_hit_rate']) * 100:+.1f}个百分点"""
        else:
            text += f"\n⚠️ 历史数据中没有评分≥70分的样本，建议降低阈值标准"

        # 模型评估
        auc = report['model_performance']['overall_auc']
        if auc >= 0.7:
            text += f"\n✅ 模型性能良好 (AUC={auc:.3f})，评分系统可靠"
        elif auc >= 0.6:
            text += f"\n⚠️ 模型性能一般 (AUC={auc:.3f})，评分系统有一定参考价值"
        else:
            text += f"\n❌ 模型性能较差 (AUC={auc:.3f})，评分系统可靠性有限"

        text += f"""

使用建议:
{'='*30}
1. 评分≥60分: 值得重点关注的预测
2. 评分40-60分: 中等风险，可适度关注
3. 评分<40分: 低成功概率，谨慎对待

注意事项:
- 评分基于历史数据训练，未来表现可能有差异
- 建议结合其他分析方法综合判断
- 定期重新训练模型以保持准确性

---
报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
评分系统版本: v1.0
"""

        return text

    def test_scoring_system(self):
        """测试评分系统"""
        print("\n🧪 测试评分系统")
        print("="*30)

        # 测试几个示例预测
        test_cases = [
            {
                'name': '第195期预测 (30, 16)',
                'pred_num1': 30,
                'pred_num2': 16,
                'period': 195,
                'optimized_confidence': 0.190
            },
            {
                'name': '高表现组合 (30, 49)',
                'pred_num1': 30,
                'pred_num2': 49,
                'period': 195,
                'optimized_confidence': 0.200
            },
            {
                'name': '常见组合 (3, 30)',
                'pred_num1': 3,
                'pred_num2': 30,
                'period': 195,
                'optimized_confidence': 0.180
            },
            {
                'name': '低表现组合 (2, 43)',
                'pred_num1': 2,
                'pred_num2': 43,
                'period': 195,
                'optimized_confidence': 0.100
            }
        ]

        test_results = []

        for test_case in test_cases:
            result = self.calculate_prediction_score(test_case)
            test_results.append({
                'name': test_case['name'],
                'numbers': f"({test_case['pred_num1']}, {test_case['pred_num2']})",
                'score': result['score'],
                'probability': result['probability'],
                'grade': result['grade'],
                'recommendation': result['recommendation']
            })

            print(f"{test_case['name']}:")
            print(f"  评分: {result['score']:.1f}分")
            print(f"  概率: {result['probability']:.1%}")
            print(f"  等级: {result['grade']}")
            print(f"  建议: {result['recommendation']}")
            print()

        return test_results

    def run_system_design(self):
        """运行评分系统设计"""
        print("🚀 启动命中率评分系统设计")
        print("="*60)

        try:
            # 1. 加载和准备数据
            self.load_and_prepare_data()

            # 2. 提取评分特征
            self.extract_scoring_features()

            # 3. 训练评分模型
            self.train_scoring_model()

            # 4. 设计评分阈值
            self.design_scoring_thresholds()

            # 5. 创建可视化
            chart_filename = self.create_scoring_visualization()

            # 6. 生成报告
            json_report, text_report = self.generate_scoring_report(chart_filename)

            # 7. 测试评分系统
            test_results = self.test_scoring_system()

            print("\n" + "="*60)
            print("✅ 命中率评分系统设计完成！")
            print("="*60)

            # 回答用户的核心问题
            all_probs = np.concatenate([self.validation_results['train_probs'],
                                       self.validation_results['test_probs']])
            all_labels = np.concatenate([self.validation_results['y_train'],
                                        self.validation_results['y_test']])

            high_score_mask = all_probs >= 0.7
            if np.sum(high_score_mask) > 0:
                high_score_hit_rate = np.mean(all_labels[high_score_mask])
                high_score_count = np.sum(high_score_mask)

                print(f"\n🎯 核心问题回答:")
                print(f"评分≥70分的预测:")
                print(f"  样本数量: {high_score_count} ({high_score_count/len(all_probs):.1%})")
                print(f"  实际命中率: {high_score_hit_rate:.1%}")
                print(f"  结论: {'可以' if high_score_hit_rate > 0.5 else '不能'}说评分≥70分代表大概率命中")
            else:
                print(f"\n🎯 核心问题回答:")
                print(f"⚠️ 历史数据中没有评分≥70分的样本")
                print(f"建议降低阈值标准，如评分≥60分")

                # 分析60分阈值
                medium_score_mask = all_probs >= 0.6
                if np.sum(medium_score_mask) > 0:
                    medium_score_hit_rate = np.mean(all_labels[medium_score_mask])
                    medium_score_count = np.sum(medium_score_mask)
                    print(f"评分≥60分的预测:")
                    print(f"  样本数量: {medium_score_count} ({medium_score_count/len(all_probs):.1%})")
                    print(f"  实际命中率: {medium_score_hit_rate:.1%}")

            print(f"\n📁 生成文件:")
            print(f"  可视化图表: {chart_filename}")
            print(f"  详细报告: {json_report}")
            print(f"  摘要报告: {text_report}")

            return {
                'model': self.model,
                'score_thresholds': self.score_thresholds,
                'validation_results': self.validation_results,
                'test_results': test_results,
                'chart_filename': chart_filename,
                'json_report': json_report,
                'text_report': text_report
            }

        except Exception as e:
            print(f"\n❌ 评分系统设计过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    scoring_system = HitRateScoringSystem()
    results = scoring_system.run_system_design()

    if results:
        print(f"\n🎉 评分系统设计成功完成！")
        print(f"现在可以使用该系统对新的预测进行评分。")
    else:
        print(f"\n❌ 评分系统设计失败！")

if __name__ == "__main__":
    main()