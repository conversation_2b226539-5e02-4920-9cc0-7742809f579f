{
  "comprehensive_report": {
    "experiment_metadata": {
      "experiment_name": "全面预测方式验证实验",
      "train_period": "2023-2024年",
      "test_period": "2025年1-179期",
      "train_size": 731,
      "test_size": 178,
      "random_seed": 42,
      "execution_time": "2025-07-13T04:06:49.563086"
    },
    "method_comparison": {
      "single_period": {
        "accuracy_rate": 0.29213483146067415,
        "accuracy_ci": [
          0.2247191011235955,
          0.3595505617977528
        ],
        "diversity_score": 0.5337078651685393,
        "calibration_error": 0.02379343125008559,
        "timing_advantage": -0.29213483146067415,
        "bet_ratio": 0.0,
        "trend_slope": 0.0005787532498941893
      },
      "continuous": {
        "accuracy_rate": 0.20786516853932585,
        "accuracy_ci": [
          0.15168539325842698,
          0.2696629213483146
        ],
        "diversity_score": 0.02247191011235955,
        "calibration_error": 0.015225890350269252,
        "timing_advantage": -0.20786516853932585,
        "bet_ratio": 0.0,
        "trend_slope": 0.0010838623858758084
      },
      "rolling": {
        "accuracy_rate": 0.2808988764044944,
        "accuracy_ci": [
          0.21348314606741572,
          0.34831460674157305
        ],
        "diversity_score": 0.5280898876404494,
        "calibration_error": 0.02276381850423022,
        "timing_advantage": -0.2808988764044944,
        "bet_ratio": 0.0,
        "trend_slope": 0.001557046979865772
      },
      "probability_dist": {
        "accuracy_rate": 0.28651685393258425,
        "accuracy_ci": [
          0.21910112359550563,
          0.3539325842696629
        ],
        "diversity_score": 0.38764044943820225,
        "calibration_error": 0.028406988975480132,
        "timing_advantage": -0.28651685393258425,
        "bet_ratio": 0.0,
        "trend_slope": 0.0005043835782090817
      }
    },
    "statistical_tests": {
      "single_period_vs_continuous": {
        "mcnemar_statistic": 4.0,
        "p_value": 0.04550026389635853,
        "significant": true
      },
      "single_period_vs_rolling": {
        "mcnemar_statistic": 0.041666666666666664,
        "p_value": 0.8382564863858263,
        "significant": false
      },
      "single_period_vs_probability_dist": {
        "mcnemar_statistic": 0.0,
        "p_value": 1.0,
        "significant": false
      },
      "continuous_vs_rolling": {
        "mcnemar_statistic": 2.526315789473684,
        "p_value": 0.11196135426374809,
        "significant": false
      },
      "continuous_vs_probability_dist": {
        "mcnemar_statistic": 3.840909090909091,
        "p_value": 0.05001639548564596,
        "significant": false
      },
      "rolling_vs_probability_dist": {
        "mcnemar_statistic": 0.0,
        "p_value": 1.0,
        "significant": false
      }
    },
    "key_findings": [
      "最高准确率方法: single_period (0.292)",
      "最高多样性方法: single_period (0.534)",
      "统计显著性差异: 1对比较显著"
    ],
    "recommendations": [
      "基于准确率选择最佳方法",
      "考虑多样性要求选择合适方法",
      "根据应用场景权衡准确率与多样性",
      "使用统计检验结果指导方法选择"
    ]
  },
  "detailed_evaluation": {
    "single_period": {
      "accuracy": {
        "accuracy_rate": 0.29213483146067415,
        "successful_predictions": 52,
        "total_predictions": 178,
        "confidence_interval_95": [
          0.2247191011235955,
          0.3595505617977528
        ]
      },
      "diversity": {
        "diversity_score": 0.5337078651685393,
        "unique_predictions": 95,
        "total_predictions": 178,
        "most_common_predictions": [
          [
            [
              