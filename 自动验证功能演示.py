#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动验证功能演示 - 演示升级版手动输入预测系统的自动验证功能
当用户输入本期真实数据时，自动验证上期预测并更新CSV文件
"""

import sys
import os
sys.path.append('.')

from 升级版手动输入预测系统 import UpgradedManualPredictionSystem

def demo_auto_verification():
    """演示自动验证功能"""
    print("🎯 自动验证功能演示")
    print("=" * 60)
    
    # 初始化系统
    system = UpgradedManualPredictionSystem()
    
    print("🔧 正在初始化系统...")
    if not system.load_data_and_build_model():
        print("❌ 系统初始化失败")
        return
    
    print("✅ 系统初始化完成")
    
    # 演示场景：输入193期数据，自动验证192期预测
    demo_data = {
        'year': 2025,
        'period': 193,
        'numbers': [8, 15, 22, 30, 37, 44]  # 假设的193期开奖数据
    }
    
    print(f"\n📝 演示场景:")
    print(f"用户输入: {demo_data['year']}年{demo_data['period']}期")
    print(f"开奖数字: {demo_data['numbers']}")
    print(f"期望效果: 自动验证192期预测并更新CSV文件")
    
    print(f"\n🚀 执行自动验证和预测流程...")
    print("=" * 50)
    
    try:
        # 调用升级版系统的预测和保存方法
        prediction_result = system.make_prediction_and_save(demo_data)
        
        if prediction_result:
            print(f"\n🎉 演示完成")
            print("=" * 40)
            print(f"✅ 自动验证: 已验证192期预测")
            print(f"✅ 数据保存: 193期数据已添加到主文件")
            print(f"✅ 新预测: 194期预测已生成")
            print(f"✅ CSV更新: prediction_data.csv已更新")
            
            print(f"\n📊 预测结果:")
            print(f"当期: {demo_data['year']}年{demo_data['period']}期")
            print(f"预测下期: {demo_data['year']}年{demo_data['period']+1}期")
            print(f"预测数字: {prediction_result['predicted_numbers']}")
            print(f"预测置信度: {prediction_result['confidence']:.3f}")
            
            return True
        else:
            print(f"\n❌ 演示失败")
            return False
            
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
        return False

def show_csv_before_after():
    """显示CSV文件更新前后对比"""
    print(f"\n📊 CSV文件更新效果")
    print("=" * 50)
    
    try:
        import pandas as pd
        
        # 读取prediction_data.csv
        if os.path.exists("prediction_data.csv"):
            df = pd.read_csv("prediction_data.csv", encoding='utf-8-sig')
            
            print(f"📄 prediction_data.csv 最新记录:")
            
            # 显示最后几行
            recent_records = df.tail(3)
            for idx, row in recent_records.iterrows():
                period_name = row['预测期号']
                predicted = [row['预测数字1'], row['预测数字2']]
                
                # 检查是否已验证
                if pd.isna(row['实际数字1']) or row['实际数字1'] == '':
                    status = "⏳ 待验证"
                    actual_info = ""
                else:
                    actual = [row[f'实际数字{i}'] for i in range(1, 7)]
                    hit_status = "✅ 命中" if row['是否命中'] == '是' else "❌ 未命中"
                    hit_info = f"命中{row['命中数字']}" if row['命中数字'] else "未命中"
                    status = f"{hit_status} {hit_info}"
                    actual_info = f" vs 实际{actual}"
                
                print(f"  {period_name}: 预测{predicted}{actual_info} {status}")
            
            # 统计信息
            total_predictions = len(df)
            verified_predictions = df[
                (~df['实际数字1'].isna()) & 
                (df['实际数字1'] != '') &
                (df['实际数字1'] != 'nan')
            ]
            
            if len(verified_predictions) > 0:
                hits = len(verified_predictions[verified_predictions['是否命中'] == '是'])
                hit_rate = hits / len(verified_predictions)
                
                print(f"\n📊 统计信息:")
                print(f"总预测期数: {total_predictions}")
                print(f"已验证期数: {len(verified_predictions)}")
                print(f"命中期数: {hits}")
                print(f"命中率: {hit_rate:.3f} ({hit_rate:.1%})")
        
        else:
            print("❌ prediction_data.csv文件不存在")
    
    except Exception as e:
        print(f"❌ 读取CSV文件失败: {e}")

def show_feature_explanation():
    """显示功能说明"""
    print(f"\n💡 自动验证功能说明")
    print("=" * 50)
    
    print(f"🎯 功能目标:")
    print(f"  当用户输入本期真实开奖数据时，系统自动:")
    print(f"  1. 查找上期对应的预测记录")
    print(f"  2. 对比预测数字与真实开奖数字")
    print(f"  3. 计算命中情况（命中数量、命中数字）")
    print(f"  4. 更新prediction_data.csv文件")
    print(f"  5. 显示验证结果")
    
    print(f"\n🔄 工作流程:")
    print(f"  用户输入: 2025年193期 [8,15,22,30,37,44]")
    print(f"  ↓")
    print(f"  系统查找: 2025年193期的预测记录")
    print(f"  ↓") 
    print(f"  自动对比: 预测[30,3] vs 实际[8,15,22,30,37,44]")
    print(f"  ↓")
    print(f"  计算命中: 命中数字30，命中数量1")
    print(f"  ↓")
    print(f"  更新CSV: 填入实际数字和命中信息")
    print(f"  ↓")
    print(f"  显示结果: ✅ 命中 (命中数字30)")
    
    print(f"\n✅ 功能优势:")
    print(f"  ✅ 自动化: 无需手动验证，输入数据即自动验证")
    print(f"  ✅ 实时性: 输入当期数据立即验证上期预测")
    print(f"  ✅ 准确性: 自动计算命中情况，避免人工错误")
    print(f"  ✅ 完整性: 同时完成验证和新预测两个任务")
    print(f"  ✅ 便捷性: 一次操作完成所有数据管理")

def main():
    """主函数"""
    print("🎯 升级版手动输入预测系统 - 自动验证功能演示")
    print("=" * 70)
    
    # 1. 显示功能说明
    show_feature_explanation()
    
    # 2. 显示当前CSV状态
    show_csv_before_after()
    
    # 3. 询问是否执行演示
    print(f"\n🚀 是否执行自动验证功能演示？")
    print(f"演示内容: 输入193期数据，自动验证192期预测")
    choice = input("输入 y 执行演示，n 跳过: ").strip().lower()
    
    if choice == 'y':
        # 4. 执行演示
        demo_success = demo_auto_verification()
        
        if demo_success:
            # 5. 显示更新后的CSV状态
            print(f"\n📊 演示后CSV文件状态:")
            show_csv_before_after()
            
            print(f"\n🎉 自动验证功能演示完成")
            print(f"✅ 功能正常工作，可以投入使用")
        else:
            print(f"\n❌ 演示失败，请检查系统配置")
    else:
        print(f"\n👋 跳过演示")
    
    print(f"\n💡 使用说明:")
    print(f"现在您可以使用以下命令启动升级版系统:")
    print(f"python 升级版手动输入预测系统.py")
    print(f"")
    print(f"当您输入本期真实数据时，系统会自动:")
    print(f"1. 验证上期预测并更新CSV文件")
    print(f"2. 添加本期数据到主文件")
    print(f"3. 生成下期预测")
    print(f"4. 保存新预测到CSV文件")

if __name__ == "__main__":
    main()
