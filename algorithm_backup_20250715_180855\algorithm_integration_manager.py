#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法集成管理器
Algorithm Integration Manager

管理多种预测算法的集成、A/B测试和性能监控

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0
"""

import pandas as pd
import numpy as np
from enhanced_prediction_algorithms import (
    EnhancedMarkovChain, 
    FrequencyAnalyzer, 
    PatternMatcher, 
    TrendAnalyzer
)
from collections import defaultdict, deque
from datetime import datetime
import json
import logging
import random

logger = logging.getLogger(__name__)

class AlgorithmIntegrationManager:
    """算法集成管理器"""
    
    def __init__(self, config=None):
        """初始化管理器"""
        self.config = config or self._get_default_config()
        
        # 初始化算法
        self.algorithms = {
            'enhanced_markov': EnhancedMarkovChain(),
            'frequency_analyzer': FrequencyAnalyzer(),
            'pattern_matcher': <PERSON><PERSON><PERSON><PERSON><PERSON>(),
            'trend_analyzer': TrendAnalyzer()
        }
        
        # A/B测试管理
        self.ab_test_groups = {}
        self.performance_history = deque(maxlen=1000)
        self.algorithm_performance = defaultdict(list)
        
        # 集成权重
        self.ensemble_weights = self.config['initial_weights'].copy()
        
        logger.info("🔧 算法集成管理器初始化完成")
        logger.info(f"  算法数量: {len(self.algorithms)}")
        logger.info(f"  A/B测试模式: {self.config['ab_test_enabled']}")
    
    def _get_default_config(self):
        """获取默认配置"""
        return {
            'ab_test_enabled': True,
            'ab_test_ratio': 0.3,  # 30%的预测使用A/B测试
            'initial_weights': {
                'enhanced_markov': 0.4,
                'frequency_analyzer': 0.3,
                'pattern_matcher': 0.2,
                'trend_analyzer': 0.1
            },
            'weight_adaptation': {
                'enabled': True,
                'learning_rate': 0.05,
                'min_weight': 0.05,
                'max_weight': 0.7
            },
            'performance_window': 50,
            'ensemble_methods': ['weighted_average', 'voting', 'stacking']
        }
    
    def train_all_algorithms(self, training_data):
        """训练所有算法"""
        logger.info("🔄 训练所有预测算法")
        
        training_results = {}
        
        for name, algorithm in self.algorithms.items():
            try:
                logger.info(f"  训练 {name}")
                algorithm.train(training_data)
                training_results[name] = {
                    'status': 'success',
                    'info': algorithm.get_model_info()
                }
            except Exception as e:
                logger.error(f"  ❌ {name} 训练失败: {e}")
                training_results[name] = {
                    'status': 'failed',
                    'error': str(e)
                }
        
        logger.info("✅ 算法训练完成")
        return training_results
    
    def predict_ensemble(self, previous_numbers, context=None):
        """集成预测"""
        # 决定是否使用A/B测试
        use_ab_test = (self.config['ab_test_enabled'] and 
                      random.random() < self.config['ab_test_ratio'])
        
        if use_ab_test:
            return self._predict_ab_test(previous_numbers, context)
        else:
            return self._predict_ensemble(previous_numbers, context)
    
    def _predict_ensemble(self, previous_numbers, context=None):
        """标准集成预测"""
        algorithm_predictions = {}
        algorithm_probabilities = {}
        
        # 获取各算法的预测
        for name, algorithm in self.algorithms.items():
            try:
                if name == 'trend_analyzer' and context:
                    predicted_nums, probs = algorithm.predict(
                        previous_numbers, 
                        context.get('period_idx', 0)
                    )
                else:
                    predicted_nums, probs = algorithm.predict(previous_numbers)
                
                algorithm_predictions[name] = predicted_nums
                algorithm_probabilities[name] = probs
                
            except Exception as e:
                logger.warning(f"⚠️ {name} 预测失败: {e}")
                # 使用默认预测
                algorithm_predictions[name] = [1, 2]
                algorithm_probabilities[name] = {i: 1/49 for i in range(1, 50)}
        
        # 集成预测结果
        ensemble_result = self._combine_predictions(
            algorithm_predictions, 
            algorithm_probabilities
        )
        
        return {
            'predicted_numbers': ensemble_result['predicted_numbers'],
            'probabilities': ensemble_result['probabilities'],
            'algorithm_predictions': algorithm_predictions,
            'algorithm_probabilities': algorithm_probabilities,
            'ensemble_method': 'weighted_average',
            'weights': self.ensemble_weights.copy(),
            'ab_test_group': None
        }
    
    def _predict_ab_test(self, previous_numbers, context=None):
        """A/B测试预测"""
        # 随机选择一个算法进行A/B测试
        test_algorithm = random.choice(list(self.algorithms.keys()))
        
        try:
            if test_algorithm == 'trend_analyzer' and context:
                predicted_nums, probs = self.algorithms[test_algorithm].predict(
                    previous_numbers, 
                    context.get('period_idx', 0)
                )
            else:
                predicted_nums, probs = self.algorithms[test_algorithm].predict(previous_numbers)
            
            return {
                'predicted_numbers': predicted_nums,
                'probabilities': probs,
                'algorithm_predictions': {test_algorithm: predicted_nums},
                'algorithm_probabilities': {test_algorithm: probs},
                'ensemble_method': 'single_algorithm',
                'weights': {test_algorithm: 1.0},
                'ab_test_group': test_algorithm
            }
            
        except Exception as e:
            logger.warning(f"⚠️ A/B测试算法 {test_algorithm} 失败: {e}")
            # 回退到集成预测
            return self._predict_ensemble(previous_numbers, context)
    
    def _combine_predictions(self, algorithm_predictions, algorithm_probabilities):
        """组合多个算法的预测结果"""
        # 加权平均概率
        combined_probs = defaultdict(float)
        total_weight = sum(self.ensemble_weights.values())
        
        for algorithm, weight in self.ensemble_weights.items():
            if algorithm in algorithm_probabilities:
                normalized_weight = weight / total_weight
                for num, prob in algorithm_probabilities[algorithm].items():
                    combined_probs[num] += prob * normalized_weight
        
        # 选择概率最高的数字
        sorted_probs = sorted(combined_probs.items(), key=lambda x: x[1], reverse=True)
        predicted_numbers = [num for num, _ in sorted_probs[:2]]
        
        return {
            'predicted_numbers': predicted_numbers,
            'probabilities': dict(combined_probs)
        }
    
    def update_performance(self, prediction_result, actual_numbers):
        """更新算法性能"""
        # 计算命中情况
        predicted_numbers = prediction_result['predicted_numbers']
        hit_numbers = list(set(predicted_numbers) & set(actual_numbers))
        is_hit = len(hit_numbers) >= 1
        hit_count = len(hit_numbers)
        
        # 记录性能
        performance_record = {
            'timestamp': datetime.now(),
            'predicted_numbers': predicted_numbers,
            'actual_numbers': actual_numbers,
            'hit_numbers': hit_numbers,
            'is_hit': is_hit,
            'hit_count': hit_count,
            'ab_test_group': prediction_result.get('ab_test_group'),
            'ensemble_method': prediction_result.get('ensemble_method'),
            'weights': prediction_result.get('weights', {})
        }
        
        self.performance_history.append(performance_record)
        
        # 更新算法性能统计
        if prediction_result.get('ab_test_group'):
            # A/B测试结果
            algorithm = prediction_result['ab_test_group']
            self.algorithm_performance[algorithm].append({
                'is_hit': is_hit,
                'hit_count': hit_count,
                'timestamp': datetime.now()
            })
        else:
            # 集成预测结果，为所有参与的算法记录性能
            for algorithm in prediction_result.get('algorithm_predictions', {}):
                self.algorithm_performance[algorithm].append({
                    'is_hit': is_hit,
                    'hit_count': hit_count,
                    'timestamp': datetime.now(),
                    'ensemble': True
                })
        
        # 自适应权重调整
        if self.config['weight_adaptation']['enabled']:
            self._adapt_weights()
        
        logger.info(f"📊 性能更新: 预测{predicted_numbers}, 实际{actual_numbers}, "
                   f"命中{'✅' if is_hit else '❌'}")
    
    def _adapt_weights(self):
        """自适应权重调整"""
        if len(self.performance_history) < self.config['performance_window']:
            return
        
        # 获取最近的性能数据
        recent_performance = list(self.performance_history)[-self.config['performance_window']:]
        
        # 计算各算法的性能
        algorithm_scores = {}
        for algorithm in self.algorithms.keys():
            algorithm_records = [
                record for record in recent_performance 
                if (record.get('ab_test_group') == algorithm or 
                    algorithm in record.get('weights', {}))
            ]
            
            if algorithm_records:
                hit_rate = sum(1 for record in algorithm_records if record['is_hit']) / len(algorithm_records)
                avg_hit_count = np.mean([record['hit_count'] for record in algorithm_records])
                
                # 综合评分
                algorithm_scores[algorithm] = 0.7 * hit_rate + 0.3 * (avg_hit_count / 2)
            else:
                algorithm_scores[algorithm] = 0.25  # 默认评分
        
        # 调整权重
        learning_rate = self.config['weight_adaptation']['learning_rate']
        min_weight = self.config['weight_adaptation']['min_weight']
        max_weight = self.config['weight_adaptation']['max_weight']
        
        # 计算新权重
        total_score = sum(algorithm_scores.values())
        if total_score > 0:
            for algorithm in self.ensemble_weights:
                target_weight = algorithm_scores[algorithm] / total_score
                current_weight = self.ensemble_weights[algorithm]
                
                # 渐进调整
                new_weight = current_weight + learning_rate * (target_weight - current_weight)
                
                # 应用边界约束
                new_weight = max(min_weight, min(max_weight, new_weight))
                
                self.ensemble_weights[algorithm] = new_weight
        
        # 标准化权重
        total_weight = sum(self.ensemble_weights.values())
        if total_weight > 0:
            for algorithm in self.ensemble_weights:
                self.ensemble_weights[algorithm] /= total_weight
        
        logger.info(f"🔧 权重已调整: {self.ensemble_weights}")
    
    def get_performance_report(self):
        """获取性能报告"""
        if not self.performance_history:
            return {'status': 'no_data'}
        
        # 总体性能
        total_predictions = len(self.performance_history)
        total_hits = sum(1 for record in self.performance_history if record['is_hit'])
        overall_hit_rate = total_hits / total_predictions if total_predictions > 0 else 0
        
        # 各算法性能
        algorithm_stats = {}
        for algorithm in self.algorithms.keys():
            algorithm_records = [
                record for record in self.performance_history 
                if (record.get('ab_test_group') == algorithm or 
                    algorithm in record.get('weights', {}))
            ]
            
            if algorithm_records:
                hits = sum(1 for record in algorithm_records if record['is_hit'])
                hit_rate = hits / len(algorithm_records)
                avg_hit_count = np.mean([record['hit_count'] for record in algorithm_records])
                
                algorithm_stats[algorithm] = {
                    'predictions': len(algorithm_records),
                    'hits': hits,
                    'hit_rate': hit_rate,
                    'avg_hit_count': avg_hit_count,
                    'current_weight': self.ensemble_weights.get(algorithm, 0)
                }
        
        # A/B测试统计
        ab_test_records = [record for record in self.performance_history if record.get('ab_test_group')]
        ab_test_stats = {
            'total_ab_tests': len(ab_test_records),
            'ab_test_ratio': len(ab_test_records) / total_predictions if total_predictions > 0 else 0
        }
        
        return {
            'status': 'success',
            'timestamp': datetime.now().isoformat(),
            'overall_performance': {
                'total_predictions': total_predictions,
                'total_hits': total_hits,
                'hit_rate': overall_hit_rate
            },
            'algorithm_performance': algorithm_stats,
            'ab_test_stats': ab_test_stats,
            'current_weights': self.ensemble_weights.copy(),
            'config': self.config
        }
    
    def export_performance_data(self, filename):
        """导出性能数据"""
        try:
            # 准备导出数据
            export_data = {
                'performance_history': [
                    {
                        'timestamp': record['timestamp'].isoformat(),
                        'predicted_numbers': record['predicted_numbers'],
                        'actual_numbers': record['actual_numbers'],
                        'hit_numbers': record['hit_numbers'],
                        'is_hit': record['is_hit'],
                        'hit_count': record['hit_count'],
                        'ab_test_group': record.get('ab_test_group'),
                        'ensemble_method': record.get('ensemble_method'),
                        'weights': record.get('weights', {})
                    }
                    for record in self.performance_history
                ],
                'performance_report': self.get_performance_report(),
                'export_timestamp': datetime.now().isoformat()
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 性能数据已导出到: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 导出性能数据失败: {e}")
            return False
    
    def get_algorithm_status(self):
        """获取算法状态"""
        status = {}
        for name, algorithm in self.algorithms.items():
            status[name] = algorithm.get_model_info()
        
        return {
            'algorithms': status,
            'ensemble_weights': self.ensemble_weights.copy(),
            'performance_records': len(self.performance_history),
            'ab_test_enabled': self.config['ab_test_enabled']
        }

# 便捷函数
def create_algorithm_manager(config=None):
    """创建算法管理器实例"""
    return AlgorithmIntegrationManager(config)

if __name__ == "__main__":
    # 示例使用
    manager = create_algorithm_manager()
    
    # 模拟训练数据
    training_data = pd.DataFrame({
        '数字1': np.random.randint(1, 50, 100),
        '数字2': np.random.randint(1, 50, 100),
        '数字3': np.random.randint(1, 50, 100),
        '数字4': np.random.randint(1, 50, 100),
        '数字5': np.random.randint(1, 50, 100),
        '数字6': np.random.randint(1, 50, 100)
    })
    
    # 训练算法
    results = manager.train_all_algorithms(training_data)
    print("训练结果:", results)
    
    # 进行预测
    prediction = manager.predict_ensemble([1, 15, 23, 30, 35, 42])
    print("预测结果:", prediction)
    
    # 更新性能
    manager.update_performance(prediction, [5, 15, 25, 35, 40, 45])
    
    # 获取性能报告
    report = manager.get_performance_report()
    print("性能报告:", report)
