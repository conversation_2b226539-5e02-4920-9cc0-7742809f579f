# 动态权重自主预测系统使用说明

## 🎯 系统概述

动态权重自主预测系统是基于严格验证的动态权重调整算法开发的可自行运行预测工具。系统能够根据历史数据动态调整数字权重，并提供实时预测功能。

## 🚀 快速开始

### 1. 运行系统
```bash
python 动态权重自主预测系统.py
```

### 2. 系统初始化
系统启动时会自动：
- 加载主数据文件 `data/processed/lottery_data_clean_no_special.csv`
- 初始化基础权重（基于最近100期数据）
- 加载系统状态（如果存在）

## 📋 主要功能

### 🎯 1. 输入当期数据并预测下期

#### 操作步骤：
1. 选择菜单选项 `1`
2. 输入年份（如：2025）
3. 输入期号（如：205）
4. 输入6个开奖数字

#### 支持的输入格式：
- **空格分隔**：`5 12 23 31 40 45`
- **逗号分隔**：`5,12,23,31,40,45`
- **混合格式**：`5, 12, 23, 31, 40, 45`

#### 系统会自动：
- 验证输入数据的有效性
- 将当期数据添加到主数据文件
- 更新动态权重（每5期更新一次）
- 生成下期预测
- 保存预测结果到CSV文件
- 保存系统状态

#### 预测结果显示：
```
📊 预测结果:
   预测期号: 2025年206期
   预测数字: [15, 32]
   预测置信度: 65.2%
   预测方法: Dynamic_Weight_Prediction
```

### 📊 2. 查看预测历史

#### 功能说明：
- 显示最近10条预测记录
- 包含预测时间、基于数据、预测结果、置信度等信息
- 显示预测统计（总数、已验证数、命中数、命中率）

#### 显示格式：
```
预测时间: 2025-07-25 12:30:45
基于数据: 2025年205期 [5, 12, 23, 31, 40, 45]
预测结果: 2025年206期 [15, 32]
置信度: 65.2% | 方法: Dynamic_Weight_Prediction
验证状态: 待验证
```

### 🔍 3. 验证预测结果

#### 操作步骤：
1. 选择菜单选项 `3`
2. 输入要验证的年份和期号
3. 输入实际开奖的6个数字
4. 系统自动计算命中情况并更新记录

#### 验证结果显示：
```
🔍 预测验证结果:
   预测期号: 2025年206期
   预测数字: [15, 32]
   实际数字: [8, 15, 22, 32, 41, 47]
   命中数量: 2
   验证结果: ✅ 命中
```

### 🔧 4. 查看系统状态

#### 显示信息：
- 系统初始化状态
- 数据文件路径和状态
- 主数据期数和最新数据
- 权重范围和更新次数
- 预测历史数量
- 文件存在状态

## 📁 文件说明

### 输入文件：
- **`data/processed/lottery_data_clean_no_special.csv`** - 主数据文件（历史开奖数据）

### 输出文件：
- **`dynamic_weight_predictions.csv`** - 预测结果文件
- **`dynamic_weight_state.json`** - 系统状态文件

### 预测结果CSV文件格式：
```csv
预测时间,基于年份,基于期号,基于数字,预测年份,预测期号,预测数字1,预测数字2,预测组合,预测置信度,预测方法,预测得分,实际数字1,实际数字2,实际数字3,实际数字4,实际数字5,实际数字6,实际组合,命中数量,是否命中,命中率,验证状态
```

## ⚙️ 系统配置

### 动态权重配置：
```python
'weight_detection': {
    'window_size': 15,          # 检测窗口大小（期数）
    'update_frequency': 5,      # 更新频率（每5期更新一次）
    'min_appearance': 2,        # 最小出现次数
    'boost_factor': 1.3,        # 权重提升因子
    'decay_factor': 0.98,       # 权重衰减因子
    'regularization': 0.05      # 正则化强度
}
```

### 预测策略配置：
```python
'prediction': {
    'base_confidence': 0.2,     # 基础置信度
    'hot_boost': 1.5,           # 热门数字加成
    'diversity_factor': 0.3,    # 多样性因子
    'min_distance': 6,          # 最小距离
    'exploration_rate': 0.25    # 探索率
}
```

## 🔬 核心算法特点

### 1. 动态权重调整
- **检测窗口**：基于最近15期数据检测权重变化
- **更新频率**：每5期自动更新一次权重
- **热门数字识别**：自动识别出现频率高于基准的数字
- **权重提升**：热门数字权重提升1.3倍
- **权重衰减**：其他数字权重按0.98衰减

### 2. 预测策略
- **综合评分**：结合动态权重、趋势权重和随机因子
- **多样性约束**：避免重复选择最近使用的数字组合
- **距离约束**：确保预测的两个数字间距不小于6
- **置信度计算**：基于权重分数计算预测置信度

### 3. 过拟合控制
- **正则化**：5%的权重平滑防止过拟合
- **稳定性控制**：防止权重过度波动
- **历史约束**：基于历史数据的合理约束

## 📈 使用建议

### 1. 数据输入
- **及时更新**：每期开奖后及时输入数据
- **准确输入**：确保输入数据的准确性
- **格式规范**：使用支持的输入格式

### 2. 预测使用
- **参考置信度**：关注预测置信度，高置信度预测更可靠
- **结合历史**：查看预测历史了解系统表现
- **及时验证**：开奖后及时验证预测结果

### 3. 系统维护
- **定期备份**：备份预测数据和系统状态文件
- **监控状态**：定期查看系统状态确保正常运行
- **数据清理**：定期清理过期的预测记录

## ⚠️ 注意事项

### 1. 数据要求
- 主数据文件必须存在且格式正确
- 输入数据必须符合规范（6个不重复的1-49数字）
- 系统需要足够的历史数据进行权重初始化

### 2. 系统限制
- 每次只预测2个数字
- 权重更新需要一定的数据积累
- 预测准确性受历史数据质量影响

### 3. 使用风险
- 预测结果仅供参考，不保证准确性
- 系统基于历史数据，无法预测突发变化
- 建议结合其他方法综合判断

## 🆘 常见问题

### Q1: 系统初始化失败怎么办？
**A**: 检查主数据文件是否存在，路径是否正确，文件格式是否符合要求。

### Q2: 预测结果不理想怎么办？
**A**: 系统需要一定的数据积累，建议持续使用并及时验证结果。

### Q3: 如何提高预测准确性？
**A**: 确保输入数据准确，及时更新数据，关注系统状态和权重变化。

### Q4: 可以修改系统参数吗？
**A**: 可以修改代码中的配置参数，但建议先了解参数含义和影响。

### Q5: 预测数据丢失怎么办？
**A**: 系统会自动保存状态，重启后会恢复。建议定期备份重要文件。

## 📞 技术支持

如有问题或建议，请查看：
- 系统状态信息
- 预测历史记录
- 错误提示信息
- 相关技术文档

---

**版本**: 1.0  
**更新时间**: 2025-07-25  
**基于**: 严格验证动态权重系统  
**核心特性**: 动态权重调整、实时预测、自动验证
