#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版置信度优化
Corrected Confidence Optimization

只优化置信度调整，不改变预测算法本身

作者: AI Assistant
创建时间: 2025-07-15
版本: 2.1 Corrected
"""

import pandas as pd
import numpy as np
from datetime import datetime
from optimized_confidence_system import OptimizedConfidenceAdjuster
import json

def get_original_prediction_for_195():
    """获取第195期的原始预测"""
    print("📊 获取第195期原始预测数据")
    print("="*40)
    
    df = pd.read_csv('prediction_data.csv')
    
    # 查找第195期的预测（在CSV中是当期期号194，预测第195期）
    period_195_row = df[df['当期期号'] == 194].iloc[0]
    
    original_prediction = {
        'period': 195,
        'predicted_numbers': [int(period_195_row['预测数字1']), int(period_195_row['预测数字2'])],
        'base_numbers': [6, 8, 12, 22, 27, 42],  # 第194期实际结果
        'original_confidence': period_195_row['预测置信度'],
        'prediction_method': period_195_row['预测方法'],
        'prediction_date': period_195_row['预测日期'],
        'prediction_time': period_195_row['预测时间']
    }
    
    print(f"原始预测数据:")
    print(f"  期号: 第{original_prediction['period']}期")
    print(f"  预测数字: {original_prediction['predicted_numbers']}")
    print(f"  原始置信度: {original_prediction['original_confidence']:.3f}")
    print(f"  预测方法: {original_prediction['prediction_method']}")
    print(f"  预测时间: {original_prediction['prediction_date']} {original_prediction['prediction_time']}")
    
    return original_prediction

def apply_optimized_confidence_only(original_prediction):
    """只对原始预测应用优化的置信度调整"""
    print(f"\n🔧 对原始预测应用优化置信度调整")
    print("="*50)
    
    # 初始化优化的置信度调整器
    adjuster = OptimizedConfidenceAdjuster()
    
    # 加载历史数据进行训练（模拟）
    df = pd.read_csv('prediction_data.csv')
    historical_data = df[df['当期期号'] <= 194].copy()
    
    print(f"使用历史数据训练置信度调整器...")
    
    # 模拟历史性能数据
    for _, row in historical_data.iterrows():
        if pd.notna(row['实际数字1']):  # 只处理有实际结果的数据
            # 预测数字和实际数字
            predicted_numbers = [row['预测数字1'], row['预测数字2']]
            actual_numbers = [
                row['实际数字1'], row['实际数字2'], row['实际数字3'],
                row['实际数字4'], row['实际数字5'], row['实际数字6']
            ]
            
            # 计算命中情况
            hit_numbers = list(set(predicted_numbers) & set(actual_numbers))
            is_hit = len(hit_numbers) >= 1
            hit_count = len(hit_numbers)
            
            # 更新历史性能
            performance_data = {
                'period': row['当期期号'],
                'original_confidence': row['预测置信度'],
                'adjusted_confidence': row['预测置信度'] * 1.1,  # 模拟调整
                'is_hit': is_hit,
                'hit_count': hit_count
            }
            
            adjuster.update_historical_performance(performance_data)
    
    print(f"历史数据训练完成，共处理 {len(adjuster.historical_performance)} 期数据")
    
    # 对第195期原始预测应用优化置信度调整
    prediction_context = {
        'period': original_prediction['period'],
        'predicted_numbers': original_prediction['predicted_numbers'],
        'base_numbers': original_prediction['base_numbers'],
        'prediction_method': original_prediction['prediction_method']
    }
    
    confidence_result = adjuster.adjust_confidence_optimized(
        original_prediction['original_confidence'], 
        prediction_context
    )
    
    # 构建完整结果
    optimized_result = {
        'period': original_prediction['period'],
        'period_name': f"2025年第{original_prediction['period']}期",
        'predicted_numbers': original_prediction['predicted_numbers'],  # 保持原始预测不变
        'base_numbers': original_prediction['base_numbers'],
        'original_prediction_info': {
            'method': original_prediction['prediction_method'],
            'date': original_prediction['prediction_date'],
            'time': original_prediction['prediction_time']
        },
        'confidence_analysis': confidence_result,
        'confidence_interpretation': adjuster.get_confidence_interpretation(
            confidence_result['final_confidence']
        ),
        'optimization_timestamp': datetime.now().isoformat(),
        'system_version': '置信度优化版 v2.1',
        'note': '只优化置信度调整，预测数字保持原始算法结果'
    }
    
    return optimized_result, adjuster

def create_comparison_report(original_prediction, optimized_result):
    """创建对比报告"""
    print(f"\n📊 生成对比报告")
    print("="*30)
    
    confidence = optimized_result['confidence_analysis']
    
    comparison = {
        'prediction_comparison': {
            'period': original_prediction['period'],
            'predicted_numbers': {
                'original_system': original_prediction['predicted_numbers'],
                'optimized_system': optimized_result['predicted_numbers'],
                'are_same': original_prediction['predicted_numbers'] == optimized_result['predicted_numbers']
            }
        },
        'confidence_comparison': {
            'original_confidence': original_prediction['original_confidence'],
            'optimized_confidence': confidence['final_confidence'],
            'improvement_ratio': confidence['final_confidence'] / original_prediction['original_confidence'],
            'improvement_percentage': (confidence['final_confidence'] / original_prediction['original_confidence'] - 1) * 100
        },
        'system_comparison': {
            'prediction_method': {
                'original': original_prediction['prediction_method'],
                'optimized': '保持原始方法不变'
            },
            'confidence_method': {
                'original': '简单线性调整',
                'optimized': 'Isotonic Regression + 多维度权重'
            }
        },
        'optimization_details': {
            'adjustment_factors': confidence['adjustment_factors'],
            'confidence_interval': confidence['confidence_interval'],
            'calibration_applied': confidence['calibration_applied']
        }
    }
    
    print(f"对比结果:")
    print(f"  预测数字一致性: {'✅ 完全一致' if comparison['prediction_comparison']['predicted_numbers']['are_same'] else '❌ 不一致'}")
    print(f"  原始置信度: {comparison['confidence_comparison']['original_confidence']:.3f}")
    print(f"  优化置信度: {comparison['confidence_comparison']['optimized_confidence']:.3f}")
    print(f"  置信度提升: {comparison['confidence_comparison']['improvement_percentage']:.1f}%")
    
    return comparison

def main():
    """主函数"""
    print("🔧 修正版置信度优化 - 只优化置信度，不改变预测")
    print("="*70)
    
    try:
        # 1. 获取原始预测
        original_prediction = get_original_prediction_for_195()
        
        # 2. 应用优化置信度调整
        optimized_result, adjuster = apply_optimized_confidence_only(original_prediction)
        
        # 3. 创建对比报告
        comparison = create_comparison_report(original_prediction, optimized_result)
        
        # 4. 显示结果
        print(f"\n🎯 第195期修正版优化结果:")
        print("="*50)
        print(f"预测数字: {optimized_result['predicted_numbers']} (与原始系统一致)")
        print(f"原始方法: {optimized_result['original_prediction_info']['method']}")
        
        confidence = optimized_result['confidence_analysis']
        print(f"\n置信度优化:")
        print(f"  原始置信度: {confidence['original_confidence']:.3f}")
        print(f"  调整置信度: {confidence['adjusted_confidence']:.3f}")
        print(f"  最终置信度: {confidence['final_confidence']:.3f}")
        print(f"  调整比例: {confidence['adjustment_ratio']:.3f}")
        print(f"  置信度区间: {confidence['confidence_interval']}")
        print(f"  校准状态: {'✅ 已应用' if confidence['calibration_applied'] else '❌ 未应用'}")
        
        print(f"\n调整因子详情:")
        factors = confidence['adjustment_factors']
        print(f"  准确率因子: {factors['accuracy_factor']:.3f}")
        print(f"  校准因子: {factors['calibration_factor']:.3f}")
        print(f"  稳定性因子: {factors['stability_factor']:.3f}")
        print(f"  趋势因子: {factors['trend_factor']:.3f}")
        print(f"  复合因子: {factors['composite_factor']:.3f}")
        
        print(f"\n置信度解释:")
        print(f"  {optimized_result['confidence_interpretation']}")
        
        # 5. 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存优化结果
        result_filename = f'corrected_period_195_prediction_{timestamp}.json'
        with open(result_filename, 'w', encoding='utf-8') as f:
            json.dump(optimized_result, f, indent=2, ensure_ascii=False, default=str)
        
        # 保存对比报告
        comparison_filename = f'correction_comparison_report_{timestamp}.json'
        with open(comparison_filename, 'w', encoding='utf-8') as f:
            json.dump(comparison, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📁 生成文件:")
        print(f"  优化结果: {result_filename}")
        print(f"  对比报告: {comparison_filename}")
        
        print(f"\n✅ 修正版优化完成！")
        print(f"📌 重要说明:")
        print(f"  - 预测数字: {optimized_result['predicted_numbers']} (保持原始)")
        print(f"  - 置信度: {confidence['original_confidence']:.3f} → {confidence['final_confidence']:.3f}")
        print(f"  - 只优化了置信度调整，预测算法保持不变")
        
        return optimized_result
        
    except Exception as e:
        print(f"\n❌ 修正过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
