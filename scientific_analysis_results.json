{
  "timestamp": "2025-07-22T21:02:17.949785",
  "scientific_analysis": {
    "statistical_performance": {
      "total_predictions": 200,
      "hits": 58,
      "actual_hit_rate": 0.29,
      "theoretical_single_hit_rate": 0.12244897959183673,
      "theoretical_double_hit_rate": 0.012755102040816327,
      "performance_vs_random": 2.368333333333333,
      "statistical_significance": "significant"
    },
    "markov_assumption_validity": {
      "validity": "invalid",
      "consistency_score": 0.0,
      "total_transitions": 187,
      "score": 0.0
    },
    "confidence_calibration_quality": {
      "quality": "poor",
      "average_calibration_error": 0.2168290873898361,
      "score": 0
    },
    "prediction_consistency": {
      "distribution_uniformity": "good",
      "gini_coefficient": -0.5446798181973369,
      "score": 154.4679818197337
    },
    "temporal_independence": {
      "independence": "good",
      "autocorrelation_lag1": -0.04634384935191976,
      "autocorrelation_lag2": -0.04852216748768465,
      "autocorrelation_lag3": -0.04376070135912171,
      "score": 53.791093933757956
    },
    "overall_scientific_score": 45.44886136302374
  },
  "best_methods": {
    "frequency_based": {
      "method_name": "frequency_based",
      "description": "基于历史频率的预测方法",
      "probabilities": {
        "13": 0.018151815181518153,
        "28": 0.02145214521452145,
        "45": 0.02557755775577558,
        "35": 0.0231023102310231,
        "3": 0.028052805280528052,
        "32": 0.02145214521452145,
        "23": 0.024752475247524754,
        "37": 0.020627062706270627,
        "7": 0.018976897689768978,
        "24": 0.02145214521452145,
        "42": 0.02392739273927393,
        "48": 0.018151815181518153,
        "40": 0.02392739273927393,
        "12": 0.018151815181518153,
        "44": 0.014026402640264026,
        "39": 0.026402640264026403,
        "27": 0.018976897689768978,
        "15": 0.02145214521452145,
        "17": 0.02145214521452145,
        "36": 0.014026402640264026,
        "2": 0.017326732673267328,
        "34": 0.017326732673267328,
        "14": 0.015676567656765675,
        "30": 0.03217821782178218,
        "22": 0.027227722772277228,
        "41": 0.017326732673267328,
        "1": 0.018151815181518153,
        "38": 0.019801980198019802,
        "11": 0.019801980198019802,
        "4": 0.02145214521452145,
        "49": 0.015676567656765675,
        "29": 0.017326732673267328,
        "33": 0.02145214521452145,
        "47": 0.018976897689768978,
        "10": 0.02145214521452145,
        "16": 0.018976897689768978,
        "43": 0.019801980198019802,
        "5": 0.018151815181518153,
        "19": 0.018976897689768978,
        "25": 0.02145214521452145,
        "20": 0.019801980198019802,
        "9": 0.018151815181518153,
        "8": 0.0231023102310231,
        "6": 0.018976897689768978,
        "21": 0.0165016501650165,
        "18": 0.0231023102310231,
        "31": 0.024752475247524754,
        "46": 0.020627062706270627,
        "26": 0.012376237623762377
      },
      "top_predictions": [
        [
          30,
          0.03217821782178218
        ],
        [
          3,
          0.028052805280528052
        ],
        [
          22,
          0.027227722772277228
        ],
        [
          39,
          0.026402640264026403
        ],
        [
          45,
          0.02557755775577558
        ],
        [
          23,
          0.024752475247524754
        ],
        [
          31,
          0.024752475247524754
        ],
        [
          42,
          0.02392739273927393
        ],
        [
          40,
          0.02392739273927393
        ],
        [
          35,
          0.0231023102310231
        ]
      ],
      "confidence_calculation": "based_on_frequency_ratio",
      "expected_accuracy": 0.06435643564356436
    },
    "improved_markov": {
      "method_name": "improved_markov",
      "description": "改进的马尔可夫链预测方法",
      "transition_probabilities": {
        