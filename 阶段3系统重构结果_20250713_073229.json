{"restructure_results": {"全面增强马尔可夫 (34.3%基线)": {"hits": 48, "total": 150, "hit_rate": 0.32}, "保守集成系统": {"hits": 48, "total": 150, "hit_rate": 0.32}, "纯马尔可夫基线": {"hits": 43, "total": 150, "hit_rate": 0.2866666666666667}}, "stability_data": {"全面增强马尔可夫 (34.3%基线)": {"consistency": 0.9043153327039511, "segment_rates": [0.26666666666666666, 0.23333333333333334, 0.3333333333333333, 0.26666666666666666, 0.5], "predictions": [[30, 43], [30, 5], [30, 5], [30, 40], [2, 3], [30, 31], [30, 40], [5, 30], [31, 40], [3, 30], [30, 16], [30, 3], [40, 43], [30, 49], [29, 30], [40, 3], [30, 3], [30, 35], [3, 40], [30, 3], [30, 3], [40, 30], [30, 40], [3, 30], [30, 43], [40, 30], [30, 5], [30, 29], [30, 29], [30, 3], [30, 25], [31, 40], [3, 40], [30, 40], [3, 2], [3, 40], [30, 29], [30, 3], [30, 2], [30, 40], [30, 10], [40, 30], [3, 49], [30, 29], [10, 3], [30, 42], [40, 30], [30, 49], [3, 30], [30, 3], [40, 3], [30, 2], [40, 31], [30, 25], [40, 3], [30, 49], [40, 2], [40, 32], [40, 5], [31, 43], [30, 3], [3, 16], [3, 30], [49, 10], [30, 49], [40, 3], [30, 3], [30, 5], [30, 2], [30, 2], [40, 15], [30, 32], [40, 49], [40, 2], [30, 16], [30, 5], [3, 40], [3, 31], [30, 15], [3, 2], [3, 40], [30, 5], [30, 3], [3, 30], [3, 29], [3, 5], [30, 5], [30, 40], [3, 40], [30, 3], [3, 30], [5, 30], [30, 25], [3, 30], [30, 43], [40, 43], [5, 3], [30, 3], [40, 30], [30, 3], [30, 3], [30, 5], [30, 5], [30, 40], [30, 29], [30, 3], [3, 30], [3, 30], [30, 2], [30, 3], [30, 5], [3, 30], [3, 29], [30, 3], [40, 30], [2, 29], [40, 32], [3, 30], [30, 3], [2, 3], [30, 3], [3, 40], [30, 40], [30, 3], [3, 30], [5, 22], [30, 3], [3, 30], [30, 40], [30, 10], [30, 15], [30, 5], [30, 17], [30, 3], [30, 2], [3, 30], [30, 3], [3, 30], [30, 40], [3, 30], [30, 40], [30, 32], [3, 29], [40, 3], [30, 49], [30, 3], [3, 40], [3, 30], [2, 3], [30, 29]]}, "保守集成系统": {"consistency": 0.9043153327039511, "segment_rates": [0.26666666666666666, 0.23333333333333334, 0.3333333333333333, 0.26666666666666666, 0.5], "predictions": [[30, 43], [30, 5], [30, 5], [30, 40], [2, 3], [30, 31], [30, 40], [5, 30], [31, 40], [3, 30], [30, 16], [30, 3], [40, 43], [30, 49], [29, 30], [40, 3], [30, 3], [30, 35], [3, 40], [30, 3], [30, 3], [40, 30], [30, 40], [3, 30], [30, 43], [40, 30], [30, 5], [30, 29], [30, 29], [30, 3], [30, 25], [31, 40], [3, 40], [30, 40], [3, 2], [3, 40], [30, 29], [30, 3], [30, 2], [30, 40], [30, 10], [40, 30], [3, 49], [30, 29], [10, 3], [30, 42], [40, 30], [30, 49], [3, 30], [30, 3], [40, 3], [30, 2], [40, 31], [30, 25], [40, 3], [30, 49], [40, 2], [40, 32], [40, 5], [31, 43], [30, 3], [3, 16], [3, 30], [49, 10], [30, 49], [40, 3], [30, 3], [30, 5], [30, 2], [30, 2], [40, 15], [30, 32], [40, 49], [40, 2], [30, 16], [30, 5], [3, 40], [3, 31], [30, 15], [3, 2], [3, 40], [30, 5], [30, 3], [3, 30], [3, 29], [3, 5], [30, 5], [30, 40], [3, 40], [30, 3], [3, 30], [5, 30], [30, 25], [3, 30], [30, 43], [40, 43], [5, 3], [30, 3], [40, 30], [30, 3], [30, 3], [30, 5], [30, 5], [30, 40], [30, 29], [30, 3], [3, 30], [3, 30], [30, 2], [30, 3], [30, 5], [3, 30], [3, 29], [30, 3], [40, 30], [2, 29], [40, 32], [3, 30], [30, 3], [2, 3], [30, 3], [3, 40], [30, 40], [30, 3], [3, 30], [5, 22], [30, 3], [3, 30], [30, 40], [30, 10], [30, 15], [30, 5], [30, 17], [30, 3], [30, 2], [3, 30], [30, 3], [3, 30], [30, 40], [3, 30], [30, 40], [30, 32], [3, 29], [40, 3], [30, 49], [30, 3], [3, 40], [3, 30], [2, 3], [30, 29]]}, "纯马尔可夫基线": {"consistency": 0.8759928317484115, "segment_rates": [0.2, 0.23333333333333334, 0.43333333333333335, 0.13333333333333333, 0.43333333333333335], "predictions": [[43, 29], [36, 5], [5, 30], [30, 43], [2, 26], [31, 29], [41, 29], [5, 15], [31, 36], [44, 31], [15, 30], [29, 5], [43, 29], [49, 30], [29, 19], [29, 40], [15, 26], [30, 19], [44, 15], [15, 44], [29, 5], [5, 40], [36, 11], [5, 29], [26, 43], [2, 15], [5, 29], [29, 15], [29, 2], [41, 15], [25, 15], [31, 29], [2, 40], [44, 36], [2, 29], [2, 44], [29, 26], [44, 2], [2, 36], [2, 26], [16, 15], [43, 15], [49, 10], [29, 5], [10, 17], [30, 42], [5, 40], [49, 2], [2, 15], [26, 41], [2, 31], [2, 31], [31, 29], [25, 26], [15, 43], [30, 49], [2, 31], [44, 25], [36, 43], [31, 43], [30, 29], [3, 16], [26, 44], [49, 10], [49, 2], [40, 15], [41, 2], [5, 15], [2, 15], [2, 16], [15, 45], [29, 49], [29, 49], [2, 41], [16, 15], [5, 30], [2, 40], [31, 3], [15, 26], [26, 19], [27, 3], [19, 30], [15, 49], [41, 19], [29, 3], [29, 5], [30, 5], [15, 29], [31, 25], [31, 44], [5, 29], [5, 15], [26, 25], [29, 3], [43, 29], [25, 43], [5, 29], [26, 30], [17, 2], [2, 31], [15, 26], [5, 16], [30, 5], [15, 27], [29, 16], [30, 12], [5, 29], [3, 26], [2, 31], [15, 26], [5, 31], [5, 29], [29, 44], [2, 49], [44, 15], [2, 29], [15, 29], [2, 3], [26, 36], [2, 25], [2, 25], [44, 31], [44, 30], [30, 2], [31, 25], [5, 31], [15, 19], [29, 10], [26, 12], [30, 26], [15, 16], [5, 29], [41, 17], [25, 36], [29, 2], [25, 49], [49, 41], [2, 41], [41, 15], [3, 41], [24, 2], [15, 31], [29, 26], [24, 2], [26, 15], [2, 31], [15, 49], [15, 19], [2, 29], [29, 19]]}}, "conservative_params": {"high_freq_boost": 1.15, "low_freq_penalty": 0.85, "rising_trend_boost": 1.1, "falling_trend_penalty": 0.9, "perturbation": 0.05, "ensemble_weight_markov": 0.6, "ensemble_weight_ml": 0.3, "ensemble_weight_external": 0.1}, "ensemble_weights": {"markov": 0.6, "ml": 0.3, "external": 0.1}}