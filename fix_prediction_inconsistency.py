#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复预测不一致性和评分计算失败问题
Fix prediction inconsistency and scoring calculation failure

问题分析：
1. 预测结果不一致：移除固定随机种子导致每次预测结果不同
2. 评分计算失败：'list' object has no attribute 'columns' 错误
"""

import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime

def analyze_prediction_inconsistency():
    """分析预测不一致性问题"""
    print("🔍 分析预测不一致性问题")
    print("="*50)
    
    print("问题1: 预测结果不一致")
    print("原因分析:")
    print("  - 优化时移除了固定随机种子 np.random.seed(42)")
    print("  - 每次运行都会产生不同的随机扰动")
    print("  - 导致相同输入产生不同预测结果")
    
    print("\n问题2: 评分计算失败")
    print("错误信息: 'list' object has no attribute 'columns'")
    print("可能原因:")
    print("  - 评分系统期望DataFrame但收到了list")
    print("  - 数据格式不匹配")
    print("  - 评分模型版本不兼容")

def test_current_system_behavior():
    """测试当前系统行为"""
    print(f"\n🧪 测试当前系统行为")
    print("="*40)
    
    try:
        # 导入系统
        sys.path.append('.')
        from 集成评分系统的预测系统 import IntegratedPredictionSystem
        
        # 创建系统实例
        system = IntegratedPredictionSystem()
        
        # 初始化系统
        if not system.initialize_system():
            print("❌ 系统初始化失败")
            return False
        
        print("✅ 系统初始化成功")
        
        # 测试预测一致性
        test_input = [10, 12, 15, 24, 25, 43]  # 2025年197期数据
        
        print(f"\n测试输入: {test_input}")
        print("进行5次预测测试:")
        
        predictions = []
        for i in range(5):
            pred, confidence = system.predict_next_period(test_input)
            predictions.append((pred, confidence))
            print(f"  第{i+1}次: {pred} (置信度: {confidence:.6f})")
        
        # 分析一致性
        unique_predictions = len(set(tuple(p[0]) for p in predictions))
        print(f"\n一致性分析:")
        print(f"  独特预测数: {unique_predictions}/5")
        print(f"  是否一致: {'否' if unique_predictions > 1 else '是'}")
        
        # 测试评分计算
        print(f"\n测试评分计算:")
        try:
            score_result = system.calculate_prediction_score({
                'pred_num1': predictions[0][0][0],
                'pred_num2': predictions[0][0][1],
                'period': 197,
                'original_confidence': predictions[0][1]
            })
            print(f"✅ 评分计算成功: {score_result['score']:.1f}分")
            return True
        except Exception as e:
            print(f"❌ 评分计算失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_random_seed_issue():
    """修复随机种子问题"""
    print(f"\n🔧 修复随机种子问题")
    print("="*40)
    
    print("解决方案选择:")
    print("1. 恢复固定种子 - 保证预测一致性")
    print("2. 保持动态种子 - 保持预测多样性")
    print("3. 混合方案 - 可选择的一致性模式")
    
    print("\n推荐方案: 混合方案")
    print("理由:")
    print("  - 默认使用固定种子保证一致性")
    print("  - 提供参数选择动态种子")
    print("  - 兼顾稳定性和多样性需求")
    
    # 读取当前文件
    with open('集成评分系统的预测系统.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复随机种子设置
    old_line = "        # np.random.seed(42)  # 注释掉固定种子以增加多样性"
    new_line = """        # 可选择的随机种子设置（修复预测一致性问题）
        if hasattr(self, 'use_fixed_seed') and self.use_fixed_seed:
            np.random.seed(42)  # 固定种子保证一致性
        # 否则使用动态种子保持多样性"""
    
    if old_line in content:
        content = content.replace(old_line, new_line)
        
        # 备份原文件
        backup_filename = f'集成评分系统的预测系统_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.py'
        with open(backup_filename, 'w', encoding='utf-8') as f:
            f.write(content.replace(new_line, old_line))
        
        # 保存修复后的文件
        with open('集成评分系统的预测系统.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 随机种子问题已修复")
        print(f"📁 原文件已备份: {backup_filename}")
        print(f"🔧 现在默认使用固定种子保证一致性")
        return True
    else:
        print(f"⚠️ 未找到目标代码行，可能已经修复")
        return False

def fix_scoring_calculation_issue():
    """修复评分计算问题"""
    print(f"\n🔧 修复评分计算问题")
    print("="*40)
    
    try:
        # 检查评分模型文件
        if os.path.exists("scoring_model.pkl"):
            import pickle
            with open("scoring_model.pkl", 'rb') as f:
                scoring_data = pickle.load(f)
            
            print("✅ 评分模型文件存在")
            print(f"   模型版本: {scoring_data.get('model_version', '未知')}")
            print(f"   训练时间: {scoring_data.get('training_date', '未知')}")
            
            # 检查模型兼容性
            if 'model' in scoring_data and hasattr(scoring_data['model'], 'predict_proba'):
                print("✅ 模型格式正确")
                
                # 测试模型预测
                try:
                    # 创建测试数据
                    test_features = np.array([[2, 15, 197, 0.028, 17, 13, 8.5, 1, 1, 1, 1, 0, 0, 0, 0, 0.8, 0.9, 0.85, 0.75, 0.82]])
                    
                    # 测试预测
                    probabilities = scoring_data['model'].predict_proba(test_features)
                    print(f"✅ 模型预测测试成功: {probabilities[0][1]:.3f}")
                    
                    return True
                    
                except Exception as e:
                    print(f"❌ 模型预测测试失败: {e}")
                    print("可能需要重新训练评分模型")
                    return False
            else:
                print("❌ 模型格式不正确")
                return False
        else:
            print("❌ 评分模型文件不存在")
            print("需要重新训练评分模型")
            return False
            
    except Exception as e:
        print(f"❌ 评分系统检查失败: {e}")
        return False

def add_consistency_control():
    """添加一致性控制功能"""
    print(f"\n🔧 添加一致性控制功能")
    print("="*40)
    
    # 读取当前文件
    with open('集成评分系统的预测系统.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 在__init__方法中添加一致性控制参数
    init_addition = """        
        # 一致性控制参数（修复预测不一致问题）
        self.use_fixed_seed = True  # 默认使用固定种子保证一致性"""
    
    # 查找__init__方法的结束位置
    init_end_pattern = "        # 预测历史记录（用于多样性控制）\n        self.prediction_history = []"
    
    if init_end_pattern in content and init_addition not in content:
        content = content.replace(init_end_pattern, init_end_pattern + init_addition)
        
        # 保存修改后的文件
        with open('集成评分系统的预测系统.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 一致性控制功能已添加")
        print("   - 默认使用固定种子保证预测一致性")
        print("   - 可通过 system.use_fixed_seed = False 启用多样性模式")
        return True
    else:
        print("⚠️ 一致性控制功能可能已存在或无法添加")
        return False

def create_consistency_test():
    """创建一致性测试"""
    print(f"\n🧪 创建一致性测试")
    print("="*40)
    
    test_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测一致性测试
Test prediction consistency after fix
"""

import sys
sys.path.append('.')

from 集成评分系统的预测系统 import IntegratedPredictionSystem

def test_consistency():
    """测试预测一致性"""
    print("🧪 测试预测一致性")
    print("="*30)
    
    # 创建系统实例
    system = IntegratedPredictionSystem()
    
    # 初始化系统
    if not system.initialize_system():
        print("❌ 系统初始化失败")
        return
    
    # 测试输入
    test_input = [10, 12, 15, 24, 25, 43]
    
    print(f"测试输入: {test_input}")
    print("\\n一致性模式测试 (use_fixed_seed=True):")
    
    # 一致性模式测试
    system.use_fixed_seed = True
    predictions_consistent = []
    
    for i in range(3):
        pred, confidence = system.predict_next_period(test_input)
        predictions_consistent.append((pred, confidence))
        print(f"  第{i+1}次: {pred} (置信度: {confidence:.6f})")
    
    # 检查一致性
    unique_consistent = len(set(tuple(p[0]) for p in predictions_consistent))
    print(f"一致性结果: {'✅ 一致' if unique_consistent == 1 else '❌ 不一致'}")
    
    print("\\n多样性模式测试 (use_fixed_seed=False):")
    
    # 多样性模式测试
    system.use_fixed_seed = False
    predictions_diverse = []
    
    for i in range(3):
        pred, confidence = system.predict_next_period(test_input)
        predictions_diverse.append((pred, confidence))
        print(f"  第{i+1}次: {pred} (置信度: {confidence:.6f})")
    
    # 检查多样性
    unique_diverse = len(set(tuple(p[0]) for p in predictions_diverse))
    print(f"多样性结果: {'✅ 多样' if unique_diverse > 1 else '⚠️ 仍然一致'}")

if __name__ == "__main__":
    test_consistency()
'''
    
    with open('test_consistency.py', 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    print("✅ 一致性测试脚本已创建: test_consistency.py")

def generate_fix_summary():
    """生成修复总结"""
    print(f"\n📋 修复总结")
    print("="*30)
    
    print("已修复的问题:")
    print("1. ✅ 预测不一致性问题")
    print("   - 添加了可选择的随机种子控制")
    print("   - 默认使用固定种子保证一致性")
    print("   - 可选择动态种子保持多样性")
    
    print("\\n2. 🔍 评分计算问题诊断")
    print("   - 检查了评分模型文件状态")
    print("   - 验证了模型兼容性")
    print("   - 提供了问题定位信息")
    
    print("\\n使用建议:")
    print("1. 默认情况下预测结果现在是一致的")
    print("2. 如需多样性，可设置 system.use_fixed_seed = False")
    print("3. 如果评分仍然失败，需要重新训练评分模型")
    print("4. 运行 test_consistency.py 验证修复效果")

def main():
    """主函数"""
    print("🔧 修复预测不一致性和评分计算问题")
    print("="*60)
    
    try:
        # 1. 分析问题
        analyze_prediction_inconsistency()
        
        # 2. 测试当前系统
        system_ok = test_current_system_behavior()
        
        # 3. 修复随机种子问题
        seed_fixed = fix_random_seed_issue()
        
        # 4. 添加一致性控制
        control_added = add_consistency_control()
        
        # 5. 检查评分计算问题
        scoring_ok = fix_scoring_calculation_issue()
        
        # 6. 创建测试脚本
        create_consistency_test()
        
        # 7. 生成总结
        generate_fix_summary()
        
        print(f"\\n🎯 修复完成状态:")
        print(f"  随机种子修复: {'✅' if seed_fixed else '⚠️'}")
        print(f"  一致性控制: {'✅' if control_added else '⚠️'}")
        print(f"  评分系统: {'✅' if scoring_ok else '❌'}")
        
        if seed_fixed and control_added:
            print(f"\\n✅ 预测一致性问题已修复！")
            print(f"现在相同输入会产生相同预测结果")
        
        if not scoring_ok:
            print(f"\\n⚠️ 评分计算问题需要进一步处理")
            print(f"建议运行: python retrain_scoring_system.py")
        
    except Exception as e:
        print(f"❌ 修复过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
