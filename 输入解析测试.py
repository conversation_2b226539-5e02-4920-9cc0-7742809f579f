#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输入解析测试
测试修复后的输入解析功能
"""

def test_input_parsing():
    """测试输入解析功能"""
    
    test_cases = [
        "10，21，40，37，02，39",  # 中文逗号 + 前导零
        "10,21,40,37,02,39",      # 英文逗号 + 前导零
        "10 21 40 37 02 39",      # 空格分隔 + 前导零
        "10, 21, 40, 37, 02, 39", # 英文逗号 + 空格 + 前导零
        "1 2 3 4 5 6",            # 正常数字
        "01 02 03 04 05 06",      # 前导零
        "10、21、40、37、02、39",  # 顿号分隔
    ]
    
    print("🧪 输入解析测试")
    print("=" * 50)
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n测试 {i}: '{test_input}'")
        
        try:
            # 模拟修复后的解析逻辑
            numbers_input = test_input.strip()
            
            # 处理中文逗号和英文逗号
            numbers_input = numbers_input.replace('，', ',').replace('、', ',')
            
            # 解析数字
            if ',' in numbers_input:
                numbers_str = numbers_input.split(',')
            else:
                numbers_str = numbers_input.split()
            
            # 转换为整数，处理前导零
            numbers = []
            for num_str in numbers_str:
                num_str = num_str.strip()
                if num_str:  # 确保不是空字符串
                    numbers.append(int(num_str))  # int()会自动处理前导零
            
            print(f"  ✅ 解析成功: {numbers}")
            
            # 验证结果
            if len(numbers) == 6:
                print(f"  ✅ 数字数量正确: {len(numbers)}个")
            else:
                print(f"  ⚠️ 数字数量错误: {len(numbers)}个 (应该是6个)")
            
            # 检查范围
            valid_range = all(1 <= num <= 49 for num in numbers)
            if valid_range:
                print(f"  ✅ 数字范围正确: 1-49")
            else:
                print(f"  ⚠️ 数字范围错误: {numbers}")
            
            # 检查重复
            if len(set(numbers)) == len(numbers):
                print(f"  ✅ 无重复数字")
            else:
                print(f"  ⚠️ 存在重复数字")
                
        except Exception as e:
            print(f"  ❌ 解析失败: {e}")
    
    print(f"\n🎉 测试完成")

if __name__ == "__main__":
    test_input_parsing()
