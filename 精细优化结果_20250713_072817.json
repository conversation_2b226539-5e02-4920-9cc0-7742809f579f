{"optimization_results": {"基础马尔可夫": {"hits": 46, "total": 178, "hit_rate": 0.25842696629213485}, "全面增强马尔可夫": {"hits": 61, "total": 178, "hit_rate": 0.34269662921348315}, "精细优化马尔可夫": {"hits": 40, "total": 178, "hit_rate": 0.2247191011235955}, "奇偶平衡基线": {"hits": 33, "total": 178, "hit_rate": 0.1853932584269663}}, "optimized_params": {"high_freq_boost": 1.18, "low_freq_penalty": 0.82, "rising_trend_boost": 1.12, "falling_trend_penalty": 0.88, "temporal_sensitivity": 0.15, "pattern_weight": 0.25, "perturbation": 0.04}, "dynamic_weights": {"frequency_factor": 0.8, "temporal_factor": 0.8, "trend_factor": 0.8, "pattern_factor": 0.8}, "performance_history": [0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], "detailed_results": {"基础马尔可夫": [{"period": 2, "predicted": [43, 41], "actual": [13, 4, 10, 6, 5, 28], "hit_count": 0, "is_hit": false}, {"period": 3, "predicted": [5, 36], "actual": [28, 1, 16, 47, 18, 42], "hit_count": 0, "is_hit": false}, {"period": 4, "predicted": [5, 30], "actual": [45, 16, 17, 7, 37, 28], "hit_count": 0, "is_hit": false}, {"period": 5, "predicted": [30, 41], "actual": [35, 21, 10, 3, 32, 40], "hit_count": 0, "is_hit": false}, {"period": 6, "predicted": [2, 26], "actual": [3, 13, 12, 28, 1, 18], "hit_count": 0, "is_hit": false}, {"period": 7, "predicted": [31, 48], "actual": [32, 12, 48, 45, 39, 7], "hit_count": 1, "is_hit": true}, {"period": 8, "predicted": [41, 29], "actual": [13, 24, 48, 23, 32, 20], "hit_count": 0, "is_hit": false}, {"period": 9, "predicted": [5, 15], "actual": [23, 17, 43, 1, 10, 13], "hit_count": 0, "is_hit": false}, {"period": 10, "predicted": [31, 7], "actual": [37, 18, 17, 47, 21, 23], "hit_count": 0, "is_hit": false}, {"period": 11, "predicted": [44, 3], "actual": [7, 45, 3, 30, 1, 10], "hit_count": 1, "is_hit": true}, {"period": 12, "predicted": [41, 30], "actual": [24, 32, 10, 15, 37, 4], "hit_count": 0, "is_hit": false}, {"period": 13, "predicted": [5, 29], "actual": [42, 43, 36, 11, 25, 47], "hit_count": 0, "is_hit": false}, {"period": 14, "predicted": [43, 2], "actual": [48, 20, 30, 39, 41, 6], "hit_count": 0, "is_hit": false}, {"period": 15, "predicted": [49, 30], "actual": [13, 37, 2, 3, 43, 12], "hit_count": 0, "is_hit": false}, {"period": 16, "predicted": [29, 43], "actual": [40, 22, 27, 42, 43, 39], "hit_count": 1, "is_hit": true}, {"period": 17, "predicted": [40, 29], "actual": [42, 29, 20, 49, 22, 6], "hit_count": 1, "is_hit": true}, {"period": 18, "predicted": [26, 2], "actual": [12, 1, 24, 33, 21, 44], "hit_count": 0, "is_hit": false}, {"period": 19, "predicted": [30, 41], "actual": [3, 23, 49, 13, 39, 26], "hit_count": 0, "is_hit": false}, {"period": 20, "predicted": [7, 44], "actual": [44, 4, 47, 1, 49, 14], "hit_count": 1, "is_hit": true}, {"period": 21, "predicted": [44, 30], "actual": [39, 19, 24, 31, 32, 30], "hit_count": 1, "is_hit": true}, {"period": 22, "predicted": [29, 5], "actual": [27, 23, 26, 14, 16, 18], "hit_count": 0, "is_hit": false}, {"period": 23, "predicted": [40, 41], "actual": [15, 43, 6, 30, 16, 9], "hit_count": 0, "is_hit": false}, {"period": 24, "predicted": [2, 11], "actual": [3, 45, 18, 40, 28, 2], "hit_count": 1, "is_hit": true}, {"period": 25, "predicted": [5, 10], "actual": [17, 42, 28, 44, 5, 20], "hit_count": 1, "is_hit": true}, {"period": 26, "predicted": [26, 43], "actual": [36, 25, 7, 2, 34, 9], "hit_count": 0, "is_hit": false}, {"period": 27, "predicted": [2, 40], "actual": [2, 6, 28, 32, 45, 16], "hit_count": 1, "is_hit": true}, {"period": 28, "predicted": [5, 29], "actual": [34, 14, 29, 33, 5, 28], "hit_count": 2, "is_hit": true}, {"period": 29, "predicted": [29, 32], "actual": [14, 46, 3, 35, 43, 10], "hit_count": 0, "is_hit": false}, {"period": 30, "predicted": [29, 2], "actual": [27, 39, 23, 45, 44, 46], "hit_count": 0, "is_hit": false}, {"period": 31, "predicted": [41, 3], "actual": [30, 23, 8, 24, 33, 25], "hit_count": 0, "is_hit": false}, {"period": 32, "predicted": [25, 49], "actual": [22, 15, 13, 2, 17, 33], "hit_count": 0, "is_hit": false}, {"period": 33, "predicted": [31, 29], "actual": [41, 22, 19, 40, 46, 39], "hit_count": 0, "is_hit": false}, {"period": 34, "predicted": [2, 3], "actual": [1, 4, 21, 40, 16, 49], "hit_count": 0, "is_hit": false}, {"period": 35, "predicted": [44, 26], "actual": [27, 35, 32, 37, 6, 18], "hit_count": 0, "is_hit": false}, {"period": 36, "predicted": [2, 3], "actual": [38, 7, 19, 47, 10, 40], "hit_count": 0, "is_hit": false}, {"period": 37, "predicted": [2, 44], "actual": [35, 32, 46, 8, 37, 16], "hit_count": 0, "is_hit": false}, {"period": 38, "predicted": [29, 26], "actual": [11, 1, 47, 39, 12, 3], "hit_count": 0, "is_hit": false}, {"period": 39, "predicted": [41, 2], "actual": [41, 48, 10, 17, 34, 43], "hit_count": 1, "is_hit": true}, {"period": 40, "predicted": [2, 26], "actual": [1, 32, 35, 30, 8, 16], "hit_count": 0, "is_hit": false}, {"period": 41, "predicted": [41, 2], "actual": [48, 35, 22, 30, 26, 4], "hit_count": 0, "is_hit": false}, {"period": 42, "predicted": [10, 16], "actual": [44, 13, 39, 22, 46, 47], "hit_count": 0, "is_hit": false}, {"period": 43, "predicted": [43, 2], "actual": [4, 27, 38, 40, 19, 35], "hit_count": 0, "is_hit": false}, {"period": 44, "predicted": [49, 10], "actual": [24, 28, 46, 34, 14, 43], "hit_count": 0, "is_hit": false}, {"period": 45, "predicted": [29, 5], "actual": [40, 28, 3, 48, 31, 8], "hit_count": 0, "is_hit": false}, {"period": 46, "predicted": [10, 17], "actual": [41, 1, 24, 34, 33, 30], "hit_count": 0, "is_hit": false}, {"period": 47, "predicted": [30, 42], "actual": [14, 22, 13, 31, 20, 16], "hit_count": 0, "is_hit": false}, {"period": 48, "predicted": [5, 40], "actual": [7, 35, 23, 33, 46, 27], "hit_count": 0, "is_hit": false}, {"period": 49, "predicted": [49, 2], "actual": [49, 5, 1, 17, 31, 6], "hit_count": 1, "is_hit": true}, {"period": 50, "predicted": [2, 10], "actual": [29, 21, 46, 48, 15, 45], "hit_count": 0, "is_hit": false}, {"period": 51, "predicted": [41, 26], "actual": [27, 22, 31, 7, 13, 18], "hit_count": 0, "is_hit": false}, {"period": 52, "predicted": [2, 31], "actual": [7, 6, 17, 13, 34, 31], "hit_count": 1, "is_hit": true}, {"period": 53, "predicted": [2, 10], "actual": [33, 48, 5, 19, 13, 17], "hit_count": 0, "is_hit": false}, {"period": 54, "predicted": [31, 29], "actual": [23, 28, 30, 35, 19, 27], "hit_count": 0, "is_hit": false}, {"period": 55, "predicted": [26, 25], "actual": [47, 36, 40, 43, 11, 37], "hit_count": 0, "is_hit": false}, {"period": 56, "predicted": [43, 11], "actual": [41, 8, 33, 30, 43, 7], "hit_count": 1, "is_hit": true}, {"period": 57, "predicted": [49, 30], "actual": [10, 11, 13, 1, 40, 22], "hit_count": 0, "is_hit": false}, {"period": 58, "predicted": [2, 31], "actual": [22, 23, 33, 34, 49, 19], "hit_count": 0, "is_hit": false}, {"period": 59, "predicted": [32, 44], "actual": [40, 16, 17, 26, 36, 4], "hit_count": 0, "is_hit": false}, {"period": 60, "predicted": [43, 5], "actual": [10, 8, 17, 13, 47, 44], "hit_count": 0, "is_hit": false}, {"period": 61, "predicted": [31, 43], "actual": [42, 28, 39, 6, 23, 35], "hit_count": 0, "is_hit": false}, {"period": 62, "predicted": [30, 49], "actual": [27, 37, 10, 26, 2, 48], "hit_count": 0, "is_hit": false}, {"period": 63, "predicted": [3, 26], "actual": [42, 5, 38, 3, 49, 21], "hit_count": 1, "is_hit": true}, {"period": 64, "predicted": [26, 44], "actual": [48, 40, 3, 33, 47, 4], "hit_count": 0, "is_hit": false}, {"period": 65, "predicted": [49, 10], "actual": [30, 41, 38, 25, 35, 16], "hit_count": 0, "is_hit": false}, {"period": 66, "predicted": [49, 2], "actual": [49, 45, 27, 22, 13, 26], "hit_count": 1, "is_hit": true}, {"period": 67, "predicted": [40, 41], "actual": [15, 35, 39, 48, 45, 11], "hit_count": 0, "is_hit": false}, {"period": 68, "predicted": [41, 3], "actual": [42, 33, 47, 40, 30, 2], "hit_count": 0, "is_hit": false}, {"period": 69, "predicted": [5, 29], "actual": [44, 30, 5, 40, 6, 15], "hit_count": 1, "is_hit": true}, {"period": 70, "predicted": [2, 26], "actual": [16, 35, 46, 25, 19, 18], "hit_count": 0, "is_hit": false}, {"period": 71, "predicted": [2, 49], "actual": [32, 9, 44, 36, 23, 40], "hit_count": 0, "is_hit": false}, {"period": 72, "predicted": [15, 1], "actual": [35, 37, 48, 33, 14, 22], "hit_count": 0, "is_hit": false}, {"period": 73, "predicted": [32, 49], "actual": [43, 22, 48, 23, 33, 10], "hit_count": 0, "is_hit": false}, {"period": 74, "predicted": [49, 32], "actual": [22, 21, 1, 47, 29, 11], "hit_count": 0, "is_hit": false}, {"period": 75, "predicted": [41, 2], "actual": [30, 5, 10, 37, 32, 7], "hit_count": 0, "is_hit": false}, {"period": 76, "predicted": [16, 2], "actual": [28, 2, 14, 10, 16, 23], "hit_count": 2, "is_hit": true}, {"period": 77, "predicted": [5, 30], "actual": [5, 23, 27, 17, 11, 39], "hit_count": 1, "is_hit": true}, {"period": 78, "predicted": [7, 2], "actual": [15, 38, 18, 3, 8, 11], "hit_count": 0, "is_hit": false}, {"period": 79, "predicted": [3, 31], "actual": [4, 20, 9, 32, 29, 44], "hit_count": 0, "is_hit": false}, {"period": 80, "predicted": [26, 15], "actual": [27, 38, 29, 2, 44, 31], "hit_count": 0, "is_hit": false}, {"period": 81, "predicted": [26, 2], "actual": [48, 8, 40, 9, 18, 27], "hit_count": 0, "is_hit": false}, {"period": 82, "predicted": [3, 27], "actual": [10, 35, 4, 45, 24, 9], "hit_count": 0, "is_hit": false}, {"period": 83, "predicted": [41, 10], "actual": [45, 4, 6, 47, 46, 11], "hit_count": 0, "is_hit": false}, {"period": 84, "predicted": [49, 15], "actual": [17, 45, 24, 23, 38, 40], "hit_count": 0, "is_hit": false}, {"period": 85, "predicted": [41, 7], "actual": [32, 31, 18, 14, 8, 19], "hit_count": 0, "is_hit": false}, {"period": 86, "predicted": [29, 3], "actual": [19, 29, 31, 2, 42, 26], "hit_count": 1, "is_hit": true}, {"period": 87, "predicted": [29, 5], "actual": [30, 16, 14, 28, 29, 3], "hit_count": 1, "is_hit": true}, {"period": 88, "predicted": [30, 5], "actual": [14, 11, 39, 10, 45, 33], "hit_count": 0, "is_hit": false}, {"period": 89, "predicted": [41, 30], "actual": [45, 19, 13, 25, 15, 27], "hit_count": 0, "is_hit": false}, {"period": 90, "predicted": [31, 25], "actual": [17, 7, 23, 38, 12, 18], "hit_count": 0, "is_hit": false}, {"period": 91, "predicted": [44, 31], "actual": [35, 23, 32, 31, 8, 24], "hit_count": 1, "is_hit": true}, {"period": 92, "predicted": [5, 29], "actual": [23, 24, 36, 3, 4, 32], "hit_count": 0, "is_hit": false}, {"period": 93, "predicted": [5, 44], "actual": [16, 23, 28, 21, 27, 46], "hit_count": 0, "is_hit": false}, {"period": 94, "predicted": [26, 25], "actual": [35, 43, 31, 37, 15, 39], "hit_count": 0, "is_hit": false}, {"period": 95, "predicted": [29, 3], "actual": [4, 43, 37, 40, 24, 16], "hit_count": 0, "is_hit": false}, {"period": 96, "predicted": [43, 5], "actual": [42, 46, 25, 5, 40, 22], "hit_count": 1, "is_hit": true}, {"period": 97, "predicted": [43, 25], "actual": [1, 32, 24, 23, 4, 2], "hit_count": 0, "is_hit": false}, {"period": 98, "predicted": [5, 29], "actual": [30, 39, 31, 8, 21, 20], "hit_count": 0, "is_hit": false}, {"period": 99, "predicted": [26, 30], "actual": [1, 34, 43, 38, 6, 22], "hit_count": 0, "is_hit": false}, {"period": 100, "predicted": [2, 40], "actual": [25, 3, 34, 8, 31, 41], "hit_count": 0, "is_hit": false}, {"period": 101, "predicted": [2, 31], "actual": [20, 49, 36, 37, 42, 30], "hit_count": 0, "is_hit": false}, {"period": 102, "predicted": [26, 15], "actual": [24, 40, 7, 30, 47, 9], "hit_count": 0, "is_hit": false}, {"period": 103, "predicted": [5, 16], "actual": [16, 28, 3, 30, 42, 49], "hit_count": 1, "is_hit": true}, {"period": 104, "predicted": [30, 5], "actual": [9, 20, 33, 11, 32, 21], "hit_count": 0, "is_hit": false}, {"period": 105, "predicted": [26, 49], "actual": [12, 28, 48, 37, 18, 43], "hit_count": 0, "is_hit": false}, {"period": 106, "predicted": [29, 43], "actual": [45, 20, 11, 23, 34, 42], "hit_count": 0, "is_hit": false}, {"period": 107, "predicted": [30, 26], "actual": [8, 9, 15, 24, 18, 42], "hit_count": 0, "is_hit": false}, {"period": 108, "predicted": [5, 3], "actual": [40, 20, 39, 49, 8, 1], "hit_count": 0, "is_hit": false}, {"period": 109, "predicted": [3, 26], "actual": [47, 16, 8, 41, 6, 19], "hit_count": 0, "is_hit": false}, {"period": 110, "predicted": [2, 49], "actual": [15, 20, 14, 46, 31, 30], "hit_count": 0, "is_hit": false}, {"period": 111, "predicted": [26, 5], "actual": [2, 17, 13, 41, 28, 36], "hit_count": 0, "is_hit": false}, {"period": 112, "predicted": [5, 31], "actual": [15, 43, 38, 24, 18, 4], "hit_count": 0, "is_hit": false}, {"period": 113, "predicted": [5, 3], "actual": [2, 26, 43, 38, 3, 9], "hit_count": 1, "is_hit": true}, {"period": 114, "predicted": [29, 44], "actual": [6, 41, 5, 46, 15, 9], "hit_count": 0, "is_hit": false}, {"period": 115, "predicted": [49, 2], "actual": [14, 7, 9, 22, 10, 4], "hit_count": 0, "is_hit": false}, {"period": 116, "predicted": [44, 40], "actual": [19, 43, 35, 40, 10, 48], "hit_count": 1, "is_hit": true}, {"period": 117, "predicted": [2, 1], "actual": [33, 46, 40, 6, 22, 45], "hit_count": 0, "is_hit": false}, {"period": 118, "predicted": [32, 49], "actual": [19, 15, 6, 39, 46, 35], "hit_count": 0, "is_hit": false}, {"period": 119, "predicted": [2, 3], "actual": [21, 37, 10, 45, 19, 30], "hit_count": 0, "is_hit": false}, {"period": 120, "predicted": [26, 41], "actual": [18, 25, 5, 35, 31, 43], "hit_count": 0, "is_hit": false}, {"period": 121, "predicted": [2, 25], "actual": [25, 7, 31, 34, 23, 39], "hit_count": 1, "is_hit": true}, {"period": 122, "predicted": [2, 3], "actual": [7, 46, 3, 2, 47, 22], "hit_count": 2, "is_hit": true}, {"period": 123, "predicted": [44, 31], "actual": [37, 30, 49, 16, 9, 12], "hit_count": 0, "is_hit": false}, {"period": 124, "predicted": [44, 30], "actual": [18, 41, 9, 14, 7, 39], "hit_count": 0, "is_hit": false}, {"period": 125, "predicted": [30, 3], "actual": [18, 25, 19, 28, 1, 8], "hit_count": 0, "is_hit": false}, {"period": 126, "predicted": [43, 31], "actual": [5, 13, 10, 28, 3, 31], "hit_count": 1, "is_hit": true}, {"period": 127, "predicted": [5, 22], "actual": [45, 35, 8, 20, 4, 22], "hit_count": 1, "is_hit": true}, {"period": 128, "predicted": [41, 10], "actual": [4, 43, 18, 45, 38, 26], "hit_count": 0, "is_hit": false}, {"period": 129, "predicted": [10, 3], "actual": [34, 45, 20, 42, 19, 25], "hit_count": 0, "is_hit": false}, {"period": 130, "predicted": [26, 12], "actual": [20, 30, 10, 33, 28, 8], "hit_count": 0, "is_hit": false}, {"period": 131, "predicted": [30, 26], "actual": [13, 44, 4, 20, 36, 30], "hit_count": 1, "is_hit": true}, {"period": 132, "predicted": [15, 16], "actual": [24, 46, 26, 42, 15, 35], "hit_count": 1, "is_hit": true}, {"period": 133, "predicted": [5, 29], "actual": [45, 3, 16, 1, 21, 38], "hit_count": 0, "is_hit": false}, {"period": 134, "predicted": [41, 17], "actual": [38, 27, 4, 23, 6, 34], "hit_count": 0, "is_hit": false}, {"period": 135, "predicted": [7, 49], "actual": [32, 44, 25, 3, 34, 33], "hit_count": 0, "is_hit": false}, {"period": 136, "predicted": [2, 29], "actual": [31, 23, 25, 33, 2, 21], "hit_count": 1, "is_hit": true}, {"period": 137, "predicted": [25, 49], "actual": [39, 35, 46, 25, 48, 45], "hit_count": 1, "is_hit": true}, {"period": 138, "predicted": [49, 41], "actual": [20, 27, 39, 47, 29, 3], "hit_count": 0, "is_hit": false}, {"period": 139, "predicted": [41, 2], "actual": [38, 27, 47, 30, 45, 9], "hit_count": 0, "is_hit": false}, {"period": 140, "predicted": [41, 26], "actual": [22, 45, 2, 41, 39, 31], "hit_count": 1, "is_hit": true}, {"period": 141, "predicted": [3, 41], "actual": [30, 22, 31, 34, 39, 3], "hit_count": 1, "is_hit": true}, {"period": 142, "predicted": [2, 41], "actual": [17, 30, 8, 29, 23, 33], "hit_count": 0, "is_hit": false}, {"period": 143, "predicted": [41, 32], "actual": [27, 19, 17, 2, 24, 15], "hit_count": 0, "is_hit": false}, {"period": 144, "predicted": [3, 26], "actual": [36, 31, 19, 1, 34, 29], "hit_count": 0, "is_hit": false}, {"period": 145, "predicted": [2, 42], "actual": [30, 25, 29, 28, 10, 49], "hit_count": 0, "is_hit": false}, {"period": 146, "predicted": [26, 49], "actual": [24, 41, 25, 3, 11, 47], "hit_count": 0, "is_hit": false}, {"period": 147, "predicted": [2, 43], "actual": [47, 14, 38, 26, 19, 35], "hit_count": 0, "is_hit": false}, {"period": 148, "predicted": [49, 10], "actual": [48, 26, 5, 31, 44, 9], "hit_count": 0, "is_hit": false}, {"period": 149, "predicted": [15, 10], "actual": [44, 43, 25, 3, 29, 19], "hit_count": 0, "is_hit": false}, {"period": 150, "predicted": [2, 29], "actual": [24, 43, 18, 31, 45, 12], "hit_count": 0, "is_hit": false}, {"period": 151, "predicted": [29, 30], "actual": [29, 49, 26, 25, 35, 39], "hit_count": 1, "is_hit": true}, {"period": 152, "predicted": [49, 41], "actual": [9, 34, 17, 29, 35, 12], "hit_count": 0, "is_hit": false}, {"period": 153, "predicted": [2, 41], "actual": [42, 11, 25, 8, 45, 9], "hit_count": 0, "is_hit": false}, {"period": 154, "predicted": [43, 41], "actual": [37, 18, 45, 25, 47, 42], "hit_count": 0, "is_hit": false}, {"period": 155, "predicted": [43, 3], "actual": [6, 34, 33, 12, 37, 20], "hit_count": 0, "is_hit": false}, {"period": 156, "predicted": [30, 32], "actual": [15, 13, 38, 17, 28, 32], "hit_count": 1, "is_hit": true}, {"period": 157, "predicted": [26, 5], "actual": [37, 5, 16, 12, 17, 3], "hit_count": 1, "is_hit": true}, {"period": 158, "predicted": [43, 44], "actual": [15, 48, 4, 17, 42, 21], "hit_count": 0, "is_hit": false}, {"period": 159, "predicted": [26, 10], "actual": [45, 34, 35, 22, 24, 8], "hit_count": 0, "is_hit": false}, {"period": 160, "predicted": [41, 19], "actual": [23, 42, 32, 46, 30, 40], "hit_count": 0, "is_hit": false}, {"period": 161, "predicted": [26, 5], "actual": [49, 41, 44, 29, 38, 18], "hit_count": 0, "is_hit": false}, {"period": 162, "predicted": [26, 49], "actual": [38, 2, 22, 48, 15, 12], "hit_count": 0, "is_hit": false}, {"period": 163, "predicted": [16, 3], "actual": [38, 18, 43, 29, 10, 32], "hit_count": 0, "is_hit": false}, {"period": 164, "predicted": [29, 26], "actual": [46, 38, 14, 39, 26, 8], "hit_count": 1, "is_hit": true}, {"period": 165, "predicted": [41, 3], "actual": [23, 37, 17, 8, 42, 49], "hit_count": 0, "is_hit": false}, {"period": 166, "predicted": [26, 7], "actual": [30, 38, 18, 14, 42, 16], "hit_count": 0, "is_hit": false}, {"period": 167, "predicted": [5, 30], "actual": [11, 27, 30, 6, 36, 3], "hit_count": 1, "is_hit": true}, {"period": 168, "predicted": [2, 15], "actual": [31, 7, 40, 15, 9, 25], "hit_count": 1, "is_hit": true}, {"period": 169, "predicted": [44, 2], "actual": [11, 2, 32, 38, 29, 31], "hit_count": 1, "is_hit": true}, {"period": 170, "predicted": [5, 26], "actual": [22, 20, 36, 44, 31, 19], "hit_count": 0, "is_hit": false}, {"period": 171, "predicted": [26, 44], "actual": [15, 4, 23, 36, 27, 7], "hit_count": 0, "is_hit": false}, {"period": 172, "predicted": [44, 49], "actual": [3, 15, 4, 39, 2, 28], "hit_count": 0, "is_hit": false}, {"period": 173, "predicted": [3, 5], "actual": [46, 32, 30, 5, 42, 4], "hit_count": 1, "is_hit": true}, {"period": 174, "predicted": [26, 5], "actual": [11, 36, 22, 24, 20, 12], "hit_count": 0, "is_hit": false}, {"period": 175, "predicted": [12, 5], "actual": [37, 3, 32, 12, 8, 29], "hit_count": 1, "is_hit": true}, {"period": 176, "predicted": [29, 19], "actual": [12, 5, 24, 28, 22, 17], "hit_count": 0, "is_hit": false}, {"period": 177, "predicted": [12, 43], "actual": [31, 30, 11, 49, 22, 41], "hit_count": 0, "is_hit": false}, {"period": 178, "predicted": [2, 26], "actual": [42, 30, 37, 20, 6, 7], "hit_count": 0, "is_hit": false}, {"period": 179, "predicted": [30, 16], "actual": [42, 8, 5, 30, 29, 25], "hit_count": 1, "is_hit": true}], "全面增强马尔可夫": [{"period": 2, "predicted": [30, 43], "actual": [13, 4, 10, 6, 5, 28], "hit_count": 0, "is_hit": false}, {"period": 3, "predicted": [30, 5], "actual": [28, 1, 16, 47, 18, 42], "hit_count": 0, "is_hit": false}, {"period": 4, "predicted": [30, 5], "actual": [45, 16, 17, 7, 37, 28], "hit_count": 0, "is_hit": false}, {"period": 5, "predicted": [30, 40], "actual": [35, 21, 10, 3, 32, 40], "hit_count": 1, "is_hit": true}, {"period": 6, "predicted": [2, 30], "actual": [3, 13, 12, 28, 1, 18], "hit_count": 0, "is_hit": false}, {"period": 7, "predicted": [30, 31], "actual": [32, 12, 48, 45, 39, 7], "hit_count": 0, "is_hit": false}, {"period": 8, "predicted": [30, 40], "actual": [13, 24, 48, 23, 32, 20], "hit_count": 0, "is_hit": false}, {"period": 9, "predicted": [5, 30], "actual": [23, 17, 43, 1, 10, 13], "hit_count": 0, "is_hit": false}, {"period": 10, "predicted": [40, 31], "actual": [37, 18, 17, 47, 21, 23], "hit_count": 0, "is_hit": false}, {"period": 11, "predicted": [30, 3], "actual": [7, 45, 3, 30, 1, 10], "hit_count": 2, "is_hit": true}, {"period": 12, "predicted": [30, 15], "actual": [24, 32, 10, 15, 37, 4], "hit_count": 1, "is_hit": true}, {"period": 13, "predicted": [30, 3], "actual": [42, 43, 36, 11, 25, 47], "hit_count": 0, "is_hit": false}, {"period": 14, "predicted": [40, 30], "actual": [48, 20, 30, 39, 41, 6], "hit_count": 1, "is_hit": true}, {"period": 15, "predicted": [30, 49], "actual": [13, 37, 2, 3, 43, 12], "hit_count": 0, "is_hit": false}, {"period": 16, "predicted": [29, 30], "actual": [40, 22, 27, 42, 43, 39], "hit_count": 0, "is_hit": false}, {"period": 17, "predicted": [40, 3], "actual": [42, 29, 20, 49, 22, 6], "hit_count": 0, "is_hit": false}, {"period": 18, "predicted": [30, 3], "actual": [12, 1, 24, 33, 21, 44], "hit_count": 0, "is_hit": false}, {"period": 19, "predicted": [30, 35], "actual": [3, 23, 49, 13, 39, 26], "hit_count": 0, "is_hit": false}, {"period": 20, "predicted": [3, 40], "actual": [44, 4, 47, 1, 49, 14], "hit_count": 0, "is_hit": false}, {"period": 21, "predicted": [30, 15], "actual": [39, 19, 24, 31, 32, 30], "hit_count": 1, "is_hit": true}, {"period": 22, "predicted": [30, 3], "actual": [27, 23, 26, 14, 16, 18], "hit_count": 0, "is_hit": false}, {"period": 23, "predicted": [30, 40], "actual": [15, 43, 6, 30, 16, 9], "hit_count": 1, "is_hit": true}, {"period": 24, "predicted": [30, 40], "actual": [3, 45, 18, 40, 28, 2], "hit_count": 1, "is_hit": true}, {"period": 25, "predicted": [3, 30], "actual": [17, 42, 28, 44, 5, 20], "hit_count": 0, "is_hit": false}, {"period": 26, "predicted": [30, 43], "actual": [36, 25, 7, 2, 34, 9], "hit_count": 0, "is_hit": false}, {"period": 27, "predicted": [40, 30], "actual": [2, 6, 28, 32, 45, 16], "hit_count": 0, "is_hit": false}, {"period": 28, "predicted": [30, 5], "actual": [34, 14, 29, 33, 5, 28], "hit_count": 1, "is_hit": true}, {"period": 29, "predicted": [30, 29], "actual": [14, 46, 3, 35, 43, 10], "hit_count": 0, "is_hit": false}, {"period": 30, "predicted": [30, 29], "actual": [27, 39, 23, 45, 44, 46], "hit_count": 0, "is_hit": false}, {"period": 31, "predicted": [30, 3], "actual": [30, 23, 8, 24, 33, 25], "hit_count": 1, "is_hit": true}, {"period": 32, "predicted": [30, 3], "actual": [22, 15, 13, 2, 17, 33], "hit_count": 0, "is_hit": false}, {"period": 33, "predicted": [31, 40], "actual": [41, 22, 19, 40, 46, 39], "hit_count": 1, "is_hit": true}, {"period": 34, "predicted": [3, 40], "actual": [1, 4, 21, 40, 16, 49], "hit_count": 1, "is_hit": true}, {"period": 35, "predicted": [30, 40], "actual": [27, 35, 32, 37, 6, 18], "hit_count": 0, "is_hit": false}, {"period": 36, "predicted": [3, 30], "actual": [38, 7, 19, 47, 10, 40], "hit_count": 0, "is_hit": false}, {"period": 37, "predicted": [3, 40], "actual": [35, 32, 46, 8, 37, 16], "hit_count": 0, "is_hit": false}, {"period": 38, "predicted": [30, 29], "actual": [11, 1, 47, 39, 12, 3], "hit_count": 0, "is_hit": false}, {"period": 39, "predicted": [30, 3], "actual": [41, 48, 10, 17, 34, 43], "hit_count": 0, "is_hit": false}, {"period": 40, "predicted": [30, 2], "actual": [1, 32, 35, 30, 8, 16], "hit_count": 1, "is_hit": true}, {"period": 41, "predicted": [30, 40], "actual": [48, 35, 22, 30, 26, 4], "hit_count": 1, "is_hit": true}, {"period": 42, "predicted": [30, 16], "actual": [44, 13, 39, 22, 46, 47], "hit_count": 0, "is_hit": false}, {"period": 43, "predicted": [30, 40], "actual": [4, 27, 38, 40, 19, 35], "hit_count": 1, "is_hit": true}, {"period": 44, "predicted": [3, 49], "actual": [24, 28, 46, 34, 14, 43], "hit_count": 0, "is_hit": false}, {"period": 45, "predicted": [30, 29], "actual": [40, 28, 3, 48, 31, 8], "hit_count": 0, "is_hit": false}, {"period": 46, "predicted": [10, 3], "actual": [41, 1, 24, 34, 33, 30], "hit_count": 0, "is_hit": false}, {"period": 47, "predicted": [30, 42], "actual": [14, 22, 13, 31, 20, 16], "hit_count": 0, "is_hit": false}, {"period": 48, "predicted": [30, 40], "actual": [7, 35, 23, 33, 46, 27], "hit_count": 0, "is_hit": false}, {"period": 49, "predicted": [30, 49], "actual": [49, 5, 1, 17, 31, 6], "hit_count": 1, "is_hit": true}, {"period": 50, "predicted": [3, 30], "actual": [29, 21, 46, 48, 15, 45], "hit_count": 0, "is_hit": false}, {"period": 51, "predicted": [30, 3], "actual": [27, 22, 31, 7, 13, 18], "hit_count": 0, "is_hit": false}, {"period": 52, "predicted": [40, 3], "actual": [7, 6, 17, 13, 34, 31], "hit_count": 0, "is_hit": false}, {"period": 53, "predicted": [30, 2], "actual": [33, 48, 5, 19, 13, 17], "hit_count": 0, "is_hit": false}, {"period": 54, "predicted": [40, 31], "actual": [23, 28, 30, 35, 19, 27], "hit_count": 0, "is_hit": false}, {"period": 55, "predicted": [30, 25], "actual": [47, 36, 40, 43, 11, 37], "hit_count": 0, "is_hit": false}, {"period": 56, "predicted": [40, 3], "actual": [41, 8, 33, 30, 43, 7], "hit_count": 0, "is_hit": false}, {"period": 57, "predicted": [30, 49], "actual": [10, 11, 13, 1, 40, 22], "hit_count": 0, "is_hit": false}, {"period": 58, "predicted": [40, 2], "actual": [22, 23, 33, 34, 49, 19], "hit_count": 0, "is_hit": false}, {"period": 59, "predicted": [40, 30], "actual": [40, 16, 17, 26, 36, 4], "hit_count": 1, "is_hit": true}, {"period": 60, "predicted": [40, 30], "actual": [10, 8, 17, 13, 47, 44], "hit_count": 0, "is_hit": false}, {"period": 61, "predicted": [30, 31], "actual": [42, 28, 39, 6, 23, 35], "hit_count": 0, "is_hit": false}, {"period": 62, "predicted": [30, 3], "actual": [27, 37, 10, 26, 2, 48], "hit_count": 0, "is_hit": false}, {"period": 63, "predicted": [3, 30], "actual": [42, 5, 38, 3, 49, 21], "hit_count": 1, "is_hit": true}, {"period": 64, "predicted": [30, 3], "actual": [48, 40, 3, 33, 47, 4], "hit_count": 1, "is_hit": true}, {"period": 65, "predicted": [49, 10], "actual": [30, 41, 38, 25, 35, 16], "hit_count": 0, "is_hit": false}, {"period": 66, "predicted": [30, 49], "actual": [49, 45, 27, 22, 13, 26], "hit_count": 1, "is_hit": true}, {"period": 67, "predicted": [40, 3], "actual": [15, 35, 39, 48, 45, 11], "hit_count": 0, "is_hit": false}, {"period": 68, "predicted": [30, 3], "actual": [42, 33, 47, 40, 30, 2], "hit_count": 1, "is_hit": true}, {"period": 69, "predicted": [30, 5], "actual": [44, 30, 5, 40, 6, 15], "hit_count": 2, "is_hit": true}, {"period": 70, "predicted": [30, 2], "actual": [16, 35, 46, 25, 19, 18], "hit_count": 0, "is_hit": false}, {"period": 71, "predicted": [30, 2], "actual": [32, 9, 44, 36, 23, 40], "hit_count": 0, "is_hit": false}, {"period": 72, "predicted": [40, 15], "actual": [35, 37, 48, 33, 14, 22], "hit_count": 0, "is_hit": false}, {"period": 73, "predicted": [30, 32], "actual": [43, 22, 48, 23, 33, 10], "hit_count": 0, "is_hit": false}, {"period": 74, "predicted": [40, 30], "actual": [22, 21, 1, 47, 29, 11], "hit_count": 0, "is_hit": false}, {"period": 75, "predicted": [40, 30], "actual": [30, 5, 10, 37, 32, 7], "hit_count": 1, "is_hit": true}, {"period": 76, "predicted": [30, 40], "actual": [28, 2, 14, 10, 16, 23], "hit_count": 0, "is_hit": false}, {"period": 77, "predicted": [30, 5], "actual": [5, 23, 27, 17, 11, 39], "hit_count": 1, "is_hit": true}, {"period": 78, "predicted": [40, 3], "actual": [15, 38, 18, 3, 8, 11], "hit_count": 1, "is_hit": true}, {"period": 79, "predicted": [3, 30], "actual": [4, 20, 9, 32, 29, 44], "hit_count": 0, "is_hit": false}, {"period": 80, "predicted": [30, 15], "actual": [27, 38, 29, 2, 44, 31], "hit_count": 0, "is_hit": false}, {"period": 81, "predicted": [3, 30], "actual": [48, 8, 40, 9, 18, 27], "hit_count": 0, "is_hit": false}, {"period": 82, "predicted": [3, 40], "actual": [10, 35, 4, 45, 24, 9], "hit_count": 0, "is_hit": false}, {"period": 83, "predicted": [30, 5], "actual": [45, 4, 6, 47, 46, 11], "hit_count": 0, "is_hit": false}, {"period": 84, "predicted": [30, 3], "actual": [17, 45, 24, 23, 38, 40], "hit_count": 0, "is_hit": false}, {"period": 85, "predicted": [30, 3], "actual": [32, 31, 18, 14, 8, 19], "hit_count": 0, "is_hit": false}, {"period": 86, "predicted": [3, 40], "actual": [19, 29, 31, 2, 42, 26], "hit_count": 0, "is_hit": false}, {"period": 87, "predicted": [3, 5], "actual": [30, 16, 14, 28, 29, 3], "hit_count": 1, "is_hit": true}, {"period": 88, "predicted": [30, 5], "actual": [14, 11, 39, 10, 45, 33], "hit_count": 0, "is_hit": false}, {"period": 89, "predicted": [30, 40], "actual": [45, 19, 13, 25, 15, 27], "hit_count": 0, "is_hit": false}, {"period": 90, "predicted": [3, 40], "actual": [17, 7, 23, 38, 12, 18], "hit_count": 0, "is_hit": false}, {"period": 91, "predicted": [30, 3], "actual": [35, 23, 32, 31, 8, 24], "hit_count": 0, "is_hit": false}, {"period": 92, "predicted": [3, 30], "actual": [23, 24, 36, 3, 4, 32], "hit_count": 1, "is_hit": true}, {"period": 93, "predicted": [30, 5], "actual": [16, 23, 28, 21, 27, 46], "hit_count": 0, "is_hit": false}, {"period": 94, "predicted": [30, 40], "actual": [35, 43, 31, 37, 15, 39], "hit_count": 0, "is_hit": false}, {"period": 95, "predicted": [3, 30], "actual": [4, 43, 37, 40, 24, 16], "hit_count": 0, "is_hit": false}, {"period": 96, "predicted": [30, 43], "actual": [42, 46, 25, 5, 40, 22], "hit_count": 0, "is_hit": false}, {"period": 97, "predicted": [40, 43], "actual": [1, 32, 24, 23, 4, 2], "hit_count": 0, "is_hit": false}, {"period": 98, "predicted": [5, 30], "actual": [30, 39, 31, 8, 21, 20], "hit_count": 1, "is_hit": true}, {"period": 99, "predicted": [30, 3], "actual": [1, 34, 43, 38, 6, 22], "hit_count": 0, "is_hit": false}, {"period": 100, "predicted": [40, 30], "actual": [25, 3, 34, 8, 31, 41], "hit_count": 0, "is_hit": false}, {"period": 101, "predicted": [30, 3], "actual": [20, 49, 36, 37, 42, 30], "hit_count": 1, "is_hit": true}, {"period": 102, "predicted": [30, 3], "actual": [24, 40, 7, 30, 47, 9], "hit_count": 1, "is_hit": true}, {"period": 103, "predicted": [30, 5], "actual": [16, 28, 3, 30, 42, 49], "hit_count": 1, "is_hit": true}, {"period": 104, "predicted": [30, 5], "actual": [9, 20, 33, 11, 32, 21], "hit_count": 0, "is_hit": false}, {"period": 105, "predicted": [30, 40], "actual": [12, 28, 48, 37, 18, 43], "hit_count": 0, "is_hit": false}, {"period": 106, "predicted": [30, 29], "actual": [45, 20, 11, 23, 34, 42], "hit_count": 0, "is_hit": false}, {"period": 107, "predicted": [30, 40], "actual": [8, 9, 15, 24, 18, 42], "hit_count": 0, "is_hit": false}, {"period": 108, "predicted": [3, 30], "actual": [40, 20, 39, 49, 8, 1], "hit_count": 0, "is_hit": false}, {"period": 109, "predicted": [3, 30], "actual": [47, 16, 8, 41, 6, 19], "hit_count": 0, "is_hit": false}, {"period": 110, "predicted": [30, 2], "actual": [15, 20, 14, 46, 31, 30], "hit_count": 1, "is_hit": true}, {"period": 111, "predicted": [30, 3], "actual": [2, 17, 13, 41, 28, 36], "hit_count": 0, "is_hit": false}, {"period": 112, "predicted": [30, 5], "actual": [15, 43, 38, 24, 18, 4], "hit_count": 0, "is_hit": false}, {"period": 113, "predicted": [30, 3], "actual": [2, 26, 43, 38, 3, 9], "hit_count": 1, "is_hit": true}, {"period": 114, "predicted": [3, 29], "actual": [6, 41, 5, 46, 15, 9], "hit_count": 0, "is_hit": false}, {"period": 115, "predicted": [30, 3], "actual": [14, 7, 9, 22, 10, 4], "hit_count": 0, "is_hit": false}, {"period": 116, "predicted": [30, 40], "actual": [19, 43, 35, 40, 10, 48], "hit_count": 1, "is_hit": true}, {"period": 117, "predicted": [2, 29], "actual": [33, 46, 40, 6, 22, 45], "hit_count": 0, "is_hit": false}, {"period": 118, "predicted": [40, 30], "actual": [19, 15, 6, 39, 46, 35], "hit_count": 0, "is_hit": false}, {"period": 119, "predicted": [3, 30], "actual": [21, 37, 10, 45, 19, 30], "hit_count": 1, "is_hit": true}, {"period": 120, "predicted": [30, 3], "actual": [18, 25, 5, 35, 31, 43], "hit_count": 0, "is_hit": false}, {"period": 121, "predicted": [2, 3], "actual": [25, 7, 31, 34, 23, 39], "hit_count": 0, "is_hit": false}, {"period": 122, "predicted": [30, 3], "actual": [7, 46, 3, 2, 47, 22], "hit_count": 1, "is_hit": true}, {"period": 123, "predicted": [3, 40], "actual": [37, 30, 49, 16, 9, 12], "hit_count": 0, "is_hit": false}, {"period": 124, "predicted": [30, 40], "actual": [18, 41, 9, 14, 7, 39], "hit_count": 0, "is_hit": false}, {"period": 125, "predicted": [30, 3], "actual": [18, 25, 19, 28, 1, 8], "hit_count": 0, "is_hit": false}, {"period": 126, "predicted": [3, 30], "actual": [5, 13, 10, 28, 3, 31], "hit_count": 1, "is_hit": true}, {"period": 127, "predicted": [30, 22], "actual": [45, 35, 8, 20, 4, 22], "hit_count": 1, "is_hit": true}, {"period": 128, "predicted": [30, 3], "actual": [4, 43, 18, 45, 38, 26], "hit_count": 0, "is_hit": false}, {"period": 129, "predicted": [3, 30], "actual": [34, 45, 20, 42, 19, 25], "hit_count": 0, "is_hit": false}, {"period": 130, "predicted": [30, 40], "actual": [20, 30, 10, 33, 28, 8], "hit_count": 1, "is_hit": true}, {"period": 131, "predicted": [30, 15], "actual": [13, 44, 4, 20, 36, 30], "hit_count": 1, "is_hit": true}, {"period": 132, "predicted": [30, 15], "actual": [24, 46, 26, 42, 15, 35], "hit_count": 1, "is_hit": true}, {"period": 133, "predicted": [30, 3], "actual": [45, 3, 16, 1, 21, 38], "hit_count": 1, "is_hit": true}, {"period": 134, "predicted": [30, 17], "actual": [38, 27, 4, 23, 6, 34], "hit_count": 0, "is_hit": false}, {"period": 135, "predicted": [30, 3], "actual": [32, 44, 25, 3, 34, 33], "hit_count": 1, "is_hit": true}, {"period": 136, "predicted": [30, 2], "actual": [31, 23, 25, 33, 2, 21], "hit_count": 1, "is_hit": true}, {"period": 137, "predicted": [3, 30], "actual": [39, 35, 46, 25, 48, 45], "hit_count": 0, "is_hit": false}, {"period": 138, "predicted": [30, 3], "actual": [20, 27, 39, 47, 29, 3], "hit_count": 1, "is_hit": true}, {"period": 139, "predicted": [30, 3], "actual": [38, 27, 47, 30, 45, 9], "hit_count": 1, "is_hit": true}, {"period": 140, "predicted": [30, 40], "actual": [22, 45, 2, 41, 39, 31], "hit_count": 0, "is_hit": false}, {"period": 141, "predicted": [3, 30], "actual": [30, 22, 31, 34, 39, 3], "hit_count": 2, "is_hit": true}, {"period": 142, "predicted": [30, 40], "actual": [17, 30, 8, 29, 23, 33], "hit_count": 1, "is_hit": true}, {"period": 143, "predicted": [30, 32], "actual": [27, 19, 17, 2, 24, 15], "hit_count": 0, "is_hit": false}, {"period": 144, "predicted": [3, 29], "actual": [36, 31, 19, 1, 34, 29], "hit_count": 1, "is_hit": true}, {"period": 145, "predicted": [30, 40], "actual": [30, 25, 29, 28, 10, 49], "hit_count": 1, "is_hit": true}, {"period": 146, "predicted": [30, 22], "actual": [24, 41, 25, 3, 11, 47], "hit_count": 0, "is_hit": false}, {"period": 147, "predicted": [30, 3], "actual": [47, 14, 38, 26, 19, 35], "hit_count": 0, "is_hit": false}, {"period": 148, "predicted": [40, 3], "actual": [48, 26, 5, 31, 44, 9], "hit_count": 0, "is_hit": false}, {"period": 149, "predicted": [30, 3], "actual": [44, 43, 25, 3, 29, 19], "hit_count": 1, "is_hit": true}, {"period": 150, "predicted": [2, 30], "actual": [24, 43, 18, 31, 45, 12], "hit_count": 0, "is_hit": false}, {"period": 151, "predicted": [30, 29], "actual": [29, 49, 26, 25, 35, 39], "hit_count": 1, "is_hit": true}, {"period": 152, "predicted": [3, 30], "actual": [9, 34, 17, 29, 35, 12], "hit_count": 0, "is_hit": false}, {"period": 153, "predicted": [30, 2], "actual": [42, 11, 25, 8, 45, 9], "hit_count": 0, "is_hit": false}, {"period": 154, "predicted": [30, 40], "actual": [37, 18, 45, 25, 47, 42], "hit_count": 0, "is_hit": false}, {"period": 155, "predicted": [30, 3], "actual": [6, 34, 33, 12, 37, 20], "hit_count": 0, "is_hit": false}, {"period": 156, "predicted": [30, 3], "actual": [15, 13, 38, 17, 28, 32], "hit_count": 0, "is_hit": false}, {"period": 157, "predicted": [5, 40], "actual": [37, 5, 16, 12, 17, 3], "hit_count": 1, "is_hit": true}, {"period": 158, "predicted": [30, 22], "actual": [15, 48, 4, 17, 42, 21], "hit_count": 0, "is_hit": false}, {"period": 159, "predicted": [30, 3], "actual": [45, 34, 35, 22, 24, 8], "hit_count": 0, "is_hit": false}, {"period": 160, "predicted": [30, 40], "actual": [23, 42, 32, 46, 30, 40], "hit_count": 2, "is_hit": true}, {"period": 161, "predicted": [30, 40], "actual": [49, 41, 44, 29, 38, 18], "hit_count": 0, "is_hit": false}, {"period": 162, "predicted": [30, 3], "actual": [38, 2, 22, 48, 15, 12], "hit_count": 0, "is_hit": false}, {"period": 163, "predicted": [3, 40], "actual": [38, 18, 43, 29, 10, 32], "hit_count": 0, "is_hit": false}, {"period": 164, "predicted": [29, 3], "actual": [46, 38, 14, 39, 26, 8], "hit_count": 0, "is_hit": false}, {"period": 165, "predicted": [3, 30], "actual": [23, 37, 17, 8, 42, 49], "hit_count": 0, "is_hit": false}, {"period": 166, "predicted": [3, 30], "actual": [30, 38, 18, 14, 42, 16], "hit_count": 1, "is_hit": true}, {"period": 167, "predicted": [30, 5], "actual": [11, 27, 30, 6, 36, 3], "hit_count": 1, "is_hit": true}, {"period": 168, "predicted": [30, 15], "actual": [31, 7, 40, 15, 9, 25], "hit_count": 1, "is_hit": true}, {"period": 169, "predicted": [3, 40], "actual": [11, 2, 32, 38, 29, 31], "hit_count": 0, "is_hit": false}, {"period": 170, "predicted": [3, 5], "actual": [22, 20, 36, 44, 31, 19], "hit_count": 0, "is_hit": false}, {"period": 171, "predicted": [3, 30], "actual": [15, 4, 23, 36, 27, 7], "hit_count": 0, "is_hit": false}, {"period": 172, "predicted": [30, 3], "actual": [3, 15, 4, 39, 2, 28], "hit_count": 1, "is_hit": true}, {"period": 173, "predicted": [30, 3], "actual": [46, 32, 30, 5, 42, 4], "hit_count": 1, "is_hit": true}, {"period": 174, "predicted": [30, 40], "actual": [11, 36, 22, 24, 20, 12], "hit_count": 0, "is_hit": false}, {"period": 175, "predicted": [30, 40], "actual": [37, 3, 32, 12, 8, 29], "hit_count": 0, "is_hit": false}, {"period": 176, "predicted": [30, 29], "actual": [12, 5, 24, 28, 22, 17], "hit_count": 0, "is_hit": false}, {"period": 177, "predicted": [30, 40], "actual": [31, 30, 11, 49, 22, 41], "hit_count": 1, "is_hit": true}, {"period": 178, "predicted": [30, 3], "actual": [42, 30, 37, 20, 6, 7], "hit_count": 1, "is_hit": true}, {"period": 179, "predicted": [30, 3], "actual": [42, 8, 5, 30, 29, 25], "hit_count": 1, "is_hit": true}], "精细优化马尔可夫": [{"period": 2, "predicted": [30, 43], "actual": [13, 4, 10, 6, 5, 28], "hit_count": 0, "is_hit": false}, {"period": 3, "predicted": [45, 48], "actual": [28, 1, 16, 47, 18, 42], "hit_count": 0, "is_hit": false}, {"period": 4, "predicted": [43, 30], "actual": [45, 16, 17, 7, 37, 28], "hit_count": 0, "is_hit": false}, {"period": 5, "predicted": [10, 15], "actual": [35, 21, 10, 3, 32, 40], "hit_count": 1, "is_hit": true}, {"period": 6, "predicted": [30, 40], "actual": [3, 13, 12, 28, 1, 18], "hit_count": 0, "is_hit": false}, {"period": 7, "predicted": [31, 48], "actual": [32, 12, 48, 45, 39, 7], "hit_count": 1, "is_hit": true}, {"period": 8, "predicted": [30, 40], "actual": [13, 24, 48, 23, 32, 20], "hit_count": 0, "is_hit": false}, {"period": 9, "predicted": [5, 49], "actual": [23, 17, 43, 1, 10, 13], "hit_count": 0, "is_hit": false}, {"period": 10, "predicted": [40, 30], "actual": [37, 18, 17, 47, 21, 23], "hit_count": 0, "is_hit": false}, {"period": 11, "predicted": [44, 26], "actual": [7, 45, 3, 30, 1, 10], "hit_count": 0, "is_hit": false}, {"period": 12, "predicted": [41, 30], "actual": [24, 32, 10, 15, 37, 4], "hit_count": 0, "is_hit": false}, {"period": 13, "predicted": [29, 30], "actual": [42, 43, 36, 11, 25, 47], "hit_count": 0, "is_hit": false}, {"period": 14, "predicted": [40, 30], "actual": [48, 20, 30, 39, 41, 6], "hit_count": 1, "is_hit": true}, {"period": 15, "predicted": [30, 31], "actual": [13, 37, 2, 3, 43, 12], "hit_count": 0, "is_hit": false}, {"period": 16, "predicted": [29, 43], "actual": [40, 22, 27, 42, 43, 39], "hit_count": 1, "is_hit": true}, {"period": 17, "predicted": [40, 3], "actual": [42, 29, 20, 49, 22, 6], "hit_count": 0, "is_hit": false}, {"period": 18, "predicted": [30, 31], "actual": [12, 1, 24, 33, 21, 44], "hit_count": 0, "is_hit": false}, {"period": 19, "predicted": [30, 41], "actual": [3, 23, 49, 13, 39, 26], "hit_count": 0, "is_hit": false}, {"period": 20, "predicted": [3, 40], "actual": [44, 4, 47, 1, 49, 14], "hit_count": 0, "is_hit": false}, {"period": 21, "predicted": [30, 15], "actual": [39, 19, 24, 31, 32, 30], "hit_count": 1, "is_hit": true}, {"period": 22, "predicted": [30, 3], "actual": [27, 23, 26, 14, 16, 18], "hit_count": 0, "is_hit": false}, {"period": 23, "predicted": [34, 49], "actual": [15, 43, 6, 30, 16, 9], "hit_count": 0, "is_hit": false}, {"period": 24, "predicted": [2, 49], "actual": [3, 45, 18, 40, 28, 2], "hit_count": 1, "is_hit": true}, {"period": 25, "predicted": [45, 48], "actual": [17, 42, 28, 44, 5, 20], "hit_count": 0, "is_hit": false}, {"period": 26, "predicted": [45, 48], "actual": [36, 25, 7, 2, 34, 9], "hit_count": 0, "is_hit": false}, {"period": 27, "predicted": [2, 40], "actual": [2, 6, 28, 32, 45, 16], "hit_count": 1, "is_hit": true}, {"period": 28, "predicted": [45, 48], "actual": [34, 14, 29, 33, 5, 28], "hit_count": 0, "is_hit": false}, {"period": 29, "predicted": [2, 15], "actual": [14, 46, 3, 35, 43, 10], "hit_count": 0, "is_hit": false}, {"period": 30, "predicted": [2, 15], "actual": [27, 39, 23, 45, 44, 46], "hit_count": 0, "is_hit": false}, {"period": 31, "predicted": [3, 30], "actual": [30, 23, 8, 24, 33, 25], "hit_count": 1, "is_hit": true}, {"period": 32, "predicted": [15, 30], "actual": [22, 15, 13, 2, 17, 33], "hit_count": 1, "is_hit": true}, {"period": 33, "predicted": [45, 48], "actual": [41, 22, 19, 40, 46, 39], "hit_count": 0, "is_hit": false}, {"period": 34, "predicted": [29, 2], "actual": [1, 4, 21, 40, 16, 49], "hit_count": 0, "is_hit": false}, {"period": 35, "predicted": [10, 15], "actual": [27, 35, 32, 37, 6, 18], "hit_count": 0, "is_hit": false}, {"period": 36, "predicted": [2, 3], "actual": [38, 7, 19, 47, 10, 40], "hit_count": 0, "is_hit": false}, {"period": 37, "predicted": [2, 44], "actual": [35, 32, 46, 8, 37, 16], "hit_count": 0, "is_hit": false}, {"period": 38, "predicted": [45, 48], "actual": [11, 1, 47, 39, 12, 3], "hit_count": 0, "is_hit": false}, {"period": 39, "predicted": [45, 48], "actual": [41, 48, 10, 17, 34, 43], "hit_count": 1, "is_hit": true}, {"period": 40, "predicted": [10, 15], "actual": [1, 32, 35, 30, 8, 16], "hit_count": 0, "is_hit": false}, {"period": 41, "predicted": [41, 23], "actual": [48, 35, 22, 30, 26, 4], "hit_count": 0, "is_hit": false}, {"period": 42, "predicted": [10, 49], "actual": [44, 13, 39, 22, 46, 47], "hit_count": 0, "is_hit": false}, {"period": 43, "predicted": [43, 2], "actual": [4, 27, 38, 40, 19, 35], "hit_count": 0, "is_hit": false}, {"period": 44, "predicted": [49, 10], "actual": [24, 28, 46, 34, 14, 43], "hit_count": 0, "is_hit": false}, {"period": 45, "predicted": [29, 5], "actual": [40, 28, 3, 48, 31, 8], "hit_count": 0, "is_hit": false}, {"period": 46, "predicted": [45, 48], "actual": [41, 1, 24, 34, 33, 30], "hit_count": 0, "is_hit": false}, {"period": 47, "predicted": [30, 42], "actual": [14, 22, 13, 31, 20, 16], "hit_count": 0, "is_hit": false}, {"period": 48, "predicted": [45, 48], "actual": [7, 35, 23, 33, 46, 27], "hit_count": 0, "is_hit": false}, {"period": 49, "predicted": [49, 2], "actual": [49, 5, 1, 17, 31, 6], "hit_count": 1, "is_hit": true}, {"period": 50, "predicted": [44, 26], "actual": [29, 21, 46, 48, 15, 45], "hit_count": 0, "is_hit": false}, {"period": 51, "predicted": [41, 26], "actual": [27, 22, 31, 7, 13, 18], "hit_count": 0, "is_hit": false}, {"period": 52, "predicted": [44, 40], "actual": [7, 6, 17, 13, 34, 31], "hit_count": 0, "is_hit": false}, {"period": 53, "predicted": [36, 17], "actual": [33, 48, 5, 19, 13, 17], "hit_count": 1, "is_hit": true}, {"period": 54, "predicted": [10, 15], "actual": [23, 28, 30, 35, 19, 27], "hit_count": 0, "is_hit": false}, {"period": 55, "predicted": [44, 23], "actual": [47, 36, 40, 43, 11, 37], "hit_count": 0, "is_hit": false}, {"period": 56, "predicted": [43, 2], "actual": [41, 8, 33, 30, 43, 7], "hit_count": 1, "is_hit": true}, {"period": 57, "predicted": [49, 2], "actual": [10, 11, 13, 1, 40, 22], "hit_count": 0, "is_hit": false}, {"period": 58, "predicted": [36, 40], "actual": [22, 23, 33, 34, 49, 19], "hit_count": 0, "is_hit": false}, {"period": 59, "predicted": [12, 26], "actual": [40, 16, 17, 26, 36, 4], "hit_count": 1, "is_hit": true}, {"period": 60, "predicted": [45, 48], "actual": [10, 8, 17, 13, 47, 44], "hit_count": 0, "is_hit": false}, {"period": 61, "predicted": [2, 43], "actual": [42, 28, 39, 6, 23, 35], "hit_count": 0, "is_hit": false}, {"period": 62, "predicted": [30, 49], "actual": [27, 37, 10, 26, 2, 48], "hit_count": 0, "is_hit": false}, {"period": 63, "predicted": [45, 48], "actual": [42, 5, 38, 3, 49, 21], "hit_count": 0, "is_hit": false}, {"period": 64, "predicted": [10, 15], "actual": [48, 40, 3, 33, 47, 4], "hit_count": 0, "is_hit": false}, {"period": 65, "predicted": [49, 10], "actual": [30, 41, 38, 25, 35, 16], "hit_count": 0, "is_hit": false}, {"period": 66, "predicted": [30, 49], "actual": [49, 45, 27, 22, 13, 26], "hit_count": 1, "is_hit": true}, {"period": 67, "predicted": [40, 41], "actual": [15, 35, 39, 48, 45, 11], "hit_count": 0, "is_hit": false}, {"period": 68, "predicted": [41, 3], "actual": [42, 33, 47, 40, 30, 2], "hit_count": 0, "is_hit": false}, {"period": 69, "predicted": [45, 48], "actual": [44, 30, 5, 40, 6, 15], "hit_count": 0, "is_hit": false}, {"period": 70, "predicted": [15, 36], "actual": [16, 35, 46, 25, 19, 18], "hit_count": 0, "is_hit": false}, {"period": 71, "predicted": [10, 15], "actual": [32, 9, 44, 36, 23, 40], "hit_count": 0, "is_hit": false}, {"period": 72, "predicted": [15, 40], "actual": [35, 37, 48, 33, 14, 22], "hit_count": 0, "is_hit": false}, {"period": 73, "predicted": [49, 2], "actual": [43, 22, 48, 23, 33, 10], "hit_count": 0, "is_hit": false}, {"period": 74, "predicted": [49, 2], "actual": [22, 21, 1, 47, 29, 11], "hit_count": 0, "is_hit": false}, {"period": 75, "predicted": [41, 2], "actual": [30, 5, 10, 37, 32, 7], "hit_count": 0, "is_hit": false}, {"period": 76, "predicted": [15, 16], "actual": [28, 2, 14, 10, 16, 23], "hit_count": 1, "is_hit": true}, {"period": 77, "predicted": [45, 48], "actual": [5, 23, 27, 17, 11, 39], "hit_count": 0, "is_hit": false}, {"period": 78, "predicted": [40, 41], "actual": [15, 38, 18, 3, 8, 11], "hit_count": 0, "is_hit": false}, {"period": 79, "predicted": [45, 48], "actual": [4, 20, 9, 32, 29, 44], "hit_count": 0, "is_hit": false}, {"period": 80, "predicted": [15, 44], "actual": [27, 38, 29, 2, 44, 31], "hit_count": 1, "is_hit": true}, {"period": 81, "predicted": [10, 15], "actual": [48, 8, 40, 9, 18, 27], "hit_count": 0, "is_hit": false}, {"period": 82, "predicted": [49, 3], "actual": [10, 35, 4, 45, 24, 9], "hit_count": 0, "is_hit": false}, {"period": 83, "predicted": [44, 30], "actual": [45, 4, 6, 47, 46, 11], "hit_count": 0, "is_hit": false}, {"period": 84, "predicted": [49, 2], "actual": [17, 45, 24, 23, 38, 40], "hit_count": 0, "is_hit": false}, {"period": 85, "predicted": [19, 5], "actual": [32, 31, 18, 14, 8, 19], "hit_count": 1, "is_hit": true}, {"period": 86, "predicted": [29, 23], "actual": [19, 29, 31, 2, 42, 26], "hit_count": 1, "is_hit": true}, {"period": 87, "predicted": [29, 5], "actual": [30, 16, 14, 28, 29, 3], "hit_count": 1, "is_hit": true}, {"period": 88, "predicted": [30, 43], "actual": [14, 11, 39, 10, 45, 33], "hit_count": 0, "is_hit": false}, {"period": 89, "predicted": [2, 49], "actual": [45, 19, 13, 25, 15, 27], "hit_count": 0, "is_hit": false}, {"period": 90, "predicted": [10, 15], "actual": [17, 7, 23, 38, 12, 18], "hit_count": 0, "is_hit": false}, {"period": 91, "predicted": [10, 15], "actual": [35, 23, 32, 31, 8, 24], "hit_count": 0, "is_hit": false}, {"period": 92, "predicted": [3, 5], "actual": [23, 24, 36, 3, 4, 32], "hit_count": 1, "is_hit": true}, {"period": 93, "predicted": [45, 48], "actual": [16, 23, 28, 21, 27, 46], "hit_count": 0, "is_hit": false}, {"period": 94, "predicted": [10, 15], "actual": [35, 43, 31, 37, 15, 39], "hit_count": 1, "is_hit": true}, {"period": 95, "predicted": [29, 3], "actual": [4, 43, 37, 40, 24, 16], "hit_count": 0, "is_hit": false}, {"period": 96, "predicted": [45, 48], "actual": [42, 46, 25, 5, 40, 22], "hit_count": 0, "is_hit": false}, {"period": 97, "predicted": [43, 40], "actual": [1, 32, 24, 23, 4, 2], "hit_count": 0, "is_hit": false}, {"period": 98, "predicted": [45, 48], "actual": [30, 39, 31, 8, 21, 20], "hit_count": 0, "is_hit": false}, {"period": 99, "predicted": [26, 30], "actual": [1, 34, 43, 38, 6, 22], "hit_count": 0, "is_hit": false}, {"period": 100, "predicted": [17, 40], "actual": [25, 3, 34, 8, 31, 41], "hit_count": 0, "is_hit": false}, {"period": 101, "predicted": [10, 15], "actual": [20, 49, 36, 37, 42, 30], "hit_count": 0, "is_hit": false}, {"period": 102, "predicted": [30, 3], "actual": [24, 40, 7, 30, 47, 9], "hit_count": 1, "is_hit": true}, {"period": 103, "predicted": [44, 5], "actual": [16, 28, 3, 30, 42, 49], "hit_count": 0, "is_hit": false}, {"period": 104, "predicted": [44, 30], "actual": [9, 20, 33, 11, 32, 21], "hit_count": 0, "is_hit": false}, {"period": 105, "predicted": [26, 49], "actual": [12, 28, 48, 37, 18, 43], "hit_count": 0, "is_hit": false}, {"period": 106, "predicted": [16, 43], "actual": [45, 20, 11, 23, 34, 42], "hit_count": 0, "is_hit": false}, {"period": 107, "predicted": [30, 12], "actual": [8, 9, 15, 24, 18, 42], "hit_count": 0, "is_hit": false}, {"period": 108, "predicted": [45, 48], "actual": [40, 20, 39, 49, 8, 1], "hit_count": 0, "is_hit": false}, {"period": 109, "predicted": [3, 41], "actual": [47, 16, 8, 41, 6, 19], "hit_count": 1, "is_hit": true}, {"period": 110, "predicted": [2, 49], "actual": [15, 20, 14, 46, 31, 30], "hit_count": 0, "is_hit": false}, {"period": 111, "predicted": [16, 5], "actual": [2, 17, 13, 41, 28, 36], "hit_count": 0, "is_hit": false}, {"period": 112, "predicted": [10, 15], "actual": [15, 43, 38, 24, 18, 4], "hit_count": 1, "is_hit": true}, {"period": 113, "predicted": [45, 48], "actual": [2, 26, 43, 38, 3, 9], "hit_count": 0, "is_hit": false}, {"period": 114, "predicted": [45, 48], "actual": [6, 41, 5, 46, 15, 9], "hit_count": 0, "is_hit": false}, {"period": 115, "predicted": [49, 29], "actual": [14, 7, 9, 22, 10, 4], "hit_count": 0, "is_hit": false}, {"period": 116, "predicted": [29, 44], "actual": [19, 43, 35, 40, 10, 48], "hit_count": 0, "is_hit": false}, {"period": 117, "predicted": [2, 49], "actual": [33, 46, 40, 6, 22, 45], "hit_count": 0, "is_hit": false}, {"period": 118, "predicted": [49, 2], "actual": [19, 15, 6, 39, 46, 35], "hit_count": 0, "is_hit": false}, {"period": 119, "predicted": [2, 49], "actual": [21, 37, 10, 45, 19, 30], "hit_count": 0, "is_hit": false}, {"period": 120, "predicted": [10, 15], "actual": [18, 25, 5, 35, 31, 43], "hit_count": 0, "is_hit": false}, {"period": 121, "predicted": [2, 49], "actual": [25, 7, 31, 34, 23, 39], "hit_count": 0, "is_hit": false}, {"period": 122, "predicted": [2, 3], "actual": [7, 46, 3, 2, 47, 22], "hit_count": 2, "is_hit": true}, {"period": 123, "predicted": [29, 31], "actual": [37, 30, 49, 16, 9, 12], "hit_count": 0, "is_hit": false}, {"period": 124, "predicted": [16, 30], "actual": [18, 41, 9, 14, 7, 39], "hit_count": 0, "is_hit": false}, {"period": 125, "predicted": [30, 40], "actual": [18, 25, 19, 28, 1, 8], "hit_count": 0, "is_hit": false}, {"period": 126, "predicted": [43, 25], "actual": [5, 13, 10, 28, 3, 31], "hit_count": 0, "is_hit": false}, {"period": 127, "predicted": [45, 48], "actual": [45, 35, 8, 20, 4, 22], "hit_count": 1, "is_hit": true}, {"period": 128, "predicted": [49, 10], "actual": [4, 43, 18, 45, 38, 26], "hit_count": 0, "is_hit": false}, {"period": 129, "predicted": [45, 48], "actual": [34, 45, 20, 42, 19, 25], "hit_count": 1, "is_hit": true}, {"period": 130, "predicted": [26, 12], "actual": [20, 30, 10, 33, 28, 8], "hit_count": 0, "is_hit": false}, {"period": 131, "predicted": [26, 49], "actual": [13, 44, 4, 20, 36, 30], "hit_count": 0, "is_hit": false}, {"period": 132, "predicted": [16, 49], "actual": [24, 46, 26, 42, 15, 35], "hit_count": 0, "is_hit": false}, {"period": 133, "predicted": [16, 5], "actual": [45, 3, 16, 1, 21, 38], "hit_count": 1, "is_hit": true}, {"period": 134, "predicted": [41, 26], "actual": [38, 27, 4, 23, 6, 34], "hit_count": 0, "is_hit": false}, {"period": 135, "predicted": [10, 15], "actual": [32, 44, 25, 3, 34, 33], "hit_count": 0, "is_hit": false}, {"period": 136, "predicted": [2, 15], "actual": [31, 23, 25, 33, 2, 21], "hit_count": 1, "is_hit": true}, {"period": 137, "predicted": [10, 15], "actual": [39, 35, 46, 25, 48, 45], "hit_count": 0, "is_hit": false}, {"period": 138, "predicted": [41, 15], "actual": [20, 27, 39, 47, 29, 3], "hit_count": 0, "is_hit": false}, {"period": 139, "predicted": [41, 2], "actual": [38, 27, 47, 30, 45, 9], "hit_count": 0, "is_hit": false}, {"period": 140, "predicted": [19, 15], "actual": [22, 45, 2, 41, 39, 31], "hit_count": 0, "is_hit": false}, {"period": 141, "predicted": [3, 41], "actual": [30, 22, 31, 34, 39, 3], "hit_count": 1, "is_hit": true}, {"period": 142, "predicted": [2, 41], "actual": [17, 30, 8, 29, 23, 33], "hit_count": 0, "is_hit": false}, {"period": 143, "predicted": [41, 15], "actual": [27, 19, 17, 2, 24, 15], "hit_count": 1, "is_hit": true}, {"period": 144, "predicted": [44, 26], "actual": [36, 31, 19, 1, 34, 29], "hit_count": 0, "is_hit": false}, {"period": 145, "predicted": [2, 42], "actual": [30, 25, 29, 28, 10, 49], "hit_count": 0, "is_hit": false}, {"period": 146, "predicted": [10, 15], "actual": [24, 41, 25, 3, 11, 47], "hit_count": 0, "is_hit": false}, {"period": 147, "predicted": [43, 5], "actual": [47, 14, 38, 26, 19, 35], "hit_count": 0, "is_hit": false}, {"period": 148, "predicted": [49, 10], "actual": [48, 26, 5, 31, 44, 9], "hit_count": 0, "is_hit": false}, {"period": 149, "predicted": [15, 10], "actual": [44, 43, 25, 3, 29, 19], "hit_count": 0, "is_hit": false}, {"period": 150, "predicted": [2, 43], "actual": [24, 43, 18, 31, 45, 12], "hit_count": 1, "is_hit": true}, {"period": 151, "predicted": [10, 15], "actual": [29, 49, 26, 25, 35, 39], "hit_count": 0, "is_hit": false}, {"period": 152, "predicted": [41, 15], "actual": [9, 34, 17, 29, 35, 12], "hit_count": 0, "is_hit": false}, {"period": 153, "predicted": [45, 48], "actual": [42, 11, 25, 8, 45, 9], "hit_count": 1, "is_hit": true}, {"period": 154, "predicted": [10, 15], "actual": [37, 18, 45, 25, 47, 42], "hit_count": 0, "is_hit": false}, {"period": 155, "predicted": [29, 43], "actual": [6, 34, 33, 12, 37, 20], "hit_count": 0, "is_hit": false}, {"period": 156, "predicted": [30, 32], "actual": [15, 13, 38, 17, 28, 32], "hit_count": 1, "is_hit": true}, {"period": 157, "predicted": [10, 15], "actual": [37, 5, 16, 12, 17, 3], "hit_count": 0, "is_hit": false}, {"period": 158, "predicted": [29, 43], "actual": [15, 48, 4, 17, 42, 21], "hit_count": 0, "is_hit": false}, {"period": 159, "predicted": [10, 15], "actual": [45, 34, 35, 22, 24, 8], "hit_count": 0, "is_hit": false}, {"period": 160, "predicted": [41, 17], "actual": [23, 42, 32, 46, 30, 40], "hit_count": 0, "is_hit": false}, {"period": 161, "predicted": [29, 5], "actual": [49, 41, 44, 29, 38, 18], "hit_count": 1, "is_hit": true}, {"period": 162, "predicted": [2, 49], "actual": [38, 2, 22, 48, 15, 12], "hit_count": 1, "is_hit": true}, {"period": 163, "predicted": [45, 48], "actual": [38, 18, 43, 29, 10, 32], "hit_count": 0, "is_hit": false}, {"period": 164, "predicted": [10, 15], "actual": [46, 38, 14, 39, 26, 8], "hit_count": 0, "is_hit": false}, {"period": 165, "predicted": [41, 29], "actual": [23, 37, 17, 8, 42, 49], "hit_count": 0, "is_hit": false}, {"period": 166, "predicted": [19, 3], "actual": [30, 38, 18, 14, 42, 16], "hit_count": 0, "is_hit": false}, {"period": 167, "predicted": [30, 26], "actual": [11, 27, 30, 6, 36, 3], "hit_count": 1, "is_hit": true}, {"period": 168, "predicted": [2, 15], "actual": [31, 7, 40, 15, 9, 25], "hit_count": 1, "is_hit": true}, {"period": 169, "predicted": [44, 17], "actual": [11, 2, 32, 38, 29, 31], "hit_count": 0, "is_hit": false}, {"period": 170, "predicted": [10, 15], "actual": [22, 20, 36, 44, 31, 19], "hit_count": 0, "is_hit": false}, {"period": 171, "predicted": [44, 15], "actual": [15, 4, 23, 36, 27, 7], "hit_count": 1, "is_hit": true}, {"period": 172, "predicted": [49, 36], "actual": [3, 15, 4, 39, 2, 28], "hit_count": 0, "is_hit": false}, {"period": 173, "predicted": [45, 48], "actual": [46, 32, 30, 5, 42, 4], "hit_count": 0, "is_hit": false}, {"period": 174, "predicted": [45, 48], "actual": [11, 36, 22, 24, 20, 12], "hit_count": 0, "is_hit": false}, {"period": 175, "predicted": [45, 48], "actual": [37, 3, 32, 12, 8, 29], "hit_count": 0, "is_hit": false}, {"period": 176, "predicted": [10, 15], "actual": [12, 5, 24, 28, 22, 17], "hit_count": 0, "is_hit": false}, {"period": 177, "predicted": [12, 43], "actual": [31, 30, 11, 49, 22, 41], "hit_count": 0, "is_hit": false}, {"period": 178, "predicted": [10, 15], "actual": [42, 30, 37, 20, 6, 7], "hit_count": 0, "is_hit": false}, {"period": 179, "predicted": [45, 48], "actual": [42, 8, 5, 30, 29, 25], "hit_count": 0, "is_hit": false}], "奇偶平衡基线": [{"period": 2, "predicted": [41, 8], "actual": [13, 4, 10, 6, 5, 28], "hit_count": 0, "is_hit": false}, {"period": 3, "predicted": [23, 8], "actual": [28, 1, 16, 47, 18, 42], "hit_count": 0, "is_hit": false}, {"period": 4, "predicted": [11, 18], "actual": [45, 16, 17, 7, 37, 28], "hit_count": 0, "is_hit": false}, {"period": 5, "predicted": [15, 14], "actual": [35, 21, 10, 3, 32, 40], "hit_count": 0, "is_hit": false}, {"period": 6, "predicted": [1, 40], "actual": [3, 13, 12, 28, 1, 18], "hit_count": 1, "is_hit": true}, {"period": 7, "predicted": [21, 28], "actual": [32, 12, 48, 45, 39, 7], "hit_count": 0, "is_hit": false}, {"period": 8, "predicted": [33, 2], "actual": [13, 24, 48, 23, 32, 20], "hit_count": 0, "is_hit": false}, {"period": 9, "predicted": [11, 2], "actual": [23, 17, 43, 1, 10, 13], "hit_count": 0, "is_hit": false}, {"period": 10, "predicted": [43, 24], "actual": [37, 18, 17, 47, 21, 23], "hit_count": 0, "is_hit": false}, {"period": 11, "predicted": [43, 12], "actual": [7, 45, 3, 30, 1, 10], "hit_count": 0, "is_hit": false}, {"period": 12, "predicted": [31, 12], "actual": [24, 32, 10, 15, 37, 4], "hit_count": 0, "is_hit": false}, {"period": 13, "predicted": [27, 16], "actual": [42, 43, 36, 11, 25, 47], "hit_count": 0, "is_hit": false}, {"period": 14, "predicted": [43, 10], "actual": [48, 20, 30, 39, 41, 6], "hit_count": 0, "is_hit": false}, {"period": 15, "predicted": [31, 46], "actual": [13, 37, 2, 3, 43, 12], "hit_count": 0, "is_hit": false}, {"period": 16, "predicted": [7, 2], "actual": [40, 22, 27, 42, 43, 39], "hit_count": 0, "is_hit": false}, {"period": 17, "predicted": [35, 26], "actual": [42, 29, 20, 49, 22, 6], "hit_count": 0, "is_hit": false}, {"period": 18, "predicted": [27, 4], "actual": [12, 1, 24, 33, 21, 44], "hit_count": 0, "is_hit": false}, {"period": 19, "predicted": [7, 38], "actual": [3, 23, 49, 13, 39, 26], "hit_count": 0, "is_hit": false}, {"period": 20, "predicted": [37, 36], "actual": [44, 4, 47, 1, 49, 14], "hit_count": 0, "is_hit": false}, {"period": 21, "predicted": [25, 42], "actual": [39, 19, 24, 31, 32, 30], "hit_count": 0, "is_hit": false}, {"period": 22, "predicted": [9, 14], "actual": [27, 23, 26, 14, 16, 18], "hit_count": 1, "is_hit": true}, {"period": 23, "predicted": [29, 44], "actual": [15, 43, 6, 30, 16, 9], "hit_count": 0, "is_hit": false}, {"period": 24, "predicted": [41, 48], "actual": [3, 45, 18, 40, 28, 2], "hit_count": 0, "is_hit": false}, {"period": 25, "predicted": [7, 44], "actual": [17, 42, 28, 44, 5, 20], "hit_count": 1, "is_hit": true}, {"period": 26, "predicted": [37, 16], "actual": [36, 25, 7, 2, 34, 9], "hit_count": 0, "is_hit": false}, {"period": 27, "predicted": [45, 24], "actual": [2, 6, 28, 32, 45, 16], "hit_count": 1, "is_hit": true}, {"period": 28, "predicted": [29, 46], "actual": [34, 14, 29, 33, 5, 28], "hit_count": 1, "is_hit": true}, {"period": 29, "predicted": [23, 28], "actual": [14, 46, 3, 35, 43, 10], "hit_count": 0, "is_hit": false}, {"period": 30, "predicted": [49, 40], "actual": [27, 39, 23, 45, 44, 46], "hit_count": 0, "is_hit": false}, {"period": 31, "predicted": [45, 38], "actual": [30, 23, 8, 24, 33, 25], "hit_count": 0, "is_hit": false}, {"period": 32, "predicted": [19, 20], "actual": [22, 15, 13, 2, 17, 33], "hit_count": 0, "is_hit": false}, {"period": 33, "predicted": [49, 34], "actual": [41, 22, 19, 40, 46, 39], "hit_count": 0, "is_hit": false}, {"period": 34, "predicted": [3, 48], "actual": [1, 4, 21, 40, 16, 49], "hit_count": 0, "is_hit": false}, {"period": 35, "predicted": [47, 42], "actual": [27, 35, 32, 37, 6, 18], "hit_count": 0, "is_hit": false}, {"period": 36, "predicted": [11, 46], "actual": [38, 7, 19, 47, 10, 40], "hit_count": 0, "is_hit": false}, {"period": 37, "predicted": [47, 22], "actual": [35, 32, 46, 8, 37, 16], "hit_count": 0, "is_hit": false}, {"period": 38, "predicted": [31, 14], "actual": [11, 1, 47, 39, 12, 3], "hit_count": 0, "is_hit": false}, {"period": 39, "predicted": [7, 4], "actual": [41, 48, 10, 17, 34, 43], "hit_count": 0, "is_hit": false}, {"period": 40, "predicted": [7, 6], "actual": [1, 32, 35, 30, 8, 16], "hit_count": 0, "is_hit": false}, {"period": 41, "predicted": [37, 48], "actual": [48, 35, 22, 30, 26, 4], "hit_count": 1, "is_hit": true}, {"period": 42, "predicted": [21, 36], "actual": [44, 13, 39, 22, 46, 47], "hit_count": 0, "is_hit": false}, {"period": 43, "predicted": [21, 2], "actual": [4, 27, 38, 40, 19, 35], "hit_count": 0, "is_hit": false}, {"period": 44, "predicted": [41, 2], "actual": [24, 28, 46, 34, 14, 43], "hit_count": 0, "is_hit": false}, {"period": 45, "predicted": [13, 28], "actual": [40, 28, 3, 48, 31, 8], "hit_count": 1, "is_hit": true}, {"period": 46, "predicted": [49, 2], "actual": [41, 1, 24, 34, 33, 30], "hit_count": 0, "is_hit": false}, {"period": 47, "predicted": [39, 10], "actual": [14, 22, 13, 31, 20, 16], "hit_count": 0, "is_hit": false}, {"period": 48, "predicted": [37, 8], "actual": [7, 35, 23, 33, 46, 27], "hit_count": 0, "is_hit": false}, {"period": 49, "predicted": [37, 30], "actual": [49, 5, 1, 17, 31, 6], "hit_count": 0, "is_hit": false}, {"period": 50, "predicted": [21, 8], "actual": [29, 21, 46, 48, 15, 45], "hit_count": 1, "is_hit": true}, {"period": 51, "predicted": [11, 10], "actual": [27, 22, 31, 7, 13, 18], "hit_count": 0, "is_hit": false}, {"period": 52, "predicted": [37, 2], "actual": [7, 6, 17, 13, 34, 31], "hit_count": 0, "is_hit": false}, {"period": 53, "predicted": [45, 20], "actual": [33, 48, 5, 19, 13, 17], "hit_count": 0, "is_hit": false}, {"period": 54, "predicted": [41, 14], "actual": [23, 28, 30, 35, 19, 27], "hit_count": 0, "is_hit": false}, {"period": 55, "predicted": [7, 20], "actual": [47, 36, 40, 43, 11, 37], "hit_count": 0, "is_hit": false}, {"period": 56, "predicted": [21, 42], "actual": [41, 8, 33, 30, 43, 7], "hit_count": 0, "is_hit": false}, {"period": 57, "predicted": [3, 8], "actual": [10, 11, 13, 1, 40, 22], "hit_count": 0, "is_hit": false}, {"period": 58, "predicted": [17, 8], "actual": [22, 23, 33, 34, 49, 19], "hit_count": 0, "is_hit": false}, {"period": 59, "predicted": [23, 36], "actual": [40, 16, 17, 26, 36, 4], "hit_count": 1, "is_hit": true}, {"period": 60, "predicted": [1, 40], "actual": [10, 8, 17, 13, 47, 44], "hit_count": 0, "is_hit": false}, {"period": 61, "predicted": [15, 20], "actual": [42, 28, 39, 6, 23, 35], "hit_count": 0, "is_hit": false}, {"period": 62, "predicted": [11, 4], "actual": [27, 37, 10, 26, 2, 48], "hit_count": 0, "is_hit": false}, {"period": 63, "predicted": [1, 44], "actual": [42, 5, 38, 3, 49, 21], "hit_count": 0, "is_hit": false}, {"period": 64, "predicted": [29, 16], "actual": [48, 40, 3, 33, 47, 4], "hit_count": 0, "is_hit": false}, {"period": 65, "predicted": [33, 14], "actual": [30, 41, 38, 25, 35, 16], "hit_count": 0, "is_hit": false}, {"period": 66, "predicted": [7, 48], "actual": [49, 45, 27, 22, 13, 26], "hit_count": 0, "is_hit": false}, {"period": 67, "predicted": [13, 44], "actual": [15, 35, 39, 48, 45, 11], "hit_count": 0, "is_hit": false}, {"period": 68, "predicted": [1, 8], "actual": [42, 33, 47, 40, 30, 2], "hit_count": 0, "is_hit": false}, {"period": 69, "predicted": [41, 26], "actual": [44, 30, 5, 40, 6, 15], "hit_count": 0, "is_hit": false}, {"period": 70, "predicted": [9, 24], "actual": [16, 35, 46, 25, 19, 18], "hit_count": 0, "is_hit": false}, {"period": 71, "predicted": [11, 6], "actual": [32, 9, 44, 36, 23, 40], "hit_count": 0, "is_hit": false}, {"period": 72, "predicted": [21, 40], "actual": [35, 37, 48, 33, 14, 22], "hit_count": 0, "is_hit": false}, {"period": 73, "predicted": [37, 2], "actual": [43, 22, 48, 23, 33, 10], "hit_count": 0, "is_hit": false}, {"period": 74, "predicted": [43, 46], "actual": [22, 21, 1, 47, 29, 11], "hit_count": 0, "is_hit": false}, {"period": 75, "predicted": [29, 34], "actual": [30, 5, 10, 37, 32, 7], "hit_count": 0, "is_hit": false}, {"period": 76, "predicted": [1, 32], "actual": [28, 2, 14, 10, 16, 23], "hit_count": 0, "is_hit": false}, {"period": 77, "predicted": [5, 6], "actual": [5, 23, 27, 17, 11, 39], "hit_count": 1, "is_hit": true}, {"period": 78, "predicted": [15, 2], "actual": [15, 38, 18, 3, 8, 11], "hit_count": 1, "is_hit": true}, {"period": 79, "predicted": [5, 44], "actual": [4, 20, 9, 32, 29, 44], "hit_count": 1, "is_hit": true}, {"period": 80, "predicted": [31, 22], "actual": [27, 38, 29, 2, 44, 31], "hit_count": 1, "is_hit": true}, {"period": 81, "predicted": [27, 6], "actual": [48, 8, 40, 9, 18, 27], "hit_count": 1, "is_hit": true}, {"period": 82, "predicted": [29, 36], "actual": [10, 35, 4, 45, 24, 9], "hit_count": 0, "is_hit": false}, {"period": 83, "predicted": [5, 48], "actual": [45, 4, 6, 47, 46, 11], "hit_count": 0, "is_hit": false}, {"period": 84, "predicted": [13, 32], "actual": [17, 45, 24, 23, 38, 40], "hit_count": 0, "is_hit": false}, {"period": 85, "predicted": [19, 6], "actual": [32, 31, 18, 14, 8, 19], "hit_count": 1, "is_hit": true}, {"period": 86, "predicted": [37, 40], "actual": [19, 29, 31, 2, 42, 26], "hit_count": 0, "is_hit": false}, {"period": 87, "predicted": [15, 18], "actual": [30, 16, 14, 28, 29, 3], "hit_count": 0, "is_hit": false}, {"period": 88, "predicted": [43, 40], "actual": [14, 11, 39, 10, 45, 33], "hit_count": 0, "is_hit": false}, {"period": 89, "predicted": [39, 26], "actual": [45, 19, 13, 25, 15, 27], "hit_count": 0, "is_hit": false}, {"period": 90, "predicted": [3, 36], "actual": [17, 7, 23, 38, 12, 18], "hit_count": 0, "is_hit": false}, {"period": 91, "predicted": [3, 18], "actual": [35, 23, 32, 31, 8, 24], "hit_count": 0, "is_hit": false}, {"period": 92, "predicted": [7, 14], "actual": [23, 24, 36, 3, 4, 32], "hit_count": 0, "is_hit": false}, {"period": 93, "predicted": [23, 12], "actual": [16, 23, 28, 21, 27, 46], "hit_count": 1, "is_hit": true}, {"period": 94, "predicted": [19, 12], "actual": [35, 43, 31, 37, 15, 39], "hit_count": 0, "is_hit": false}, {"period": 95, "predicted": [13, 28], "actual": [4, 43, 37, 40, 24, 16], "hit_count": 0, "is_hit": false}, {"period": 96, "predicted": [19, 36], "actual": [42, 46, 25, 5, 40, 22], "hit_count": 0, "is_hit": false}, {"period": 97, "predicted": [9, 34], "actual": [1, 32, 24, 23, 4, 2], "hit_count": 0, "is_hit": false}, {"period": 98, "predicted": [17, 14], "actual": [30, 39, 31, 8, 21, 20], "hit_count": 0, "is_hit": false}, {"period": 99, "predicted": [45, 36], "actual": [1, 34, 43, 38, 6, 22], "hit_count": 0, "is_hit": false}, {"period": 100, "predicted": [43, 12], "actual": [25, 3, 34, 8, 31, 41], "hit_count": 0, "is_hit": false}, {"period": 101, "predicted": [29, 36], "actual": [20, 49, 36, 37, 42, 30], "hit_count": 1, "is_hit": true}, {"period": 102, "predicted": [15, 14], "actual": [24, 40, 7, 30, 47, 9], "hit_count": 0, "is_hit": false}, {"period": 103, "predicted": [43, 12], "actual": [16, 28, 3, 30, 42, 49], "hit_count": 0, "is_hit": false}, {"period": 104, "predicted": [5, 36], "actual": [9, 20, 33, 11, 32, 21], "hit_count": 0, "is_hit": false}, {"period": 105, "predicted": [45, 18], "actual": [12, 28, 48, 37, 18, 43], "hit_count": 1, "is_hit": true}, {"period": 106, "predicted": [39, 40], "actual": [45, 20, 11, 23, 34, 42], "hit_count": 0, "is_hit": false}, {"period": 107, "predicted": [35, 44], "actual": [8, 9, 15, 24, 18, 42], "hit_count": 0, "is_hit": false}, {"period": 108, "predicted": [9, 34], "actual": [40, 20, 39, 49, 8, 1], "hit_count": 0, "is_hit": false}, {"period": 109, "predicted": [49, 44], "actual": [47, 16, 8, 41, 6, 19], "hit_count": 0, "is_hit": false}, {"period": 110, "predicted": [15, 24], "actual": [15, 20, 14, 46, 31, 30], "hit_count": 1, "is_hit": true}, {"period": 111, "predicted": [1, 18], "actual": [2, 17, 13, 41, 28, 36], "hit_count": 0, "is_hit": false}, {"period": 112, "predicted": [19, 44], "actual": [15, 43, 38, 24, 18, 4], "hit_count": 0, "is_hit": false}, {"period": 113, "predicted": [41, 48], "actual": [2, 26, 43, 38, 3, 9], "hit_count": 0, "is_hit": false}, {"period": 114, "predicted": [25, 26], "actual": [6, 41, 5, 46, 15, 9], "hit_count": 0, "is_hit": false}, {"period": 115, "predicted": [17, 40], "actual": [14, 7, 9, 22, 10, 4], "hit_count": 0, "is_hit": false}, {"period": 116, "predicted": [29, 30], "actual": [19, 43, 35, 40, 10, 48], "hit_count": 0, "is_hit": false}, {"period": 117, "predicted": [23, 32], "actual": [33, 46, 40, 6, 22, 45], "hit_count": 0, "is_hit": false}, {"period": 118, "predicted": [17, 32], "actual": [19, 15, 6, 39, 46, 35], "hit_count": 0, "is_hit": false}, {"period": 119, "predicted": [29, 24], "actual": [21, 37, 10, 45, 19, 30], "hit_count": 0, "is_hit": false}, {"period": 120, "predicted": [23, 38], "actual": [18, 25, 5, 35, 31, 43], "hit_count": 0, "is_hit": false}, {"period": 121, "predicted": [3, 30], "actual": [25, 7, 31, 34, 23, 39], "hit_count": 0, "is_hit": false}, {"period": 122, "predicted": [49, 6], "actual": [7, 46, 3, 2, 47, 22], "hit_count": 0, "is_hit": false}, {"period": 123, "predicted": [1, 6], "actual": [37, 30, 49, 16, 9, 12], "hit_count": 0, "is_hit": false}, {"period": 124, "predicted": [11, 14], "actual": [18, 41, 9, 14, 7, 39], "hit_count": 1, "is_hit": true}, {"period": 125, "predicted": [3, 22], "actual": [18, 25, 19, 28, 1, 8], "hit_count": 0, "is_hit": false}, {"period": 126, "predicted": [43, 28], "actual": [5, 13, 10, 28, 3, 31], "hit_count": 1, "is_hit": true}, {"period": 127, "predicted": [7, 36], "actual": [45, 35, 8, 20, 4, 22], "hit_count": 0, "is_hit": false}, {"period": 128, "predicted": [15, 10], "actual": [4, 43, 18, 45, 38, 26], "hit_count": 0, "is_hit": false}, {"period": 129, "predicted": [33, 46], "actual": [34, 45, 20, 42, 19, 25], "hit_count": 0, "is_hit": false}, {"period": 130, "predicted": [19, 6], "actual": [20, 30, 10, 33, 28, 8], "hit_count": 0, "is_hit": false}, {"period": 131, "predicted": [11, 24], "actual": [13, 44, 4, 20, 36, 30], "hit_count": 0, "is_hit": false}, {"period": 132, "predicted": [27, 12], "actual": [24, 46, 26, 42, 15, 35], "hit_count": 0, "is_hit": false}, {"period": 133, "predicted": [41, 42], "actual": [45, 3, 16, 1, 21, 38], "hit_count": 0, "is_hit": false}, {"period": 134, "predicted": [1, 44], "actual": [38, 27, 4, 23, 6, 34], "hit_count": 0, "is_hit": false}, {"period": 135, "predicted": [35, 48], "actual": [32, 44, 25, 3, 34, 33], "hit_count": 0, "is_hit": false}, {"period": 136, "predicted": [45, 8], "actual": [31, 23, 25, 33, 2, 21], "hit_count": 0, "is_hit": false}, {"period": 137, "predicted": [39, 48], "actual": [39, 35, 46, 25, 48, 45], "hit_count": 2, "is_hit": true}, {"period": 138, "predicted": [35, 46], "actual": [20, 27, 39, 47, 29, 3], "hit_count": 0, "is_hit": false}, {"period": 139, "predicted": [3, 14], "actual": [38, 27, 47, 30, 45, 9], "hit_count": 0, "is_hit": false}, {"period": 140, "predicted": [17, 38], "actual": [22, 45, 2, 41, 39, 31], "hit_count": 0, "is_hit": false}, {"period": 141, "predicted": [11, 4], "actual": [30, 22, 31, 34, 39, 3], "hit_count": 0, "is_hit": false}, {"period": 142, "predicted": [29, 42], "actual": [17, 30, 8, 29, 23, 33], "hit_count": 1, "is_hit": true}, {"period": 143, "predicted": [1, 4], "actual": [27, 19, 17, 2, 24, 15], "hit_count": 0, "is_hit": false}, {"period": 144, "predicted": [27, 24], "actual": [36, 31, 19, 1, 34, 29], "hit_count": 0, "is_hit": false}, {"period": 145, "predicted": [23, 38], "actual": [30, 25, 29, 28, 10, 49], "hit_count": 0, "is_hit": false}, {"period": 146, "predicted": [37, 4], "actual": [24, 41, 25, 3, 11, 47], "hit_count": 0, "is_hit": false}, {"period": 147, "predicted": [1, 44], "actual": [47, 14, 38, 26, 19, 35], "hit_count": 0, "is_hit": false}, {"period": 148, "predicted": [29, 26], "actual": [48, 26, 5, 31, 44, 9], "hit_count": 1, "is_hit": true}, {"period": 149, "predicted": [35, 8], "actual": [44, 43, 25, 3, 29, 19], "hit_count": 0, "is_hit": false}, {"period": 150, "predicted": [5, 16], "actual": [24, 43, 18, 31, 45, 12], "hit_count": 0, "is_hit": false}, {"period": 151, "predicted": [29, 14], "actual": [29, 49, 26, 25, 35, 39], "hit_count": 1, "is_hit": true}, {"period": 152, "predicted": [3, 38], "actual": [9, 34, 17, 29, 35, 12], "hit_count": 0, "is_hit": false}, {"period": 153, "predicted": [19, 8], "actual": [42, 11, 25, 8, 45, 9], "hit_count": 1, "is_hit": true}, {"period": 154, "predicted": [17, 26], "actual": [37, 18, 45, 25, 47, 42], "hit_count": 0, "is_hit": false}, {"period": 155, "predicted": [35, 40], "actual": [6, 34, 33, 12, 37, 20], "hit_count": 0, "is_hit": false}, {"period": 156, "predicted": [15, 20], "actual": [15, 13, 38, 17, 28, 32], "hit_count": 1, "is_hit": true}, {"period": 157, "predicted": [9, 46], "actual": [37, 5, 16, 12, 17, 3], "hit_count": 0, "is_hit": false}, {"period": 158, "predicted": [19, 34], "actual": [15, 48, 4, 17, 42, 21], "hit_count": 0, "is_hit": false}, {"period": 159, "predicted": [1, 20], "actual": [45, 34, 35, 22, 24, 8], "hit_count": 0, "is_hit": false}, {"period": 160, "predicted": [39, 48], "actual": [23, 42, 32, 46, 30, 40], "hit_count": 0, "is_hit": false}, {"period": 161, "predicted": [25, 12], "actual": [49, 41, 44, 29, 38, 18], "hit_count": 0, "is_hit": false}, {"period": 162, "predicted": [3, 44], "actual": [38, 2, 22, 48, 15, 12], "hit_count": 0, "is_hit": false}, {"period": 163, "predicted": [5, 10], "actual": [38, 18, 43, 29, 10, 32], "hit_count": 1, "is_hit": true}, {"period": 164, "predicted": [43, 38], "actual": [46, 38, 14, 39, 26, 8], "hit_count": 1, "is_hit": true}, {"period": 165, "predicted": [13, 38], "actual": [23, 37, 17, 8, 42, 49], "hit_count": 0, "is_hit": false}, {"period": 166, "predicted": [13, 32], "actual": [30, 38, 18, 14, 42, 16], "hit_count": 0, "is_hit": false}, {"period": 167, "predicted": [33, 4], "actual": [11, 27, 30, 6, 36, 3], "hit_count": 0, "is_hit": false}, {"period": 168, "predicted": [21, 16], "actual": [31, 7, 40, 15, 9, 25], "hit_count": 0, "is_hit": false}, {"period": 169, "predicted": [13, 14], "actual": [11, 2, 32, 38, 29, 31], "hit_count": 0, "is_hit": false}, {"period": 170, "predicted": [49, 6], "actual": [22, 20, 36, 44, 31, 19], "hit_count": 0, "is_hit": false}, {"period": 171, "predicted": [23, 10], "actual": [15, 4, 23, 36, 27, 7], "hit_count": 1, "is_hit": true}, {"period": 172, "predicted": [43, 4], "actual": [3, 15, 4, 39, 2, 28], "hit_count": 1, "is_hit": true}, {"period": 173, "predicted": [13, 10], "actual": [46, 32, 30, 5, 42, 4], "hit_count": 0, "is_hit": false}, {"period": 174, "predicted": [33, 38], "actual": [11, 36, 22, 24, 20, 12], "hit_count": 0, "is_hit": false}, {"period": 175, "predicted": [37, 20], "actual": [37, 3, 32, 12, 8, 29], "hit_count": 1, "is_hit": true}, {"period": 176, "predicted": [27, 14], "actual": [12, 5, 24, 28, 22, 17], "hit_count": 0, "is_hit": false}, {"period": 177, "predicted": [3, 20], "actual": [31, 30, 11, 49, 22, 41], "hit_count": 0, "is_hit": false}, {"period": 178, "predicted": [39, 32], "actual": [42, 30, 37, 20, 6, 7], "hit_count": 0, "is_hit": false}, {"period": 179, "predicted": [25, 14], "actual": [42, 8, 5, 30, 29, 25], "hit_count": 1, "is_hit": true}]}}