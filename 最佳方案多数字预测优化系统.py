#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最佳方案多数字预测优化系统
使用奇偶平衡和29%理论马尔可夫生成多个候选数字，然后挑选最佳组合
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
import json
from datetime import datetime
from itertools import combinations
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class MultiNumberPredictionSystem:
    """多数字预测优化系统"""
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.full_data = None
        self.train_data = None
        self.test_data = None
        
        # 预测方案配置
        self.prediction_methods = {
            '奇偶平衡': self._odd_even_multi_prediction,
            '29%理论马尔可夫': self._theoretical_markov_multi_prediction,
            '混合策略': self._hybrid_multi_prediction
        }
        
        self.results = {}
        
    def load_and_prepare_data(self):
        """加载数据并分割"""
        print(f"🔢 多数字预测优化系统")
        print("=" * 60)
        
        try:
            # 加载完整数据
            self.full_data = pd.read_csv(self.data_file)
            self.full_data = self.full_data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            print(f"✅ 完整数据加载: {len(self.full_data)}期")
            
            # 数据分割：2023-2024年作为训练集
            self.train_data = self.full_data[
                (self.full_data['年份'] >= 2023) & 
                (self.full_data['年份'] <= 2024)
            ].copy()
            
            # 测试集：2025年1-179期
            self.test_data = self.full_data[
                (self.full_data['年份'] == 2025) & 
                (self.full_data['期号'] <= 179)
            ].copy()
            
            print(f"✅ 训练集: {len(self.train_data)}期 (2023-2024年)")
            print(f"✅ 测试集: {len(self.test_data)}期 (2025年1-179期)")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def _build_markov_model(self):
        """构建29%理论马尔可夫模型"""
        if hasattr(self, '_markov_prob'):
            return
        
        print(f"🔧 构建29%理论马尔可夫模型...")
        
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 计算转移概率，使用拉普拉斯平滑
        self._markov_prob = {}
        for curr_num in range(1, 50):
            if curr_num in transition_count:
                total = sum(transition_count[curr_num].values())
                smoothed_total = total + 49
                self._markov_prob[curr_num] = {}
                
                for next_num in range(1, 50):
                    count = transition_count[curr_num].get(next_num, 0)
                    self._markov_prob[curr_num][next_num] = (count + 1) / smoothed_total
            else:
                self._markov_prob[curr_num] = {
                    next_num: 1/49 for next_num in range(1, 50)
                }
        
        print(f"✅ 马尔可夫模型构建完成")
    
    def _odd_even_multi_prediction(self, prev_numbers, period_info, num_candidates=10):
        """奇偶平衡多数字预测"""
        odd_candidates = [n for n in range(1, 50) if n % 2 == 1]
        even_candidates = [n for n in range(1, 50) if n % 2 == 0]
        
        # 生成多个奇偶组合
        candidates = []
        np.random.seed(42 + period_info['period'])  # 确保可重现
        
        for _ in range(num_candidates):
            odd_choice = np.random.choice(odd_candidates)
            even_choice = np.random.choice(even_candidates)
            candidates.append([odd_choice, even_choice])
        
        return candidates
    
    def _theoretical_markov_multi_prediction(self, prev_numbers, period_info, num_candidates=10):
        """29%理论马尔可夫多数字预测"""
        if not hasattr(self, '_markov_prob'):
            self._build_markov_model()
        
        candidates = []
        
        for seed_offset in range(num_candidates):
            np.random.seed(42 + period_info['period'] + seed_offset)
            
            number_probs = defaultdict(float)
            total_prob = 0.0
            
            for prev_num in prev_numbers:
                if prev_num in self._markov_prob:
                    for next_num, prob in self._markov_prob[prev_num].items():
                        number_probs[next_num] += prob
                        total_prob += prob
            
            if total_prob > 0:
                for num in number_probs:
                    number_probs[num] /= total_prob
            
            # 添加随机扰动
            perturbation = 0.05
            for num in number_probs:
                noise = np.random.normal(0, perturbation * number_probs[num])
                number_probs[num] = max(0, number_probs[num] + noise)
            
            if len(number_probs) >= 2:
                sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
                candidates.append([num for num, prob in sorted_numbers[:2]])
            else:
                candidates.append([1, 2])
        
        return candidates
    
    def _hybrid_multi_prediction(self, prev_numbers, period_info, num_candidates=10):
        """混合策略多数字预测"""
        # 一半使用奇偶平衡，一半使用马尔可夫
        half = num_candidates // 2
        
        odd_even_candidates = self._odd_even_multi_prediction(prev_numbers, period_info, half)
        markov_candidates = self._theoretical_markov_multi_prediction(prev_numbers, period_info, num_candidates - half)
        
        return odd_even_candidates + markov_candidates
    
    def run_multi_prediction_validation(self):
        """运行多数字预测验证"""
        print(f"\n🎯 开始多数字预测验证")
        print("=" * 60)
        
        # 初始化结果存储
        for method_name in self.prediction_methods.keys():
            self.results[method_name] = {
                'all_candidates': [],
                'selected_predictions': [],
                'hits': [],
                'total_periods': 0,
                'hit_periods': 0,
                'hit_rate': 0.0,
                'selection_strategy': 'best_historical'
            }
        
        # 逐期预测验证
        print(f"预测期数: {len(self.test_data)}期")
        print(f"每期生成10个候选组合，选择历史表现最佳的组合")
        print()
        
        for idx, test_row in self.test_data.iterrows():
            period = test_row['期号']
            actual_numbers = [test_row[f'数字{j}'] for j in range(1, 7)]
            
            # 获取前一期数字作为预测输入
            if idx == self.test_data.index[0]:
                prev_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                prev_numbers = set([self.test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            period_info = {'year': test_row['年份'], 'period': period}
            
            # 对每个预测方法进行多数字预测
            for method_name, prediction_func in self.prediction_methods.items():
                try:
                    # 生成多个候选预测
                    candidates = prediction_func(prev_numbers, period_info, num_candidates=10)
                    
                    # 选择最佳候选（基于历史表现）
                    selected_prediction = self._select_best_candidate(
                        candidates, method_name, period, idx
                    )
                    
                    # 计算命中情况
                    predicted_set = set(selected_prediction)
                    actual_set = set(actual_numbers)
                    hit_count = len(predicted_set & actual_set)
                    is_hit = hit_count >= 1
                    
                    # 记录结果
                    self.results[method_name]['all_candidates'].append(candidates)
                    self.results[method_name]['selected_predictions'].append(selected_prediction)
                    self.results[method_name]['hits'].append(is_hit)
                    self.results[method_name]['total_periods'] += 1
                    if is_hit:
                        self.results[method_name]['hit_periods'] += 1
                        
                except Exception as e:
                    print(f"⚠️ {method_name}预测失败: {e}")
        
        # 计算最终命中率
        for method_name in self.results:
            result = self.results[method_name]
            if result['total_periods'] > 0:
                result['hit_rate'] = result['hit_periods'] / result['total_periods']
        
        print(f"✅ 多数字预测验证完成")
    
    def _select_best_candidate(self, candidates, method_name, current_period, current_idx):
        """选择最佳候选预测"""
        if current_idx <= 10:  # 前10期使用简单策略
            return candidates[0]
        
        # 基于历史表现选择最佳候选
        candidate_scores = []
        
        # 获取最近10期的历史数据进行评估
        recent_test_data = self.test_data.iloc[max(0, current_idx-10):current_idx]
        
        for candidate in candidates:
            score = self._evaluate_candidate_historical_performance(
                candidate, recent_test_data, method_name
            )
            candidate_scores.append(score)
        
        # 选择得分最高的候选
        best_idx = np.argmax(candidate_scores)
        return candidates[best_idx]
    
    def _evaluate_candidate_historical_performance(self, candidate, historical_data, method_name):
        """评估候选预测的历史表现"""
        if len(historical_data) == 0:
            return 0.5  # 默认分数
        
        # 计算该候选在历史数据上的命中率
        hits = 0
        total = len(historical_data)
        
        for _, row in historical_data.iterrows():
            actual_numbers = [row[f'数字{j}'] for j in range(1, 7)]
            predicted_set = set(candidate)
            actual_set = set(actual_numbers)
            
            if len(predicted_set & actual_set) >= 1:
                hits += 1
        
        return hits / total if total > 0 else 0.5
    
    def analyze_selection_effectiveness(self):
        """分析选择策略的有效性"""
        print(f"\n📊 选择策略有效性分析")
        print("=" * 60)
        
        for method_name, result in self.results.items():
            print(f"\n{method_name} 分析:")
            print("-" * 40)
            
            # 计算如果随机选择的平均表现
            random_hit_rates = []
            all_candidates = result['all_candidates']
            
            for period_idx, candidates in enumerate(all_candidates):
                if period_idx < len(self.test_data):
                    actual_numbers = [self.test_data.iloc[period_idx][f'数字{j}'] for j in range(1, 7)]
                    actual_set = set(actual_numbers)
                    
                    period_hit_rates = []
                    for candidate in candidates:
                        predicted_set = set(candidate)
                        hit_count = len(predicted_set & actual_set)
                        is_hit = hit_count >= 1
                        period_hit_rates.append(1 if is_hit else 0)
                    
                    if period_hit_rates:
                        random_hit_rates.append(np.mean(period_hit_rates))
            
            avg_random_hit_rate = np.mean(random_hit_rates) if random_hit_rates else 0
            actual_hit_rate = result['hit_rate']
            
            print(f"实际选择命中率: {actual_hit_rate:.3f}")
            print(f"随机选择平均命中率: {avg_random_hit_rate:.3f}")
            print(f"选择策略提升: {actual_hit_rate - avg_random_hit_rate:+.3f}")
            
            # 分析最佳候选的分布
            if all_candidates:
                best_candidate_positions = []
                for period_idx, candidates in enumerate(all_candidates):
                    if period_idx < len(self.test_data):
                        actual_numbers = [self.test_data.iloc[period_idx][f'数字{j}'] for j in range(1, 7)]
                        actual_set = set(actual_numbers)
                        
                        candidate_hits = []
                        for candidate in candidates:
                            predicted_set = set(candidate)
                            hit_count = len(predicted_set & actual_set)
                            candidate_hits.append(hit_count)
                        
                        if candidate_hits:
                            best_position = np.argmax(candidate_hits)
                            best_candidate_positions.append(best_position)
                
                if best_candidate_positions:
                    avg_best_position = np.mean(best_candidate_positions)
                    print(f"最佳候选平均位置: {avg_best_position:.1f} (0-9)")
                    print(f"选择策略准确度: {(9-avg_best_position)/9*100:.1f}%")
    
    def generate_comprehensive_report(self):
        """生成综合报告"""
        print(f"\n📋 多数字预测综合报告")
        print("=" * 70)
        
        # 按命中率排序
        sorted_results = sorted(self.results.items(), key=lambda x: x[1]['hit_rate'], reverse=True)
        
        print(f"{'排名':<4} {'预测方法':<20} {'总期数':<8} {'命中期数':<8} {'命中率':<10} {'vs单一预测'}")
        print("-" * 75)
        
        # 单一预测基准（之前的结果）
        single_prediction_baselines = {
            '奇偶平衡': 0.292,
            '29%理论马尔可夫': 0.287,
            '混合策略': 0.290  # 估算值
        }
        
        for rank, (method_name, result) in enumerate(sorted_results, 1):
            hit_rate = result['hit_rate']
            total_periods = result['total_periods']
            hit_periods = result['hit_periods']
            
            # 与单一预测对比
            baseline_name = method_name
            if baseline_name in single_prediction_baselines:
                baseline_rate = single_prediction_baselines[baseline_name]
                improvement = hit_rate - baseline_rate
                vs_single = f"{improvement:+.3f}"
            else:
                vs_single = "N/A"
            
            medal = "🥇" if rank == 1 else "🥈" if rank == 2 else "🥉" if rank == 3 else "  "
            
            print(f"{medal:<4} {method_name:<20} {total_periods:<8} {hit_periods:<8} {hit_rate:.3f}      {vs_single}")
        
        # 详细分析
        print(f"\n📈 详细性能分析:")
        print("-" * 50)
        
        hit_rates = [result['hit_rate'] for result in self.results.values()]
        print(f"平均命中率: {np.mean(hit_rates):.3f}")
        print(f"最高命中率: {np.max(hit_rates):.3f}")
        print(f"最低命中率: {np.min(hit_rates):.3f}")
        print(f"标准差: {np.std(hit_rates):.3f}")
        
        # 与理论基线对比
        print(f"\n📊 基线对比:")
        print("-" * 30)
        print(f"29.2%理论基线: 0.292")
        print(f"理论随机基线: 0.082")
        
        best_result = sorted_results[0]
        best_method = best_result[0]
        best_rate = best_result[1]['hit_rate']
        
        print(f"\n🏆 最佳方法: {best_method}")
        print(f"命中率: {best_rate:.3f}")
        print(f"vs 29.2%基线: {best_rate - 0.292:+.3f}")
        print(f"vs 随机基线: {best_rate - 0.082:+.3f}")
    
    def show_prediction_examples(self):
        """显示预测示例"""
        print(f"\n🔍 预测示例展示")
        print("=" * 60)
        
        # 显示前5期的详细预测过程
        for method_name, result in self.results.items():
            print(f"\n{method_name} 前5期预测示例:")
            print("-" * 50)
            
            for i in range(min(5, len(result['all_candidates']))):
                period = i + 1
                candidates = result['all_candidates'][i]
                selected = result['selected_predictions'][i]
                is_hit = result['hits'][i]
                
                if i < len(self.test_data):
                    actual = [self.test_data.iloc[i][f'数字{j}'] for j in range(1, 7)]
                    
                    print(f"第{period}期:")
                    print(f"  候选预测: {candidates[:3]}... (共10个)")
                    print(f"  选择预测: {selected}")
                    print(f"  实际开奖: {actual}")
                    print(f"  命中情况: {'✅' if is_hit else '❌'}")
                    print()
    
    def save_results(self):
        """保存结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f"多数字预测优化结果_{timestamp}.json"
        
        # 处理numpy类型
        def convert_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_types(v) for v in obj]
            else:
                return obj
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(convert_types(self.results), f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 结果已保存: {results_file}")

def main():
    """主函数"""
    print("🔢 最佳方案多数字预测优化系统")
    print("使用奇偶平衡和29%理论马尔可夫生成多个候选，选择最佳组合")
    print("=" * 80)
    
    system = MultiNumberPredictionSystem()
    
    # 1. 加载和准备数据
    if not system.load_and_prepare_data():
        return
    
    # 2. 运行多数字预测验证
    system.run_multi_prediction_validation()
    
    # 3. 分析选择策略有效性
    system.analyze_selection_effectiveness()
    
    # 4. 生成综合报告
    system.generate_comprehensive_report()
    
    # 5. 显示预测示例
    system.show_prediction_examples()
    
    # 6. 保存结果
    system.save_results()
    
    print(f"\n🎉 多数字预测优化验证完成")

if __name__ == "__main__":
    main()
