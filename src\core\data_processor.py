#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Data Processor for Lottery Prediction System
Handles data loading, cleaning, feature engineering, and train/test splitting
"""

import pandas as pd
import numpy as np
import re
from typing import Tuple, Dict, List
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')


class LotteryDataProcessor:
    """
    Comprehensive data processor for lottery prediction system
    Focuses on 6 main numbers (excluding special codes)
    """
    
    def __init__(self, data_file: str = '数据集.txt'):
        self.data_file = data_file
        self.df = None
        self.train_df = None
        self.test_df = None
        self.feature_columns = []
        
    def load_and_clean_data(self) -> pd.DataFrame:
        """
        Load and clean raw lottery data
        Returns: Cleaned DataFrame with proper structure
        """
        print("=== 数据加载与清洗 ===")
        
        # Read raw data
        with open(self.data_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        data = []
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Parse each line using regex
            # Format: 180 期：14，37，28，43，01，41，特码是 29
            match = re.match(r'(\d+)\s*期：(.+?)，特码是\s*(\d+)', line)
            if match:
                period = int(match.group(1))
                numbers_str = match.group(2)
                special_code = int(match.group(3))
                
                # Parse 6 main numbers
                numbers = [int(x.strip()) for x in numbers_str.split('，')]
                
                if len(numbers) == 6:
                    # Sort numbers for consistency (optional)
                    numbers_sorted = sorted(numbers)
                    row = [period] + numbers_sorted + [special_code]
                    data.append(row)
        
        # Create DataFrame
        columns = ['期号', '数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        self.df = pd.DataFrame(data, columns=columns)
        
        # Sort by period number (ascending order)
        self.df = self.df.sort_values('期号').reset_index(drop=True)
        
        print(f"数据加载完成，共{len(self.df)}期数据")
        print("数据预览：")
        print(self.df.head())
        
        # Save cleaned data
        self.df.to_csv('lottery_data_clean.csv', index=False, encoding='utf-8-sig')
        print("清洗后数据已保存为 lottery_data_clean.csv")
        
        return self.df
    
    def split_dataset(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Split dataset into training and testing sets
        Training: periods 1-160
        Testing: periods 161-180
        """
        print("\n=== 数据集划分 ===")
        
        if self.df is None:
            raise ValueError("请先加载数据")
        
        # Split based on period numbers
        train_df = self.df[self.df['期号'] <= 160].copy()
        test_df = self.df[self.df['期号'] >= 161].copy()
        
        self.train_df = train_df
        self.test_df = test_df
        
        print(f"训练集：第1-160期，共{len(train_df)}期数据")
        print(f"测试集：第161-180期，共{len(test_df)}期数据")
        
        return train_df, test_df
    
    def create_basic_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create basic statistical features
        """
        features_df = df.copy()
        
        # Basic statistics for 6 main numbers
        main_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        # Sum and average
        features_df['和值'] = features_df[main_cols].sum(axis=1)
        features_df['平均值'] = features_df[main_cols].mean(axis=1)
        
        # Range and variance
        features_df['极差'] = features_df[main_cols].max(axis=1) - features_df[main_cols].min(axis=1)
        features_df['方差'] = features_df[main_cols].var(axis=1)
        features_df['标准差'] = features_df[main_cols].std(axis=1)
        
        # Odd/Even counts
        odd_count = features_df[main_cols].apply(lambda x: sum(num % 2 for num in x), axis=1)
        features_df['奇数个数'] = odd_count
        features_df['偶数个数'] = 6 - odd_count
        
        # Big/Small counts (threshold: 25)
        big_count = features_df[main_cols].apply(lambda x: sum(num > 25 for num in x), axis=1)
        features_df['大数个数'] = big_count
        features_df['小数个数'] = 6 - big_count
        
        return features_df
    
    def create_time_series_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create enhanced time series features using rolling windows
        """
        features_df = df.copy()
        window_sizes = [3, 5, 7]  # Reduced window sizes to avoid overfitting
        main_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']

        # Rolling statistics for sum
        for window in window_sizes:
            features_df[f'和值_mean_{window}'] = features_df['和值'].rolling(window=window).mean()
            features_df[f'和值_std_{window}'] = features_df['和值'].rolling(window=window).std()

        # Rolling statistics for key indicators
        for window in [3, 5]:  # Only use smaller windows for these
            features_df[f'奇数个数_mean_{window}'] = features_df['奇数个数'].rolling(window=window).mean()
            features_df[f'大数个数_mean_{window}'] = features_df['大数个数'].rolling(window=window).mean()
            features_df[f'极差_mean_{window}'] = features_df['极差'].rolling(window=window).mean()

        # Add trend features
        features_df['和值_trend_3'] = features_df['和值'].diff(3)
        features_df['奇数个数_trend_3'] = features_df['奇数个数'].diff(3)

        # Add frequency features for each number position
        for col in main_cols:
            # Most frequent number in recent periods
            features_df[f'{col}_mode_5'] = features_df[col].rolling(window=5).apply(
                lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else x.mean(), raw=False
            )

        return features_df
    
    def create_lag_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create selective lag features for time series prediction
        """
        features_df = df.copy()
        main_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']

        # Create lag features for each number position (reduced lags)
        for lag in [1, 2]:  # Reduced from [1,2,3] to avoid overfitting
            for col in main_cols:
                features_df[f'{col}_lag_{lag}'] = features_df[col].shift(lag)

        # Create lag features for key statistics only
        for lag in [1, 2]:
            features_df[f'和值_lag_{lag}'] = features_df['和值'].shift(lag)
            features_df[f'奇数个数_lag_{lag}'] = features_df['奇数个数'].shift(lag)
            features_df[f'大数个数_lag_{lag}'] = features_df['大数个数'].shift(lag)

        # Add difference features (change from previous period)
        for col in main_cols:
            features_df[f'{col}_diff_1'] = features_df[col].diff(1)

        return features_df

    def select_features(self, features_df: pd.DataFrame, target_col: str,
                       max_features: int = 15) -> List[str]:
        """
        Select the most important features for a specific target
        """
        # Get feature columns (exclude target columns)
        target_cols = ['期号', '数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        feature_cols = [col for col in features_df.columns if col not in target_cols]

        # Remove features with too many NaN values
        valid_features = []
        for col in feature_cols:
            if features_df[col].notna().sum() > len(features_df) * 0.7:  # At least 70% valid data
                valid_features.append(col)

        if len(valid_features) <= max_features:
            return valid_features

        # Prepare data for feature selection
        X = features_df[valid_features].fillna(0)
        y = features_df[target_col]

        # Remove rows where target is NaN
        valid_idx = y.notna()
        X = X[valid_idx]
        y = y[valid_idx]

        if len(X) == 0:
            return valid_features[:max_features]

        # Use mutual information for feature selection
        try:
            selector = SelectKBest(score_func=mutual_info_regression, k=max_features)
            selector.fit(X, y)
            selected_features = [valid_features[i] for i in selector.get_support(indices=True)]
            return selected_features
        except:
            # Fallback to correlation-based selection
            correlations = X.corrwith(y).abs().sort_values(ascending=False)
            return correlations.head(max_features).index.tolist()

    def feature_engineering(self) -> pd.DataFrame:
        """
        Complete feature engineering pipeline with feature selection
        """
        print("\n=== 特征工程 ===")

        if self.df is None:
            raise ValueError("请先加载数据")

        # Start with basic features
        features_df = self.create_basic_features(self.df)

        # Add time series features
        features_df = self.create_time_series_features(features_df)

        # Add lag features
        features_df = self.create_lag_features(features_df)

        # Remove rows with NaN values (due to rolling windows and lags)
        features_df = features_df.dropna().reset_index(drop=True)

        print(f"初始特征创建完成，共{len([col for col in features_df.columns if col not in ['期号', '数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']])}个特征")

        # Store all feature columns for later use
        target_cols = ['期号', '数字1', '数字2', '数字3', '数字4', '数字5', '数字6', '特码']
        self.all_feature_columns = [col for col in features_df.columns if col not in target_cols]

        # For now, use all features (selection will be done per target in model training)
        self.feature_columns = self.all_feature_columns

        print(f"特征工程完成，保留{len(self.feature_columns)}个特征用于建模")

        # Save enhanced features
        features_df.to_csv('lottery_features.csv', index=False, encoding='utf-8-sig')
        print("特征数据已保存为 lottery_features.csv")

        return features_df
    
    def get_processed_data(self) -> Dict:
        """
        Get fully processed data ready for model training
        """
        if self.df is None:
            self.load_and_clean_data()
        
        # Apply feature engineering
        features_df = self.feature_engineering()
        
        # Split dataset
        train_df, test_df = self.split_dataset()
        
        # Apply same feature engineering to train/test splits
        train_features = self.create_basic_features(train_df)
        train_features = self.create_time_series_features(train_features)
        train_features = self.create_lag_features(train_features)
        train_features = train_features.dropna().reset_index(drop=True)
        
        test_features = self.create_basic_features(test_df)
        test_features = self.create_time_series_features(test_features)
        test_features = self.create_lag_features(test_features)
        test_features = test_features.dropna().reset_index(drop=True)
        
        return {
            'full_data': features_df,
            'train_data': train_features,
            'test_data': test_features,
            'feature_columns': self.feature_columns,
            'target_columns': ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        }


if __name__ == "__main__":
    # Test the data processor
    processor = LotteryDataProcessor()
    data = processor.get_processed_data()
    
    print("\n=== 数据处理完成 ===")
    print(f"完整数据集: {len(data['full_data'])}行")
    print(f"训练集: {len(data['train_data'])}行")
    print(f"测试集: {len(data['test_data'])}行")
    print(f"特征数量: {len(data['feature_columns'])}")
