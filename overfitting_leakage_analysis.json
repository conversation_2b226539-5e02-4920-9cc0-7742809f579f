{
  "timestamp": "2025-07-22T21:30:34.298807",
  "analysis_results": {
    "overfitting_analysis": {
      "temporal_performance": {
        "window_performances": [
          {
            "start_period": 2,
            "end_period": 21,
            "hit_rate": 0.2,
            "sample_size": 20
          },
          {
            "start_period": 7,
            "end_period": 26,
            "hit_rate": 0.25,
            "sample_size": 20
          },
          {
            "start_period": 12,
            "end_period": 31,
            "hit_rate": 0.3,
            "sample_size": 20
          },
          {
            "start_period": 17,
            "end_period": 36,
            "hit_rate": 0.3,
            "sample_size": 20
          },
          {
            "start_period": 22,
            "end_period": 41,
            "hit_rate": 0.35,
            "sample_size": 20
          },
          {
            "start_period": 27,
            "end_period": 46,
            "hit_rate": 0.25,
            "sample_size": 20
          },
          {
            "start_period": 32,
            "end_period": 51,
            "hit_rate": 0.15,
            "sample_size": 20
          },
          {
            "start_period": 37,
            "end_period": 56,
            "hit_rate": 0.1,
            "sample_size": 20
          },
          {
            "start_period": 42,
            "end_period": 61,
            "hit_rate": 0.1,
            "sample_size": 20
          },
          {
            "start_period": 47,
            "end_period": 66,
            "hit_rate": 0.2,
            "sample_size": 20
          },
          {
            "start_period": 52,
            "end_period": 71,
            "hit_rate": 0.3,
            "sample_size": 20
          },
          {
            "start_period": 57,
            "end_period": 76,
            "hit_rate": 0.35,
            "sample_size": 20
          },
          {
            "start_period": 62,
            "end_period": 81,
            "hit_rate": 0.35,
            "sample_size": 20
          },
          {
            "start_period": 67,
            "end_period": 86,
            "hit_rate": 0.35,
            "sample_size": 20
          },
          {
            "start_period": 72,
            "end_period": 91,
            "hit_rate": 0.3,
            "sample_size": 20
          },
          {
            "start_period": 77,
            "end_period": 96,
            "hit_rate": 0.3,
            "sample_size": 20
          },
          {
            "start_period": 82,
            "end_period": 101,
            "hit_rate": 0.3,
            "sample_size": 20
          },
          {
            "start_period": 87,
            "end_period": 106,
            "hit_rate": 0.3,
            "sample_size": 20
          },
          {
            "start_period": 92,
            "end_period": 111,
            "hit_rate": 0.3,
            "sample_size": 20
          },
          {
            "start_period": 97,
            "end_period": 116,
            "hit_rate": 0.2,
            "sample_size": 20
          },
          {
            "start_period": 102,
            "end_period": 121,
            "hit_rate": 0.25,
            "sample_size": 20
          },
          {
            "start_period": 107,
            "end_period": 126,
            "hit_rate": 0.25,
            "sample_size": 20
          },
          {
            "start_period": 112,
            "end_period": 131,
            "hit_rate": 0.35,
            "sample_size": 20
          },
          {
            "start_period": 117,
            "end_period": 136,
            "hit_rate": 0.45,
            "sample_size": 20
          },
          {
            "start_period": 122,
            "end_period": 141,
            "hit_rate": 0.35,
            "sample_size": 20
          },
          {
            "start_period": 127,
            "end_period": 146,
            "hit_rate": 0.3,
            "sample_size": 20
          },
          {
            "start_period": 132,
            "end_period": 151,
            "hit_rate": 0.3,
            "sample_size": 20
          },
          {
            "start_period": 137,
            "end_period": 156,
            "hit_rate": 0.35,
            "sample_size": 20
          },
          {
            "start_period": 142,
            "end_period": 161,
            "hit_rate": 0.35,
            "sample_size": 20
          },
          {
            "start_period": 147,
            "end_period": 166,
            "hit_rate": 0.3,
            "sample_size": 20
          },
          {
            "start_period": 152,
            "end_period": 171,
            "hit_rate": 0.3,
            "sample_size": 20
          },
          {
            "start_period": 157,
            "end_period": 176,
            "hit_rate": 0.2,
            "sample_size": 20
          },
          {
            "start_period": 162,
            "end_period": 181,
            "hit_rate": 0.25,
            "sample_size": 20
          },
          {
            "start_period": 167,
            "end_period": 186,
            "hit_rate": 0.35,
            "sample_size": 20
          },
          {
            "start_period": 172,
            "end_period": 191,
            "hit_rate": 0.3,
            "sample_size": 20
          },
          {
            "start_period": 177,
            "end_period": 196,
            "hit_rate": 0.4,
            "sample_size": 20
          }
        ],
        "performance_trend": 0.002741312741312746,
        "performance_volatility": 0.07510281019219874,
        "avg_performance": 0.28611111111111115,
        "performance_decline": false
      },
      "train_test_gap": {
        "train_hit_rate": 0.2733812949640288,
        "test_hit_rate": 0.3333333333333333,
        "performance_gap": -0.05995203836930452,
        "relative_gap": -0.21929824561403494,
        "statistical_significance": false,
        "p_value": 0.4939091476751164,
        "train_sample_size": 139,
        "test_sample_size": 60
      },
      "model_complexity": {
        "complexity_indicators": {
          "method_diversity": 1,
          "prediction_space_coverage": 0.20408163265306123,
          "confidence_granularity": 1,
          "confidence_range": 0.0,
          "parameter_count_estimate": 1
        },
        "complexity_score": 20.70408163265306,
        "methods_distribution": {
          "Best_Ensemble_Method_v3.0": 203
        },
        "is_overly_complex": false
      },
      "generalization_ability": {
        "cv_results": [
          {
            "train_performance": 0.26666666666666666,
            "test_performance": 0.3,
            "performance_gap": -0.033333333333333326
          },
          {
            "train_performance": 0.3,
            "test_performance": 0.0,
            "performance_gap": 0.3
          },
          {
            "train_performance": 0.23333333333333334,
            "test_performance": 0.2,
            "performance_gap": 0.033333333333333326
          },
          {
            "train_performance": 0.16666666666666666,
            "test_performance": 0.4,
            "performance_gap": -0.23333333333333336
          },
          {
            "train_performance": 0.2,
            "test_performance": 0.3,
            "performance_gap": -0.09999999999999998
          },
          {
            "train_performance": 0.3,
            "test_performance": 0.3,
            "performance_gap": 0.0
          },
          {
            "train_performance": 0.3333333333333333,
            "test_performance": 0.3,
            "performance_gap": 0.033333333333333326
          },
          {
            "train_performance": 0.3,
            "test_performance": 0.3,
            "performance_gap": 0.0
          },
          {
            "train_performance": 0.3,
            "test_performance": 0.2,
            "performance_gap": 0.09999999999999998
          },
          {
            "train_performance": 0.26666666666666666,
            "test_performance": 0.5,
            "performance_gap": -0.23333333333333334
          },
          {
            "train_performance": 0.3333333333333333,
            "test_performance": 0.2,
            "performance_gap": 0.1333333333333333
          },
          {
            "train_performance": 0.3,
            "test_performance": 0.4,
            "performance_gap": -0.10000000000000003
          },
          {
            "train_performance": 0.36666666666666664,
            "test_performance": 0.3,
            "performance_gap": 0.06666666666666665
          },
          {
            "train_performance": 0.3,
            "test_performance": 0.3,
            "performance_gap": 0.0
          },
          {
            "train_performance": 0.3333333333333333,
            "test_performance": 0.2,
            "performance_gap": 0.1333333333333333
          },
          {
            "train_performance": 0.26666666666666666,
            "test_performance": 0.4,
            "performance_gap": -0.13333333333333336
          }
        ],
        "avg_train_performance": 0.28541666666666665,
        "avg_test_performance": 0.2875,
        "avg_performance_gap": -0.0020833333333333433,
        "performance_gap_std": 0.13461299982625088,
        "generalization_score": 30.610166753541222,
        "cv_folds": 16
      },
      "prediction_stability": {
        "avg_prediction_change": 1.8535353535353536,
        "avg_confidence_change": 0.0,
        "prediction_change_std": 0.3535714267310533,
        "confidence_change_std": 0.0,
        "stability_score": 7.323232323232318,
        "total_predictions_analyzed": 198
      },
      "overfitting_risk_score": 36.974862818018295
    },
    "data_leakage_analysis": {
      "temporal_leakage": {
        "temporal_violations": [
          {
            "period": 1,
            "prediction_time": 