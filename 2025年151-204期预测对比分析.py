#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2025年151-204期预测数据与真实数据对比分析
生成完整的预测对比CSV文件，包含命中率等详细数据
"""

import pandas as pd
import numpy as np
from datetime import datetime
from collections import defaultdict, Counter
import warnings
warnings.filterwarnings('ignore')

class PredictionComparisonAnalysis:
    """2025年151-204期预测对比分析系统"""
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.fixed_results_file = "修复后预测验证结果.csv"
        self.output_file = "2025年151-204期预测对比分析.csv"
        
        # 数据存储
        self.full_data = None
        self.fixed_results = None
        self.comparison_results = []
        
    def load_data(self):
        """加载数据"""
        try:
            # 加载原始数据
            self.full_data = pd.read_csv(self.data_file, encoding='utf-8')
            self.full_data = self.full_data.dropna()
            print(f"✅ 加载原始数据: {len(self.full_data)} 条记录")
            
            # 加载修复后的预测结果
            try:
                self.fixed_results = pd.read_csv(self.fixed_results_file, encoding='utf-8')
                print(f"✅ 加载修复后预测结果: {len(self.fixed_results)} 条记录")
            except FileNotFoundError:
                print("⚠️ 未找到修复后预测结果文件，将使用原始数据生成预测")
                self.fixed_results = None
            
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def get_actual_data_for_period(self, year, period):
        """获取指定期号的实际开奖数据"""
        try:
            actual_data = self.full_data[
                (self.full_data['年份'] == year) & 
                (self.full_data['期号'] == period)
            ]
            
            if len(actual_data) == 0:
                return None
            
            actual_row = actual_data.iloc[0]
            actual_numbers = []
            for i in range(1, 7):
                num = actual_row[f'数字{i}']
                if pd.notna(num):
                    actual_numbers.append(int(num))
            
            return {
                'year': int(actual_row['年份']),
                'period': int(actual_row['期号']),
                'numbers': actual_numbers,
                'date': actual_row.get('开奖日期', '未知'),
                'sum': sum(actual_numbers),
                'range': max(actual_numbers) - min(actual_numbers),
                'odd_count': sum(1 for n in actual_numbers if n % 2 == 1),
                'even_count': sum(1 for n in actual_numbers if n % 2 == 0)
            }
            
        except Exception as e:
            print(f"⚠️ 获取第{period}期实际数据失败: {e}")
            return None
    
    def get_prediction_for_period(self, period):
        """获取指定期号的预测数据"""
        try:
            if self.fixed_results is not None:
                # 从修复后的结果中获取预测
                pred_data = self.fixed_results[
                    self.fixed_results['预测目标期号'] == f"2025年{period}期"
                ]
                
                if len(pred_data) > 0:
                    pred_row = pred_data.iloc[0]
                    return {
                        'predicted_numbers': [int(pred_row['预测数字1']), int(pred_row['预测数字2'])],
                        'confidence': float(pred_row['预测置信度']),
                        'method': pred_row.get('预测方法', 'Fixed_Ensemble_Diversified'),
                        'training_size': int(pred_row['训练数据量']),
                        'weight_config': pred_row.get('权重配置', '频率40%+马尔可夫30%+统计30%'),
                        'diversity_applied': pred_row.get('多样性应用', True),
                        'quality_check': pred_row.get('质量检查', '通过')
                    }
            
            # 如果没有预测数据，生成默认预测
            return self.generate_default_prediction(period)
            
        except Exception as e:
            print(f"⚠️ 获取第{period}期预测数据失败: {e}")
            return self.generate_default_prediction(period)
    
    def generate_default_prediction(self, period):
        """生成默认预测（基于历史频率）"""
        try:
            # 获取历史数据
            historical_data = self.full_data[
                (self.full_data['年份'] < 2025) |
                ((self.full_data['年份'] == 2025) & (self.full_data['期号'] < period))
            ]
            
            # 统计数字频率
            all_numbers = []
            for _, row in historical_data.iterrows():
                for i in range(1, 7):
                    num = row[f'数字{i}']
                    if pd.notna(num):
                        all_numbers.append(int(num))
            
            number_freq = Counter(all_numbers)
            # 选择频率最高的两个数字
            top_numbers = [num for num, _ in number_freq.most_common(2)]
            
            if len(top_numbers) < 2:
                top_numbers = [25, 26]  # 默认值
            
            return {
                'predicted_numbers': top_numbers,
                'confidence': 0.15,
                'method': 'Default_Frequency_Based',
                'training_size': len(historical_data),
                'weight_config': '默认频率分析',
                'diversity_applied': False,
                'quality_check': '默认'
            }
            
        except Exception as e:
            return {
                'predicted_numbers': [25, 26],
                'confidence': 0.10,
                'method': 'Default_Fallback',
                'training_size': 0,
                'weight_config': '默认',
                'diversity_applied': False,
                'quality_check': '失败'
            }
    
    def calculate_hit_analysis(self, predicted_numbers, actual_numbers):
        """计算命中分析"""
        try:
            pred_set = set(predicted_numbers)
            actual_set = set(actual_numbers)
            
            # 基本命中分析
            hit_numbers = pred_set & actual_set
            miss_numbers = pred_set - actual_set
            hit_count = len(hit_numbers)
            
            # 计算各种指标
            precision = hit_count / len(pred_set) if len(pred_set) > 0 else 0
            recall = hit_count / len(actual_set) if len(actual_set) > 0 else 0
            f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            
            # 命中质量评估
            if hit_count == 2:
                hit_quality = "完美命中"
                quality_score = 100
            elif hit_count == 1:
                hit_quality = "部分命中"
                quality_score = 50
            else:
                hit_quality = "未命中"
                quality_score = 0
            
            # 位置分析
            position_info = []
            for pred_num in predicted_numbers:
                if pred_num in actual_numbers:
                    position = actual_numbers.index(pred_num) + 1
                    position_info.append(f"数字{pred_num}在第{position}位")
                else:
                    # 找最接近的数字
                    closest_num = min(actual_numbers, key=lambda x: abs(x - pred_num))
                    diff = abs(closest_num - pred_num)
                    position_info.append(f"数字{pred_num}未命中,最接近{closest_num}(差{diff})")
            
            # 数字特征分析
            pred_sum = sum(predicted_numbers)
            actual_sum = sum(actual_numbers)
            sum_diff = abs(pred_sum - actual_sum)
            
            pred_range = max(predicted_numbers) - min(predicted_numbers) if len(predicted_numbers) > 1 else 0
            actual_range = max(actual_numbers) - min(actual_numbers)
            range_diff = abs(pred_range - actual_range)
            
            return {
                'hit_count': hit_count,
                'hit_numbers': sorted(list(hit_numbers)),
                'miss_numbers': sorted(list(miss_numbers)),
                'hit_rate': precision,
                'precision': precision,
                'recall': recall,
                'f1_score': f1_score,
                'hit_quality': hit_quality,
                'quality_score': quality_score,
                'position_info': position_info,
                'pred_sum': pred_sum,
                'actual_sum': actual_sum,
                'sum_diff': sum_diff,
                'pred_range': pred_range,
                'actual_range': actual_range,
                'range_diff': range_diff
            }
            
        except Exception as e:
            print(f"⚠️ 命中分析计算失败: {e}")
            return {
                'hit_count': 0,
                'hit_numbers': [],
                'miss_numbers': predicted_numbers,
                'hit_rate': 0.0,
                'precision': 0.0,
                'recall': 0.0,
                'f1_score': 0.0,
                'hit_quality': '分析失败',
                'quality_score': 0,
                'position_info': ['分析失败'],
                'pred_sum': sum(predicted_numbers) if predicted_numbers else 0,
                'actual_sum': sum(actual_numbers) if actual_numbers else 0,
                'sum_diff': 0,
                'pred_range': 0,
                'actual_range': 0,
                'range_diff': 0
            }
    
    def generate_comprehensive_comparison(self):
        """生成2025年151-204期的综合对比分析"""
        print("📊 开始生成2025年151-204期预测对比分析...")
        print("=" * 70)
        
        # 确定期号范围
        start_period = 151
        end_period = 204
        year = 2025
        
        print(f"📅 分析期间: {year}年{start_period}-{end_period}期")
        
        # 检查实际数据可用性
        available_periods = []
        for period in range(start_period, end_period + 1):
            actual_data = self.get_actual_data_for_period(year, period)
            if actual_data:
                available_periods.append(period)
        
        print(f"📊 可用实际数据: {len(available_periods)}期 (期号{min(available_periods)}-{max(available_periods)})")
        
        # 逐期生成对比分析
        for period in range(start_period, end_period + 1):
            try:
                print(f"\n🔍 分析第{period}期...")
                
                # 获取实际数据
                actual_data = self.get_actual_data_for_period(year, period)
                if not actual_data:
                    print(f"   ⚠️ 第{period}期实际数据不可用，跳过")
                    continue
                
                # 获取预测数据
                prediction_data = self.get_prediction_for_period(period)
                
                # 计算命中分析
                hit_analysis = self.calculate_hit_analysis(
                    prediction_data['predicted_numbers'],
                    actual_data['numbers']
                )
                
                # 构建完整的对比记录
                comparison_record = {
                    # 基本信息
                    '序号': len(self.comparison_results) + 1,
                    '年份': actual_data['year'],
                    '期号': actual_data['period'],
                    '期号标识': f"{actual_data['year']}年{actual_data['period']}期",
                    '开奖日期': actual_data['date'],
                    '分析日期': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    
                    # 预测数据
                    '预测数字1': prediction_data['predicted_numbers'][0],
                    '预测数字2': prediction_data['predicted_numbers'][1],
                    '预测数字组合': str(prediction_data['predicted_numbers']),
                    '预测置信度': round(prediction_data['confidence'], 4),
                    '预测方法': prediction_data['method'],
                    '训练数据量': prediction_data['training_size'],
                    '权重配置': prediction_data['weight_config'],
                    '多样性应用': prediction_data['diversity_applied'],
                    '质量检查': prediction_data['quality_check'],
                    
                    # 实际数据
                    '实际数字1': actual_data['numbers'][0],
                    '实际数字2': actual_data['numbers'][1],
                    '实际数字3': actual_data['numbers'][2],
                    '实际数字4': actual_data['numbers'][3],
                    '实际数字5': actual_data['numbers'][4],
                    '实际数字6': actual_data['numbers'][5],
                    '实际数字组合': str(actual_data['numbers']),
                    '实际数字和': actual_data['sum'],
                    '实际数字范围': actual_data['range'],
                    '实际奇数个数': actual_data['odd_count'],
                    '实际偶数个数': actual_data['even_count'],
                    
                    # 命中分析
                    '命中数量': hit_analysis['hit_count'],
                    '是否命中': '是' if hit_analysis['hit_count'] > 0 else '否',
                    '命中数字': ','.join(map(str, hit_analysis['hit_numbers'])) if hit_analysis['hit_numbers'] else '无',
                    '未命中数字': ','.join(map(str, hit_analysis['miss_numbers'])) if hit_analysis['miss_numbers'] else '无',
                    '命中率': round(hit_analysis['hit_rate'], 4),
                    '精确度': round(hit_analysis['precision'], 4),
                    '召回率': round(hit_analysis['recall'], 4),
                    'F1分数': round(hit_analysis['f1_score'], 4),
                    '命中质量': hit_analysis['hit_quality'],
                    '质量评分': hit_analysis['quality_score'],
                    
                    # 详细分析
                    '位置分析': '; '.join(hit_analysis['position_info']),
                    '预测数字和': hit_analysis['pred_sum'],
                    '数字和差异': hit_analysis['sum_diff'],
                    '预测数字范围': hit_analysis['pred_range'],
                    '数字范围差异': hit_analysis['range_diff'],
                    
                    # 统计特征
                    '预测奇偶比': f"{sum(1 for n in prediction_data['predicted_numbers'] if n % 2 == 1)}:{sum(1 for n in prediction_data['predicted_numbers'] if n % 2 == 0)}",
                    '实际奇偶比': f"{actual_data['odd_count']}:{actual_data['even_count']}",
                    
                    # 备注信息
                    '数据来源': '修复后预测系统' if self.fixed_results is not None else '默认预测',
                    '备注': f"基于{prediction_data['training_size']}期历史数据预测"
                }
                
                self.comparison_results.append(comparison_record)
                
                # 显示进度
                print(f"   预测: {prediction_data['predicted_numbers']}")
                print(f"   实际: {actual_data['numbers']}")
                print(f"   命中: {hit_analysis['hit_quality']} ({hit_analysis['hit_count']}个)")
                
            except Exception as e:
                print(f"⚠️ 处理第{period}期失败: {e}")
                continue
        
        print(f"\n✅ 对比分析完成，共处理 {len(self.comparison_results)} 期")
        return len(self.comparison_results) > 0
    
    def save_comparison_results(self):
        """保存对比分析结果"""
        try:
            if not self.comparison_results:
                print("❌ 无对比结果可保存")
                return False
            
            # 转换为DataFrame
            results_df = pd.DataFrame(self.comparison_results)
            
            # 保存到CSV文件
            results_df.to_csv(self.output_file, index=False, encoding='utf-8')
            
            print(f"✅ 对比分析结果已保存到: {self.output_file}")
            
            # 生成统计摘要
            self.generate_summary_statistics(results_df)
            
            return True
            
        except Exception as e:
            print(f"❌ 保存对比结果失败: {e}")
            return False
    
    def generate_summary_statistics(self, results_df):
        """生成统计摘要"""
        print(f"\n📊 2025年151-204期预测对比统计摘要")
        print("=" * 70)
        
        try:
            total_periods = len(results_df)
            hit_periods = len(results_df[results_df['是否命中'] == '是'])
            overall_hit_rate = hit_periods / total_periods if total_periods > 0 else 0
            
            print(f"🎯 基本统计:")
            print(f"   分析期数: {total_periods}")
            print(f"   命中期数: {hit_periods}")
            print(f"   整体命中率: {overall_hit_rate:.1%}")
            
            # 命中质量分布
            quality_dist = results_df['命中质量'].value_counts()
            print(f"\n🏆 命中质量分布:")
            for quality, count in quality_dist.items():
                percentage = count / total_periods * 100
                print(f"   {quality}: {count}期 ({percentage:.1f}%)")
            
            # 平均指标
            avg_confidence = results_df['预测置信度'].mean()
            avg_quality_score = results_df['质量评分'].mean()
            avg_sum_diff = results_df['数字和差异'].mean()
            
            print(f"\n📈 平均指标:")
            print(f"   平均置信度: {avg_confidence:.4f}")
            print(f"   平均质量评分: {avg_quality_score:.1f}")
            print(f"   平均数字和差异: {avg_sum_diff:.1f}")
            
            # 预测方法统计
            method_stats = results_df['预测方法'].value_counts()
            print(f"\n🔧 预测方法统计:")
            for method, count in method_stats.items():
                method_hit_rate = len(results_df[(results_df['预测方法'] == method) & (results_df['是否命中'] == '是')]) / count
                print(f"   {method}: {count}期 (命中率{method_hit_rate:.1%})")
            
            # 时间趋势分析
            if total_periods >= 10:
                mid_point = results_df['期号'].median()
                first_half = results_df[results_df['期号'] <= mid_point]
                second_half = results_df[results_df['期号'] > mid_point]
                
                first_half_rate = len(first_half[first_half['是否命中'] == '是']) / len(first_half) if len(first_half) > 0 else 0
                second_half_rate = len(second_half[second_half['是否命中'] == '是']) / len(second_half) if len(second_half) > 0 else 0
                
                print(f"\n📅 时间趋势分析:")
                print(f"   前半段命中率: {first_half_rate:.1%}")
                print(f"   后半段命中率: {second_half_rate:.1%}")
                print(f"   趋势: {'上升' if second_half_rate > first_half_rate else '下降' if second_half_rate < first_half_rate else '稳定'}")
            
        except Exception as e:
            print(f"⚠️ 统计摘要生成失败: {e}")
    
    def run_analysis(self):
        """运行完整的对比分析"""
        print("🚀 开始2025年151-204期预测对比分析...")
        print("=" * 80)
        
        # 1. 加载数据
        if not self.load_data():
            return False
        
        # 2. 生成对比分析
        if not self.generate_comprehensive_comparison():
            return False
        
        # 3. 保存结果
        if not self.save_comparison_results():
            return False
        
        print(f"\n🎉 2025年151-204期预测对比分析完成！")
        print(f"📁 结果文件: {self.output_file}")
        print(f"📊 包含完整的预测数据、真实数据对比和命中率分析")
        
        return True

def main():
    """主函数"""
    print("📊 2025年151-204期预测数据与真实数据对比分析")
    print("生成完整的预测对比CSV文件")
    print("=" * 80)
    
    analyzer = PredictionComparisonAnalysis()
    
    # 确认执行
    print("⚠️ 注意：此操作将分析2025年151-204期的预测与实际数据对比")
    confirm = input("确认开始分析? (y/n): ").strip().lower()
    
    if confirm != 'y':
        print("❌ 操作已取消")
        return
    
    # 执行分析
    success = analyzer.run_analysis()
    
    if success:
        print(f"\n✅ 分析完成！请查看生成的CSV文件了解详细结果")
    else:
        print(f"\n❌ 分析失败")

if __name__ == "__main__":
    main()
