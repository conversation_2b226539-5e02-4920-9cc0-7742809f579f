#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模式稳定性分析器
分析近期滚动窗口性能的趋势，识别性能下滑模式
为状态评估器提供模式稳定性评分
"""

import pandas as pd
import numpy as np
import json
from collections import defaultdict, deque
from datetime import datetime
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class PatternStabilityAnalyzer:
    """
    模式稳定性分析器
    监控预测系统性能的时间稳定性
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.data = None
        self.train_data = None
        self.test_data = None
        
        # 分析配置
        self.config = {
            'stability_window': 60,      # 稳定性分析窗口（期数）
            'performance_window': 30,    # 性能计算窗口（期数）
            'trend_sensitivity': 0.1,    # 趋势敏感度
            'volatility_threshold': 0.05, # 波动性阈值
            'min_samples': 20            # 最小样本数
        }
        
        # 性能历史记录
        self.performance_history = []
        self.stability_scores = []
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            # 使用最优配置
            self.train_data = self.data[(self.data['年份'] >= 2023) & (self.data['年份'] < 2025)].copy()
            self.test_data = self.data[(self.data['年份'] == 2025) & (self.data['期号'] <= 182)].copy()
            
            print(f"✅ 数据加载成功")
            print(f"  训练数据: {len(self.train_data)}期")
            print(f"  测试数据: {len(self.test_data)}期")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def build_markov_model(self, data_subset):
        """构建马尔可夫模型"""
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(data_subset) - 1):
            current_numbers = set([data_subset.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([data_subset.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                transition_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        return transition_prob
    
    def predict_with_markov(self, previous_numbers, transition_prob):
        """使用马尔可夫模型预测"""
        if not transition_prob:
            return [1, 2]
        
        number_probs = defaultdict(float)
        total_prob = 0.0
        
        for prev_num in previous_numbers:
            if prev_num in transition_prob:
                for next_num, prob in transition_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            return [num for num, prob in sorted_numbers[:2]]
        else:
            return [1, 2]
    
    def calculate_rolling_performance(self):
        """计算滚动窗口性能"""
        print(f"\n📈 计算滚动窗口性能")
        print("=" * 60)
        
        window_size = 731  # 2年训练窗口
        test_size = self.config['performance_window']  # 30期测试窗口
        
        # 合并训练和测试数据用于滚动分析
        all_data = pd.concat([self.train_data, self.test_data], ignore_index=True)
        
        performance_records = []
        
        # 滚动窗口分析
        for start_idx in range(window_size, len(all_data) - test_size, 5):  # 每5期滚动一次
            # 训练窗口
            train_window = all_data.iloc[start_idx - window_size:start_idx]
            
            # 测试窗口
            test_window = all_data.iloc[start_idx:start_idx + test_size]
            
            # 构建模型
            transition_prob = self.build_markov_model(train_window)
            
            if not transition_prob:
                continue
            
            # 评估性能
            success_count = 0
            total_predictions = 0
            
            for idx, test_row in test_window.iterrows():
                actual_numbers = set([test_row[f'数字{j}'] for j in range(1, 7)])
                
                # 获取前一期数字
                if idx == test_window.index[0]:
                    prev_numbers = set([train_window.iloc[-1][f'数字{j}'] for j in range(1, 7)])
                else:
                    prev_idx = test_window.index[test_window.index.get_loc(idx) - 1]
                    prev_numbers = set([test_window.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
                
                # 预测
                predicted_numbers = self.predict_with_markov(prev_numbers, transition_prob)
                
                # 评估
                hit_count = len(set(predicted_numbers) & actual_numbers)
                is_success = hit_count >= 1
                
                if is_success:
                    success_count += 1
                total_predictions += 1
            
            # 记录性能
            if total_predictions > 0:
                performance = success_count / total_predictions
                period_info = {
                    'start_period': all_data.iloc[start_idx]['期号'],
                    'start_year': all_data.iloc[start_idx]['年份'],
                    'end_period': all_data.iloc[start_idx + test_size - 1]['期号'],
                    'end_year': all_data.iloc[start_idx + test_size - 1]['年份'],
                    'performance': performance,
                    'success_count': success_count,
                    'total_predictions': total_predictions
                }
                performance_records.append(period_info)
        
        self.performance_history = performance_records
        
        print(f"✅ 滚动性能计算完成，共{len(performance_records)}个窗口")
        
        return performance_records
    
    def analyze_performance_trends(self):
        """分析性能趋势"""
        print(f"\n📊 分析性能趋势")
        print("-" * 40)
        
        if len(self.performance_history) < self.config['min_samples']:
            print(f"❌ 性能历史数据不足（需要至少{self.config['min_samples']}个样本）")
            return None
        
        performances = [record['performance'] for record in self.performance_history]
        
        # 基本统计
        mean_performance = np.mean(performances)
        std_performance = np.std(performances)
        
        print(f"  平均性能: {mean_performance:.3f} ({mean_performance*100:.1f}%)")
        print(f"  性能标准差: {std_performance:.3f}")
        print(f"  变异系数: {std_performance/mean_performance:.3f}")
        
        # 趋势分析
        x = np.arange(len(performances))
        slope, intercept, r_value, p_value, std_err = stats.linregress(x, performances)
        
        print(f"\n  趋势分析:")
        print(f"    斜率: {slope:.6f}")
        print(f"    相关系数: {r_value:.3f}")
        print(f"    p值: {p_value:.4f}")
        print(f"    趋势显著性: {'是' if p_value < 0.05 else '否'}")
        
        # 趋势方向
        if slope > self.config['trend_sensitivity']:
            trend_direction = "上升"
        elif slope < -self.config['trend_sensitivity']:
            trend_direction = "下降"
        else:
            trend_direction = "稳定"
        
        print(f"    趋势方向: {trend_direction}")
        
        # 最近性能变化
        recent_window = min(10, len(performances) // 3)
        recent_performances = performances[-recent_window:]
        early_performances = performances[:recent_window]
        
        recent_mean = np.mean(recent_performances)
        early_mean = np.mean(early_performances)
        performance_change = recent_mean - early_mean
        
        print(f"\n  最近性能变化:")
        print(f"    早期平均: {early_mean:.3f}")
        print(f"    最近平均: {recent_mean:.3f}")
        print(f"    变化幅度: {performance_change:+.3f} ({performance_change*100:+.1f}个百分点)")
        
        return {
            'mean_performance': mean_performance,
            'std_performance': std_performance,
            'trend_slope': slope,
            'trend_r_value': r_value,
            'trend_p_value': p_value,
            'trend_direction': trend_direction,
            'performance_change': performance_change,
            'recent_mean': recent_mean,
            'early_mean': early_mean
        }
    
    def calculate_stability_scores(self):
        """计算稳定性评分"""
        print(f"\n🎯 计算稳定性评分")
        print("-" * 40)
        
        if not self.performance_history:
            print("❌ 无性能历史数据")
            return []
        
        stability_window = self.config['stability_window']
        
        for i in range(len(self.performance_history)):
            # 获取当前位置的稳定性窗口
            start_idx = max(0, i - stability_window + 1)
            window_performances = [
                record['performance'] 
                for record in self.performance_history[start_idx:i+1]
            ]
            
            if len(window_performances) < 5:  # 最少5个样本
                stability_score = 0.5  # 默认中性分数
            else:
                # 计算稳定性指标
                
                # 1. 波动性评分（低波动性得高分）
                volatility = np.std(window_performances)
                volatility_score = max(0, min(1, 1 - volatility / 0.1))
                
                # 2. 趋势评分（正趋势得高分）
                if len(window_performances) >= 3:
                    x = np.arange(len(window_performances))
                    slope, _, _, _, _ = stats.linregress(x, window_performances)
                    trend_score = max(0, min(1, 0.5 + slope * 10))
                else:
                    trend_score = 0.5
                
                # 3. 相对性能评分（高于基准得高分）
                mean_performance = np.mean(window_performances)
                baseline = 0.232  # 理论基准
                relative_score = max(0, min(1, mean_performance / baseline))
                
                # 综合稳定性评分
                stability_score = (
                    0.4 * volatility_score +
                    0.3 * trend_score +
                    0.3 * relative_score
                )
            
            self.stability_scores.append({
                'period_info': self.performance_history[i],
                'stability_score': stability_score,
                'window_size': len(window_performances),
                'window_mean': np.mean(window_performances) if window_performances else 0,
                'window_std': np.std(window_performances) if len(window_performances) > 1 else 0
            })
        
        print(f"✅ 稳定性评分计算完成，共{len(self.stability_scores)}个评分")
        
        # 统计稳定性评分分布
        scores = [record['stability_score'] for record in self.stability_scores]
        print(f"  平均稳定性评分: {np.mean(scores):.3f}")
        print(f"  稳定性评分范围: {np.min(scores):.3f} - {np.max(scores):.3f}")
        
        return self.stability_scores
    
    def get_current_stability_score(self, current_period_idx=None):
        """获取当前稳定性评分"""
        if not self.stability_scores:
            return 0.5  # 默认中性评分
        
        if current_period_idx is None:
            # 返回最新的稳定性评分
            return self.stability_scores[-1]['stability_score']
        else:
            # 返回指定期数的稳定性评分
            if 0 <= current_period_idx < len(self.stability_scores):
                return self.stability_scores[current_period_idx]['stability_score']
            else:
                return 0.5
    
    def analyze_stability_patterns(self):
        """分析稳定性模式"""
        print(f"\n🔍 分析稳定性模式")
        print("-" * 40)
        
        if not self.stability_scores:
            print("❌ 无稳定性评分数据")
            return None
        
        scores = [record['stability_score'] for record in self.stability_scores]
        
        # 高稳定性期间
        high_stability_threshold = 0.7
        high_stability_periods = [
            record for record in self.stability_scores 
            if record['stability_score'] >= high_stability_threshold
        ]
        
        # 低稳定性期间
        low_stability_threshold = 0.3
        low_stability_periods = [
            record for record in self.stability_scores 
            if record['stability_score'] <= low_stability_threshold
        ]
        
        print(f"  高稳定性期间: {len(high_stability_periods)} ({len(high_stability_periods)/len(scores):.1%})")
        print(f"  低稳定性期间: {len(low_stability_periods)} ({len(low_stability_periods)/len(scores):.1%})")
        
        # 稳定性趋势
        x = np.arange(len(scores))
        slope, _, r_value, p_value, _ = stats.linregress(x, scores)
        
        print(f"\n  稳定性趋势:")
        print(f"    趋势斜率: {slope:.6f}")
        print(f"    相关系数: {r_value:.3f}")
        print(f"    趋势显著性: {'是' if p_value < 0.05 else '否'}")
        
        return {
            'high_stability_count': len(high_stability_periods),
            'low_stability_count': len(low_stability_periods),
            'stability_trend_slope': slope,
            'stability_trend_r': r_value,
            'stability_trend_p': p_value
        }

def main():
    """主函数"""
    print("🎯 模式稳定性分析器")
    print("监控预测系统性能的时间稳定性")
    print("=" * 70)
    
    # 初始化分析器
    analyzer = PatternStabilityAnalyzer()
    
    # 1. 加载数据
    if not analyzer.load_data():
        return
    
    # 2. 计算滚动窗口性能
    performance_history = analyzer.calculate_rolling_performance()
    
    # 3. 分析性能趋势
    trend_analysis = analyzer.analyze_performance_trends()
    
    # 4. 计算稳定性评分
    stability_scores = analyzer.calculate_stability_scores()
    
    # 5. 分析稳定性模式
    stability_patterns = analyzer.analyze_stability_patterns()
    
    # 6. 保存结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"模式稳定性分析结果_{timestamp}.json"
    
    # 处理numpy类型
    def convert_numpy_types(obj):
        if isinstance(obj, dict):
            return {k: convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(v) for v in obj]
        elif hasattr(obj, 'item'):
            return obj.item()
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, (np.integer, int)):
            return int(obj)
        elif isinstance(obj, (np.floating, float)):
            return float(obj)
        else:
            return obj
    
    results = {
        'config': analyzer.config,
        'performance_history': convert_numpy_types(performance_history),
        'trend_analysis': convert_numpy_types(trend_analysis),
        'stability_scores': convert_numpy_types(stability_scores[:10]),  # 保存前10个评分
        'stability_patterns': convert_numpy_types(stability_patterns),
        'analysis_timestamp': datetime.now().isoformat()
    }
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 模式稳定性分析结果已保存: {results_file}")
    
    # 7. 总结
    current_stability = analyzer.get_current_stability_score()
    
    print(f"\n🎉 模式稳定性分析完成")
    print("=" * 50)
    print(f"✅ 性能历史窗口: {len(performance_history)}")
    print(f"✅ 稳定性评分: {len(stability_scores)}")
    print(f"✅ 当前稳定性: {current_stability:.3f}")
    
    if trend_analysis:
        print(f"✅ 性能趋势: {trend_analysis['trend_direction']}")
        print(f"✅ 最近性能变化: {trend_analysis['performance_change']*100:+.1f}个百分点")
    
    print(f"\n💡 稳定性评估:")
    if current_stability >= 0.7:
        print(f"  🟢 高稳定性：建议投注")
    elif current_stability >= 0.4:
        print(f"  🟡 中等稳定性：谨慎投注")
    else:
        print(f"  🔴 低稳定性：建议跳过")

if __name__ == "__main__":
    main()
