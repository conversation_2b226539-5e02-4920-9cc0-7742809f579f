#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态置信度集成模块
Dynamic Confidence Integration Module

将动态置信度调整器与现有系统集成
支持实时监控、自动校准和性能优化

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0
"""

import os
import json
import logging
from datetime import datetime
from enhanced_dynamic_confidence_adjuster import EnhancedDynamicConfidenceAdjuster
from confidence_system_wrapper import calculate_confidence
from algorithm_wrapper import get_enhanced_prediction

logger = logging.getLogger(__name__)

class DynamicConfidenceIntegration:
    """动态置信度集成模块"""
    
    def __init__(self, config=None):
        """初始化集成模块"""
        self.config = config or self._get_default_config()
        
        # 初始化动态置信度调整器
        self.adjuster = EnhancedDynamicConfidenceAdjuster(
            self.config.get('adjuster_config')
        )
        
        # 集成状态
        self.integration_enabled = self.config['integration_enabled']
        self.integration_mode = self.config['integration_mode']
        self.integration_ratio = self.config['integration_ratio']
        
        # 性能统计
        self.performance_stats = {
            'total_predictions': 0,
            'adjusted_predictions': 0,
            'start_time': datetime.now()
        }
        
        logger.info("🔧 动态置信度集成模块初始化完成")
        logger.info(f"  集成状态: {'启用' if self.integration_enabled else '禁用'}")
        logger.info(f"  集成模式: {self.integration_mode}")
        logger.info(f"  集成比例: {self.integration_ratio:.1%}")
    
    def _get_default_config(self):
        """获取默认配置"""
        return {
            'integration_enabled': True,
            'integration_mode': 'hybrid',  # hybrid, override, weighted
            'integration_ratio': 0.7,  # 动态调整器的权重
            'adjuster_config': None,  # 使用调整器默认配置
            'confidence_bounds': {
                'min_confidence': 0.05,
                'max_confidence': 0.95
            },
            'performance_monitoring': {
                'enabled': True,
                'log_interval': 50  # 每50次预测记录一次性能
            }
        }
    
    def get_integrated_confidence(self, predicted_numbers, prediction_context=None):
        """获取集成置信度"""
        self.performance_stats['total_predictions'] += 1
        
        if not self.integration_enabled:
            # 如果集成禁用，使用原始置信度系统
            return self._get_original_confidence(predicted_numbers, prediction_context)
        
        # 获取原始置信度
        original_result = self._get_original_confidence(predicted_numbers, prediction_context)
        original_confidence = original_result['final_confidence']
        
        # 使用动态调整器调整置信度
        adjusted_confidence = self.adjuster.adjust_confidence(
            original_confidence, prediction_context
        )
        
        # 根据集成模式计算最终置信度
        if self.integration_mode == 'override':
            # 完全使用调整后的置信度
            final_confidence = adjusted_confidence
        elif self.integration_mode == 'weighted':
            # 加权平均
            final_confidence = (
                self.integration_ratio * adjusted_confidence +
                (1 - self.integration_ratio) * original_confidence
            )
        else:  # hybrid
            # 智能混合：根据调整幅度决定权重
            adjustment_ratio = adjusted_confidence / original_confidence if original_confidence > 0 else 1.0
            
            if adjustment_ratio > 1.2:
                # 大幅提升，更倾向于调整后的值
                weight = min(0.9, self.integration_ratio + 0.2)
            elif adjustment_ratio < 0.8:
                # 大幅降低，更倾向于调整后的值
                weight = min(0.9, self.integration_ratio + 0.2)
            else:
                # 适中调整，使用标准权重
                weight = self.integration_ratio
            
            final_confidence = weight * adjusted_confidence + (1 - weight) * original_confidence
        
        # 应用边界约束
        min_conf = self.config.get('confidence_bounds', {}).get('min_confidence', 0.05)
        max_conf = self.config.get('confidence_bounds', {}).get('max_confidence', 0.95)
        final_confidence = max(min_conf, min(max_conf, final_confidence))
        
        # 构建结果
        result = {
            'final_confidence': final_confidence,
            'original_confidence': original_confidence,
            'adjusted_confidence': adjusted_confidence,
            'adjustment_ratio': adjusted_confidence / original_confidence if original_confidence > 0 else 1.0,
            'integration_mode': self.integration_mode,
            'integration_weight': self.integration_ratio,
            'timestamp': datetime.now().isoformat()
        }
        
        # 记录性能统计
        self.performance_stats['adjusted_predictions'] += 1
        
        # 性能监控
        log_interval = self.config.get('performance_monitoring', {}).get('log_interval', 50)
        if (self.performance_stats['total_predictions'] % log_interval == 0):
            self._log_performance()
        
        return result
    
    def _get_original_confidence(self, predicted_numbers, prediction_context=None):
        """获取原始置信度"""
        try:
            # 使用现有的置信度系统
            confidence_result = calculate_confidence(predicted_numbers, prediction_context)
            
            # 确保结果格式一致
            if isinstance(confidence_result, dict) and 'final_confidence' in confidence_result:
                return confidence_result
            elif isinstance(confidence_result, (int, float)):
                return {'final_confidence': confidence_result}
            else:
                logger.warning(f"未知的置信度结果格式: {type(confidence_result)}")
                return {'final_confidence': 0.5}
                
        except Exception as e:
            logger.error(f"获取原始置信度失败: {e}")
            return {'final_confidence': 0.5}
    
    def update_prediction_result(self, prediction_result):
        """更新预测结果"""
        try:
            # 更新动态调整器
            self.adjuster.update_prediction_result(prediction_result)
            
        except Exception as e:
            logger.error(f"更新预测结果失败: {e}")
    
    def _log_performance(self):
        """记录性能统计"""
        logger.info(f"📊 动态置信度集成性能:")
        logger.info(f"  总预测次数: {self.performance_stats['total_predictions']}")
        logger.info(f"  调整预测次数: {self.performance_stats['adjusted_predictions']}")
        logger.info(f"  调整比例: {self.performance_stats['adjusted_predictions'] / self.performance_stats['total_predictions']:.1%}")
    
    def get_performance_report(self):
        """获取性能报告"""
        # 获取调整器状态
        adjuster_status = self.adjuster.get_status_report()
        
        return {
            'timestamp': datetime.now().isoformat(),
            'integration_stats': self.performance_stats.copy(),
            'integration_config': {
                'enabled': self.integration_enabled,
                'mode': self.integration_mode,
                'ratio': self.integration_ratio
            },
            'adjuster_status': adjuster_status
        }
    
    def export_performance_data(self, filename):
        """导出性能数据"""
        try:
            # 准备导出数据
            export_data = {
                'performance_report': self.get_performance_report(),
                'export_timestamp': datetime.now().isoformat()
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 性能数据已导出到: {filename}")
            
            # 同时导出调整器数据
            adjuster_filename = f"adjuster_{filename}"
            self.adjuster.export_calibration_data(adjuster_filename)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 导出性能数据失败: {e}")
            return False
    
    def set_integration_mode(self, mode, ratio=None):
        """设置集成模式"""
        if mode in ['hybrid', 'override', 'weighted']:
            self.integration_mode = mode
            logger.info(f"集成模式已更新为: {mode}")
        else:
            logger.warning(f"未知的集成模式: {mode}")
        
        if ratio is not None and 0 <= ratio <= 1:
            self.integration_ratio = ratio
            logger.info(f"集成比例已更新为: {ratio:.1%}")
    
    def enable_integration(self, enabled=True):
        """启用或禁用集成"""
        self.integration_enabled = enabled
        logger.info(f"动态置信度集成已{'启用' if enabled else '禁用'}")
    
    def perform_manual_calibration(self):
        """执行手动校准"""
        try:
            logger.info("🔧 执行手动校准")
            self.adjuster.perform_auto_calibration()
            logger.info("✅ 手动校准完成")
            return True
        except Exception as e:
            logger.error(f"❌ 手动校准失败: {e}")
            return False

# 全局集成实例
_global_integration = None

def get_confidence_integration(config=None):
    """获取全局集成实例"""
    global _global_integration
    if _global_integration is None:
        _global_integration = DynamicConfidenceIntegration(config)
    return _global_integration

def get_integrated_confidence(predicted_numbers, prediction_context=None):
    """获取集成置信度的全局函数"""
    integration = get_confidence_integration()
    return integration.get_integrated_confidence(predicted_numbers, prediction_context)

def update_prediction_result(prediction_result):
    """更新预测结果的全局函数"""
    integration = get_confidence_integration()
    integration.update_prediction_result(prediction_result)

def get_performance_report():
    """获取性能报告的全局函数"""
    integration = get_confidence_integration()
    return integration.get_performance_report()

def perform_manual_calibration():
    """执行手动校准的全局函数"""
    integration = get_confidence_integration()
    return integration.perform_manual_calibration()

# 集成预测函数
def predict_with_dynamic_confidence(previous_numbers, context=None):
    """集成预测函数"""
    # 获取预测结果
    prediction_result = get_enhanced_prediction(previous_numbers, context)
    
    # 获取集成置信度
    confidence_result = get_integrated_confidence(
        prediction_result['predicted_numbers'], 
        context
    )
    
    # 更新预测结果
    prediction_result['original_confidence'] = prediction_result.get('confidence', 0.5)
    prediction_result['confidence'] = confidence_result['final_confidence']
    prediction_result['confidence_details'] = {
        'original': confidence_result['original_confidence'],
        'adjusted': confidence_result['adjusted_confidence'],
        'final': confidence_result['final_confidence'],
        'integration_mode': confidence_result['integration_mode']
    }
    
    return prediction_result

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 示例使用
    integration = get_confidence_integration()
    
    # 测试集成置信度
    test_numbers = [5, 40]
    test_context = {
        'previous_numbers': [1, 15, 23, 30, 35, 42],
        'data_source': '测试数据'
    }
    
    confidence_result = integration.get_integrated_confidence(test_numbers, test_context)
    
    print(f"集成置信度测试:")
    print(f"  原始置信度: {confidence_result['original_confidence']:.3f}")
    print(f"  调整置信度: {confidence_result['adjusted_confidence']:.3f}")
    print(f"  最终置信度: {confidence_result['final_confidence']:.3f}")
    print(f"  调整比例: {confidence_result['adjustment_ratio']:.3f}")
    print(f"  集成模式: {confidence_result['integration_mode']}")
    
    # 测试集成预测
    prediction = predict_with_dynamic_confidence(test_context['previous_numbers'], test_context)
    
    print(f"\n集成预测测试:")
    print(f"  预测数字: {prediction['predicted_numbers']}")
    print(f"  最终置信度: {prediction['confidence']:.3f}")
    print(f"  原始置信度: {prediction['original_confidence']:.3f}")
    
    # 获取性能报告
    report = integration.get_performance_report()
    print(f"\n性能报告:")
    print(f"  总预测次数: {report['integration_stats']['total_predictions']}")
    print(f"  调整预测次数: {report['integration_stats']['adjusted_predictions']}")
    print(f"  集成模式: {report['integration_config']['mode']}")
    print(f"  集成比例: {report['integration_config']['ratio']:.1%}")
