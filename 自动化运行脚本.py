#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化运行脚本 - 34.3%预测系统
一键部署、预测、监控的完整自动化解决方案
"""

import os
import sys
import json
import time
import logging
from datetime import datetime, timedelta
import subprocess
import warnings
warnings.filterwarnings('ignore')

class AutomatedPredictionRunner:
    """自动化预测运行器"""
    
    def __init__(self):
        self.log_file = f"automation_log_{datetime.now().strftime('%Y%m%d')}.log"
        self.setup_logging()
        
        # 系统文件
        self.system_files = {
            'production_system': '生产级预测系统.py',
            'monitoring_dashboard': '系统监控面板.py',
            'data_file': 'data/processed/lottery_data_clean_no_special.csv'
        }
        
        # 运行配置
        self.run_config = {
            'auto_predict': True,           # 自动预测
            'auto_monitor': True,           # 自动监控
            'monitor_interval': 3600,       # 监控间隔(秒)
            'max_retries': 3,              # 最大重试次数
            'backup_enabled': True,         # 启用备份
            'alert_enabled': True          # 启用告警
        }
        
        # 系统状态
        self.system_status = {
            'production_system': 'unknown',
            'monitoring_system': 'unknown',
            'last_prediction': None,
            'last_monitoring': None,
            'error_count': 0
        }
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def check_prerequisites(self):
        """检查前置条件"""
        self.logger.info("🔍 检查系统前置条件")
        
        # 检查文件存在
        missing_files = []
        for name, file_path in self.system_files.items():
            if not os.path.exists(file_path):
                missing_files.append(f"{name}: {file_path}")
        
        if missing_files:
            self.logger.error(f"❌ 缺少必要文件: {missing_files}")
            return False
        
        # 检查Python环境
        try:
            import pandas as pd
            import numpy as np
            import matplotlib.pyplot as plt
            self.logger.info("✅ Python依赖检查通过")
        except ImportError as e:
            self.logger.error(f"❌ Python依赖缺失: {e}")
            return False
        
        # 检查数据文件
        try:
            import pandas as pd
            data = pd.read_csv(self.system_files['data_file'])
            if len(data) == 0:
                self.logger.error("❌ 数据文件为空")
                return False
            self.logger.info(f"✅ 数据文件检查通过 ({len(data)}条记录)")
        except Exception as e:
            self.logger.error(f"❌ 数据文件检查失败: {e}")
            return False
        
        self.logger.info("✅ 前置条件检查完成")
        return True
    
    def deploy_production_system(self):
        """部署生产系统"""
        self.logger.info("🚀 部署生产系统")
        
        try:
            # 运行生产系统
            result = subprocess.run([
                sys.executable, self.system_files['production_system']
            ], capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                self.logger.info("✅ 生产系统部署成功")
                self.system_status['production_system'] = 'running'
                return True
            else:
                self.logger.error(f"❌ 生产系统部署失败: {result.stderr}")
                self.system_status['production_system'] = 'failed'
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 生产系统部署异常: {e}")
            self.system_status['production_system'] = 'error'
            return False
    
    def run_monitoring_system(self):
        """运行监控系统"""
        self.logger.info("📊 运行监控系统")
        
        try:
            # 运行监控面板
            result = subprocess.run([
                sys.executable, self.system_files['monitoring_dashboard']
            ], capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                self.logger.info("✅ 监控系统运行成功")
                self.system_status['monitoring_system'] = 'completed'
                self.system_status['last_monitoring'] = datetime.now().isoformat()
                return True
            else:
                self.logger.error(f"❌ 监控系统运行失败: {result.stderr}")
                self.system_status['monitoring_system'] = 'failed'
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 监控系统运行异常: {e}")
            self.system_status['monitoring_system'] = 'error'
            return False
    
    def execute_prediction(self, current_numbers=None):
        """执行预测"""
        self.logger.info("🎯 执行预测任务")
        
        try:
            # 导入生产系统
            sys.path.append('.')
            from 生产级预测系统 import ProductionPredictionSystem
            
            # 初始化系统
            system = ProductionPredictionSystem()
            if not system.initialize_system():
                self.logger.error("❌ 预测系统初始化失败")
                return None
            
            # 获取当前期数字
            if current_numbers is None:
                # 使用最新一期数据
                latest_data = system.full_data.iloc[-1]
                current_numbers = [latest_data[f'数字{i}'] for i in range(1, 7)]
            
            # 执行预测
            prediction_report = system.predict_next_period(current_numbers)
            
            self.logger.info(f"✅ 预测完成: {prediction_report['prediction']}")
            self.logger.info(f"   置信度: {prediction_report['confidence']:.3f}")
            
            self.system_status['last_prediction'] = prediction_report
            
            return prediction_report
            
        except Exception as e:
            self.logger.error(f"❌ 预测执行失败: {e}")
            self.system_status['error_count'] += 1
            return None
    
    def backup_system_data(self):
        """备份系统数据"""
        if not self.run_config['backup_enabled']:
            return True
        
        self.logger.info("💾 执行系统备份")
        
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_dir = f"backup_{timestamp}"
            
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
            
            # 备份配置文件
            config_files = ['production_config.json']
            for config_file in config_files:
                if os.path.exists(config_file):
                    import shutil
                    shutil.copy2(config_file, backup_dir)
            
            # 备份日志文件
            log_files = [f for f in os.listdir('.') if f.endswith('.log')]
            for log_file in log_files:
                import shutil
                shutil.copy2(log_file, backup_dir)
            
            self.logger.info(f"✅ 系统备份完成: {backup_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 系统备份失败: {e}")
            return False
    
    def check_system_health(self):
        """检查系统健康状态"""
        self.logger.info("🏥 检查系统健康状态")
        
        health_status = {
            'overall': 'healthy',
            'issues': [],
            'warnings': [],
            'timestamp': datetime.now().isoformat()
        }
        
        # 检查错误计数
        if self.system_status['error_count'] > 5:
            health_status['issues'].append('错误次数过多')
            health_status['overall'] = 'unhealthy'
        elif self.system_status['error_count'] > 2:
            health_status['warnings'].append('错误次数较多')
            health_status['overall'] = 'warning'
        
        # 检查系统状态
        if self.system_status['production_system'] == 'failed':
            health_status['issues'].append('生产系统故障')
            health_status['overall'] = 'unhealthy'
        
        if self.system_status['monitoring_system'] == 'failed':
            health_status['warnings'].append('监控系统故障')
            if health_status['overall'] == 'healthy':
                health_status['overall'] = 'warning'
        
        # 检查最近预测
        if self.system_status['last_prediction'] is None:
            health_status['warnings'].append('无预测记录')
            if health_status['overall'] == 'healthy':
                health_status['overall'] = 'warning'
        
        self.logger.info(f"系统健康状态: {health_status['overall']}")
        if health_status['issues']:
            self.logger.warning(f"发现问题: {health_status['issues']}")
        if health_status['warnings']:
            self.logger.warning(f"发现警告: {health_status['warnings']}")
        
        return health_status
    
    def send_alert(self, message, level='info'):
        """发送告警"""
        if not self.run_config['alert_enabled']:
            return
        
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        alert_message = f"[{timestamp}] {level.upper()}: {message}"
        
        # 记录到日志
        if level == 'error':
            self.logger.error(alert_message)
        elif level == 'warning':
            self.logger.warning(alert_message)
        else:
            self.logger.info(alert_message)
        
        # 这里可以添加其他告警方式，如邮件、短信等
        # send_email(alert_message)
        # send_sms(alert_message)
    
    def run_full_automation(self):
        """运行完整自动化流程"""
        self.logger.info("🤖 启动完整自动化流程")
        self.logger.info("=" * 60)
        
        # 1. 检查前置条件
        if not self.check_prerequisites():
            self.send_alert("前置条件检查失败", 'error')
            return False
        
        # 2. 部署生产系统
        if not self.deploy_production_system():
            self.send_alert("生产系统部署失败", 'error')
            return False
        
        # 3. 执行预测
        prediction_result = self.execute_prediction()
        if prediction_result is None:
            self.send_alert("预测执行失败", 'error')
        else:
            self.send_alert(f"预测完成: {prediction_result['prediction']}", 'info')
        
        # 4. 运行监控
        if not self.run_monitoring_system():
            self.send_alert("监控系统运行失败", 'warning')
        
        # 5. 备份数据
        if not self.backup_system_data():
            self.send_alert("系统备份失败", 'warning')
        
        # 6. 健康检查
        health_status = self.check_system_health()
        if health_status['overall'] != 'healthy':
            self.send_alert(f"系统健康状态: {health_status['overall']}", 'warning')
        
        self.logger.info("🎉 自动化流程完成")
        return True
    
    def run_continuous_monitoring(self):
        """运行持续监控"""
        self.logger.info("🔄 启动持续监控模式")
        
        try:
            while True:
                # 执行监控
                self.run_monitoring_system()
                
                # 健康检查
                health_status = self.check_system_health()
                
                # 如果系统不健康，尝试修复
                if health_status['overall'] == 'unhealthy':
                    self.logger.warning("⚠️ 系统不健康，尝试修复")
                    self.deploy_production_system()
                
                # 等待下次监控
                self.logger.info(f"⏰ 等待{self.run_config['monitor_interval']}秒后进行下次监控")
                time.sleep(self.run_config['monitor_interval'])
                
        except KeyboardInterrupt:
            self.logger.info("🛑 用户中断，停止持续监控")
        except Exception as e:
            self.logger.error(f"❌ 持续监控异常: {e}")
    
    def generate_automation_report(self):
        """生成自动化报告"""
        self.logger.info("📋 生成自动化报告")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'system_status': self.system_status,
            'run_config': self.run_config,
            'health_check': self.check_system_health()
        }
        
        # 保存报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"automation_report_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"✅ 自动化报告已保存: {report_file}")
        
        # 打印报告摘要
        print("\n📊 自动化报告摘要")
        print("=" * 40)
        print(f"生产系统状态: {self.system_status['production_system']}")
        print(f"监控系统状态: {self.system_status['monitoring_system']}")
        print(f"错误计数: {self.system_status['error_count']}")
        print(f"系统健康: {report['health_check']['overall']}")
        
        if self.system_status['last_prediction']:
            pred = self.system_status['last_prediction']
            print(f"最近预测: {pred['prediction']} (置信度: {pred['confidence']:.3f})")
        
        return report

def main():
    """主函数"""
    print("🤖 34.3%预测系统自动化运行器")
    print("=" * 60)
    
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description='34.3%预测系统自动化运行')
    parser.add_argument('--mode', choices=['full', 'predict', 'monitor', 'continuous'], 
                       default='full', help='运行模式')
    parser.add_argument('--numbers', nargs=6, type=int, 
                       help='当前期6个数字 (用于预测)')
    
    args = parser.parse_args()
    
    # 初始化自动化运行器
    runner = AutomatedPredictionRunner()
    
    try:
        if args.mode == 'full':
            # 完整自动化流程
            success = runner.run_full_automation()
            if success:
                print("✅ 自动化流程执行成功")
            else:
                print("❌ 自动化流程执行失败")
        
        elif args.mode == 'predict':
            # 仅执行预测
            current_numbers = args.numbers if args.numbers else None
            result = runner.execute_prediction(current_numbers)
            if result:
                print(f"预测结果: {result['prediction']}")
                print(f"置信度: {result['confidence']:.3f}")
        
        elif args.mode == 'monitor':
            # 仅执行监控
            runner.run_monitoring_system()
        
        elif args.mode == 'continuous':
            # 持续监控模式
            runner.run_continuous_monitoring()
        
        # 生成报告
        runner.generate_automation_report()
        
    except Exception as e:
        runner.logger.error(f"❌ 自动化运行异常: {e}")
        print(f"❌ 运行失败: {e}")
    
    print("\n🎉 自动化运行完成")

if __name__ == "__main__":
    main()
