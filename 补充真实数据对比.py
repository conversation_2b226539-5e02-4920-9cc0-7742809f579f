#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
补充真实数据对比 - 将188-192期真实数据补充到prediction_data.csv
修正期号对应关系并添加命中验证
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class RealDataCompletion:
    """真实数据补充系统"""
    
    def __init__(self):
        self.prediction_data_file = "prediction_data.csv"
        self.main_data_file = "data/processed/lottery_data_clean_no_special.csv"
        
    def load_data(self):
        """加载数据文件"""
        try:
            # 加载预测数据
            self.prediction_df = pd.read_csv(self.prediction_data_file, encoding='utf-8-sig')
            print(f"✅ 预测数据加载完成: {len(self.prediction_df)}条记录")
            
            # 加载主数据
            self.main_df = pd.read_csv(self.main_data_file)
            print(f"✅ 主数据加载完成: {len(self.main_df)}期")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def analyze_period_mapping(self):
        """分析期号对应关系"""
        print(f"\n🔍 分析期号对应关系")
        print("=" * 60)
        
        # 查看主数据中的最新期号
        latest_main_data = self.main_df[self.main_df['年份'] == 2025].tail(10)
        print(f"主数据文件中2025年最新10期:")
        for _, row in latest_main_data.iterrows():
            numbers = [row[f'数字{i}'] for i in range(1, 7)]
            print(f"  {row['年份']}年{row['期号']}期: {numbers}")
        
        # 查看预测数据中的未验证记录
        unverified = self.prediction_df[
            (self.prediction_df['实际数字1'].isna()) | 
            (self.prediction_df['实际数字1'] == '') |
            (self.prediction_df['实际数字1'] == 'nan')
        ].copy()
        
        print(f"\n预测数据文件中未验证的预测:")
        for _, row in unverified.iterrows():
            print(f"  {row['预测期号']}: 预测[{row['预测数字1']}, {row['预测数字2']}]")
        
        return latest_main_data, unverified
    
    def calculate_hit_result(self, predicted_numbers, actual_numbers):
        """计算命中结果"""
        try:
            predicted_set = set([int(x) for x in predicted_numbers if pd.notna(x)])
            actual_set = set([int(x) for x in actual_numbers if pd.notna(x)])
            
            # 计算命中的数字
            hit_numbers = predicted_set & actual_set
            hit_count = len(hit_numbers)
            is_hit = hit_count >= 1
            
            return {
                'hit_count': hit_count,
                'is_hit': is_hit,
                'hit_numbers': sorted(list(hit_numbers))
            }
            
        except Exception as e:
            print(f"❌ 计算命中结果失败: {e}")
            return None
    
    def update_prediction_with_real_data(self):
        """更新预测数据与真实数据对比"""
        print(f"\n🔄 更新预测数据与真实数据对比")
        print("=" * 60)
        
        # 期号映射关系 (修正错误的对应关系)
        period_mapping = {
            # prediction_data.csv中的预测期号 -> 主数据文件中的实际期号
            "2025年188期": 187,  # 预测188期 -> 实际187期
            "2025年189期": 188,  # 预测189期 -> 实际188期  
            "2025年190期": 189,  # 预测190期 -> 实际189期
            "2025年191期": 190,  # 预测191期 -> 实际190期
            "2025年192期": 191,  # 预测192期 -> 实际191期
        }
        
        updates_made = 0
        
        for predicted_period, actual_period in period_mapping.items():
            try:
                # 查找预测记录
                prediction_rows = self.prediction_df[
                    self.prediction_df['预测期号'] == predicted_period
                ]
                
                if len(prediction_rows) == 0:
                    print(f"⚠️ 未找到{predicted_period}的预测记录")
                    continue
                
                # 使用最新的预测记录
                pred_idx = prediction_rows.index[-1]
                pred_row = self.prediction_df.loc[pred_idx]
                
                # 查找对应的真实数据
                actual_rows = self.main_df[
                    (self.main_df['年份'] == 2025) & 
                    (self.main_df['期号'] == actual_period)
                ]
                
                if len(actual_rows) == 0:
                    print(f"⚠️ 未找到2025年{actual_period}期的真实数据")
                    continue
                
                actual_row = actual_rows.iloc[0]
                actual_numbers = [actual_row[f'数字{i}'] for i in range(1, 7)]
                
                # 获取预测数字
                predicted_numbers = [pred_row['预测数字1'], pred_row['预测数字2']]
                
                # 计算命中结果
                hit_result = self.calculate_hit_result(predicted_numbers, actual_numbers)
                if hit_result is None:
                    continue
                
                # 更新预测记录
                for i, num in enumerate(actual_numbers, 1):
                    self.prediction_df.loc[pred_idx, f'实际数字{i}'] = num
                
                self.prediction_df.loc[pred_idx, '命中数量'] = hit_result['hit_count']
                self.prediction_df.loc[pred_idx, '是否命中'] = '是' if hit_result['is_hit'] else '否'
                self.prediction_df.loc[pred_idx, '命中数字'] = ','.join(map(str, hit_result['hit_numbers'])) if hit_result['hit_numbers'] else ''
                
                # 更新备注
                current_note = self.prediction_df.loc[pred_idx, '备注']
                if pd.isna(current_note) or current_note == '':
                    self.prediction_df.loc[pred_idx, '备注'] = '真实数据补充'
                else:
                    self.prediction_df.loc[pred_idx, '备注'] = f"{current_note},真实数据补充"
                
                # 显示更新结果
                status = "✅" if hit_result['is_hit'] else "❌"
                hit_info = f"命中{hit_result['hit_numbers']}" if hit_result['hit_numbers'] else "未命中"
                print(f"  {predicted_period} -> 2025年{actual_period}期: 预测{predicted_numbers} vs 实际{actual_numbers} {status} {hit_info}")
                
                updates_made += 1
                
            except Exception as e:
                print(f"❌ 更新{predicted_period}失败: {e}")
                continue
        
        print(f"\n✅ 完成更新: {updates_made}期数据")
        return updates_made > 0
    
    def save_updated_data(self):
        """保存更新后的数据"""
        try:
            # 备份原文件
            backup_file = f"prediction_data_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            self.prediction_df.to_csv(backup_file, index=False, encoding='utf-8-sig')
            print(f"✅ 原文件已备份: {backup_file}")
            
            # 保存更新后的文件
            self.prediction_df.to_csv(self.prediction_data_file, index=False, encoding='utf-8-sig')
            print(f"✅ 预测数据已更新: {self.prediction_data_file}")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return False
    
    def show_updated_statistics(self):
        """显示更新后的统计"""
        try:
            print(f"\n📊 更新后统计")
            print("=" * 50)
            
            total_predictions = len(self.prediction_df)
            
            # 已验证的预测
            verified_predictions = self.prediction_df[
                (~self.prediction_df['实际数字1'].isna()) & 
                (self.prediction_df['实际数字1'] != '') &
                (self.prediction_df['实际数字1'] != 'nan')
            ].copy()
            
            unverified_count = total_predictions - len(verified_predictions)
            
            print(f"总预测期数: {total_predictions}")
            print(f"已验证期数: {len(verified_predictions)}")
            print(f"待验证期数: {unverified_count}")
            
            if len(verified_predictions) > 0:
                hits = len(verified_predictions[verified_predictions['是否命中'] == '是'])
                hit_rate = hits / len(verified_predictions)
                
                print(f"命中期数: {hits}")
                print(f"命中率: {hit_rate:.3f} ({hit_rate:.1%})")
                
                # 最新补充的验证结果
                recent_updated = verified_predictions[
                    verified_predictions['备注'].str.contains('真实数据补充', na=False)
                ].tail(5)
                
                if len(recent_updated) > 0:
                    print(f"\n最新补充的{len(recent_updated)}期验证结果:")
                    for _, row in recent_updated.iterrows():
                        status = "✅" if row['是否命中'] == '是' else "❌"
                        hit_info = f"命中{row['命中数字']}" if row['命中数字'] else "未命中"
                        print(f"  {row['预测期号']}: 预测[{row['预测数字1']},{row['预测数字2']}] {status} {hit_info}")
            
        except Exception as e:
            print(f"❌ 显示统计失败: {e}")
    
    def show_remaining_unverified(self):
        """显示剩余未验证的预测"""
        try:
            unverified = self.prediction_df[
                (self.prediction_df['实际数字1'].isna()) | 
                (self.prediction_df['实际数字1'] == '') |
                (self.prediction_df['实际数字1'] == 'nan')
            ].copy()
            
            if len(unverified) == 0:
                print(f"\n🎉 所有预测都已验证完成！")
            else:
                print(f"\n📋 剩余未验证的预测 ({len(unverified)}条)")
                print("=" * 60)
                for _, row in unverified.iterrows():
                    print(f"  {row['预测期号']}: 预测[{row['预测数字1']}, {row['预测数字2']}]")
                    print(f"    原因: 主数据文件中暂无对应期号的真实数据")
            
        except Exception as e:
            print(f"❌ 显示未验证预测失败: {e}")

def main():
    """主函数"""
    print("🔄 补充真实数据对比系统")
    print("将188-192期真实数据补充到prediction_data.csv")
    print("=" * 60)
    
    system = RealDataCompletion()
    
    # 1. 加载数据
    if not system.load_data():
        return
    
    # 2. 分析期号对应关系
    system.analyze_period_mapping()
    
    # 3. 确认执行更新
    print(f"\n❓ 确认操作")
    print("=" * 40)
    print("即将执行以下操作:")
    print("1. 从主数据文件提取187-191期真实开奖数据")
    print("2. 更新prediction_data.csv中对应的预测验证")
    print("3. 计算命中情况并更新统计")
    print("4. 备份原文件并保存更新")
    
    confirm = input("\n是否继续执行？(y/n): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return
    
    # 4. 执行更新
    if system.update_prediction_with_real_data():
        # 5. 保存更新后的数据
        if system.save_updated_data():
            # 6. 显示更新后的统计
            system.show_updated_statistics()
            
            # 7. 显示剩余未验证的预测
            system.show_remaining_unverified()
            
            print(f"\n🎉 真实数据补充完成！")
        else:
            print(f"\n❌ 保存文件失败")
    else:
        print(f"\n❌ 数据更新失败")

if __name__ == "__main__":
    main()
