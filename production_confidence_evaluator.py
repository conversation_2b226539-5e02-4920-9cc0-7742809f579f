#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境多维度置信度评估器
Production Multi-Dimensional Confidence Evaluator

可直接集成到现有预测系统的生产就绪模块
支持热插拔替换原有置信度计算方法

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0 Production Ready
"""

import pandas as pd
import numpy as np
from collections import defaultdict, deque
from datetime import datetime
import json
import logging
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductionConfidenceEvaluator:
    """生产环境多维度置信度评估器"""
    
    def __init__(self, config_file=None):
        """初始化评估器"""
        self.config = self._load_config(config_file)
        self.prediction_history = deque(maxlen=self.config['history_window'])
        self.performance_cache = {}
        self.last_update = datetime.now()
        
        logger.info("🚀 生产环境多维度置信度评估器初始化完成")
        logger.info(f"  配置版本: {self.config['version']}")
        logger.info(f"  历史窗口: {self.config['history_window']}")
    
    def _load_config(self, config_file):
        """加载配置"""
        default_config = {
            "version": "1.0",
            "history_window": 200,
            "confidence_weights": {
                "base_prediction": 0.25,
                "historical_performance": 0.25,
                "pattern_stability": 0.20,
                "data_quality": 0.15,
                "prediction_consistency": 0.15
            },
            "confidence_bounds": {
                "min_confidence": 0.05,
                "max_confidence": 0.95
            },
            "cache_ttl": 300,  # 缓存5分钟
            "enable_logging": True
        }
        
        if config_file:
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
            except Exception as e:
                logger.warning(f"配置文件加载失败，使用默认配置: {e}")
        
        return default_config
    
    def evaluate_confidence(self, predicted_numbers, prediction_context):
        """
        评估预测置信度
        
        Args:
            predicted_numbers: 预测的数字列表
            prediction_context: 预测上下文信息
                - previous_numbers: 前期数字
                - number_probs: 数字概率分布
                - candidates: 候选预测列表
                - data_source: 数据来源
                - period_idx: 期号
        
        Returns:
            dict: 包含最终置信度和各维度详情的字典
        """
        try:
            # 计算各维度置信度
            base_conf = self._calculate_base_prediction_confidence(
                prediction_context.get('number_probs', {}), 
                predicted_numbers
            )
            
            historical_conf = self._calculate_historical_performance_confidence(
                predicted_numbers
            )
            
            stability_conf = self._calculate_pattern_stability_confidence(
                prediction_context.get('previous_numbers', []),
                prediction_context.get('period_idx', 0)
            )
            
            quality_conf = self._calculate_data_quality_confidence(
                prediction_context.get('previous_numbers', []),
                prediction_context.get('data_source', '未知')
            )
            
            consistency_conf = self._calculate_prediction_consistency_confidence(
                predicted_numbers,
                prediction_context.get('candidates', [])
            )
            
            # 加权计算最终置信度
            weights = self.config['confidence_weights']
            final_confidence = (
                weights['base_prediction'] * base_conf +
                weights['historical_performance'] * historical_conf +
                weights['pattern_stability'] * stability_conf +
                weights['data_quality'] * quality_conf +
                weights['prediction_consistency'] * consistency_conf
            )
            
            # 应用边界约束
            min_conf = self.config['confidence_bounds']['min_confidence']
            max_conf = self.config['confidence_bounds']['max_confidence']
            final_confidence = max(min_conf, min(max_conf, final_confidence))
            
            # 构建结果
            result = {
                'final_confidence': final_confidence,
                'confidence_details': {
                    'base_prediction': base_conf,
                    'historical_performance': historical_conf,
                    'pattern_stability': stability_conf,
                    'data_quality': quality_conf,
                    'prediction_consistency': consistency_conf
                },
                'evaluation_timestamp': datetime.now().isoformat(),
                'config_version': self.config['version']
            }
            
            if self.config['enable_logging']:
                logger.info(f"置信度评估完成: {final_confidence:.3f}")
            
            return result
            
        except Exception as e:
            logger.error(f"置信度评估失败: {e}")
            return self._get_fallback_confidence()
    
    def _calculate_base_prediction_confidence(self, number_probs, predicted_numbers):
        """计算基础预测置信度"""
        if not number_probs or not predicted_numbers:
            return 0.3
        
        try:
            # 获取预测数字的概率
            pred_probs = [number_probs.get(num, 0) for num in predicted_numbers]
            base_conf = np.mean(pred_probs) if pred_probs else 0
            
            # 计算概率分布的集中度
            all_probs = list(number_probs.values())
            if len(all_probs) > 1:
                concentration = np.std(all_probs) * 2
            else:
                concentration = 0
            
            # 计算预测数字与其他数字的概率差距
            other_probs = [p for n, p in number_probs.items() if n not in predicted_numbers]
            if other_probs and pred_probs:
                prob_advantage = max(0, np.mean(pred_probs) - np.mean(other_probs))
            else:
                prob_advantage = 0
            
            # 综合计算
            final_conf = 0.4 * base_conf + 0.3 * concentration + 0.3 * prob_advantage
            return max(0.1, min(0.9, final_conf))
            
        except Exception as e:
            logger.warning(f"基础预测置信度计算失败: {e}")
            return 0.3
    
    def _calculate_historical_performance_confidence(self, predicted_numbers):
        """计算历史表现置信度"""
        if not self.prediction_history:
            return 0.5
        
        try:
            # 计算每个数字的历史命中率
            hit_rates = {}
            for num in range(1, 50):
                predictions = [p for p in self.prediction_history 
                             if num in p.get('predicted_numbers', [])]
                if predictions:
                    hits = [p for p in predictions if p.get('is_hit', False)]
                    hit_rates[num] = len(hits) / len(predictions)
                else:
                    hit_rates[num] = 0.5
            
            # 计算当前预测数字的平均历史命中率
            pred_hit_rates = [hit_rates.get(num, 0.5) for num in predicted_numbers]
            avg_hit_rate = np.mean(pred_hit_rates) if pred_hit_rates else 0.5
            
            return max(0.1, min(0.9, avg_hit_rate))
            
        except Exception as e:
            logger.warning(f"历史表现置信度计算失败: {e}")
            return 0.5
    
    def _calculate_pattern_stability_confidence(self, previous_numbers, period_idx):
        """计算模式稳定性置信度"""
        if not previous_numbers:
            return 0.5
        
        try:
            # 定义基本模式特征
            pattern_features = self._extract_pattern_features(previous_numbers)
            
            # 基于周期性的稳定性评估
            stability_base = 0.5 + 0.1 * np.sin(period_idx * 0.1)
            stability_trend = 0.05 * np.cos(period_idx * 0.05)
            
            # 基于模式特征的稳定性
            feature_stability = self._evaluate_feature_stability(pattern_features)
            
            # 综合计算
            stability_conf = 0.4 * stability_base + 0.2 * stability_trend + 0.4 * feature_stability
            
            return max(0.3, min(0.9, stability_conf))
            
        except Exception as e:
            logger.warning(f"模式稳定性置信度计算失败: {e}")
            return 0.5
    
    def _extract_pattern_features(self, numbers):
        """提取模式特征"""
        if not numbers:
            return {}
        
        features = {}
        
        # 奇偶特征
        odd_count = sum(1 for n in numbers if n % 2 == 1)
        features['odd_ratio'] = odd_count / len(numbers)
        
        # 大小特征
        large_count = sum(1 for n in numbers if n > 25)
        features['large_ratio'] = large_count / len(numbers)
        
        # 和值特征
        features['sum'] = sum(numbers)
        features['avg'] = features['sum'] / len(numbers)
        
        # 跨度特征
        features['span'] = max(numbers) - min(numbers)
        
        # 连号特征
        sorted_nums = sorted(numbers)
        consecutive_count = 0
        for i in range(len(sorted_nums) - 1):
            if sorted_nums[i+1] - sorted_nums[i] == 1:
                consecutive_count += 1
        features['consecutive_ratio'] = consecutive_count / max(1, len(numbers) - 1)
        
        return features
    
    def _evaluate_feature_stability(self, features):
        """评估特征稳定性"""
        # 简化的稳定性评估
        stability_scores = []
        
        # 奇偶比例稳定性
        odd_ratio = features.get('odd_ratio', 0.5)
        odd_stability = 1 - abs(odd_ratio - 0.5) * 2  # 越接近0.5越稳定
        stability_scores.append(odd_stability)
        
        # 大小比例稳定性
        large_ratio = features.get('large_ratio', 0.5)
        size_stability = 1 - abs(large_ratio - 0.5) * 2
        stability_scores.append(size_stability)
        
        # 和值稳定性
        avg_val = features.get('avg', 25)
        avg_stability = 1 - abs(avg_val - 25) / 25  # 越接近25越稳定
        stability_scores.append(max(0, avg_stability))
        
        return np.mean(stability_scores) if stability_scores else 0.5
    
    def _calculate_data_quality_confidence(self, previous_numbers, data_source):
        """计算数据质量置信度"""
        try:
            # 数据来源质量评分
            source_quality = {
                "真实数据": 1.0,
                "用户验证": 0.9,
                "自动验证": 0.8,
                "历史数据": 0.85,
                "用户输入": 0.9,
                "预测数据": 0.6
            }
            
            # 数据完整性评分
            completeness = len(previous_numbers) / 6 if previous_numbers else 0
            
            # 数据一致性评分
            consistency = self._check_data_consistency(previous_numbers)
            
            # 综合计算
            quality_conf = (
                0.5 * source_quality.get(data_source, 0.6) +
                0.3 * completeness +
                0.2 * consistency
            )
            
            return max(0.4, min(1.0, quality_conf))
            
        except Exception as e:
            logger.warning(f"数据质量置信度计算失败: {e}")
            return 0.6
    
    def _check_data_consistency(self, numbers):
        """检查数据一致性"""
        if not numbers:
            return 0.0
        
        try:
            # 检查数字范围
            range_check = all(1 <= n <= 49 for n in numbers)
            
            # 检查重复
            unique_check = len(numbers) == len(set(numbers))
            
            # 检查数量
            count_check = len(numbers) <= 6
            
            consistency = (range_check + unique_check + count_check) / 3
            return consistency
            
        except Exception:
            return 0.0
    
    def _calculate_prediction_consistency_confidence(self, predicted_numbers, candidates):
        """计算预测一致性置信度"""
        if not candidates or len(candidates) < 2:
            return 0.5
        
        try:
            # 计算候选预测中包含预测数字的比例
            consistency_scores = []
            for num in predicted_numbers:
                count = sum(1 for candidate in candidates if num in candidate)
                consistency = count / len(candidates)
                consistency_scores.append(consistency)
            
            # 计算平均一致性
            avg_consistency = np.mean(consistency_scores) if consistency_scores else 0.5
            
            # 计算候选预测的多样性
            diversity = self._calculate_candidates_diversity(candidates)
            
            # 综合计算
            consistency_conf = 0.7 * avg_consistency + 0.3 * (1 - diversity)
            
            return max(0.2, min(0.9, consistency_conf))
            
        except Exception as e:
            logger.warning(f"预测一致性置信度计算失败: {e}")
            return 0.5
    
    def _calculate_candidates_diversity(self, candidates):
        """计算候选预测的多样性"""
        if not candidates or len(candidates) < 2:
            return 0.0
        
        try:
            # 计算所有候选中不同数字的数量
            all_numbers = set()
            for candidate in candidates:
                all_numbers.update(candidate)
            
            # 多样性 = 不同数字数量 / 理论最大数字数量
            max_possible = min(49, len(candidates) * 2)
            diversity = len(all_numbers) / max_possible if max_possible > 0 else 0
            
            return min(1.0, diversity)
            
        except Exception:
            return 0.0
    
    def _get_fallback_confidence(self):
        """获取备用置信度"""
        return {
            'final_confidence': 0.3,
            'confidence_details': {
                'base_prediction': 0.3,
                'historical_performance': 0.3,
                'pattern_stability': 0.3,
                'data_quality': 0.3,
                'prediction_consistency': 0.3
            },
            'evaluation_timestamp': datetime.now().isoformat(),
            'config_version': self.config['version'],
            'fallback': True
        }
    
    def update_prediction_result(self, prediction_result):
        """更新预测结果到历史记录"""
        try:
            self.prediction_history.append(prediction_result)
            self.last_update = datetime.now()
            
            if self.config['enable_logging']:
                logger.info(f"预测结果已更新到历史记录，当前历史记录数: {len(self.prediction_history)}")
                
        except Exception as e:
            logger.error(f"更新预测结果失败: {e}")
    
    def get_system_status(self):
        """获取系统状态"""
        return {
            'status': 'active',
            'history_count': len(self.prediction_history),
            'last_update': self.last_update.isoformat(),
            'config_version': self.config['version'],
            'cache_size': len(self.performance_cache)
        }
    
    def export_config(self, filename):
        """导出当前配置"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            logger.info(f"配置已导出到: {filename}")
            return True
        except Exception as e:
            logger.error(f"配置导出失败: {e}")
            return False

# 便捷函数，用于快速集成到现有系统
def create_confidence_evaluator(config_file=None):
    """创建置信度评估器实例"""
    return ProductionConfidenceEvaluator(config_file)

def evaluate_prediction_confidence(predicted_numbers, prediction_context, evaluator=None):
    """
    评估预测置信度的便捷函数
    
    Args:
        predicted_numbers: 预测数字列表
        prediction_context: 预测上下文
        evaluator: 评估器实例（可选，如果不提供会创建新实例）
    
    Returns:
        dict: 置信度评估结果
    """
    if evaluator is None:
        evaluator = create_confidence_evaluator()
    
    return evaluator.evaluate_confidence(predicted_numbers, prediction_context)

# 示例用法
if __name__ == "__main__":
    # 创建评估器
    evaluator = create_confidence_evaluator()
    
    # 示例预测上下文
    context = {
        'previous_numbers': [1, 15, 23, 30, 35, 42],
        'number_probs': {i: 0.02 for i in range(1, 50)},  # 示例概率分布
        'candidates': [[5, 40], [3, 30], [15, 25]],
        'data_source': '真实数据',
        'period_idx': 100
    }
    
    # 评估置信度
    result = evaluator.evaluate_confidence([5, 40], context)
    
    print("🎯 置信度评估结果:")
    print(f"  最终置信度: {result['final_confidence']:.3f}")
    print(f"  基础预测: {result['confidence_details']['base_prediction']:.3f}")
    print(f"  历史表现: {result['confidence_details']['historical_performance']:.3f}")
    print(f"  模式稳定: {result['confidence_details']['pattern_stability']:.3f}")
    print(f"  数据质量: {result['confidence_details']['data_quality']:.3f}")
    print(f"  预测一致: {result['confidence_details']['prediction_consistency']:.3f}")
    
    # 获取系统状态
    status = evaluator.get_system_status()
    print(f"\n📊 系统状态: {status}")
