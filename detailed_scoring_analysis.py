#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细评分分析
Detailed scoring analysis to find the real issues
"""

import pandas as pd
import numpy as np

def analyze_high_scores():
    """分析异常高分记录"""
    print("📊 分析异常高分记录")
    print("="*50)
    
    try:
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        
        # 查找评分大于95的记录
        high_scores = df[pd.to_numeric(df['预测评分'], errors='coerce') > 95].copy()
        
        print(f"发现 {len(high_scores)} 条评分>95的记录:")
        
        for idx, row in high_scores.iterrows():
            print(f"\n第{idx+1}行:")
            print(f"   期号: {row['当期年份']}年{row['当期期号']}期")
            print(f"   预测: [{row['预测数字1']}, {row['预测数字2']}]")
            print(f"   置信度: {row['预测置信度']}")
            print(f"   评分: {row['预测评分']}")
            print(f"   等级: {row['评分等级']}")
            print(f"   实际开奖: [{row['实际数字1']}, {row['实际数字2']}, {row['实际数字3']}, {row['实际数字4']}, {row['实际数字5']}, {row['实际数字6']}]")
            print(f"   是否命中: {row['是否命中']}")
            print(f"   命中数字: {row['命中数字']}")
        
        return high_scores
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def analyze_hit_vs_score_correlation():
    """分析命中情况与评分的相关性"""
    print(f"\n📈 分析命中情况与评分的相关性")
    print("="*50)
    
    try:
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        
        # 过滤有效数据
        valid_data = df[
            df['是否命中'].notna() & 
            (df['是否命中'] != '') &
            pd.to_numeric(df['预测评分'], errors='coerce').notna()
        ].copy()
        
        valid_data['评分数值'] = pd.to_numeric(valid_data['预测评分'], errors='coerce')
        
        # 按命中情况分组分析
        hit_data = valid_data[valid_data['是否命中'] == '是']
        miss_data = valid_data[valid_data['是否命中'] == '否']
        
        print(f"命中记录统计:")
        print(f"   总数: {len(hit_data)}")
        print(f"   平均评分: {hit_data['评分数值'].mean():.1f}")
        print(f"   评分范围: {hit_data['评分数值'].min():.1f} - {hit_data['评分数值'].max():.1f}")
        
        print(f"\n未命中记录统计:")
        print(f"   总数: {len(miss_data)}")
        print(f"   平均评分: {miss_data['评分数值'].mean():.1f}")
        print(f"   评分范围: {miss_data['评分数值'].min():.1f} - {miss_data['评分数值'].max():.1f}")
        
        # 分析高分但未命中的记录
        high_score_miss = miss_data[miss_data['评分数值'] > 80]
        
        print(f"\n🚨 高分但未命中的异常记录 ({len(high_score_miss)}条):")
        
        for idx, row in high_score_miss.iterrows():
            print(f"\n第{idx+1}行:")
            print(f"   期号: {row['当期年份']}年{row['当期期号']}期")
            print(f"   预测: [{row['预测数字1']}, {row['预测数字2']}]")
            print(f"   评分: {row['预测评分']} ({row['评分等级']})")
            print(f"   实际: [{row['实际数字1']}, {row['实际数字2']}, {row['实际数字3']}, {row['实际数字4']}, {row['实际数字5']}, {row['实际数字6']}]")
            print(f"   命中: {row['是否命中']}")
            
            # 手动验证命中情况
            try:
                pred1 = int(float(row['预测数字1']))
                pred2 = int(float(row['预测数字2']))
                actual = [
                    int(float(row['实际数字1'])),
                    int(float(row['实际数字2'])),
                    int(float(row['实际数字3'])),
                    int(float(row['实际数字4'])),
                    int(float(row['实际数字5'])),
                    int(float(row['实际数字6']))
                ]
                
                manual_hits = []
                if pred1 in actual:
                    manual_hits.append(pred1)
                if pred2 in actual:
                    manual_hits.append(pred2)
                
                print(f"   手动验证: {'命中' if manual_hits else '未命中'} {manual_hits}")
                
                if manual_hits and row['是否命中'] == '否':
                    print(f"   ⚠️ 发现错误：应该命中但记录为未命中！")
                elif not manual_hits and row['是否命中'] == '是':
                    print(f"   ⚠️ 发现错误：不应该命中但记录为命中！")
                    
            except (ValueError, TypeError):
                print(f"   ❌ 数据格式错误，无法验证")
        
        return high_score_miss
        
    except Exception as e:
        print(f"❌ 相关性分析失败: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def analyze_scoring_algorithm_issues():
    """分析评分算法问题"""
    print(f"\n🔍 分析评分算法问题")
    print("="*50)
    
    print("评分算法逻辑检查:")
    print("1. 基础评分 = 置信度 × 1000")
    print("2. 数字特征调整:")
    print("   - 和数40-60: ×1.2")
    print("   - 差值10-20: ×1.1") 
    print("   - 高频数字[2,3,5,15,16]: ×1.3")
    print("3. 分数范围: 10-100")
    print("4. 等级划分:")
    print("   - A+: 80-100分")
    print("   - A: 70-79分")
    print("   - B: 60-69分")
    print("   - C: 50-59分")
    print("   - D: 0-49分")
    
    # 模拟几个评分计算
    test_cases = [
        {'pred': [2, 15], 'confidence': 0.028, 'expected_high': True},
        {'pred': [30, 40], 'confidence': 0.025, 'expected_high': False},
        {'pred': [43, 2], 'confidence': 0.028, 'expected_high': False}
    ]
    
    print(f"\n🧪 评分算法测试:")
    
    for i, case in enumerate(test_cases):
        pred1, pred2 = case['pred']
        confidence = case['confidence']
        
        # 模拟评分计算
        base_score = confidence * 1000
        
        # 数字特征调整
        num_sum = pred1 + pred2
        num_diff = abs(pred1 - pred2)
        
        if num_sum >= 40 and num_sum <= 60:
            base_score *= 1.2
        if num_diff >= 10 and num_diff <= 20:
            base_score *= 1.1
        if pred1 in [2, 3, 5, 15, 16] or pred2 in [2, 3, 5, 15, 16]:
            base_score *= 1.3
        
        final_score = max(10, min(100, base_score))
        
        print(f"   测试{i+1}: 预测{case['pred']}, 置信度{confidence}")
        print(f"     计算过程: {confidence}×1000 = {confidence*1000}")
        print(f"     和数调整: {num_sum} ({'是' if 40<=num_sum<=60 else '否'})")
        print(f"     差值调整: {num_diff} ({'是' if 10<=num_diff<=20 else '否'})")
        print(f"     高频调整: {'是' if pred1 in [2,3,5,15,16] or pred2 in [2,3,5,15,16] else '否'}")
        print(f"     最终评分: {final_score:.1f}")

def identify_root_cause():
    """识别根本原因"""
    print(f"\n🎯 识别问题根本原因")
    print("="*50)
    
    print("可能的问题原因:")
    print("1. 📊 评分算法过于激进")
    print("   - 高频数字加成1.3倍可能过高")
    print("   - 多重加成叠加导致分数虚高")
    print("   - 置信度基础分数计算可能有问题")
    
    print("\n2. 🎯 命中判断逻辑正确")
    print("   - 经过验证，命中判断算法是正确的")
    print("   - 用户可能误解了某些案例")
    print("   - 预测[43,2]对实际[3,7,15,33,39,45]确实未命中")
    
    print("\n3. 📈 评分与实际表现不符")
    print("   - 高评分预测的实际命中率需要验证")
    print("   - 可能存在评分算法与实际预测能力不匹配")
    print("   - 需要重新校准评分系统")

def main():
    """主函数"""
    print("🔍 详细评分和命中逻辑分析")
    print("="*60)
    
    # 1. 分析异常高分
    high_scores = analyze_high_scores()
    
    # 2. 分析命中与评分相关性
    high_score_miss = analyze_hit_vs_score_correlation()
    
    # 3. 分析评分算法
    analyze_scoring_algorithm_issues()
    
    # 4. 识别根本原因
    identify_root_cause()
    
    print(f"\n📋 总结:")
    print(f"   1. 命中判断逻辑是正确的 ✅")
    print(f"   2. 评分算法可能过于乐观 ⚠️")
    print(f"   3. 高分预测的实际命中率需要验证 🔍")
    print(f"   4. 建议重新校准评分系统 💡")

if __name__ == "__main__":
    main()
