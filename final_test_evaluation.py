#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试集评估
Final Test Set Evaluation

对第191-195期进行最终评估，验证系统在未见数据上的表现

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0
"""

import pandas as pd
import numpy as np
from datetime import datetime
import json
import matplotlib.pyplot as plt
from sklearn.metrics import brier_score_loss
from confidence_backtest_validation import ConfidenceBacktestValidator

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class FinalTestEvaluator:
    """最终测试评估器"""
    
    def __init__(self):
        """初始化评估器"""
        self.validator = ConfidenceBacktestValidator()
        print("🎯 最终测试集评估器初始化")
        print("="*40)
    
    def load_test_data(self):
        """加载测试数据"""
        print("📊 加载测试数据")
        print("="*20)
        
        df = pd.read_csv('prediction_data.csv')
        df_clean = df[df['实际数字1'].notna()].copy()
        
        # 测试集：第191-195期
        test_data = df_clean[
            (df_clean['当期期号'] >= 191) & (df_clean['当期期号'] <= 195)
        ].copy()
        
        # 历史数据：第1-190期（用于置信度调整）
        historical_data = df_clean[df_clean['当期期号'] <= 190].copy()
        
        print(f"测试数据: {len(test_data)} 期 (第191-195期)")
        print(f"历史数据: {len(historical_data)} 期 (第1-190期)")
        
        return test_data, historical_data
    
    def evaluate_test_set(self, test_data, historical_data):
        """评估测试集"""
        print("\n🔬 评估测试集表现")
        print("="*30)
        
        # 准备历史性能数据
        historical_performance = []
        for _, row in historical_data.iterrows():
            features = self.validator.extract_prediction_features(row)
            historical_performance.append({
                'period': features['period'],
                'adjusted_confidence': features['original_confidence'] * 1.1,  # 模拟调整
                'is_hit': features['is_hit'],
                'hit_count': features['hit_count']
            })
        
        # 评估测试集
        test_results = []
        
        for _, row in test_data.iterrows():
            # 提取特征
            features = self.validator.extract_prediction_features(row)
            
            # 使用历史数据进行置信度调整
            adjusted_confidence, adjustment_factor, adjustment_details = \
                self.validator.simulate_dynamic_confidence_adjustment(features, historical_performance)
            
            # 记录结果
            test_result = {
                'period': features['period'],
                'original_confidence': features['original_confidence'],
                'adjusted_confidence': adjusted_confidence,
                'adjustment_factor': adjustment_factor,
                'predicted_numbers': features['predicted_numbers'],
                'actual_numbers': features['actual_numbers'],
                'is_hit': features['is_hit'],
                'hit_count': features['hit_count'],
                'hit_numbers': features['hit_numbers'],
                'adjustment_details': adjustment_details
            }
            
            test_results.append(test_result)
            
            print(f"  第{features['period']}期: {features['predicted_numbers']} → {features['actual_numbers']}")
            print(f"    置信度: {features['original_confidence']:.3f} → {adjusted_confidence:.3f}")
            print(f"    命中: {'✅' if features['is_hit'] else '❌'} ({features['hit_count']}个)")
        
        return test_results
    
    def calculate_test_metrics(self, test_results):
        """计算测试指标"""
        print("\n📊 计算测试指标")
        print("="*20)
        
        # 基本统计
        total_tests = len(test_results)
        hit_count = sum(1 for r in test_results if r['is_hit'])
        hit_rate = hit_count / total_tests
        
        # 置信度统计
        original_confidences = [r['original_confidence'] for r in test_results]
        adjusted_confidences = [r['adjusted_confidence'] for r in test_results]
        adjustment_factors = [r['adjustment_factor'] for r in test_results]
        
        # Brier Score
        hit_flags = [1 if r['is_hit'] else 0 for r in test_results]
        brier_score_original = brier_score_loss(hit_flags, original_confidences)
        brier_score_adjusted = brier_score_loss(hit_flags, adjusted_confidences)
        
        # 校准分析
        mean_confidence = np.mean(adjusted_confidences)
        calibration_gap = hit_rate - mean_confidence
        
        metrics = {
            'total_tests': total_tests,
            'hit_count': hit_count,
            'hit_rate': hit_rate,
            'mean_original_confidence': np.mean(original_confidences),
            'mean_adjusted_confidence': np.mean(adjusted_confidences),
            'mean_adjustment_factor': np.mean(adjustment_factors),
            'brier_score_original': brier_score_original,
            'brier_score_adjusted': brier_score_adjusted,
            'brier_score_improvement': brier_score_original - brier_score_adjusted,
            'calibration_gap': calibration_gap,
            'confidence_improvement_rate': np.mean([
                1 if adj > orig else 0 
                for adj, orig in zip(adjusted_confidences, original_confidences)
            ])
        }
        
        print(f"  测试期数: {total_tests}")
        print(f"  命中期数: {hit_count}")
        print(f"  命中率: {hit_rate:.1%}")
        print(f"  原始Brier Score: {brier_score_original:.4f}")
        print(f"  调整后Brier Score: {brier_score_adjusted:.4f}")
        print(f"  Brier Score改进: {metrics['brier_score_improvement']:.4f}")
        print(f"  校准差距: {calibration_gap:.3f}")
        print(f"  置信度提升率: {metrics['confidence_improvement_rate']:.1%}")
        
        return metrics
    
    def create_test_visualization(self, test_results, metrics):
        """创建测试可视化"""
        print("\n📊 生成测试可视化")
        print("="*20)
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('最终测试集评估结果', fontsize=16, fontweight='bold')
        
        # 1. 置信度对比
        ax1 = axes[0, 0]
        periods = [r['period'] for r in test_results]
        original_conf = [r['original_confidence'] for r in test_results]
        adjusted_conf = [r['adjusted_confidence'] for r in test_results]
        
        x = np.arange(len(periods))
        width = 0.35
        
        ax1.bar(x - width/2, original_conf, width, label='原始置信度', alpha=0.7)
        ax1.bar(x + width/2, adjusted_conf, width, label='调整置信度', alpha=0.7)
        ax1.set_xlabel('期号')
        ax1.set_ylabel('置信度')
        ax1.set_title('置信度对比')
        ax1.set_xticks(x)
        ax1.set_xticklabels([f'{p}期' for p in periods])
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 命中情况
        ax2 = axes[0, 1]
        hit_flags = [r['is_hit'] for r in test_results]
        colors = ['green' if hit else 'red' for hit in hit_flags]
        
        ax2.bar(range(len(periods)), [1]*len(periods), color=colors, alpha=0.7)
        ax2.set_xlabel('期号')
        ax2.set_ylabel('命中状态')
        ax2.set_title('命中情况')
        ax2.set_xticks(range(len(periods)))
        ax2.set_xticklabels([f'{p}期' for p in periods])
        ax2.set_ylim(0, 1.2)
        
        # 添加命中标记
        for i, (period, hit) in enumerate(zip(periods, hit_flags)):
            ax2.text(i, 0.5, '✅' if hit else '❌', ha='center', va='center', fontsize=12)
        
        # 3. 调整因子
        ax3 = axes[1, 0]
        adjustment_factors = [r['adjustment_factor'] for r in test_results]
        
        ax3.plot(periods, adjustment_factors, 'bo-', linewidth=2, markersize=8)
        ax3.axhline(1.0, color='red', linestyle='--', label='无调整线')
        ax3.set_xlabel('期号')
        ax3.set_ylabel('调整因子')
        ax3.set_title('置信度调整因子')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 性能指标总结
        ax4 = axes[1, 1]
        ax4.axis('off')
        
        # 创建性能指标文本
        metrics_text = f"""
测试集性能总结

总测试期数: {metrics['total_tests']}
命中期数: {metrics['hit_count']}
命中率: {metrics['hit_rate']:.1%}

置信度调整效果:
原始平均置信度: {metrics['mean_original_confidence']:.3f}
调整后平均置信度: {metrics['mean_adjusted_confidence']:.3f}
平均调整因子: {metrics['mean_adjustment_factor']:.3f}

预测质量:
原始Brier Score: {metrics['brier_score_original']:.4f}
调整后Brier Score: {metrics['brier_score_adjusted']:.4f}
改进幅度: {metrics['brier_score_improvement']:.4f}

校准质量:
校准差距: {metrics['calibration_gap']:.3f}
置信度提升率: {metrics['confidence_improvement_rate']:.1%}
        """
        
        ax4.text(0.1, 0.9, metrics_text, transform=ax4.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace')
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'final_test_evaluation_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"测试可视化已保存: {filename}")
        
        return filename
    
    def generate_final_report(self, test_results, metrics):
        """生成最终报告"""
        print("\n📋 生成最终评估报告")
        print("="*20)
        
        # 创建详细报告
        report = {
            'evaluation_info': {
                'evaluation_date': datetime.now().isoformat(),
                'test_periods': '191-195',
                'total_tests': len(test_results),
                'system_version': '动态置信度调整系统 v2.0'
            },
            'test_results': test_results,
            'performance_metrics': metrics,
            'conclusions': self._generate_conclusions(metrics)
        }
        
        # 保存JSON报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_filename = f'final_test_report_{timestamp}.json'
        
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        # 生成文本报告
        text_report = self._generate_text_report(report)
        text_filename = f'final_test_summary_{timestamp}.txt'
        
        with open(text_filename, 'w', encoding='utf-8') as f:
            f.write(text_report)
        
        print(f"详细报告已保存: {json_filename}")
        print(f"摘要报告已保存: {text_filename}")
        
        return json_filename, text_filename
    
    def _generate_conclusions(self, metrics):
        """生成结论"""
        conclusions = []
        
        # 命中率评估
        if metrics['hit_rate'] >= 0.4:
            conclusions.append("✅ 命中率表现良好，达到预期水平")
        else:
            conclusions.append("⚠️ 命中率偏低，需要优化预测算法")
        
        # Brier Score评估
        if metrics['brier_score_improvement'] > 0:
            conclusions.append("✅ 动态置信度调整有效改善了预测质量")
        else:
            conclusions.append("⚠️ 动态置信度调整效果有限")
        
        # 校准评估
        if abs(metrics['calibration_gap']) < 0.1:
            conclusions.append("✅ 置信度校准良好，预测可靠性高")
        else:
            conclusions.append("⚠️ 置信度校准存在偏差，需要调整参数")
        
        # 系统稳定性
        if metrics['confidence_improvement_rate'] > 0.5:
            conclusions.append("✅ 系统稳定性良好，大部分预测获得置信度提升")
        else:
            conclusions.append("⚠️ 系统稳定性需要改进")
        
        return conclusions
    
    def _generate_text_report(self, report):
        """生成文本报告"""
        metrics = report['performance_metrics']
        
        text = f"""
动态置信度调整系统最终测试评估报告
{'='*60}

评估信息:
{'='*30}
评估日期: {report['evaluation_info']['evaluation_date'][:19]}
测试期间: 第{report['evaluation_info']['test_periods']}期
测试总数: {report['evaluation_info']['total_tests']}期
系统版本: {report['evaluation_info']['system_version']}

测试结果详情:
{'='*30}"""
        
        for result in report['test_results']:
            hit_mark = '✅' if result['is_hit'] else '❌'
            text += f"""
第{result['period']}期: {hit_mark}
  预测数字: {result['predicted_numbers']}
  实际数字: {result['actual_numbers']}
  命中数字: {result['hit_numbers']}
  原始置信度: {result['original_confidence']:.3f}
  调整置信度: {result['adjusted_confidence']:.3f}
  调整因子: {result['adjustment_factor']:.3f}"""
        
        text += f"""

性能指标总结:
{'='*30}
总体命中率: {metrics['hit_rate']:.1%} ({metrics['hit_count']}/{metrics['total_tests']})
原始平均置信度: {metrics['mean_original_confidence']:.3f}
调整后平均置信度: {metrics['mean_adjusted_confidence']:.3f}
平均调整因子: {metrics['mean_adjustment_factor']:.3f}

预测质量评估:
原始Brier Score: {metrics['brier_score_original']:.4f}
调整后Brier Score: {metrics['brier_score_adjusted']:.4f}
Brier Score改进: {metrics['brier_score_improvement']:.4f}

校准质量评估:
校准差距: {metrics['calibration_gap']:.3f}
置信度提升率: {metrics['confidence_improvement_rate']:.1%}

结论和建议:
{'='*30}"""
        
        for conclusion in report['conclusions']:
            text += f"\n{conclusion}"
        
        text += f"""

最终评级:
{'='*30}
预测准确性: {'优秀' if metrics['hit_rate'] >= 0.5 else '良好' if metrics['hit_rate'] >= 0.3 else '需改进'}
置信度校准: {'优秀' if abs(metrics['calibration_gap']) < 0.05 else '良好' if abs(metrics['calibration_gap']) < 0.1 else '需改进'}
系统改进效果: {'显著' if metrics['brier_score_improvement'] > 0.01 else '轻微' if metrics['brier_score_improvement'] > 0 else '无效果'}
整体表现: {'优秀' if metrics['hit_rate'] >= 0.4 and metrics['brier_score_improvement'] > 0 else '良好' if metrics['hit_rate'] >= 0.3 else '需改进'}

---
报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
评估系统版本: 最终测试评估器 v1.0
"""
        
        return text
    
    def run_final_evaluation(self):
        """运行最终评估"""
        print("🎯 启动最终测试集评估")
        print("="*50)
        
        try:
            # 1. 加载测试数据
            test_data, historical_data = self.load_test_data()
            
            # 2. 评估测试集
            test_results = self.evaluate_test_set(test_data, historical_data)
            
            # 3. 计算测试指标
            metrics = self.calculate_test_metrics(test_results)
            
            # 4. 创建可视化
            chart_filename = self.create_test_visualization(test_results, metrics)
            
            # 5. 生成最终报告
            json_report, text_report = self.generate_final_report(test_results, metrics)
            
            print("\n" + "="*50)
            print("✅ 最终测试集评估完成！")
            print("="*50)
            
            print(f"\n🎯 最终评估结果:")
            print(f"  测试期数: {metrics['total_tests']}")
            print(f"  命中率: {metrics['hit_rate']:.1%}")
            print(f"  Brier Score改进: {metrics['brier_score_improvement']:.4f}")
            print(f"  校准差距: {metrics['calibration_gap']:.3f}")
            
            print(f"\n📁 生成文件:")
            print(f"  可视化图表: {chart_filename}")
            print(f"  详细报告: {json_report}")
            print(f"  摘要报告: {text_report}")
            
            return {
                'test_results': test_results,
                'metrics': metrics,
                'chart_filename': chart_filename,
                'json_report': json_report,
                'text_report': text_report
            }
            
        except Exception as e:
            print(f"\n❌ 评估过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    evaluator = FinalTestEvaluator()
    results = evaluator.run_final_evaluation()
    
    if results:
        print(f"\n🎉 最终评估成功完成！")
    else:
        print(f"\n❌ 最终评估失败！")

if __name__ == "__main__":
    main()
