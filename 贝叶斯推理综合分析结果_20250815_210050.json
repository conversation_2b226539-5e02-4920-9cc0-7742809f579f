{
  "analysis_timestamp": "20250815_210050",
  "data_summary": {
    "train_periods": 366,
    "test_periods": 179,
    "train_data_file": "2024年训练数据.csv",
    "test_data_file": "2025年测试数据.csv"
  },
  "analysis_results": {
    "bayesian_frequencies": {
      "1": {
        "count": 37,
        "frequency": 0.016848816029143898,
        "bayesian_prob": 0.017288444040036398,
        "ci_lower": 0.012268231109004515,
        "ci_upper": 0.023139202356349364,
        "alpha_posterior": 38,
        "beta_posterior": 2160
      },
      "2": {
        "count": 52,
        "frequency": 0.023679417122040074,
        "bayesian_prob": 0.024112829845313922,
        "ci_lower": 0.01812138185596096,
        "ci_upper": 0.030923527760143333,
        "alpha_posterior": 53,
        "beta_posterior": 2145
      },
      "3": {
        "count": 51,
        "frequency": 0.023224043715846996,
        "bayesian_prob": 0.023657870791628753,
        "ci_lower": 0.0177262012988127,
        "ci_upper": 0.030409551718421756,
        "alpha_posterior": 52,
        "beta_posterior": 2146
      },
      "4": {
        "count": 28,
        "frequency": 0.012750455373406194,
        "bayesian_prob": 0.013193812556869881,
        "ci_lower": 0.008857465771989377,
        "ci_upper": 0.01836724619371324,
        "alpha_posterior": 29,
        "beta_posterior": 2169
      },
      "5": {
        "count": 66,
        "frequency": 0.030054644808743168,
        "bayesian_prob": 0.030482256596906277,
        "ci_lower": 0.02371056236518541,
        "ci_upper": 0.038062467801310396,
        "alpha_posterior": 67,
        "beta_posterior": 2131
      },
      "6": {
        "count": 41,
        "frequency": 0.018670309653916212,
        "bayesian_prob": 0.01910828025477707,
        "ci_lower": 0.013811705030652474,
        "ci_upper": 0.02523242178374953,
        "alpha_posterior": 42,
        "beta_posterior": 2156
      },
      "7": {
        "count": 43,
        "frequency": 0.019581056466302368,
        "bayesian_prob": 0.020018198362147407,
        "ci_lower": 0.0145886545224123,
        "ci_upper": 0.026273808096915742,
        "alpha_posterior": 44,
        "beta_posterior": 2154
      },
      "8": {
        "count": 32,
        "frequency": 0.014571948998178506,
        "bayesian_prob": 0.015013648771610554,
        "ci_lower": 0.010361372639535281,
        "ci_upper": 0.020500139645810423,
        "alpha_posterior": 33,
        "beta_posterior": 2165
      },
      "9": {
        "count": 46,
        "frequency": 0.020947176684881604,
        "bayesian_prob": 0.021383075523202913,
        "ci_lower": 0.015759950665878684,
        "ci_upper": 0.02783000469518169,
        "alpha_posterior": 47,
        "beta_posterior": 2151
      },
      "10": {
        "count": 52,
        "frequency": 0.023679417122040074,
        "bayesian_prob": 0.024112829845313922,
        "ci_lower": 0.01812138185596096,
        "ci_upper": 0.030923527760143333,
        "alpha_posterior": 53,
        "beta_posterior": 2145
      },
      "11": {
        "count": 46,
        "frequency": 0.020947176684881604,
        "bayesian_prob": 0.021383075523202913,
        "ci_lower": 0.015759950665878684,
        "ci_upper": 0.02783000469518169,
        "alpha_posterior": 47,
        "beta_posterior": 2151
      },
      "12": {
        "count": 50,
        "frequency": 0.022768670309653915,
        "bayesian_prob": 0.023202911737943584,
        "ci_lower": 0.01733163944161955,
        "ci_upper": 0.029894956076486907,
        "alpha_posterior": 51,
        "beta_posterior": 2147
      },
      "13": {
        "count": 44,
        "frequency": 0.020036429872495445,
        "bayesian_prob": 0.020473157415832575,
        "ci_lower": 0.014978329436911264,
        "ci_upper": 0.026793298793508866,
        "alpha_posterior": 45,
        "beta_posterior": 2153
      },
      "14": {
        "count": 30,
        "frequency": 0.01366120218579235,
        "bayesian_prob": 0.014103730664240218,
        "ci_lower": 0.009606760612156364,
        "ci_upper": 0.019436360163245705,
        "alpha_posterior": 31,
        "beta_posterior": 2167
      },
      "15": {
        "count": 61,
        "frequency": 0.027777777777777776,
        "bayesian_prob": 0.028207461328480437,
        "ci_lower": 0.021703272378996573,
        "ci_upper": 0.03552401334404173,
        "alpha_posterior": 62,
        "beta_posterior": 2136
      },
      "16": {
        "count": 48,
        "frequency": 0.02185792349726776,
        "bayesian_prob": 0.022292993630573247,
        "ci_lower": 0.016544444284650173,
        "ci_upper": 0.02886383331235595,
        "alpha_posterior": 49,
        "beta_posterior": 2149
      },
      "17": {
        "count": 47,
        "frequency": 0.021402550091074682,
        "bayesian_prob": 0.02183803457688808,
        "ci_lower": 0.01615184952133731,
        "ci_upper": 0.028347267531010512,
        "alpha_posterior": 48,
        "beta_posterior": 2150
      },
      "18": {
        "count": 41,
        "frequency": 0.018670309653916212,
        "bayesian_prob": 0.01910828025477707,
        "ci_lower": 0.013811705030652474,
        "ci_upper": 0.02523242178374953,
        "alpha_posterior": 42,
        "beta_posterior": 2156
      },
      "19": {
        "count": 56,
        "frequency": 0.025500910746812388,
        "bayesian_prob": 0.025932666060054597,
        "ci_lower": 0.01970796151782136,
        "ci_upper": 0.03297356644472564,
        "alpha_posterior": 57,
        "beta_posterior": 2141
      },
      "20": {
        "count": 40,
        "frequency": 0.018214936247723135,
        "bayesian_prob": 0.0186533212010919,
        "ci_lower": 0.013424486624387173,
        "ci_upper": 0.02471046977070177,
        "alpha_posterior": 41,
        "beta_posterior": 2157
      },
      "21": {
        "count": 36,
        "frequency": 0.01639344262295082,
        "bayesian_prob": 0.01683348498635123,
        "ci_lower": 0.011884728376582923,
        "ci_upper": 0.0226135263574791,
        "alpha_posterior": 37,
        "beta_posterior": 2161
      },
      "22": {
        "count": 39,
        "frequency": 0.017759562841530054,
        "bayesian_prob": 0.018198362147406732,
        "ci_lower": 0.013038146084496716,
        "ci_upper": 0.02418763804410455,
        "alpha_posterior": 40,
        "beta_posterior": 2158
      },
      "23": {
        "count": 46,
        "frequency": 0.020947176684881604,
        "bayesian_prob": 0.021383075523202913,
        "ci_lower": 0.015759950665878684,
        "ci_upper": 0.02783000469518169,
        "alpha_posterior": 47,
        "beta_posterior": 2151
      },
      "24": {
        "count": 42,
        "frequency": 0.01912568306010929,
        "bayesian_prob": 0.019563239308462238,
        "ci_lower": 0.014199770630397433,
        "ci_upper": 0.02575352488607556,
        "alpha_posterior": 43,
        "beta_posterior": 2155
      },
      "25": {
        "count": 51,
        "frequency": 0.023224043715846996,
        "bayesian_prob": 0.023657870791628753,
        "ci_lower": 0.0177262012988127,
        "ci_upper": 0.030409551718421756,
        "alpha_posterior": 52,
        "beta_posterior": 2146
      },
      "26": {
        "count": 54,
        "frequency": 0.02459016393442623,
        "bayesian_prob": 0.02502274795268426,
        "ci_lower": 0.01891353164728209,
        "ci_upper": 0.031949688663475674,
        "alpha_posterior": 55,
        "beta_posterior": 2143
      },
      "27": {
        "count": 50,
        "frequency": 0.022768670309653915,
        "bayesian_prob": 0.023202911737943584,
        "ci_lower": 0.01733163944161955,
        "ci_upper": 0.029894956076486907,
        "alpha_posterior": 51,
        "beta_posterior": 2147
      },
      "28": {
        "count": 36,
        "frequency": 0.01639344262295082,
        "bayesian_prob": 0.01683348498635123,
        "ci_lower": 0.011884728376582923,
        "ci_upper": 0.0226135263574791,
        "alpha_posterior": 37,
        "beta_posterior": 2161
      },
      "29": {
        "count": 54,
        "frequency": 0.02459016393442623,
        "bayesian_prob": 0.02502274795268426,
        "ci_lower": 0.01891353164728209,
        "ci_upper": 0.031949688663475674,
        "alpha_posterior": 55,
        "beta_posterior": 2143
      },
      "30": {
        "count": 43,
        "frequency": 0.019581056466302368,
        "bayesian_prob": 0.020018198362147407,
        "ci_lower": 0.0145886545224123,
        "ci_upper": 0.026273808096915742,
        "alpha_posterior": 44,
        "beta_posterior": 2154
      },
      "31": {
        "count": 49,
        "frequency": 0.022313296903460837,
        "bayesian_prob": 0.022747952684258416,
        "ci_lower": 0.016937714174445253,
        "ci_upper": 0.029379722889875746,
        "alpha_posterior": 50,
        "beta_posterior": 2148
      },
      "32": {
        "count": 44,
        "frequency": 0.020036429872495445,
        "bayesian_prob": 0.020473157415832575,
        "ci_lower": 0.014978329436911264,
        "ci_upper": 0.026793298793508866,
        "alpha_posterior": 45,
        "beta_posterior": 2153
      },
      "33": {
        "count": 39,
        "frequency": 0.017759562841530054,
        "bayesian_prob": 0.018198362147406732,
        "ci_lower": 0.013038146084496716,
        "ci_upper": 0.02418763804410455,
        "alpha_posterior": 40,
        "beta_posterior": 2158
      },
      "34": {
        "count": 43,
        "frequency": 0.019581056466302368,
        "bayesian_prob": 0.020018198362147407,
        "ci_lower": 0.0145886545224123,
        "ci_upper": 0.026273808096915742,
        "alpha_posterior": 44,
        "beta_posterior": 2154
      },
      "35": {
        "count": 42,
        "frequency": 0.01912568306010929,
        "bayesian_prob": 0.019563239308462238,
        "ci_lower": 0.014199770630397433,
        "ci_upper": 0.02575352488607556,
        "alpha_posterior": 43,
        "beta_posterior": 2155
      },
      "36": {
        "count": 52,
        "frequency": 0.023679417122040074,
        "bayesian_prob": 0.024112829845313922,
        "ci_lower": 0.01812138185596096,
        "ci_upper": 0.030923527760143333,
        "alpha_posterior": 53,
        "beta_posterior": 2145
      },
      "37": {
        "count": 33,
        "frequency": 0.015027322404371584,
        "bayesian_prob": 0.015468607825295723,
        "ci_lower": 0.010740522462084198,
        "ci_upper": 0.021030180054723614,
        "alpha_posterior": 34,
        "beta_posterior": 2164
      },
      "38": {
        "count": 46,
        "frequency": 0.020947176684881604,
        "bayesian_prob": 0.021383075523202913,
        "ci_lower": 0.015759950665878684,
        "ci_upper": 0.02783000469518169,
        "alpha_posterior": 47,
        "beta_posterior": 2151
      },
      "39": {
        "count": 32,
        "frequency": 0.014571948998178506,
        "bayesian_prob": 0.015013648771610554,
        "ci_lower": 0.010361372639535281,
        "ci_upper": 0.020500139645810423,
        "alpha_posterior": 33,
        "beta_posterior": 2165
      },
      "40": {
        "count": 53,
        "frequency": 0.02413479052823315,
        "bayesian_prob": 0.02456778889899909,
        "ci_lower": 0.018517164061302663,
        "ci_upper": 0.03143690130375654,
        "alpha_posterior": 54,
        "beta_posterior": 2144
      },
      "41": {
        "count": 41,
        "frequency": 0.018670309653916212,
        "bayesian_prob": 0.01910828025477707,
        "ci_lower": 0.013811705030652474,
        "ci_upper": 0.02523242178374953,
        "alpha_posterior": 42,
        "beta_posterior": 2156
      },
      "42": {
        "count": 45,
        "frequency": 0.020491803278688523,
        "bayesian_prob": 0.020928116469517744,
        "ci_lower": 0.015368769609238496,
        "ci_upper": 0.027312022838548507,
        "alpha_posterior": 46,
        "beta_posterior": 2152
      },
      "43": {
        "count": 52,
        "frequency": 0.023679417122040074,
        "bayesian_prob": 0.024112829845313922,
        "ci_lower": 0.01812138185596096,
        "ci_upper": 0.030923527760143333,
        "alpha_posterior": 53,
        "beta_posterior": 2145
      },
      "44": {
        "count": 56,
        "frequency": 0.025500910746812388,
        "bayesian_prob": 0.025932666060054597,
        "ci_lower": 0.01970796151782136,
        "ci_upper": 0.03297356644472564,
        "alpha_posterior": 57,
        "beta_posterior": 2141
      },
      "45": {
        "count": 45,
        "frequency": 0.020491803278688523,
        "bayesian_prob": 0.020928116469517744,
        "ci_lower": 0.015368769609238496,
        "ci_upper": 0.027312022838548507,
        "alpha_posterior": 46,
        "beta_posterior": 2152
      },
      "46": {
        "count": 34,
        "frequency": 0.015482695810564663,
        "bayesian_prob": 0.01592356687898089,
        "ci_lower": 0.011120830252418942,
        "ci_upper": 0.021559059238583648,
        "alpha_posterior": 35,
        "beta_posterior": 2163
      },
      "47": {
        "count": 38,
        "frequency": 0.017304189435336976,
        "bayesian_prob": 0.017743403093721567,
        "ci_lower": 0.012652716011726963,
        "ci_upper": 0.023663893859832988,
        "alpha_posterior": 39,
        "beta_posterior": 2159
      },
      "48": {
        "count": 39,
        "frequency": 0.017759562841530054,
        "bayesian_prob": 0.018198362147406732,
        "ci_lower": 0.013038146084496716,
        "ci_upper": 0.02418763804410455,
        "alpha_posterior": 40,
        "beta_posterior": 2158
      },
      "49": {
        "count": 51,
        "frequency": 0.023224043715846996,
        "bayesian_prob": 0.023657870791628753,
        "ci_lower": 0.0177262012988127,
        "ci_upper": 0.030409551718421756,
        "alpha_posterior": 52,
        "beta_posterior": 2146
      }
    },
    "conditional_probabilities": {
      "number_dependencies": [
        {
          "number_a": 11,
          "number_b": 49,
          "conditional_prob": 0.2826086956521739,
          "joint_count": 13.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 37,
          "number_b": 5,
          "conditional_prob": 0.2727272727272727,
          "joint_count": 9.0,
          "marginal_count_a": 33.0
        },
        {
          "number_a": 45,
          "number_b": 27,
          "conditional_prob": 0.26666666666666666,
          "joint_count": 12.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 47,
          "number_b": 5,
          "conditional_prob": 0.2631578947368421,
          "joint_count": 10.0,
          "marginal_count_a": 38.0
        },
        {
          "number_a": 11,
          "number_b": 15,
          "conditional_prob": 0.2608695652173913,
          "joint_count": 12.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 12,
          "number_b": 15,
          "conditional_prob": 0.26,
          "joint_count": 13.0,
          "marginal_count_a": 50.0
        },
        {
          "number_a": 33,
          "number_b": 19,
          "conditional_prob": 0.2564102564102564,
          "joint_count": 10.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 49,
          "number_b": 11,
          "conditional_prob": 0.2549019607843137,
          "joint_count": 13.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 4,
          "number_b": 3,
          "conditional_prob": 0.25,
          "joint_count": 7.0,
          "marginal_count_a": 28.0
        },
        {
          "number_a": 8,
          "number_b": 36,
          "conditional_prob": 0.25,
          "joint_count": 8.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 10,
          "number_b": 5,
          "conditional_prob": 0.25,
          "joint_count": 13.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 28,
          "number_b": 49,
          "conditional_prob": 0.25,
          "joint_count": 9.0,
          "marginal_count_a": 36.0
        },
        {
          "number_a": 39,
          "number_b": 49,
          "conditional_prob": 0.25,
          "joint_count": 8.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 40,
          "number_b": 5,
          "conditional_prob": 0.24528301886792453,
          "joint_count": 13.0,
          "marginal_count_a": 53.0
        },
        {
          "number_a": 45,
          "number_b": 43,
          "conditional_prob": 0.24444444444444444,
          "joint_count": 11.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 37,
          "number_b": 40,
          "conditional_prob": 0.24242424242424243,
          "joint_count": 8.0,
          "marginal_count_a": 33.0
        },
        {
          "number_a": 27,
          "number_b": 45,
          "conditional_prob": 0.24,
          "joint_count": 12.0,
          "marginal_count_a": 50.0
        },
        {
          "number_a": 9,
          "number_b": 5,
          "conditional_prob": 0.2391304347826087,
          "joint_count": 11.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 9,
          "number_b": 19,
          "conditional_prob": 0.2391304347826087,
          "joint_count": 11.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 11,
          "number_b": 2,
          "conditional_prob": 0.2391304347826087,
          "joint_count": 11.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 14,
          "number_b": 9,
          "conditional_prob": 0.23333333333333334,
          "joint_count": 7.0,
          "marginal_count_a": 30.0
        },
        {
          "number_a": 14,
          "number_b": 16,
          "conditional_prob": 0.23333333333333334,
          "joint_count": 7.0,
          "marginal_count_a": 30.0
        },
        {
          "number_a": 22,
          "number_b": 19,
          "conditional_prob": 0.23076923076923078,
          "joint_count": 9.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 13,
          "number_b": 5,
          "conditional_prob": 0.22727272727272727,
          "joint_count": 10.0,
          "marginal_count_a": 44.0
        },
        {
          "number_a": 40,
          "number_b": 15,
          "conditional_prob": 0.22641509433962265,
          "joint_count": 12.0,
          "marginal_count_a": 53.0
        },
        {
          "number_a": 21,
          "number_b": 36,
          "conditional_prob": 0.2222222222222222,
          "joint_count": 8.0,
          "marginal_count_a": 36.0
        },
        {
          "number_a": 42,
          "number_b": 5,
          "conditional_prob": 0.2222222222222222,
          "joint_count": 10.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 45,
          "number_b": 44,
          "conditional_prob": 0.2222222222222222,
          "joint_count": 10.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 12,
          "number_b": 5,
          "conditional_prob": 0.22,
          "joint_count": 11.0,
          "marginal_count_a": 50.0
        },
        {
          "number_a": 6,
          "number_b": 5,
          "conditional_prob": 0.21951219512195122,
          "joint_count": 9.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 41,
          "number_b": 40,
          "conditional_prob": 0.21951219512195122,
          "joint_count": 9.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 8,
          "number_b": 43,
          "conditional_prob": 0.21875,
          "joint_count": 7.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 9,
          "number_b": 3,
          "conditional_prob": 0.21739130434782608,
          "joint_count": 10.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 38,
          "number_b": 3,
          "conditional_prob": 0.21739130434782608,
          "joint_count": 10.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 49,
          "number_b": 5,
          "conditional_prob": 0.21568627450980393,
          "joint_count": 11.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 4,
          "number_b": 19,
          "conditional_prob": 0.21428571428571427,
          "joint_count": 6.0,
          "marginal_count_a": 28.0
        },
        {
          "number_a": 4,
          "number_b": 45,
          "conditional_prob": 0.21428571428571427,
          "joint_count": 6.0,
          "marginal_count_a": 28.0
        },
        {
          "number_a": 4,
          "number_b": 49,
          "conditional_prob": 0.21428571428571427,
          "joint_count": 6.0,
          "marginal_count_a": 28.0
        },
        {
          "number_a": 15,
          "number_b": 12,
          "conditional_prob": 0.21311475409836064,
          "joint_count": 13.0,
          "marginal_count_a": 61.0
        },
        {
          "number_a": 17,
          "number_b": 3,
          "conditional_prob": 0.2127659574468085,
          "joint_count": 10.0,
          "marginal_count_a": 47.0
        },
        {
          "number_a": 37,
          "number_b": 16,
          "conditional_prob": 0.21212121212121213,
          "joint_count": 7.0,
          "marginal_count_a": 33.0
        },
        {
          "number_a": 2,
          "number_b": 11,
          "conditional_prob": 0.21153846153846154,
          "joint_count": 11.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 36,
          "number_b": 26,
          "conditional_prob": 0.21153846153846154,
          "joint_count": 11.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 43,
          "number_b": 19,
          "conditional_prob": 0.21153846153846154,
          "joint_count": 11.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 43,
          "number_b": 45,
          "conditional_prob": 0.21153846153846154,
          "joint_count": 11.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 47,
          "number_b": 10,
          "conditional_prob": 0.21052631578947367,
          "joint_count": 8.0,
          "marginal_count_a": 38.0
        },
        {
          "number_a": 47,
          "number_b": 40,
          "conditional_prob": 0.21052631578947367,
          "joint_count": 8.0,
          "marginal_count_a": 38.0
        },
        {
          "number_a": 7,
          "number_b": 29,
          "conditional_prob": 0.20930232558139536,
          "joint_count": 9.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 30,
          "number_b": 5,
          "conditional_prob": 0.20930232558139536,
          "joint_count": 9.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 30,
          "number_b": 15,
          "conditional_prob": 0.20930232558139536,
          "joint_count": 9.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 34,
          "number_b": 44,
          "conditional_prob": 0.20930232558139536,
          "joint_count": 9.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 46,
          "number_b": 6,
          "conditional_prob": 0.20588235294117646,
          "joint_count": 7.0,
          "marginal_count_a": 34.0
        },
        {
          "number_a": 46,
          "number_b": 25,
          "conditional_prob": 0.20588235294117646,
          "joint_count": 7.0,
          "marginal_count_a": 34.0
        },
        {
          "number_a": 46,
          "number_b": 49,
          "conditional_prob": 0.20588235294117646,
          "joint_count": 7.0,
          "marginal_count_a": 34.0
        },
        {
          "number_a": 22,
          "number_b": 5,
          "conditional_prob": 0.20512820512820512,
          "joint_count": 8.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 22,
          "number_b": 16,
          "conditional_prob": 0.20512820512820512,
          "joint_count": 8.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 33,
          "number_b": 15,
          "conditional_prob": 0.20512820512820512,
          "joint_count": 8.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 33,
          "number_b": 43,
          "conditional_prob": 0.20512820512820512,
          "joint_count": 8.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 48,
          "number_b": 29,
          "conditional_prob": 0.20512820512820512,
          "joint_count": 8.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 48,
          "number_b": 44,
          "conditional_prob": 0.20512820512820512,
          "joint_count": 8.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 13,
          "number_b": 15,
          "conditional_prob": 0.20454545454545456,
          "joint_count": 9.0,
          "marginal_count_a": 44.0
        },
        {
          "number_a": 13,
          "number_b": 40,
          "conditional_prob": 0.20454545454545456,
          "joint_count": 9.0,
          "marginal_count_a": 44.0
        },
        {
          "number_a": 32,
          "number_b": 44,
          "conditional_prob": 0.20454545454545456,
          "joint_count": 9.0,
          "marginal_count_a": 44.0
        },
        {
          "number_a": 26,
          "number_b": 36,
          "conditional_prob": 0.2037037037037037,
          "joint_count": 11.0,
          "marginal_count_a": 54.0
        },
        {
          "number_a": 14,
          "number_b": 45,
          "conditional_prob": 0.2,
          "joint_count": 6.0,
          "marginal_count_a": 30.0
        },
        {
          "number_a": 20,
          "number_b": 5,
          "conditional_prob": 0.2,
          "joint_count": 8.0,
          "marginal_count_a": 40.0
        },
        {
          "number_a": 20,
          "number_b": 15,
          "conditional_prob": 0.2,
          "joint_count": 8.0,
          "marginal_count_a": 40.0
        },
        {
          "number_a": 20,
          "number_b": 18,
          "conditional_prob": 0.2,
          "joint_count": 8.0,
          "marginal_count_a": 40.0
        },
        {
          "number_a": 20,
          "number_b": 44,
          "conditional_prob": 0.2,
          "joint_count": 8.0,
          "marginal_count_a": 40.0
        },
        {
          "number_a": 42,
          "number_b": 3,
          "conditional_prob": 0.2,
          "joint_count": 9.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 45,
          "number_b": 29,
          "conditional_prob": 0.2,
          "joint_count": 9.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 5,
          "number_b": 10,
          "conditional_prob": 0.19696969696969696,
          "joint_count": 13.0,
          "marginal_count_a": 66.0
        },
        {
          "number_a": 5,
          "number_b": 40,
          "conditional_prob": 0.19696969696969696,
          "joint_count": 13.0,
          "marginal_count_a": 66.0
        },
        {
          "number_a": 15,
          "number_b": 11,
          "conditional_prob": 0.19672131147540983,
          "joint_count": 12.0,
          "marginal_count_a": 61.0
        },
        {
          "number_a": 15,
          "number_b": 40,
          "conditional_prob": 0.19672131147540983,
          "joint_count": 12.0,
          "marginal_count_a": 61.0
        },
        {
          "number_a": 19,
          "number_b": 9,
          "conditional_prob": 0.19642857142857142,
          "joint_count": 11.0,
          "marginal_count_a": 56.0
        },
        {
          "number_a": 19,
          "number_b": 43,
          "conditional_prob": 0.19642857142857142,
          "joint_count": 11.0,
          "marginal_count_a": 56.0
        },
        {
          "number_a": 3,
          "number_b": 9,
          "conditional_prob": 0.19607843137254902,
          "joint_count": 10.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 3,
          "number_b": 10,
          "conditional_prob": 0.19607843137254902,
          "joint_count": 10.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 3,
          "number_b": 17,
          "conditional_prob": 0.19607843137254902,
          "joint_count": 10.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 3,
          "number_b": 38,
          "conditional_prob": 0.19607843137254902,
          "joint_count": 10.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 9,
          "number_b": 10,
          "conditional_prob": 0.1956521739130435,
          "joint_count": 9.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 9,
          "number_b": 49,
          "conditional_prob": 0.1956521739130435,
          "joint_count": 9.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 23,
          "number_b": 19,
          "conditional_prob": 0.1956521739130435,
          "joint_count": 9.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 23,
          "number_b": 29,
          "conditional_prob": 0.1956521739130435,
          "joint_count": 9.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 23,
          "number_b": 31,
          "conditional_prob": 0.1956521739130435,
          "joint_count": 9.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 23,
          "number_b": 44,
          "conditional_prob": 0.1956521739130435,
          "joint_count": 9.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 6,
          "number_b": 12,
          "conditional_prob": 0.1951219512195122,
          "joint_count": 8.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 6,
          "number_b": 35,
          "conditional_prob": 0.1951219512195122,
          "joint_count": 8.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 6,
          "number_b": 44,
          "conditional_prob": 0.1951219512195122,
          "joint_count": 8.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 18,
          "number_b": 10,
          "conditional_prob": 0.1951219512195122,
          "joint_count": 8.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 18,
          "number_b": 20,
          "conditional_prob": 0.1951219512195122,
          "joint_count": 8.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 41,
          "number_b": 19,
          "conditional_prob": 0.1951219512195122,
          "joint_count": 8.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 41,
          "number_b": 31,
          "conditional_prob": 0.1951219512195122,
          "joint_count": 8.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 21,
          "number_b": 10,
          "conditional_prob": 0.19444444444444445,
          "joint_count": 7.0,
          "marginal_count_a": 36.0
        },
        {
          "number_a": 21,
          "number_b": 12,
          "conditional_prob": 0.19444444444444445,
          "joint_count": 7.0,
          "marginal_count_a": 36.0
        },
        {
          "number_a": 21,
          "number_b": 18,
          "conditional_prob": 0.19444444444444445,
          "joint_count": 7.0,
          "marginal_count_a": 36.0
        },
        {
          "number_a": 21,
          "number_b": 24,
          "conditional_prob": 0.19444444444444445,
          "joint_count": 7.0,
          "marginal_count_a": 36.0
        },
        {
          "number_a": 21,
          "number_b": 34,
          "conditional_prob": 0.19444444444444445,
          "joint_count": 7.0,
          "marginal_count_a": 36.0
        },
        {
          "number_a": 21,
          "number_b": 43,
          "conditional_prob": 0.19444444444444445,
          "joint_count": 7.0,
          "marginal_count_a": 36.0
        },
        {
          "number_a": 28,
          "number_b": 15,
          "conditional_prob": 0.19444444444444445,
          "joint_count": 7.0,
          "marginal_count_a": 36.0
        },
        {
          "number_a": 28,
          "number_b": 34,
          "conditional_prob": 0.19444444444444445,
          "joint_count": 7.0,
          "marginal_count_a": 36.0
        },
        {
          "number_a": 28,
          "number_b": 36,
          "conditional_prob": 0.19444444444444445,
          "joint_count": 7.0,
          "marginal_count_a": 36.0
        },
        {
          "number_a": 10,
          "number_b": 3,
          "conditional_prob": 0.19230769230769232,
          "joint_count": 10.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 10,
          "number_b": 44,
          "conditional_prob": 0.19230769230769232,
          "joint_count": 10.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 17,
          "number_b": 5,
          "conditional_prob": 0.19148936170212766,
          "joint_count": 9.0,
          "marginal_count_a": 47.0
        },
        {
          "number_a": 17,
          "number_b": 10,
          "conditional_prob": 0.19148936170212766,
          "joint_count": 9.0,
          "marginal_count_a": 47.0
        },
        {
          "number_a": 17,
          "number_b": 40,
          "conditional_prob": 0.19148936170212766,
          "joint_count": 9.0,
          "marginal_count_a": 47.0
        },
        {
          "number_a": 24,
          "number_b": 15,
          "conditional_prob": 0.19047619047619047,
          "joint_count": 8.0,
          "marginal_count_a": 42.0
        },
        {
          "number_a": 24,
          "number_b": 32,
          "conditional_prob": 0.19047619047619047,
          "joint_count": 8.0,
          "marginal_count_a": 42.0
        },
        {
          "number_a": 35,
          "number_b": 6,
          "conditional_prob": 0.19047619047619047,
          "joint_count": 8.0,
          "marginal_count_a": 42.0
        },
        {
          "number_a": 35,
          "number_b": 26,
          "conditional_prob": 0.19047619047619047,
          "joint_count": 8.0,
          "marginal_count_a": 42.0
        },
        {
          "number_a": 35,
          "number_b": 27,
          "conditional_prob": 0.19047619047619047,
          "joint_count": 8.0,
          "marginal_count_a": 42.0
        },
        {
          "number_a": 1,
          "number_b": 16,
          "conditional_prob": 0.1891891891891892,
          "joint_count": 7.0,
          "marginal_count_a": 37.0
        },
        {
          "number_a": 1,
          "number_b": 19,
          "conditional_prob": 0.1891891891891892,
          "joint_count": 7.0,
          "marginal_count_a": 37.0
        },
        {
          "number_a": 1,
          "number_b": 44,
          "conditional_prob": 0.1891891891891892,
          "joint_count": 7.0,
          "marginal_count_a": 37.0
        },
        {
          "number_a": 8,
          "number_b": 3,
          "conditional_prob": 0.1875,
          "joint_count": 6.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 8,
          "number_b": 33,
          "conditional_prob": 0.1875,
          "joint_count": 6.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 16,
          "number_b": 40,
          "conditional_prob": 0.1875,
          "joint_count": 9.0,
          "marginal_count_a": 48.0
        },
        {
          "number_a": 39,
          "number_b": 24,
          "conditional_prob": 0.1875,
          "joint_count": 6.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 7,
          "number_b": 23,
          "conditional_prob": 0.18604651162790697,
          "joint_count": 8.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 7,
          "number_b": 30,
          "conditional_prob": 0.18604651162790697,
          "joint_count": 8.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 7,
          "number_b": 31,
          "conditional_prob": 0.18604651162790697,
          "joint_count": 8.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 7,
          "number_b": 42,
          "conditional_prob": 0.18604651162790697,
          "joint_count": 8.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 30,
          "number_b": 7,
          "conditional_prob": 0.18604651162790697,
          "joint_count": 8.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 30,
          "number_b": 25,
          "conditional_prob": 0.18604651162790697,
          "joint_count": 8.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 34,
          "number_b": 17,
          "conditional_prob": 0.18604651162790697,
          "joint_count": 8.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 34,
          "number_b": 27,
          "conditional_prob": 0.18604651162790697,
          "joint_count": 8.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 31,
          "number_b": 23,
          "conditional_prob": 0.1836734693877551,
          "joint_count": 9.0,
          "marginal_count_a": 49.0
        },
        {
          "number_a": 31,
          "number_b": 26,
          "conditional_prob": 0.1836734693877551,
          "joint_count": 9.0,
          "marginal_count_a": 49.0
        },
        {
          "number_a": 31,
          "number_b": 44,
          "conditional_prob": 0.1836734693877551,
          "joint_count": 9.0,
          "marginal_count_a": 49.0
        },
        {
          "number_a": 13,
          "number_b": 11,
          "conditional_prob": 0.18181818181818182,
          "joint_count": 8.0,
          "marginal_count_a": 44.0
        },
        {
          "number_a": 13,
          "number_b": 25,
          "conditional_prob": 0.18181818181818182,
          "joint_count": 8.0,
          "marginal_count_a": 44.0
        },
        {
          "number_a": 13,
          "number_b": 26,
          "conditional_prob": 0.18181818181818182,
          "joint_count": 8.0,
          "marginal_count_a": 44.0
        },
        {
          "number_a": 32,
          "number_b": 24,
          "conditional_prob": 0.18181818181818182,
          "joint_count": 8.0,
          "marginal_count_a": 44.0
        },
        {
          "number_a": 32,
          "number_b": 25,
          "conditional_prob": 0.18181818181818182,
          "joint_count": 8.0,
          "marginal_count_a": 44.0
        },
        {
          "number_a": 32,
          "number_b": 29,
          "conditional_prob": 0.18181818181818182,
          "joint_count": 8.0,
          "marginal_count_a": 44.0
        },
        {
          "number_a": 37,
          "number_b": 26,
          "conditional_prob": 0.18181818181818182,
          "joint_count": 6.0,
          "marginal_count_a": 33.0
        },
        {
          "number_a": 22,
          "number_b": 2,
          "conditional_prob": 0.1794871794871795,
          "joint_count": 7.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 22,
          "number_b": 17,
          "conditional_prob": 0.1794871794871795,
          "joint_count": 7.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 22,
          "number_b": 40,
          "conditional_prob": 0.1794871794871795,
          "joint_count": 7.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 33,
          "number_b": 29,
          "conditional_prob": 0.1794871794871795,
          "joint_count": 7.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 48,
          "number_b": 2,
          "conditional_prob": 0.1794871794871795,
          "joint_count": 7.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 48,
          "number_b": 18,
          "conditional_prob": 0.1794871794871795,
          "joint_count": 7.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 48,
          "number_b": 19,
          "conditional_prob": 0.1794871794871795,
          "joint_count": 7.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 48,
          "number_b": 36,
          "conditional_prob": 0.1794871794871795,
          "joint_count": 7.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 48,
          "number_b": 49,
          "conditional_prob": 0.1794871794871795,
          "joint_count": 7.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 4,
          "number_b": 5,
          "conditional_prob": 0.17857142857142858,
          "joint_count": 5.0,
          "marginal_count_a": 28.0
        },
        {
          "number_a": 4,
          "number_b": 16,
          "conditional_prob": 0.17857142857142858,
          "joint_count": 5.0,
          "marginal_count_a": 28.0
        },
        {
          "number_a": 4,
          "number_b": 23,
          "conditional_prob": 0.17857142857142858,
          "joint_count": 5.0,
          "marginal_count_a": 28.0
        },
        {
          "number_a": 4,
          "number_b": 29,
          "conditional_prob": 0.17857142857142858,
          "joint_count": 5.0,
          "marginal_count_a": 28.0
        },
        {
          "number_a": 4,
          "number_b": 42,
          "conditional_prob": 0.17857142857142858,
          "joint_count": 5.0,
          "marginal_count_a": 28.0
        },
        {
          "number_a": 4,
          "number_b": 43,
          "conditional_prob": 0.17857142857142858,
          "joint_count": 5.0,
          "marginal_count_a": 28.0
        },
        {
          "number_a": 19,
          "number_b": 33,
          "conditional_prob": 0.17857142857142858,
          "joint_count": 10.0,
          "marginal_count_a": 56.0
        },
        {
          "number_a": 44,
          "number_b": 10,
          "conditional_prob": 0.17857142857142858,
          "joint_count": 10.0,
          "marginal_count_a": 56.0
        },
        {
          "number_a": 44,
          "number_b": 45,
          "conditional_prob": 0.17857142857142858,
          "joint_count": 10.0,
          "marginal_count_a": 56.0
        },
        {
          "number_a": 42,
          "number_b": 7,
          "conditional_prob": 0.17777777777777778,
          "joint_count": 8.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 45,
          "number_b": 15,
          "conditional_prob": 0.17777777777777778,
          "joint_count": 8.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 45,
          "number_b": 23,
          "conditional_prob": 0.17777777777777778,
          "joint_count": 8.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 3,
          "number_b": 15,
          "conditional_prob": 0.17647058823529413,
          "joint_count": 9.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 3,
          "number_b": 19,
          "conditional_prob": 0.17647058823529413,
          "joint_count": 9.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 3,
          "number_b": 42,
          "conditional_prob": 0.17647058823529413,
          "joint_count": 9.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 3,
          "number_b": 44,
          "conditional_prob": 0.17647058823529413,
          "joint_count": 9.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 25,
          "number_b": 10,
          "conditional_prob": 0.17647058823529413,
          "joint_count": 9.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 25,
          "number_b": 26,
          "conditional_prob": 0.17647058823529413,
          "joint_count": 9.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 25,
          "number_b": 44,
          "conditional_prob": 0.17647058823529413,
          "joint_count": 9.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 46,
          "number_b": 1,
          "conditional_prob": 0.17647058823529413,
          "joint_count": 6.0,
          "marginal_count_a": 34.0
        },
        {
          "number_a": 46,
          "number_b": 43,
          "conditional_prob": 0.17647058823529413,
          "joint_count": 6.0,
          "marginal_count_a": 34.0
        },
        {
          "number_a": 49,
          "number_b": 9,
          "conditional_prob": 0.17647058823529413,
          "joint_count": 9.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 49,
          "number_b": 28,
          "conditional_prob": 0.17647058823529413,
          "joint_count": 9.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 20,
          "number_b": 2,
          "conditional_prob": 0.175,
          "joint_count": 7.0,
          "marginal_count_a": 40.0
        },
        {
          "number_a": 20,
          "number_b": 3,
          "conditional_prob": 0.175,
          "joint_count": 7.0,
          "marginal_count_a": 40.0
        },
        {
          "number_a": 20,
          "number_b": 10,
          "conditional_prob": 0.175,
          "joint_count": 7.0,
          "marginal_count_a": 40.0
        },
        {
          "number_a": 20,
          "number_b": 19,
          "conditional_prob": 0.175,
          "joint_count": 7.0,
          "marginal_count_a": 40.0
        },
        {
          "number_a": 20,
          "number_b": 27,
          "conditional_prob": 0.175,
          "joint_count": 7.0,
          "marginal_count_a": 40.0
        },
        {
          "number_a": 20,
          "number_b": 42,
          "conditional_prob": 0.175,
          "joint_count": 7.0,
          "marginal_count_a": 40.0
        },
        {
          "number_a": 9,
          "number_b": 15,
          "conditional_prob": 0.17391304347826086,
          "joint_count": 8.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 9,
          "number_b": 26,
          "conditional_prob": 0.17391304347826086,
          "joint_count": 8.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 11,
          "number_b": 13,
          "conditional_prob": 0.17391304347826086,
          "joint_count": 8.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 11,
          "number_b": 19,
          "conditional_prob": 0.17391304347826086,
          "joint_count": 8.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 23,
          "number_b": 7,
          "conditional_prob": 0.17391304347826086,
          "joint_count": 8.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 23,
          "number_b": 27,
          "conditional_prob": 0.17391304347826086,
          "joint_count": 8.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 23,
          "number_b": 38,
          "conditional_prob": 0.17391304347826086,
          "joint_count": 8.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 23,
          "number_b": 45,
          "conditional_prob": 0.17391304347826086,
          "joint_count": 8.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 38,
          "number_b": 17,
          "conditional_prob": 0.17391304347826086,
          "joint_count": 8.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 38,
          "number_b": 23,
          "conditional_prob": 0.17391304347826086,
          "joint_count": 8.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 38,
          "number_b": 44,
          "conditional_prob": 0.17391304347826086,
          "joint_count": 8.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 2,
          "number_b": 10,
          "conditional_prob": 0.17307692307692307,
          "joint_count": 9.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 2,
          "number_b": 29,
          "conditional_prob": 0.17307692307692307,
          "joint_count": 9.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 10,
          "number_b": 2,
          "conditional_prob": 0.17307692307692307,
          "joint_count": 9.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 10,
          "number_b": 9,
          "conditional_prob": 0.17307692307692307,
          "joint_count": 9.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 10,
          "number_b": 17,
          "conditional_prob": 0.17307692307692307,
          "joint_count": 9.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 10,
          "number_b": 25,
          "conditional_prob": 0.17307692307692307,
          "joint_count": 9.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 36,
          "number_b": 29,
          "conditional_prob": 0.17307692307692307,
          "joint_count": 9.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 43,
          "number_b": 26,
          "conditional_prob": 0.17307692307692307,
          "joint_count": 9.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 6,
          "number_b": 18,
          "conditional_prob": 0.17073170731707318,
          "joint_count": 7.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 6,
          "number_b": 19,
          "conditional_prob": 0.17073170731707318,
          "joint_count": 7.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 6,
          "number_b": 31,
          "conditional_prob": 0.17073170731707318,
          "joint_count": 7.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 6,
          "number_b": 46,
          "conditional_prob": 0.17073170731707318,
          "joint_count": 7.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 18,
          "number_b": 6,
          "conditional_prob": 0.17073170731707318,
          "joint_count": 7.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 18,
          "number_b": 21,
          "conditional_prob": 0.17073170731707318,
          "joint_count": 7.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 18,
          "number_b": 36,
          "conditional_prob": 0.17073170731707318,
          "joint_count": 7.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 18,
          "number_b": 48,
          "conditional_prob": 0.17073170731707318,
          "joint_count": 7.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 41,
          "number_b": 15,
          "conditional_prob": 0.17073170731707318,
          "joint_count": 7.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 41,
          "number_b": 25,
          "conditional_prob": 0.17073170731707318,
          "joint_count": 7.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 41,
          "number_b": 26,
          "conditional_prob": 0.17073170731707318,
          "joint_count": 7.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 41,
          "number_b": 30,
          "conditional_prob": 0.17073170731707318,
          "joint_count": 7.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 41,
          "number_b": 49,
          "conditional_prob": 0.17073170731707318,
          "joint_count": 7.0,
          "marginal_count_a": 41.0
        },
        {
          "number_a": 17,
          "number_b": 2,
          "conditional_prob": 0.1702127659574468,
          "joint_count": 8.0,
          "marginal_count_a": 47.0
        },
        {
          "number_a": 17,
          "number_b": 12,
          "conditional_prob": 0.1702127659574468,
          "joint_count": 8.0,
          "marginal_count_a": 47.0
        },
        {
          "number_a": 17,
          "number_b": 15,
          "conditional_prob": 0.1702127659574468,
          "joint_count": 8.0,
          "marginal_count_a": 47.0
        },
        {
          "number_a": 17,
          "number_b": 34,
          "conditional_prob": 0.1702127659574468,
          "joint_count": 8.0,
          "marginal_count_a": 47.0
        },
        {
          "number_a": 17,
          "number_b": 38,
          "conditional_prob": 0.1702127659574468,
          "joint_count": 8.0,
          "marginal_count_a": 47.0
        },
        {
          "number_a": 40,
          "number_b": 13,
          "conditional_prob": 0.16981132075471697,
          "joint_count": 9.0,
          "marginal_count_a": 53.0
        },
        {
          "number_a": 40,
          "number_b": 16,
          "conditional_prob": 0.16981132075471697,
          "joint_count": 9.0,
          "marginal_count_a": 53.0
        },
        {
          "number_a": 40,
          "number_b": 17,
          "conditional_prob": 0.16981132075471697,
          "joint_count": 9.0,
          "marginal_count_a": 53.0
        },
        {
          "number_a": 40,
          "number_b": 41,
          "conditional_prob": 0.16981132075471697,
          "joint_count": 9.0,
          "marginal_count_a": 53.0
        },
        {
          "number_a": 5,
          "number_b": 9,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 11.0,
          "marginal_count_a": 66.0
        },
        {
          "number_a": 5,
          "number_b": 12,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 11.0,
          "marginal_count_a": 66.0
        },
        {
          "number_a": 5,
          "number_b": 49,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 11.0,
          "marginal_count_a": 66.0
        },
        {
          "number_a": 14,
          "number_b": 5,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 5.0,
          "marginal_count_a": 30.0
        },
        {
          "number_a": 14,
          "number_b": 10,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 5.0,
          "marginal_count_a": 30.0
        },
        {
          "number_a": 14,
          "number_b": 19,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 5.0,
          "marginal_count_a": 30.0
        },
        {
          "number_a": 14,
          "number_b": 29,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 5.0,
          "marginal_count_a": 30.0
        },
        {
          "number_a": 14,
          "number_b": 30,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 5.0,
          "marginal_count_a": 30.0
        },
        {
          "number_a": 14,
          "number_b": 33,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 5.0,
          "marginal_count_a": 30.0
        },
        {
          "number_a": 14,
          "number_b": 34,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 5.0,
          "marginal_count_a": 30.0
        },
        {
          "number_a": 16,
          "number_b": 5,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 8.0,
          "marginal_count_a": 48.0
        },
        {
          "number_a": 16,
          "number_b": 22,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 8.0,
          "marginal_count_a": 48.0
        },
        {
          "number_a": 21,
          "number_b": 5,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 6.0,
          "marginal_count_a": 36.0
        },
        {
          "number_a": 21,
          "number_b": 16,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 6.0,
          "marginal_count_a": 36.0
        },
        {
          "number_a": 21,
          "number_b": 26,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 6.0,
          "marginal_count_a": 36.0
        },
        {
          "number_a": 24,
          "number_b": 5,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 7.0,
          "marginal_count_a": 42.0
        },
        {
          "number_a": 24,
          "number_b": 7,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 7.0,
          "marginal_count_a": 42.0
        },
        {
          "number_a": 24,
          "number_b": 12,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 7.0,
          "marginal_count_a": 42.0
        },
        {
          "number_a": 24,
          "number_b": 21,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 7.0,
          "marginal_count_a": 42.0
        },
        {
          "number_a": 24,
          "number_b": 25,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 7.0,
          "marginal_count_a": 42.0
        },
        {
          "number_a": 24,
          "number_b": 42,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 7.0,
          "marginal_count_a": 42.0
        },
        {
          "number_a": 26,
          "number_b": 25,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 9.0,
          "marginal_count_a": 54.0
        },
        {
          "number_a": 26,
          "number_b": 31,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 9.0,
          "marginal_count_a": 54.0
        },
        {
          "number_a": 26,
          "number_b": 43,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 9.0,
          "marginal_count_a": 54.0
        },
        {
          "number_a": 28,
          "number_b": 2,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 6.0,
          "marginal_count_a": 36.0
        },
        {
          "number_a": 28,
          "number_b": 31,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 6.0,
          "marginal_count_a": 36.0
        },
        {
          "number_a": 28,
          "number_b": 40,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 6.0,
          "marginal_count_a": 36.0
        },
        {
          "number_a": 29,
          "number_b": 2,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 9.0,
          "marginal_count_a": 54.0
        },
        {
          "number_a": 29,
          "number_b": 7,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 9.0,
          "marginal_count_a": 54.0
        },
        {
          "number_a": 29,
          "number_b": 23,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 9.0,
          "marginal_count_a": 54.0
        },
        {
          "number_a": 29,
          "number_b": 36,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 9.0,
          "marginal_count_a": 54.0
        },
        {
          "number_a": 29,
          "number_b": 45,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 9.0,
          "marginal_count_a": 54.0
        },
        {
          "number_a": 35,
          "number_b": 16,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 7.0,
          "marginal_count_a": 42.0
        },
        {
          "number_a": 35,
          "number_b": 19,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 7.0,
          "marginal_count_a": 42.0
        },
        {
          "number_a": 35,
          "number_b": 25,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 7.0,
          "marginal_count_a": 42.0
        },
        {
          "number_a": 35,
          "number_b": 29,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 7.0,
          "marginal_count_a": 42.0
        },
        {
          "number_a": 35,
          "number_b": 31,
          "conditional_prob": 0.16666666666666666,
          "joint_count": 7.0,
          "marginal_count_a": 42.0
        },
        {
          "number_a": 31,
          "number_b": 7,
          "conditional_prob": 0.16326530612244897,
          "joint_count": 8.0,
          "marginal_count_a": 49.0
        },
        {
          "number_a": 31,
          "number_b": 27,
          "conditional_prob": 0.16326530612244897,
          "joint_count": 8.0,
          "marginal_count_a": 49.0
        },
        {
          "number_a": 31,
          "number_b": 41,
          "conditional_prob": 0.16326530612244897,
          "joint_count": 8.0,
          "marginal_count_a": 49.0
        },
        {
          "number_a": 31,
          "number_b": 43,
          "conditional_prob": 0.16326530612244897,
          "joint_count": 8.0,
          "marginal_count_a": 49.0
        },
        {
          "number_a": 7,
          "number_b": 16,
          "conditional_prob": 0.16279069767441862,
          "joint_count": 7.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 7,
          "number_b": 19,
          "conditional_prob": 0.16279069767441862,
          "joint_count": 7.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 7,
          "number_b": 24,
          "conditional_prob": 0.16279069767441862,
          "joint_count": 7.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 7,
          "number_b": 26,
          "conditional_prob": 0.16279069767441862,
          "joint_count": 7.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 7,
          "number_b": 27,
          "conditional_prob": 0.16279069767441862,
          "joint_count": 7.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 7,
          "number_b": 32,
          "conditional_prob": 0.16279069767441862,
          "joint_count": 7.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 30,
          "number_b": 16,
          "conditional_prob": 0.16279069767441862,
          "joint_count": 7.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 30,
          "number_b": 31,
          "conditional_prob": 0.16279069767441862,
          "joint_count": 7.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 30,
          "number_b": 41,
          "conditional_prob": 0.16279069767441862,
          "joint_count": 7.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 34,
          "number_b": 3,
          "conditional_prob": 0.16279069767441862,
          "joint_count": 7.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 34,
          "number_b": 16,
          "conditional_prob": 0.16279069767441862,
          "joint_count": 7.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 34,
          "number_b": 21,
          "conditional_prob": 0.16279069767441862,
          "joint_count": 7.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 34,
          "number_b": 28,
          "conditional_prob": 0.16279069767441862,
          "joint_count": 7.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 34,
          "number_b": 36,
          "conditional_prob": 0.16279069767441862,
          "joint_count": 7.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 34,
          "number_b": 38,
          "conditional_prob": 0.16279069767441862,
          "joint_count": 7.0,
          "marginal_count_a": 43.0
        },
        {
          "number_a": 1,
          "number_b": 6,
          "conditional_prob": 0.16216216216216217,
          "joint_count": 6.0,
          "marginal_count_a": 37.0
        },
        {
          "number_a": 1,
          "number_b": 12,
          "conditional_prob": 0.16216216216216217,
          "joint_count": 6.0,
          "marginal_count_a": 37.0
        },
        {
          "number_a": 1,
          "number_b": 15,
          "conditional_prob": 0.16216216216216217,
          "joint_count": 6.0,
          "marginal_count_a": 37.0
        },
        {
          "number_a": 1,
          "number_b": 31,
          "conditional_prob": 0.16216216216216217,
          "joint_count": 6.0,
          "marginal_count_a": 37.0
        },
        {
          "number_a": 1,
          "number_b": 32,
          "conditional_prob": 0.16216216216216217,
          "joint_count": 6.0,
          "marginal_count_a": 37.0
        },
        {
          "number_a": 1,
          "number_b": 38,
          "conditional_prob": 0.16216216216216217,
          "joint_count": 6.0,
          "marginal_count_a": 37.0
        },
        {
          "number_a": 1,
          "number_b": 43,
          "conditional_prob": 0.16216216216216217,
          "joint_count": 6.0,
          "marginal_count_a": 37.0
        },
        {
          "number_a": 1,
          "number_b": 46,
          "conditional_prob": 0.16216216216216217,
          "joint_count": 6.0,
          "marginal_count_a": 37.0
        },
        {
          "number_a": 1,
          "number_b": 47,
          "conditional_prob": 0.16216216216216217,
          "joint_count": 6.0,
          "marginal_count_a": 37.0
        },
        {
          "number_a": 1,
          "number_b": 49,
          "conditional_prob": 0.16216216216216217,
          "joint_count": 6.0,
          "marginal_count_a": 37.0
        },
        {
          "number_a": 19,
          "number_b": 3,
          "conditional_prob": 0.16071428571428573,
          "joint_count": 9.0,
          "marginal_count_a": 56.0
        },
        {
          "number_a": 19,
          "number_b": 5,
          "conditional_prob": 0.16071428571428573,
          "joint_count": 9.0,
          "marginal_count_a": 56.0
        },
        {
          "number_a": 19,
          "number_b": 22,
          "conditional_prob": 0.16071428571428573,
          "joint_count": 9.0,
          "marginal_count_a": 56.0
        },
        {
          "number_a": 19,
          "number_b": 23,
          "conditional_prob": 0.16071428571428573,
          "joint_count": 9.0,
          "marginal_count_a": 56.0
        },
        {
          "number_a": 44,
          "number_b": 3,
          "conditional_prob": 0.16071428571428573,
          "joint_count": 9.0,
          "marginal_count_a": 56.0
        },
        {
          "number_a": 44,
          "number_b": 23,
          "conditional_prob": 0.16071428571428573,
          "joint_count": 9.0,
          "marginal_count_a": 56.0
        },
        {
          "number_a": 44,
          "number_b": 25,
          "conditional_prob": 0.16071428571428573,
          "joint_count": 9.0,
          "marginal_count_a": 56.0
        },
        {
          "number_a": 44,
          "number_b": 31,
          "conditional_prob": 0.16071428571428573,
          "joint_count": 9.0,
          "marginal_count_a": 56.0
        },
        {
          "number_a": 44,
          "number_b": 32,
          "conditional_prob": 0.16071428571428573,
          "joint_count": 9.0,
          "marginal_count_a": 56.0
        },
        {
          "number_a": 44,
          "number_b": 34,
          "conditional_prob": 0.16071428571428573,
          "joint_count": 9.0,
          "marginal_count_a": 56.0
        },
        {
          "number_a": 12,
          "number_b": 6,
          "conditional_prob": 0.16,
          "joint_count": 8.0,
          "marginal_count_a": 50.0
        },
        {
          "number_a": 12,
          "number_b": 17,
          "conditional_prob": 0.16,
          "joint_count": 8.0,
          "marginal_count_a": 50.0
        },
        {
          "number_a": 12,
          "number_b": 27,
          "conditional_prob": 0.16,
          "joint_count": 8.0,
          "marginal_count_a": 50.0
        },
        {
          "number_a": 12,
          "number_b": 36,
          "conditional_prob": 0.16,
          "joint_count": 8.0,
          "marginal_count_a": 50.0
        },
        {
          "number_a": 12,
          "number_b": 43,
          "conditional_prob": 0.16,
          "joint_count": 8.0,
          "marginal_count_a": 50.0
        },
        {
          "number_a": 27,
          "number_b": 12,
          "conditional_prob": 0.16,
          "joint_count": 8.0,
          "marginal_count_a": 50.0
        },
        {
          "number_a": 27,
          "number_b": 23,
          "conditional_prob": 0.16,
          "joint_count": 8.0,
          "marginal_count_a": 50.0
        },
        {
          "number_a": 27,
          "number_b": 26,
          "conditional_prob": 0.16,
          "joint_count": 8.0,
          "marginal_count_a": 50.0
        },
        {
          "number_a": 27,
          "number_b": 29,
          "conditional_prob": 0.16,
          "joint_count": 8.0,
          "marginal_count_a": 50.0
        },
        {
          "number_a": 27,
          "number_b": 31,
          "conditional_prob": 0.16,
          "joint_count": 8.0,
          "marginal_count_a": 50.0
        },
        {
          "number_a": 27,
          "number_b": 34,
          "conditional_prob": 0.16,
          "joint_count": 8.0,
          "marginal_count_a": 50.0
        },
        {
          "number_a": 27,
          "number_b": 35,
          "conditional_prob": 0.16,
          "joint_count": 8.0,
          "marginal_count_a": 50.0
        },
        {
          "number_a": 13,
          "number_b": 17,
          "conditional_prob": 0.1590909090909091,
          "joint_count": 7.0,
          "marginal_count_a": 44.0
        },
        {
          "number_a": 13,
          "number_b": 29,
          "conditional_prob": 0.1590909090909091,
          "joint_count": 7.0,
          "marginal_count_a": 44.0
        },
        {
          "number_a": 13,
          "number_b": 43,
          "conditional_prob": 0.1590909090909091,
          "joint_count": 7.0,
          "marginal_count_a": 44.0
        },
        {
          "number_a": 13,
          "number_b": 44,
          "conditional_prob": 0.1590909090909091,
          "joint_count": 7.0,
          "marginal_count_a": 44.0
        },
        {
          "number_a": 32,
          "number_b": 7,
          "conditional_prob": 0.1590909090909091,
          "joint_count": 7.0,
          "marginal_count_a": 44.0
        },
        {
          "number_a": 32,
          "number_b": 42,
          "conditional_prob": 0.1590909090909091,
          "joint_count": 7.0,
          "marginal_count_a": 44.0
        },
        {
          "number_a": 47,
          "number_b": 1,
          "conditional_prob": 0.15789473684210525,
          "joint_count": 6.0,
          "marginal_count_a": 38.0
        },
        {
          "number_a": 47,
          "number_b": 11,
          "conditional_prob": 0.15789473684210525,
          "joint_count": 6.0,
          "marginal_count_a": 38.0
        },
        {
          "number_a": 47,
          "number_b": 12,
          "conditional_prob": 0.15789473684210525,
          "joint_count": 6.0,
          "marginal_count_a": 38.0
        },
        {
          "number_a": 47,
          "number_b": 23,
          "conditional_prob": 0.15789473684210525,
          "joint_count": 6.0,
          "marginal_count_a": 38.0
        },
        {
          "number_a": 3,
          "number_b": 40,
          "conditional_prob": 0.1568627450980392,
          "joint_count": 8.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 25,
          "number_b": 13,
          "conditional_prob": 0.1568627450980392,
          "joint_count": 8.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 25,
          "number_b": 30,
          "conditional_prob": 0.1568627450980392,
          "joint_count": 8.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 25,
          "number_b": 32,
          "conditional_prob": 0.1568627450980392,
          "joint_count": 8.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 49,
          "number_b": 15,
          "conditional_prob": 0.1568627450980392,
          "joint_count": 8.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 49,
          "number_b": 39,
          "conditional_prob": 0.1568627450980392,
          "joint_count": 8.0,
          "marginal_count_a": 51.0
        },
        {
          "number_a": 8,
          "number_b": 15,
          "conditional_prob": 0.15625,
          "joint_count": 5.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 8,
          "number_b": 20,
          "conditional_prob": 0.15625,
          "joint_count": 5.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 8,
          "number_b": 27,
          "conditional_prob": 0.15625,
          "joint_count": 5.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 8,
          "number_b": 28,
          "conditional_prob": 0.15625,
          "joint_count": 5.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 8,
          "number_b": 34,
          "conditional_prob": 0.15625,
          "joint_count": 5.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 8,
          "number_b": 35,
          "conditional_prob": 0.15625,
          "joint_count": 5.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 8,
          "number_b": 38,
          "conditional_prob": 0.15625,
          "joint_count": 5.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 8,
          "number_b": 41,
          "conditional_prob": 0.15625,
          "joint_count": 5.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 39,
          "number_b": 1,
          "conditional_prob": 0.15625,
          "joint_count": 5.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 39,
          "number_b": 2,
          "conditional_prob": 0.15625,
          "joint_count": 5.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 39,
          "number_b": 5,
          "conditional_prob": 0.15625,
          "joint_count": 5.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 39,
          "number_b": 18,
          "conditional_prob": 0.15625,
          "joint_count": 5.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 39,
          "number_b": 25,
          "conditional_prob": 0.15625,
          "joint_count": 5.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 39,
          "number_b": 29,
          "conditional_prob": 0.15625,
          "joint_count": 5.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 39,
          "number_b": 37,
          "conditional_prob": 0.15625,
          "joint_count": 5.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 39,
          "number_b": 42,
          "conditional_prob": 0.15625,
          "joint_count": 5.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 39,
          "number_b": 46,
          "conditional_prob": 0.15625,
          "joint_count": 5.0,
          "marginal_count_a": 32.0
        },
        {
          "number_a": 42,
          "number_b": 15,
          "conditional_prob": 0.15555555555555556,
          "joint_count": 7.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 42,
          "number_b": 16,
          "conditional_prob": 0.15555555555555556,
          "joint_count": 7.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 42,
          "number_b": 20,
          "conditional_prob": 0.15555555555555556,
          "joint_count": 7.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 42,
          "number_b": 24,
          "conditional_prob": 0.15555555555555556,
          "joint_count": 7.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 42,
          "number_b": 25,
          "conditional_prob": 0.15555555555555556,
          "joint_count": 7.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 42,
          "number_b": 32,
          "conditional_prob": 0.15555555555555556,
          "joint_count": 7.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 42,
          "number_b": 49,
          "conditional_prob": 0.15555555555555556,
          "joint_count": 7.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 45,
          "number_b": 9,
          "conditional_prob": 0.15555555555555556,
          "joint_count": 7.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 45,
          "number_b": 12,
          "conditional_prob": 0.15555555555555556,
          "joint_count": 7.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 45,
          "number_b": 19,
          "conditional_prob": 0.15555555555555556,
          "joint_count": 7.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 45,
          "number_b": 25,
          "conditional_prob": 0.15555555555555556,
          "joint_count": 7.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 45,
          "number_b": 49,
          "conditional_prob": 0.15555555555555556,
          "joint_count": 7.0,
          "marginal_count_a": 45.0
        },
        {
          "number_a": 2,
          "number_b": 5,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 8.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 2,
          "number_b": 17,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 8.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 10,
          "number_b": 15,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 8.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 10,
          "number_b": 18,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 8.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 10,
          "number_b": 36,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 8.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 10,
          "number_b": 47,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 8.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 22,
          "number_b": 15,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 22,
          "number_b": 23,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 22,
          "number_b": 27,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 22,
          "number_b": 44,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 22,
          "number_b": 45,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 33,
          "number_b": 2,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 33,
          "number_b": 8,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 33,
          "number_b": 23,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 33,
          "number_b": 24,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 33,
          "number_b": 26,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 33,
          "number_b": 36,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 33,
          "number_b": 45,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 36,
          "number_b": 5,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 8.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 36,
          "number_b": 8,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 8.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 36,
          "number_b": 10,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 8.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 36,
          "number_b": 12,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 8.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 36,
          "number_b": 21,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 8.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 36,
          "number_b": 44,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 8.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 43,
          "number_b": 12,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 8.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 43,
          "number_b": 31,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 8.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 43,
          "number_b": 33,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 8.0,
          "marginal_count_a": 52.0
        },
        {
          "number_a": 48,
          "number_b": 5,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 48,
          "number_b": 11,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 48,
          "number_b": 15,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 48,
          "number_b": 31,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 48,
          "number_b": 34,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 48,
          "number_b": 38,
          "conditional_prob": 0.15384615384615385,
          "joint_count": 6.0,
          "marginal_count_a": 39.0
        },
        {
          "number_a": 9,
          "number_b": 12,
          "conditional_prob": 0.15217391304347827,
          "joint_count": 7.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 9,
          "number_b": 14,
          "conditional_prob": 0.15217391304347827,
          "joint_count": 7.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 9,
          "number_b": 17,
          "conditional_prob": 0.15217391304347827,
          "joint_count": 7.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 9,
          "number_b": 38,
          "conditional_prob": 0.15217391304347827,
          "joint_count": 7.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 9,
          "number_b": 40,
          "conditional_prob": 0.15217391304347827,
          "joint_count": 7.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 9,
          "number_b": 43,
          "conditional_prob": 0.15217391304347827,
          "joint_count": 7.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 9,
          "number_b": 45,
          "conditional_prob": 0.15217391304347827,
          "joint_count": 7.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 11,
          "number_b": 29,
          "conditional_prob": 0.15217391304347827,
          "joint_count": 7.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 23,
          "number_b": 5,
          "conditional_prob": 0.15217391304347827,
          "joint_count": 7.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 23,
          "number_b": 15,
          "conditional_prob": 0.15217391304347827,
          "joint_count": 7.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 38,
          "number_b": 9,
          "conditional_prob": 0.15217391304347827,
          "joint_count": 7.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 38,
          "number_b": 19,
          "conditional_prob": 0.15217391304347827,
          "joint_count": 7.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 38,
          "number_b": 25,
          "conditional_prob": 0.15217391304347827,
          "joint_count": 7.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 38,
          "number_b": 26,
          "conditional_prob": 0.15217391304347827,
          "joint_count": 7.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 38,
          "number_b": 31,
          "conditional_prob": 0.15217391304347827,
          "joint_count": 7.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 38,
          "number_b": 34,
          "conditional_prob": 0.15217391304347827,
          "joint_count": 7.0,
          "marginal_count_a": 46.0
        },
        {
          "number_a": 5,
          "number_b": 13,
          "conditional_prob": 0.15151515151515152,
          "joint_count": 10.0,
          "marginal_count_a": 66.0
        },
        {
          "number_a": 5,
          "number_b": 42,
          "conditional_prob": 0.15151515151515152,
          "joint_count": 10.0,
          "marginal_count_a": 66.0
        },
        {
          "number_a": 5,
          "number_b": 47,
          "conditional_prob": 0.15151515151515152,
          "joint_count": 10.0,
          "marginal_count_a": 66.0
        },
        {
          "number_a": 37,
          "number_b": 3,
          "conditional_prob": 0.15151515151515152,
          "joint_count": 5.0,
          "marginal_count_a": 33.0
        },
        {
          "number_a": 37,
          "number_b": 11,
          "conditional_prob": 0.15151515151515152,
          "joint_count": 5.0,
          "marginal_count_a": 33.0
        },
        {
          "number_a": 37,
          "number_b": 17,
          "conditional_prob": 0.15151515151515152,
          "joint_count": 5.0,
          "marginal_count_a": 33.0
        },
        {
          "number_a": 37,
          "number_b": 39,
          "conditional_prob": 0.15151515151515152,
          "joint_count": 5.0,
          "marginal_count_a": 33.0
        },
        {
          "number_a": 37,
          "number_b": 43,
          "conditional_prob": 0.15151515151515152,
          "joint_count": 5.0,
          "marginal_count_a": 33.0
        },
        {
          "number_a": 37,
          "number_b": 44,
          "conditional_prob": 0.15151515151515152,
          "joint_count": 5.0,
          "marginal_count_a": 33.0
        },
        {
          "number_a": 37,
          "number_b": 49,
          "conditional_prob": 0.15151515151515152,
          "joint_count": 5.0,
          "marginal_count_a": 33.0
        },
        {
          "number_a": 40,
          "number_b": 3,
          "conditional_prob": 0.1509433962264151,
          "joint_count": 8.0,
          "marginal_count_a": 53.0
        },
        {
          "number_a": 40,
          "number_b": 37,
          "conditional_prob": 0.1509433962264151,
          "joint_count": 8.0,
          "marginal_count_a": 53.0
        },
        {
          "number_a": 40,
          "number_b": 47,
          "conditional_prob": 0.1509433962264151,
          "joint_count": 8.0,
          "marginal_count_a": 53.0
        }
      ],
      "conditional_matrix": "[[0.         0.10810811 0.         ... 0.16216216 0.         0.16216216]\n [0.07692308 0.         0.09615385 ... 0.07692308 0.13461538 0.11538462]\n [0.         0.09803922 0.         ... 0.07843137 0.05882353 0.07843137]\n ...\n [0.15789474 0.10526316 0.10526316 ... 0.         0.07894737 0.05263158]\n [0.         0.17948718 0.07692308 ... 0.07692308 0.         0.17948718]\n [0.11764706 0.11764706 0.07843137 ... 0.03921569 0.1372549  0.        ]]",
      "position_preferences": {
        "position_1": [
          [
            "43",
            0.040983606557377046
          ],
          [
            "5",
            0.03551912568306011
          ],
          [
            "11",
            0.03278688524590164
          ],
          [
            "10",
            0.030054644808743168
          ],
          [
            "25",
            0.030054644808743168
          ]
        ],
        "position_2": [
          [
            "44",
            0.040983606557377046
          ],
          [
            "7",
            0.03825136612021858
          ],
          [
            "41",
            0.030054644808743168
          ],
          [
            "15",
            0.030054644808743168
          ],
          [
            "25",
            0.0273224043715847
          ]
        ],
        "position_3": [
          [
            "3",
            0.040983606557377046
          ],
          [
            "29",
            0.03825136612021858
          ],
          [
            "15",
            0.03825136612021858
          ],
          [
            "9",
            0.03551912568306011
          ],
          [
            "45",
            0.03278688524590164
          ]
        ],
        "position_4": [
          [
            "29",
            0.03825136612021858
          ],
          [
            "19",
            0.03551912568306011
          ],
          [
            "12",
            0.03278688524590164
          ],
          [
            "33",
            0.03278688524590164
          ],
          [
            "27",
            0.03278688524590164
          ]
        ],
        "position_5": [
          [
            "40",
            0.040983606557377046
          ],
          [
            "5",
            0.03825136612021858
          ],
          [
            "38",
            0.03551912568306011
          ],
          [
            "12",
            0.030054644808743168
          ],
          [
            "26",
            0.030054644808743168
          ]
        ],
        "position_6": [
          [
            "27",
            0.040983606557377046
          ],
          [
            "36",
            0.03551912568306011
          ],
          [
            "43",
            0.03551912568306011
          ],
          [
            "5",
            0.03278688524590164
          ],
          [
            "15",
            0.03278688524590164
          ]
        ]
      }
    },
    "model_selection": {
      "scores": {
        "simple_frequency": {
          "log_likelihood": -8510.956841920071,
          "aic": 17119.913683840143,
          "bic": 17311.14271717681,
          "complexity": 1,
          "parameters": 49,
          "description": "基于历史频率的简单预测"
        },
        "position_aware": {
          "log_likelihood": -8386.299451125606,
          "aic": 17360.59890225121,
          "bic": 18507.97310227121,
          "complexity": 2,
          "parameters": 294,
          "description": "考虑位置偏好的预测模型"
        },
        "conditional_dependency": {
          "log_likelihood": -9362.05252611208,
          "aic": 23526.10505222416,
          "bic": 32896.32768572084,
          "complexity": 3,
          "parameters": 2401,
          "description": "基于数字间条件概率的模型"
        },
        "bayesian_ensemble": {
          "log_likelihood": -8805.614423681885,
          "aic": 23099.22884736377,
          "bic": 33808.05471421712,
          "complexity": 4,
          "parameters": 2744,
          "description": "贝叶斯方法集成多种特征"
        }
      },
      "ranking": [
        [
          "simple_frequency",
          {
            "log_likelihood": -8510.956841920071,
            "aic": 17119.913683840143,
            "bic": 17311.14271717681,
            "complexity": 1,
            "parameters": 49,
            "description": "基于历史频率的简单预测"
          }
        ],
        [
          "position_aware",
          {
            "log_likelihood": -8386.299451125606,
            "aic": 17360.59890225121,
            "bic": 18507.97310227121,
            "complexity": 2,
            "parameters": 294,
            "description": "考虑位置偏好的预测模型"
          }
        ],
        [
          "bayesian_ensemble",
          {
            "log_likelihood": -8805.614423681885,
            "aic": 23099.22884736377,
            "bic": 33808.05471421712,
            "complexity": 4,
            "parameters": 2744,
            "description": "贝叶斯方法集成多种特征"
          }
        ],
        [
          "conditional_dependency",
          {
            "log_likelihood": -9362.05252611208,
            "aic": 23526.10505222416,
            "bic": 32896.32768572084,
            "complexity": 3,
            "parameters": 2401,
            "description": "基于数字间条件概率的模型"
          }
        ]
      ],
      "recommended": "simple_frequency"
    },
    "two_number_hit_analysis": {
      "0": 0.7678571428571429,
      "1": 0.2193877551020408,
      "2": 0.012755102040816327
    },
    "patterns": {
      "consecutive": {
        "average": 0.6311475409836066,
        "distribution": {
          "0": 181,
          "2": 34,
          "1": 145,
          "3": 6
        },
        "counts": [
          0,
          2,
          0,
          0,
          0,
          1,
          2,
          0,
          1,
          1,
          0,
          0,
          0,
          0,
          1,
          0,
          0,
          1,
          1,
          1,
          0,
          0,
          0,
          1,
          0,
          0,
          1,
          0,
          1,
          2,
          0,
          1,
          2,
          2,
          0,
          0,
          0,
          1,
          0,
          0,
          0,
          1,
          0,
          0,
          0,
          1,
          1,
          0,
          1,
          0,
          1,
          1,
          0,
          1,
          1,
          0,
          1,
          0,
          0,
          0,
          0,
          0,
          1,
          1,
          0,
          1,
          1,
          1,
          1,
          1,
          0,
          1,
          1,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          2,
          0,
          0,
          0,
          1,
          1,
          0,
          2,
          0,
          0,
          0,
          0,
          1,
          1,
          0,
          2,
          0,
          0,
          0,
          1,
          2,
          1,
          1,
          0,
          0,
          1,
          0,
          0,
          1,
          1,
          0,
          2,
          1,
          0,
          0,
          1,
          0,
          1,
          0,
          1,
          0,
          0,
          2,
          0,
          0,
          1,
          0,
          1,
          0,
          1,
          0,
          0,
          2,
          0,
          0,
          1,
          0,
          3,
          1,
          0,
          1,
          1,
          1,
          1,
          0,
          0,
          0,
          1,
          1,
          1,
          0,
          1,
          1,
          0,
          0,
          1,
          1,
          1,
          1,
          1,
          1,
          1,
          0,
          1,
          1,
          0,
          0,
          1,
          1,
          0,
          1,
          1,
          2,
          0,
          1,
          0,
          1,
          2,
          0,
          0,
          1,
          1,
          0,
          0,
          1,
          1,
          0,
          2,
          1,
          1,
          0,
          2,
          0,
          1,
          0,
          1,
          0,
          2,
          1,
          0,
          0,
          0,
          1,
          2,
          0,
          0,
          0,
          0,
          0,
          2,
          0,
          0,
          0,
          0,
          1,
          1,
          0,
          1,
          0,
          1,
          0,
          0,
          1,
          0,
          1,
          1,
          1,
          0,
          1,
          0,
          1,
          1,
          1,
          1,
          1,
          1,
          2,
          0,
          2,
          1,
          1,
          1,
          0,
          0,
          1,
          0,
          1,
          0,
          1,
          1,
          2,
          0,
          2,
          0,
          3,
          1,
          0,
          1,
          2,
          1,
          1,
          1,
          0,
          3,
          0,
          1,
          1,
          2,
          1,
          0,
          2,
          1,
          0,
          1,
          0,
          0,
          0,
          0,
          0,
          1,
          0,
          0,
          0,
          1,
          0,
          2,
          1,
          0,
          0,
          1,
          0,
          0,
          0,
          1,
          1,
          1,
          1,
          1,
          0,
          0,
          2,
          0,
          0,
          2,
          1,
          0,
          0,
          2,
          0,
          1,
          1,
          0,
          0,
          1,
          0,
          0,
          2,
          0,
          1,
          0,
          0,
          1,
          1,
          0,
          1,
          1,
          1,
          0,
          3,
          1,
          3,
          0,
          1,
          0,
          0,
          2,
          0,
          0,
          2,
          1,
          0,
          0,
          0,
          0,
          1,
          0,
          0,
          1,
          0,
          1,
          1,
          0,
          0,
          0,
          0,
          2,
          0,
          1,
          0,
          3,
          0,
          1,
          0,
          0,
          1,
          1
        ]
      },
      "odd_even": {
        "distribution": {
          