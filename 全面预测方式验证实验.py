#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面预测方式验证实验
基于2025年1-179期测试集数据，对比分析不同预测方式的实际效果
严格避免数据泄露，使用统一评估标准，提供可重现的实验结果
"""

import pandas as pd
import numpy as np
import json
from collections import defaultdict, Counter
from datetime import datetime
from scipy import stats
from scipy.stats import chi2_contingency, mannwhitneyu
import warnings
warnings.filterwarnings('ignore')

class ComprehensivePredictionValidator:
    """
    全面预测方式验证器
    对比分析四种预测方式的实际效果
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.data = None
        self.train_data = None
        self.test_data = None
        self.transition_prob = {}
        
        # 实验配置
        self.config = {
            'experiment_name': '全面预测方式验证实验',
            'train_period': '2023-2024年',
            'test_period': '2025年1-179期',
            'prediction_methods': [
                'single_period',      # 单期预测
                'continuous',         # 连续预测
                'rolling',           # 滚动预测
                'probability_dist'   # 概率分布预测
            ],
            'evaluation_metrics': [
                'accuracy_rate',      # 预测准确率
                'diversity_score',    # 预测多样性
                'confidence_calibration',  # 置信度校准
                'error_accumulation', # 误差累积效应
                'betting_effectiveness'  # 投注建议有效性
            ],
            'random_seed': 42
        }
        
        # 实验结果存储
        self.experiment_results = {}
        
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print(f"📊 加载和准备实验数据")
        print("=" * 60)
        
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            # 严格的数据分割，避免数据泄露
            self.train_data = self.data[
                (self.data['年份'] >= 2023) & (self.data['年份'] < 2025)
            ].copy()
            
            self.test_data = self.data[
                (self.data['年份'] == 2025) & (self.data['期号'] <= 179)
            ].copy()
            
            print(f"✅ 数据加载成功")
            print(f"  训练集: {len(self.train_data)}期 ({self.config['train_period']})")
            print(f"  测试集: {len(self.test_data)}期 ({self.config['test_period']})")
            print(f"  数据分割: 严格时间序列分割，无数据泄露")
            
            # 验证数据完整性
            self._validate_data_integrity()
            
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def _validate_data_integrity(self):
        """验证数据完整性"""
        # 检查训练集和测试集无重叠
        train_periods = set(zip(self.train_data['年份'], self.train_data['期号']))
        test_periods = set(zip(self.test_data['年份'], self.test_data['期号']))
        
        overlap = train_periods & test_periods
        if overlap:
            raise ValueError(f"数据泄露检测：训练集和测试集存在重叠期间 {overlap}")
        
        # 检查数据连续性
        if len(self.test_data) != 179:
            print(f"⚠️ 警告：测试集期数为{len(self.test_data)}，不是预期的179期")
        
        print(f"✅ 数据完整性验证通过，无数据泄露")
    
    def build_base_model(self):
        """构建基础模型"""
        print(f"\n🔧 构建基础预测模型")
        print("-" * 40)
        
        # 构建马尔可夫转移概率（仅使用训练数据）
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        self.transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                self.transition_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        # 构建频率分析（仅使用训练数据）
        self.number_frequency = Counter()
        for _, row in self.train_data.iterrows():
            numbers = [row[f'数字{j}'] for j in range(1, 7)]
            self.number_frequency.update(numbers)
        
        print(f"✅ 基础模型构建完成")
        print(f"  马尔可夫状态: {len(self.transition_prob)}个")
        print(f"  频率统计: {len(self.number_frequency)}个数字")
    
    def method_single_period_prediction(self):
        """方法1：单期预测方式"""
        print(f"\n🎯 执行单期预测方式验证")
        print("-" * 40)
        
        predictions = []
        
        # 为每个测试期进行单期预测
        for idx, test_row in self.test_data.iterrows():
            period_num = test_row['期号']
            actual_numbers = set([test_row[f'数字{j}'] for j in range(1, 7)])
            
            # 获取前一期数字（基于真实数据）
            if idx == self.test_data.index[0]:
                # 第一期基于训练集最后一期
                prev_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                # 后续期基于测试集前一期的真实数据
                prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                prev_numbers = set([self.test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            # 进行预测
            predicted_numbers, confidence = self._markov_prediction(prev_numbers)
            
            # 计算结果
            hit_count = len(set(predicted_numbers) & actual_numbers)
            is_success = hit_count >= 1
            
            predictions.append({
                'period': period_num,
                'previous_numbers': list(prev_numbers),
                'predicted_numbers': predicted_numbers,
                'actual_numbers': list(actual_numbers),
                'hit_count': hit_count,
                'is_success': is_success,
                'confidence': confidence,
                'method': 'single_period'
            })
        
        print(f"✅ 单期预测完成，共{len(predictions)}期")
        return predictions
    
    def method_continuous_prediction(self):
        """方法2：连续预测方式"""
        print(f"\n🎯 执行连续预测方式验证")
        print("-" * 40)
        
        predictions = []
        
        # 基于训练集最后一期进行连续预测
        base_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
        current_numbers = base_numbers
        
        print(f"  基础数字: {sorted(list(base_numbers))}")
        
        for idx, test_row in self.test_data.iterrows():
            period_num = test_row['期号']
            actual_numbers = set([test_row[f'数字{j}'] for j in range(1, 7)])
            
            # 基于当前状态预测
            predicted_numbers, confidence = self._markov_prediction(current_numbers)
            
            # 计算结果
            hit_count = len(set(predicted_numbers) & actual_numbers)
            is_success = hit_count >= 1
            
            predictions.append({
                'period': period_num,
                'previous_numbers': list(current_numbers),
                'predicted_numbers': predicted_numbers,
                'actual_numbers': list(actual_numbers),
                'hit_count': hit_count,
                'is_success': is_success,
                'confidence': confidence,
                'method': 'continuous'
            })
            
            # 更新状态（使用预测结果，这是连续预测的特点）
            current_numbers = set(predicted_numbers + list(current_numbers)[:4])
        
        print(f"✅ 连续预测完成，共{len(predictions)}期")
        return predictions
    
    def method_rolling_prediction(self):
        """方法3：滚动预测方式"""
        print(f"\n🎯 执行滚动预测方式验证")
        print("-" * 40)
        
        predictions = []
        rolling_window = 30  # 滚动窗口大小
        
        for idx, test_row in self.test_data.iterrows():
            period_num = test_row['期号']
            actual_numbers = set([test_row[f'数字{j}'] for j in range(1, 7)])
            
            # 构建滚动训练集（包含最新的真实数据）
            if idx == self.test_data.index[0]:
                # 第一期使用原始训练集
                rolling_train = self.train_data.copy()
                prev_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                # 后续期加入已知的测试数据
                known_test_data = self.test_data.iloc[:self.test_data.index.get_loc(idx)]
                rolling_train = pd.concat([
                    self.train_data.tail(len(self.train_data) - rolling_window),
                    known_test_data
                ], ignore_index=True)
                
                prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                prev_numbers = set([self.test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            # 基于滚动数据重新构建模型
            rolling_transition = self._build_rolling_transition(rolling_train)
            
            # 进行预测
            predicted_numbers, confidence = self._markov_prediction_with_transition(
                prev_numbers, rolling_transition
            )
            
            # 计算结果
            hit_count = len(set(predicted_numbers) & actual_numbers)
            is_success = hit_count >= 1
            
            predictions.append({
                'period': period_num,
                'previous_numbers': list(prev_numbers),
                'predicted_numbers': predicted_numbers,
                'actual_numbers': list(actual_numbers),
                'hit_count': hit_count,
                'is_success': is_success,
                'confidence': confidence,
                'method': 'rolling',
                'rolling_train_size': len(rolling_train)
            })
        
        print(f"✅ 滚动预测完成，共{len(predictions)}期")
        return predictions
    
    def method_probability_distribution_prediction(self):
        """方法4：概率分布预测方式"""
        print(f"\n🎯 执行概率分布预测方式验证")
        print("-" * 40)
        
        predictions = []
        
        for idx, test_row in self.test_data.iterrows():
            period_num = test_row['期号']
            actual_numbers = set([test_row[f'数字{j}'] for j in range(1, 7)])
            
            # 获取前一期数字
            if idx == self.test_data.index[0]:
                prev_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                prev_numbers = set([self.test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            # 计算概率分布
            prob_distribution = self._calculate_probability_distribution(prev_numbers)
            
            # 基于概率分布选择预测数字
            predicted_numbers = self._select_from_distribution(prob_distribution)
            
            # 计算置信度（基于概率分布的熵）
            confidence = self._calculate_distribution_confidence(prob_distribution)
            
            # 计算结果
            hit_count = len(set(predicted_numbers) & actual_numbers)
            is_success = hit_count >= 1
            
            predictions.append({
                'period': period_num,
                'previous_numbers': list(prev_numbers),
                'predicted_numbers': predicted_numbers,
                'actual_numbers': list(actual_numbers),
                'hit_count': hit_count,
                'is_success': is_success,
                'confidence': confidence,
                'method': 'probability_dist',
                'prob_distribution': dict(list(prob_distribution.items())[:10])  # 保存前10个概率
            })
        
        print(f"✅ 概率分布预测完成，共{len(predictions)}期")
        return predictions
    
    def _markov_prediction(self, previous_numbers):
        """马尔可夫预测"""
        return self._markov_prediction_with_transition(previous_numbers, self.transition_prob)
    
    def _markov_prediction_with_transition(self, previous_numbers, transition_prob):
        """使用指定转移概率的马尔可夫预测"""
        if not transition_prob:
            return [1, 2], 0.3
        
        number_probs = defaultdict(float)
        total_prob = 0.0
        
        for prev_num in previous_numbers:
            if prev_num in transition_prob:
                for next_num, prob in transition_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            predicted_numbers = [num for num, prob in sorted_numbers[:2]]
            confidence = np.mean([prob for num, prob in sorted_numbers[:2]]) * 2
            return predicted_numbers, min(0.9, confidence)
        else:
            return [1, 2], 0.3
    
    def _build_rolling_transition(self, rolling_data):
        """构建滚动转移概率"""
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(rolling_data) - 1):
            current_numbers = set([rolling_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([rolling_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                transition_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        return transition_prob
    
    def _calculate_probability_distribution(self, previous_numbers):
        """计算概率分布"""
        # 结合马尔可夫和频率分析
        markov_probs = defaultdict(float)
        freq_probs = defaultdict(float)
        
        # 马尔可夫概率
        total_markov_prob = 0.0
        for prev_num in previous_numbers:
            if prev_num in self.transition_prob:
                for next_num, prob in self.transition_prob[prev_num].items():
                    markov_probs[next_num] += prob
                    total_markov_prob += prob
        
        if total_markov_prob > 0:
            for num in markov_probs:
                markov_probs[num] /= total_markov_prob
        
        # 频率概率
        total_freq = sum(self.number_frequency.values())
        for num in range(1, 50):
            freq_probs[num] = self.number_frequency.get(num, 1) / total_freq
        
        # 组合概率
        combined_probs = {}
        for num in range(1, 50):
            markov_weight = 0.7
            freq_weight = 0.3
            combined_probs[num] = (
                markov_weight * markov_probs.get(num, 0) +
                freq_weight * freq_probs.get(num, 0)
            )
        
        return combined_probs
    
    def _select_from_distribution(self, prob_distribution):
        """从概率分布中选择预测数字"""
        sorted_probs = sorted(prob_distribution.items(), key=lambda x: x[1], reverse=True)
        return [num for num, prob in sorted_probs[:2]]
    
    def _calculate_distribution_confidence(self, prob_distribution):
        """计算分布置信度"""
        probs = list(prob_distribution.values())
        # 使用负熵作为置信度指标
        entropy = -sum(p * np.log2(p + 1e-10) for p in probs if p > 0)
        max_entropy = np.log2(len(probs))
        confidence = 1 - (entropy / max_entropy)
        return confidence

    def run_comprehensive_experiment(self):
        """运行全面验证实验"""
        print(f"\n🚀 开始全面预测方式验证实验")
        print("=" * 80)
        print(f"实验名称: {self.config['experiment_name']}")
        print(f"训练期间: {self.config['train_period']}")
        print(f"测试期间: {self.config['test_period']}")
        print(f"随机种子: {self.config['random_seed']}")

        # 设置随机种子确保可重现
        np.random.seed(self.config['random_seed'])

        # 执行四种预测方式
        methods_results = {}

        # 方法1：单期预测
        methods_results['single_period'] = self.method_single_period_prediction()

        # 方法2：连续预测
        methods_results['continuous'] = self.method_continuous_prediction()

        # 方法3：滚动预测
        methods_results['rolling'] = self.method_rolling_prediction()

        # 方法4：概率分布预测
        methods_results['probability_dist'] = self.method_probability_distribution_prediction()

        self.experiment_results = methods_results
        return methods_results

    def evaluate_accuracy_rate(self, predictions):
        """评估预测准确率"""
        total_predictions = len(predictions)
        successful_predictions = sum(1 for p in predictions if p['is_success'])
        accuracy_rate = successful_predictions / total_predictions if total_predictions > 0 else 0

        # 计算置信区间
        confidence_interval = stats.binom.interval(0.95, total_predictions, accuracy_rate)
        ci_lower = confidence_interval[0] / total_predictions
        ci_upper = confidence_interval[1] / total_predictions

        return {
            'accuracy_rate': accuracy_rate,
            'successful_predictions': successful_predictions,
            'total_predictions': total_predictions,
            'confidence_interval_95': (ci_lower, ci_upper)
        }

    def evaluate_diversity_score(self, predictions):
        """评估预测多样性"""
        prediction_sets = [tuple(sorted(p['predicted_numbers'])) for p in predictions]
        unique_predictions = len(set(prediction_sets))
        total_predictions = len(predictions)
        diversity_score = unique_predictions / total_predictions if total_predictions > 0 else 0

        # 计算重复模式
        prediction_counter = Counter(prediction_sets)
        most_common = prediction_counter.most_common(5)

        return {
            'diversity_score': diversity_score,
            'unique_predictions': unique_predictions,
            'total_predictions': total_predictions,
            'most_common_predictions': most_common
        }

    def evaluate_confidence_calibration(self, predictions):
        """评估置信度校准"""
        # 将预测按置信度分组
        confidence_bins = np.linspace(0, 1, 11)  # 10个区间
        bin_accuracies = []
        bin_confidences = []
        bin_counts = []

        for i in range(len(confidence_bins) - 1):
            bin_lower = confidence_bins[i]
            bin_upper = confidence_bins[i + 1]

            bin_predictions = [
                p for p in predictions
                if bin_lower <= p['confidence'] < bin_upper
            ]

            if bin_predictions:
                bin_accuracy = sum(1 for p in bin_predictions if p['is_success']) / len(bin_predictions)
                bin_confidence = np.mean([p['confidence'] for p in bin_predictions])

                bin_accuracies.append(bin_accuracy)
                bin_confidences.append(bin_confidence)
                bin_counts.append(len(bin_predictions))
            else:
                bin_accuracies.append(0)
                bin_confidences.append((bin_lower + bin_upper) / 2)
                bin_counts.append(0)

        # 计算校准误差
        calibration_error = np.mean([
            abs(acc - conf) * count for acc, conf, count in
            zip(bin_accuracies, bin_confidences, bin_counts)
        ]) / sum(bin_counts) if sum(bin_counts) > 0 else 0

        return {
            'calibration_error': calibration_error,
            'bin_accuracies': bin_accuracies,
            'bin_confidences': bin_confidences,
            'bin_counts': bin_counts
        }

    def evaluate_error_accumulation(self, predictions):
        """评估误差累积效应"""
        # 计算滑动窗口准确率
        window_size = 30
        sliding_accuracies = []

        for i in range(len(predictions) - window_size + 1):
            window_predictions = predictions[i:i + window_size]
            window_accuracy = sum(1 for p in window_predictions if p['is_success']) / window_size
            sliding_accuracies.append(window_accuracy)

        # 计算趋势
        if len(sliding_accuracies) > 1:
            x = np.arange(len(sliding_accuracies))
            slope, intercept, r_value, p_value, std_err = stats.linregress(x, sliding_accuracies)
        else:
            slope, r_value, p_value = 0, 0, 1

        return {
            'sliding_accuracies': sliding_accuracies,
            'trend_slope': slope,
            'trend_correlation': r_value,
            'trend_p_value': p_value,
            'initial_accuracy': sliding_accuracies[0] if sliding_accuracies else 0,
            'final_accuracy': sliding_accuracies[-1] if sliding_accuracies else 0
        }

    def evaluate_betting_effectiveness(self, predictions):
        """评估投注建议有效性"""
        # 简化的投注策略：基于置信度阈值
        confidence_threshold = 0.4

        bet_predictions = [p for p in predictions if p['confidence'] >= confidence_threshold]
        skip_predictions = [p for p in predictions if p['confidence'] < confidence_threshold]

        bet_accuracy = (
            sum(1 for p in bet_predictions if p['is_success']) / len(bet_predictions)
            if bet_predictions else 0
        )
        skip_accuracy = (
            sum(1 for p in skip_predictions if p['is_success']) / len(skip_predictions)
            if skip_predictions else 0
        )

        timing_advantage = bet_accuracy - skip_accuracy

        return {
            'bet_accuracy': bet_accuracy,
            'skip_accuracy': skip_accuracy,
            'timing_advantage': timing_advantage,
            'bet_count': len(bet_predictions),
            'skip_count': len(skip_predictions),
            'bet_ratio': len(bet_predictions) / len(predictions) if predictions else 0
        }

    def comprehensive_evaluation(self):
        """全面评估所有方法"""
        print(f"\n📊 全面评估所有预测方法")
        print("=" * 60)

        evaluation_results = {}

        for method_name, predictions in self.experiment_results.items():
            print(f"\n评估方法: {method_name}")

            method_evaluation = {
                'accuracy': self.evaluate_accuracy_rate(predictions),
                'diversity': self.evaluate_diversity_score(predictions),
                'calibration': self.evaluate_confidence_calibration(predictions),
                'error_accumulation': self.evaluate_error_accumulation(predictions),
                'betting_effectiveness': self.evaluate_betting_effectiveness(predictions)
            }

            evaluation_results[method_name] = method_evaluation

            # 显示关键指标
            acc = method_evaluation['accuracy']['accuracy_rate']
            div = method_evaluation['diversity']['diversity_score']
            cal = method_evaluation['calibration']['calibration_error']
            bet = method_evaluation['betting_effectiveness']['timing_advantage']

            print(f"  准确率: {acc:.3f}")
            print(f"  多样性: {div:.3f}")
            print(f"  校准误差: {cal:.3f}")
            print(f"  择时优势: {bet:+.3f}")

        return evaluation_results

    def statistical_significance_testing(self, evaluation_results):
        """统计显著性检验"""
        print(f"\n🔬 统计显著性检验")
        print("=" * 60)

        methods = list(evaluation_results.keys())
        significance_results = {}

        # 准确率的成对比较
        print(f"\n准确率成对比较 (McNemar检验):")
        for i, method1 in enumerate(methods):
            for j, method2 in enumerate(methods):
                if i < j:
                    pred1 = self.experiment_results[method1]
                    pred2 = self.experiment_results[method2]

                    # 构建2x2列联表
                    both_correct = sum(1 for p1, p2 in zip(pred1, pred2)
                                     if p1['is_success'] and p2['is_success'])
                    only_1_correct = sum(1 for p1, p2 in zip(pred1, pred2)
                                       if p1['is_success'] and not p2['is_success'])
                    only_2_correct = sum(1 for p1, p2 in zip(pred1, pred2)
                                       if not p1['is_success'] and p2['is_success'])
                    both_wrong = sum(1 for p1, p2 in zip(pred1, pred2)
                                   if not p1['is_success'] and not p2['is_success'])

                    # McNemar检验
                    if only_1_correct + only_2_correct > 0:
                        mcnemar_stat = (abs(only_1_correct - only_2_correct) - 1)**2 / (only_1_correct + only_2_correct)
                        p_value = 1 - stats.chi2.cdf(mcnemar_stat, 1)
                    else:
                        mcnemar_stat = 0
                        p_value = 1.0

                    significance_results[f"{method1}_vs_{method2}"] = {
                        'mcnemar_statistic': mcnemar_stat,
                        'p_value': p_value,
                        'significant': p_value < 0.05
                    }

                    print(f"  {method1} vs {method2}: p={p_value:.4f} {'*' if p_value < 0.05 else ''}")

        return significance_results

    def generate_comprehensive_report(self, evaluation_results, significance_results):
        """生成综合分析报告"""
        print(f"\n📋 生成综合分析报告")
        print("=" * 60)

        report = {
            'experiment_metadata': {
                'experiment_name': self.config['experiment_name'],
                'train_period': self.config['train_period'],
                'test_period': self.config['test_period'],
                'train_size': len(self.train_data),
                'test_size': len(self.test_data),
                'random_seed': self.config['random_seed'],
                'execution_time': datetime.now().isoformat()
            },
            'method_comparison': {},
            'statistical_tests': significance_results,
            'key_findings': [],
            'recommendations': []
        }

        # 方法对比
        for method_name, evaluation in evaluation_results.items():
            report['method_comparison'][method_name] = {
                'accuracy_rate': evaluation['accuracy']['accuracy_rate'],
                'accuracy_ci': evaluation['accuracy']['confidence_interval_95'],
                'diversity_score': evaluation['diversity']['diversity_score'],
                'calibration_error': evaluation['calibration']['calibration_error'],
                'timing_advantage': evaluation['betting_effectiveness']['timing_advantage'],
                'bet_ratio': evaluation['betting_effectiveness']['bet_ratio'],
                'trend_slope': evaluation['error_accumulation']['trend_slope']
            }

        # 关键发现
        accuracies = {method: eval_result['accuracy']['accuracy_rate']
                     for method, eval_result in evaluation_results.items()}
        best_accuracy_method = max(accuracies, key=accuracies.get)

        diversities = {method: eval_result['diversity']['diversity_score']
                      for method, eval_result in evaluation_results.items()}
        best_diversity_method = max(diversities, key=diversities.get)

        report['key_findings'] = [
            f"最高准确率方法: {best_accuracy_method} ({accuracies[best_accuracy_method]:.3f})",
            f"最高多样性方法: {best_diversity_method} ({diversities[best_diversity_method]:.3f})",
            f"统计显著性差异: {sum(1 for result in significance_results.values() if result['significant'])}对比较显著"
        ]

        # 建议
        report['recommendations'] = [
            "基于准确率选择最佳方法",
            "考虑多样性要求选择合适方法",
            "根据应用场景权衡准确率与多样性",
            "使用统计检验结果指导方法选择"
        ]

        return report

def main():
    """主函数"""
    print("🔬 全面预测方式验证实验")
    print("基于2025年1-179期测试集数据的严格对比验证")
    print("=" * 80)

    # 初始化验证器
    validator = ComprehensivePredictionValidator()

    # 1. 加载和准备数据
    if not validator.load_and_prepare_data():
        return

    # 2. 构建基础模型
    validator.build_base_model()

    # 3. 运行全面实验
    experiment_results = validator.run_comprehensive_experiment()

    # 4. 全面评估
    evaluation_results = validator.comprehensive_evaluation()

    # 5. 统计显著性检验
    significance_results = validator.statistical_significance_testing(evaluation_results)

    # 6. 生成综合报告
    comprehensive_report = validator.generate_comprehensive_report(
        evaluation_results, significance_results
    )

    # 7. 保存结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"全面预测验证实验结果_{timestamp}.json"

    # 处理numpy类型
    def convert_numpy_types(obj):
        if isinstance(obj, dict):
            return {k: convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(v) for v in obj]
        elif hasattr(obj, 'item'):
            return obj.item()
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, (np.integer, int)):
            return int(obj)
        elif isinstance(obj, (np.floating, float)):
            return float(obj)
        else:
            return obj

    final_results = {
        'comprehensive_report': convert_numpy_types(comprehensive_report),
        'detailed_evaluation': convert_numpy_types(evaluation_results),
        'experiment_data': convert_numpy_types(experiment_results)
    }

    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(final_results, f, ensure_ascii=False, indent=2)

    print(f"\n✅ 实验结果已保存: {results_file}")

    # 8. 显示关键结果
    print(f"\n🎉 实验总结")
    print("=" * 50)

    for finding in comprehensive_report['key_findings']:
        print(f"✅ {finding}")

    print(f"\n💡 主要建议:")
    for recommendation in comprehensive_report['recommendations']:
        print(f"  - {recommendation}")

if __name__ == "__main__":
    main()
