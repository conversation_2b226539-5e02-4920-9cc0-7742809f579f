#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练数据区间调节实验可视化分析
生成图表和详细的数据分析表格
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class TrainingIntervalVisualization:
    """训练数据区间调节实验可视化分析"""
    
    def __init__(self):
        # 实验结果数据
        self.results_data = {
            '2023-2024训练/2025测试': {
                'train_periods': 731,
                'test_periods': 178,
                'success_rate': 0.292,
                'p_value': None,
                'significant': False,
                'is_baseline': True
            },
            '80%/20%': {
                'train_periods': 1310,
                'test_periods': 328,
                'success_rate': 0.223,
                'p_value': 0.0052,
                'significant': True,
                'is_baseline': False
            },
            '85%/15%': {
                'train_periods': 1392,
                'test_periods': 246,
                'success_rate': 0.220,
                'p_value': 0.0116,
                'significant': True,
                'is_baseline': False
            },
            '90%/10%': {
                'train_periods': 1474,
                'test_periods': 164,
                'success_rate': 0.262,
                'p_value': 0.4400,
                'significant': False,
                'is_baseline': False
            },
            '95%/5%': {
                'train_periods': 1556,
                'test_periods': 82,
                'success_rate': 0.232,
                'p_value': 0.2743,
                'significant': False,
                'is_baseline': False
            }
        }
        
        self.baseline_performance = 0.292
        
    def create_performance_comparison_chart(self):
        """创建性能对比图表"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 准备数据
        methods = list(self.results_data.keys())
        success_rates = [data['success_rate'] for data in self.results_data.values()]
        train_periods = [data['train_periods'] for data in self.results_data.values()]
        colors = ['red' if data['is_baseline'] else 'blue' for data in self.results_data.values()]
        
        # 图1：成功率对比
        bars1 = ax1.bar(range(len(methods)), success_rates, color=colors, alpha=0.7)
        ax1.axhline(y=self.baseline_performance, color='red', linestyle='--', alpha=0.8, label='基准线 (29.2%)')
        ax1.set_xlabel('数据分割方式')
        ax1.set_ylabel('成功率')
        ax1.set_title('不同数据分割方式的预测成功率对比')
        ax1.set_xticks(range(len(methods)))
        ax1.set_xticklabels(methods, rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (bar, rate) in enumerate(zip(bars1, success_rates)):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                    f'{rate:.1%}', ha='center', va='bottom')
        
        # 图2：训练数据量与性能关系
        ax2.scatter(train_periods, success_rates, c=colors, s=100, alpha=0.7)
        ax2.axhline(y=self.baseline_performance, color='red', linestyle='--', alpha=0.8)
        
        # 添加趋势线
        z = np.polyfit(train_periods, success_rates, 1)
        p = np.poly1d(z)
        ax2.plot(train_periods, p(train_periods), "r--", alpha=0.8, label=f'趋势线 (斜率={z[0]:.6f})')
        
        ax2.set_xlabel('训练数据期数')
        ax2.set_ylabel('成功率')
        ax2.set_title('训练数据量与预测性能关系')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 添加点标签
        for i, method in enumerate(methods):
            ax2.annotate(method, (train_periods[i], success_rates[i]), 
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        plt.tight_layout()
        plt.savefig('训练数据区间调节性能对比图.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return fig
    
    def create_statistical_significance_chart(self):
        """创建统计显著性分析图表"""
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        
        # 准备数据（排除基准）
        methods = [k for k, v in self.results_data.items() if not v['is_baseline']]
        p_values = [v['p_value'] for k, v in self.results_data.items() if not v['is_baseline']]
        success_rates = [v['success_rate'] for k, v in self.results_data.items() if not v['is_baseline']]
        significant = [v['significant'] for k, v in self.results_data.items() if not v['is_baseline']]
        
        # 创建散点图
        colors = ['red' if sig else 'blue' for sig in significant]
        sizes = [100 if sig else 60 for sig in significant]
        
        scatter = ax.scatter(success_rates, p_values, c=colors, s=sizes, alpha=0.7)
        
        # 添加显著性阈值线
        ax.axhline(y=0.05, color='red', linestyle='--', alpha=0.8, label='显著性阈值 (p=0.05)')
        ax.axvline(x=self.baseline_performance, color='green', linestyle='--', alpha=0.8, label='基准性能 (29.2%)')
        
        # 设置坐标轴
        ax.set_xlabel('预测成功率')
        ax.set_ylabel('p值')
        ax.set_title('统计显著性分析：成功率 vs p值')
        ax.set_yscale('log')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加标签
        for i, method in enumerate(methods):
            ax.annotate(method, (success_rates[i], p_values[i]), 
                       xytext=(5, 5), textcoords='offset points', fontsize=9)
        
        # 添加图例说明
        ax.text(0.02, 0.98, '红色：显著差异\n蓝色：无显著差异', 
               transform=ax.transAxes, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        plt.savefig('训练数据区间调节显著性分析图.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return fig
    
    def generate_detailed_analysis_table(self):
        """生成详细分析表格"""
        print("📊 训练数据区间调节实验详细分析表")
        print("=" * 100)
        
        # 创建DataFrame
        df_data = []
        for method, data in self.results_data.items():
            improvement = (data['success_rate'] - self.baseline_performance) * 100
            train_ratio = data['train_periods'] / (data['train_periods'] + data['test_periods'])
            
            df_data.append({
                '分割方式': method,
                '训练期数': data['train_periods'],
                '测试期数': data['test_periods'],
                '训练比例': f"{train_ratio:.1%}",
                '成功率': f"{data['success_rate']:.3f}",
                '成功率%': f"{data['success_rate']*100:.1f}%",
                '相对基准': f"{improvement:+.1f}%",
                'p值': f"{data['p_value']:.4f}" if data['p_value'] is not None else "基准",
                '显著性': "✓" if data['significant'] else ("基准" if data['is_baseline'] else "✗"),
                '风险等级': self._assess_overfitting_risk(train_ratio)
            })
        
        df = pd.DataFrame(df_data)
        
        # 按成功率排序
        df = df.sort_values('成功率', ascending=False)
        
        print(df.to_string(index=False))
        
        # 保存为CSV
        df.to_csv('训练数据区间调节详细分析表.csv', index=False, encoding='utf-8-sig')
        print(f"\n✅ 详细分析表已保存为: 训练数据区间调节详细分析表.csv")
        
        return df
    
    def _assess_overfitting_risk(self, train_ratio):
        """评估过拟合风险"""
        if train_ratio >= 0.95:
            return "高风险"
        elif train_ratio >= 0.90:
            return "中等风险"
        else:
            return "低风险"
    
    def generate_correlation_analysis(self):
        """生成相关性分析"""
        print(f"\n🔍 相关性分析")
        print("=" * 50)
        
        # 提取数据
        train_periods = [data['train_periods'] for data in self.results_data.values()]
        success_rates = [data['success_rate'] for data in self.results_data.values()]
        
        # 计算相关系数
        correlation = np.corrcoef(train_periods, success_rates)[0, 1]
        
        print(f"训练数据量与成功率的相关系数: {correlation:.3f}")
        
        if abs(correlation) > 0.7:
            strength = "强"
        elif abs(correlation) > 0.5:
            strength = "中等"
        elif abs(correlation) > 0.3:
            strength = "弱"
        else:
            strength = "极弱"
        
        direction = "正" if correlation > 0 else "负"
        
        print(f"相关性强度: {strength}{direction}相关")
        
        if correlation < -0.5:
            print("⚠️ 发现强负相关：训练数据量增加导致性能下降")
            print("💡 建议：避免使用过多历史数据，保持适中的训练窗口")
        
        return correlation
    
    def generate_recommendations(self):
        """生成优化建议"""
        print(f"\n💡 生产系统优化建议")
        print("=" * 50)
        
        # 找到最佳配置
        best_method = max(self.results_data.items(), key=lambda x: x[1]['success_rate'])
        best_name, best_data = best_method
        
        print(f"🎯 最优配置: {best_name}")
        print(f"  - 成功率: {best_data['success_rate']:.3f} ({best_data['success_rate']*100:.1f}%)")
        print(f"  - 训练数据: {best_data['train_periods']}期")
        print(f"  - 测试数据: {best_data['test_periods']}期")
        
        if best_data['is_baseline']:
            print(f"✅ 当前基准配置已是最优选择，建议保持不变")
        else:
            improvement = (best_data['success_rate'] - self.baseline_performance) * 100
            print(f"✅ 建议切换到最优配置，预期提升: +{improvement:.1f}%")
        
        print(f"\n❌ 不建议的配置:")
        for method, data in self.results_data.items():
            if data['significant'] and data['success_rate'] < self.baseline_performance:
                decline = (self.baseline_performance - data['success_rate']) * 100
                print(f"  - {method}: 显著降低性能 -{decline:.1f}%")
        
        print(f"\n⚠️ 风险提醒:")
        for method, data in self.results_data.items():
            train_ratio = data['train_periods'] / (data['train_periods'] + data['test_periods'])
            if train_ratio >= 0.95:
                print(f"  - {method}: 高过拟合风险（训练比例{train_ratio:.1%}）")

def main():
    """主函数"""
    print("🎯 训练数据区间调节实验可视化分析")
    print("=" * 60)
    
    # 初始化可视化分析
    viz = TrainingIntervalVisualization()
    
    # 1. 生成详细分析表格
    df = viz.generate_detailed_analysis_table()
    
    # 2. 相关性分析
    correlation = viz.generate_correlation_analysis()
    
    # 3. 生成优化建议
    viz.generate_recommendations()
    
    # 4. 创建可视化图表
    print(f"\n📈 生成可视化图表...")
    try:
        fig1 = viz.create_performance_comparison_chart()
        print("✅ 性能对比图表已生成")
    except Exception as e:
        print(f"⚠️ 图表生成失败: {e}")
    
    try:
        fig2 = viz.create_statistical_significance_chart()
        print("✅ 显著性分析图表已生成")
    except Exception as e:
        print(f"⚠️ 图表生成失败: {e}")
    
    # 5. 生成总结报告
    print(f"\n📋 实验总结")
    print("=" * 40)
    print(f"✅ 验证了5种不同的数据分割配置")
    print(f"✅ 发现当前基准配置是最优选择")
    print(f"✅ 识别了训练数据量与性能的负相关关系")
    print(f"✅ 提供了统计显著性验证")
    print(f"✅ 生成了详细的分析表格和可视化图表")
    
    print(f"\n🎉 分析完成！所有结果已保存到文件。")

if __name__ == "__main__":
    main()
