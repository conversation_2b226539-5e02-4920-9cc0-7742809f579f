#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
审查prediction_data.csv与lottery_data_clean_no_special.csv的数据一致性
Audit data consistency between prediction_data.csv and lottery_data_clean_no_special.csv
"""

import pandas as pd
import numpy as np

def load_data():
    """加载两个数据文件"""
    print("📊 加载数据文件")
    print("="*40)
    
    # 加载真实开奖数据
    lottery_df = pd.read_csv('data/processed/lottery_data_clean_no_special.csv')
    print(f"✅ 真实开奖数据: {len(lottery_df)}期")
    print(f"   年份范围: {lottery_df['年份'].min()}-{lottery_df['年份'].max()}")
    print(f"   期号范围: {lottery_df['期号'].min()}-{lottery_df['期号'].max()}")
    
    # 加载预测数据
    prediction_df = pd.read_csv('prediction_data.csv')
    print(f"✅ 预测数据: {len(prediction_df)}期")
    print(f"   预测期号范围: {prediction_df['预测期号'].iloc[0]} - {prediction_df['预测期号'].iloc[-1]}")
    
    return lottery_df, prediction_df

def analyze_data_structure():
    """分析数据结构"""
    print(f"\n🔍 分析数据结构")
    print("="*40)
    
    lottery_df, prediction_df = load_data()
    
    print(f"真实开奖数据结构:")
    print(f"  列名: {list(lottery_df.columns)}")
    print(f"  数据示例 (2025年前5期):")
    
    # 查看2025年的数据
    lottery_2025 = lottery_df[lottery_df['年份'] == 2025].head(10)
    if len(lottery_2025) > 0:
        for _, row in lottery_2025.iterrows():
            numbers = [row[f'数字{i}'] for i in range(1, 7)]
            print(f"    {row['年份']}年{row['期号']}期: {numbers}")
    else:
        print("    ⚠️ 没有找到2025年的数据")
    
    print(f"\n预测数据结构:")
    print(f"  预测期号示例:")
    for i in range(min(5, len(prediction_df))):
        row = prediction_df.iloc[i]
        pred_nums = [row['预测数字1'], row['预测数字2']]
        actual_nums = [row[f'实际数字{j}'] for j in range(1, 7) if pd.notna(row[f'实际数字{j}'])]
        print(f"    {row['预测期号']}: 预测{pred_nums}, 实际{actual_nums}")

def cross_validate_data():
    """交叉验证数据"""
    print(f"\n🔍 交叉验证数据一致性")
    print("="*50)
    
    lottery_df, prediction_df = load_data()
    
    # 检查预测数据中的实际数字是否与真实开奖数据匹配
    mismatches = []
    matches = []
    
    for _, pred_row in prediction_df.iterrows():
        # 解析预测期号
        period_str = pred_row['预测期号']  # 格式: "2025年X期"
        
        try:
            # 提取年份和期号
            if '年' in period_str and '期' in period_str:
                year_str = period_str.split('年')[0]
                period_str_num = period_str.split('年')[1].replace('期', '')
                
                year = int(year_str)
                period = int(period_str_num)
                
                # 在真实数据中查找对应期号
                lottery_match = lottery_df[
                    (lottery_df['年份'] == year) & 
                    (lottery_df['期号'] == period)
                ]
                
                if len(lottery_match) > 0:
                    # 获取真实开奖数字
                    real_numbers = [lottery_match.iloc[0][f'数字{i}'] for i in range(1, 7)]
                    real_numbers_sorted = sorted(real_numbers)
                    
                    # 获取预测数据中的实际数字
                    pred_actual_numbers = []
                    for i in range(1, 7):
                        val = pred_row[f'实际数字{i}']
                        if pd.notna(val) and val != '':
                            pred_actual_numbers.append(float(val))
                    
                    pred_actual_numbers_sorted = sorted(pred_actual_numbers)
                    
                    # 比较数据
                    if len(pred_actual_numbers) > 0:
                        if real_numbers_sorted == pred_actual_numbers_sorted:
                            matches.append({
                                'period': f"{year}年{period}期",
                                'real_numbers': real_numbers_sorted,
                                'pred_actual_numbers': pred_actual_numbers_sorted,
                                'status': '✅ 匹配'
                            })
                        else:
                            mismatches.append({
                                'period': f"{year}年{period}期",
                                'real_numbers': real_numbers_sorted,
                                'pred_actual_numbers': pred_actual_numbers_sorted,
                                'status': '❌ 不匹配'
                            })
                    else:
                        # 预测数据中没有实际数字
                        mismatches.append({
                            'period': f"{year}年{period}期",
                            'real_numbers': real_numbers_sorted,
                            'pred_actual_numbers': [],
                            'status': '⚠️ 预测数据中无实际数字'
                        })
                else:
                    # 真实数据中没有找到对应期号
                    mismatches.append({
                        'period': f"{year}年{period}期",
                        'real_numbers': None,
                        'pred_actual_numbers': pred_actual_numbers if 'pred_actual_numbers' in locals() else [],
                        'status': '❌ 真实数据中无此期号'
                    })
                    
        except Exception as e:
            print(f"⚠️ 解析期号失败: {period_str}, 错误: {e}")
    
    # 输出结果
    print(f"验证结果:")
    print(f"  匹配数量: {len(matches)}")
    print(f"  不匹配数量: {len(mismatches)}")
    
    if len(matches) > 0:
        print(f"\n✅ 匹配示例 (前5个):")
        for match in matches[:5]:
            print(f"  {match['period']}: {match['real_numbers']}")
    
    if len(mismatches) > 0:
        print(f"\n❌ 不匹配详情 (前10个):")
        for mismatch in mismatches[:10]:
            print(f"  {mismatch['period']}: {mismatch['status']}")
            if mismatch['real_numbers']:
                print(f"    真实数据: {mismatch['real_numbers']}")
            if mismatch['pred_actual_numbers']:
                print(f"    预测数据: {mismatch['pred_actual_numbers']}")
            print()
    
    return matches, mismatches

def check_year_period_mapping():
    """检查年份期号映射"""
    print(f"\n🔍 检查年份期号映射")
    print("="*40)
    
    lottery_df, prediction_df = load_data()
    
    # 检查2025年的数据是否存在
    lottery_2025 = lottery_df[lottery_df['年份'] == 2025]
    print(f"真实数据中2025年期数: {len(lottery_2025)}")
    
    if len(lottery_2025) > 0:
        print(f"2025年期号范围: {lottery_2025['期号'].min()}-{lottery_2025['期号'].max()}")
        print(f"2025年前10期数据:")
        for _, row in lottery_2025.head(10).iterrows():
            numbers = [row[f'数字{i}'] for i in range(1, 7)]
            print(f"  {row['年份']}年{row['期号']}期: {numbers}")
    else:
        print("❌ 真实数据中没有2025年的数据！")
        
        # 检查最新的数据
        latest_data = lottery_df.tail(10)
        print(f"\n最新10期真实数据:")
        for _, row in latest_data.iterrows():
            numbers = [row[f'数字{i}'] for i in range(1, 7)]
            print(f"  {row['年份']}年{row['期号']}期: {numbers}")

def identify_data_source_issue():
    """识别数据源问题"""
    print(f"\n🔍 识别数据源问题")
    print("="*40)
    
    lottery_df, prediction_df = load_data()
    
    # 分析预测数据的年份分布
    print(f"预测数据分析:")
    print(f"  所有预测都标记为2025年")
    print(f"  预测期号: 2-194期")
    
    # 分析真实数据的年份分布
    print(f"\n真实数据分析:")
    year_counts = lottery_df['年份'].value_counts().sort_index()
    for year, count in year_counts.items():
        period_range = lottery_df[lottery_df['年份'] == year]['期号']
        print(f"  {year}年: {count}期 (期号: {period_range.min()}-{period_range.max()})")
    
    # 检查数据对应关系
    print(f"\n❌ 发现的问题:")
    print(f"1. 预测数据标记为2025年2-194期")
    print(f"2. 但真实数据中可能没有2025年的数据")
    print(f"3. 预测数据应该对应真实数据的某个年份区间")
    
    # 建议修正方案
    print(f"\n💡 建议修正方案:")
    print(f"1. 确定预测数据实际对应的年份")
    print(f"2. 修正预测数据中的年份标记")
    print(f"3. 重新验证命中情况")

def suggest_correction():
    """建议修正方案"""
    print(f"\n🔧 数据修正建议")
    print("="*40)
    
    lottery_df, prediction_df = load_data()
    
    # 分析可能的对应关系
    total_lottery_periods = len(lottery_df)
    total_prediction_periods = len(prediction_df)
    
    print(f"数据量分析:")
    print(f"  真实数据总期数: {total_lottery_periods}")
    print(f"  预测数据期数: {total_prediction_periods}")
    
    # 假设预测数据对应真实数据的最后192期
    if total_lottery_periods >= total_prediction_periods:
        start_index = total_lottery_periods - total_prediction_periods
        corresponding_real_data = lottery_df.iloc[start_index:].reset_index(drop=True)
        
        print(f"\n建议对应关系:")
        print(f"  预测数据第1期 → 真实数据第{start_index+1}期")
        print(f"  预测数据第{total_prediction_periods}期 → 真实数据第{total_lottery_periods}期")
        
        print(f"\n对应的真实数据范围:")
        for i in [0, 1, 2, -3, -2, -1]:  # 显示前3期和后3期
            if 0 <= i < len(corresponding_real_data) or len(corresponding_real_data) + i >= 0:
                idx = i if i >= 0 else len(corresponding_real_data) + i
                row = corresponding_real_data.iloc[idx]
                numbers = [row[f'数字{i}'] for i in range(1, 7)]
                print(f"    {row['年份']}年{row['期号']}期: {numbers}")
    
    print(f"\n🔧 修正步骤:")
    print(f"1. 重新映射预测数据的年份和期号")
    print(f"2. 使用正确的真实开奖数据")
    print(f"3. 重新计算命中情况")
    print(f"4. 验证数据一致性")

def main():
    """主函数"""
    print("🔍 审查prediction_data.csv与lottery_data_clean_no_special.csv数据一致性")
    print("="*80)
    
    try:
        # 1. 分析数据结构
        analyze_data_structure()
        
        # 2. 交叉验证数据
        matches, mismatches = cross_validate_data()
        
        # 3. 检查年份期号映射
        check_year_period_mapping()
        
        # 4. 识别数据源问题
        identify_data_source_issue()
        
        # 5. 建议修正方案
        suggest_correction()
        
        print(f"\n🎯 审查结论:")
        print(f"prediction_data.csv文件存在数据映射问题：")
        print(f"1. 预测数据标记为2025年，但真实数据可能没有2025年数据")
        print(f"2. 需要重新映射预测数据到正确的真实开奖数据")
        print(f"3. 当前的命中验证结果可能不准确")
        
    except Exception as e:
        print(f"❌ 审查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
