#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
每月命中率详细分析
Monthly hit rate detailed analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime
import calendar

def analyze_monthly_hit_rates_fixed():
    """修复的每月命中率分析"""
    print("📅 每月命中率详细分析")
    print("="*60)
    
    try:
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        
        # 过滤有效数据
        valid_data = df[
            df['是否命中'].notna() & 
            (df['是否命中'] != '') &
            df['当期年份'].notna() &
            df['当期期号'].notna()
        ].copy()
        
        print(f"有效数据: {len(valid_data)}条")
        
        # 基于期号推算月份（假设每月约16-17期）
        # 2025年从第2期开始，大致每月16期
        def period_to_month(period):
            if period <= 17:
                return 1  # 1月
            elif period <= 33:
                return 2  # 2月
            elif period <= 49:
                return 3  # 3月
            elif period <= 65:
                return 4  # 4月
            elif period <= 81:
                return 5  # 5月
            elif period <= 97:
                return 6  # 6月
            elif period <= 113:
                return 7  # 7月
            elif period <= 129:
                return 8  # 8月
            elif period <= 145:
                return 9  # 9月
            elif period <= 161:
                return 10  # 10月
            elif period <= 177:
                return 11  # 11月
            else:
                return 12  # 12月
        
        # 添加推算月份
        valid_data['推算月份'] = valid_data['当期期号'].apply(period_to_month)
        
        # 按月统计
        monthly_stats = []
        
        for month in range(1, 13):
            month_data = valid_data[valid_data['推算月份'] == month]
            
            if len(month_data) > 0:
                total_predictions = len(month_data)
                hit_count = len(month_data[month_data['是否命中'] == '是'])
                hit_rate = (hit_count / total_predictions * 100) if total_predictions > 0 else 0
                
                # 期号范围
                min_period = month_data['当期期号'].min()
                max_period = month_data['当期期号'].max()
                
                monthly_stats.append({
                    'month': month,
                    'month_name': calendar.month_name[month][:3],
                    'total': total_predictions,
                    'hits': hit_count,
                    'hit_rate': hit_rate,
                    'period_range': f"{min_period}-{max_period}期"
                })
        
        # 显示结果
        print(f"\n2025年各月命中率:")
        print(f"{'月份':<8} {'期号范围':<12} {'预测数':<8} {'命中数':<8} {'命中率':<8}")
        print("-" * 55)
        
        total_all = 0
        hits_all = 0
        
        for stat in monthly_stats:
            print(f"{stat['month_name']:<8} {stat['period_range']:<12} {stat['total']:<8} {stat['hits']:<8} {stat['hit_rate']:<7.1f}%")
            total_all += stat['total']
            hits_all += stat['hits']
        
        # 总体统计
        overall_hit_rate = (hits_all / total_all * 100) if total_all > 0 else 0
        print(f"{'总计':<8} {'2-199期':<12} {total_all:<8} {hits_all:<8} {overall_hit_rate:<7.1f}%")
        
        # 月度趋势分析
        if len(monthly_stats) > 1:
            print(f"\n📊 月度趋势分析:")
            
            hit_rates = [stat['hit_rate'] for stat in monthly_stats]
            
            # 最好和最差月份
            best_month = max(monthly_stats, key=lambda x: x['hit_rate'])
            worst_month = min(monthly_stats, key=lambda x: x['hit_rate'])
            
            print(f"   最佳月份: {best_month['month_name']} ({best_month['hit_rate']:.1f}%)")
            print(f"   最差月份: {worst_month['month_name']} ({worst_month['hit_rate']:.1f}%)")
            print(f"   月度差异: {best_month['hit_rate'] - worst_month['hit_rate']:.1f}%")
            
            # 计算标准差
            hit_rate_std = np.std(hit_rates)
            print(f"   标准差: {hit_rate_std:.1f}%")
            
            # 趋势分析
            months = [stat['month'] for stat in monthly_stats]
            correlation = np.corrcoef(months, hit_rates)[0,1] if len(months) > 1 else 0
            
            print(f"   时间趋势相关性: {correlation:.3f}")
            
            if correlation > 0.3:
                print("   📈 命中率呈上升趋势")
            elif correlation < -0.3:
                print("   📉 命中率呈下降趋势")
            else:
                print("   ➡️ 命中率无明显趋势")
        
        return monthly_stats
        
    except Exception as e:
        print(f"❌ 月度分析失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def analyze_prediction_evolution():
    """分析预测演化过程"""
    print(f"\n🔄 预测演化过程分析")
    print("="*50)
    
    try:
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        
        # 按期号分段分析
        segments = [
            (2, 50, "早期 (2-50期)"),
            (51, 100, "中期 (51-100期)"),
            (101, 150, "后期 (101-150期)"),
            (151, 199, "最新期 (151-199期)")
        ]
        
        print("各阶段预测特征分析:")
        
        for start, end, period_name in segments:
            segment_data = df[
                (df['当期期号'] >= start) & 
                (df['当期期号'] <= end) &
                df['预测数字1'].notna() & 
                df['预测数字2'].notna()
            ]
            
            if len(segment_data) == 0:
                continue
            
            print(f"\n{period_name}:")
            
            # 统计预测数字
            predictions = []
            for _, row in segment_data.iterrows():
                try:
                    num1 = int(float(row['预测数字1']))
                    num2 = int(float(row['预测数字2']))
                    predictions.extend([num1, num2])
                except (ValueError, TypeError):
                    continue
            
            if predictions:
                from collections import Counter
                pred_counter = Counter(predictions)
                
                print(f"   预测次数: {len(segment_data)}")
                print(f"   最频繁数字: {pred_counter.most_common(5)}")
                
                # 分析2和3的频率
                count_2 = pred_counter.get(2, 0)
                count_3 = pred_counter.get(3, 0)
                total_nums = len(predictions)
                
                print(f"   数字2频率: {count_2}/{total_nums} ({count_2/total_nums*100:.1f}%)")
                print(f"   数字3频率: {count_3}/{total_nums} ({count_3/total_nums*100:.1f}%)")
                
                # 命中率
                hit_data = segment_data[segment_data['是否命中'].notna() & (segment_data['是否命中'] != '')]
                if len(hit_data) > 0:
                    hit_count = len(hit_data[hit_data['是否命中'] == '是'])
                    hit_rate = hit_count / len(hit_data) * 100
                    print(f"   命中率: {hit_rate:.1f}% ({hit_count}/{len(hit_data)})")
        
        return True
        
    except Exception as e:
        print(f"❌ 演化分析失败: {e}")
        return False

def analyze_system_learning_failure():
    """分析系统学习失败的原因"""
    print(f"\n🧠 系统学习失败原因分析")
    print("="*50)
    
    print("为什么系统没有从200期预测中学习？")
    
    print(f"\n1. 🔒 静态参数问题:")
    print("   - 马尔可夫转移矩阵在初始化后基本不变")
    print("   - 特征权重固化，不会根据结果调整")
    print("   - 缺乏在线学习机制")
    
    print(f"\n2. 📊 反馈回路缺失:")
    print("   - 系统不会分析预测失败的原因")
    print("   - 没有建立'失败-调整-改进'的循环")
    print("   - 评分系统与实际命中率脱节")
    
    print(f"\n3. 🎯 目标函数错误:")
    print("   - 优化的是评分而不是命中率")
    print("   - 评分算法本身可能有偏差")
    print("   - 缺乏真实的损失函数")
    
    print(f"\n4. 🔄 数据使用方式:")
    print("   - 只使用历史数据训练，不使用实时反馈")
    print("   - 新的预测结果没有用于模型更新")
    print("   - 缺乏增量学习能力")
    
    print(f"\n5. 🎲 随机性认知偏差:")
    print("   - 过度相信历史模式会重复")
    print("   - 忽略了彩票的真随机特性")
    print("   - 试图在噪声中找信号")

def critical_insights():
    """批判性洞察"""
    print(f"\n💡 批判性洞察")
    print("="*40)
    
    print("200期预测[2,3]的深层含义:")
    
    print(f"\n🔍 这不是bug，是feature:")
    print("   - 系统'学会'了在历史数据中2,3确实高频")
    print("   - 从算法角度，这是'正确'的行为")
    print("   - 问题在于历史数据可能不代表未来")
    
    print(f"\n🎯 预测vs随机的哲学问题:")
    print("   - 如果彩票真的是随机的，任何预测都是徒劳")
    print("   - 如果不是随机的，我们的方法可能不对")
    print("   - 我们可能在解决一个不存在的问题")
    
    print(f"\n⚖️ 评估标准的困境:")
    print("   - 用什么标准评判一个预测随机事件的系统？")
    print("   - 29%的命中率是好还是坏？")
    print("   - 随机选择的期望命中率是多少？")
    
    print(f"\n🤔 存在的价值:")
    print("   - 即使预测不准，系统可能仍有价值")
    print("   - 提供结构化的思考框架")
    print("   - 帮助用户理解概率和不确定性")
    print("   - 心理安慰和决策支持")

def main():
    """主函数"""
    print("📊 每月命中率与预测固化深度分析")
    print("="*80)
    
    # 1. 每月命中率分析
    monthly_stats = analyze_monthly_hit_rates_fixed()
    
    # 2. 预测演化分析
    analyze_prediction_evolution()
    
    # 3. 系统学习失败分析
    analyze_system_learning_failure()
    
    # 4. 批判性洞察
    critical_insights()
    
    print(f"\n🎯 最终结论")
    print("="*60)
    print("200期后还预测[2,3]揭示了深层问题:")
    print("   1. 📊 数字2占14.6%，数字3占8.3%的预测频率")
    print("   2. 🔒 算法完全依赖历史模式，无学习能力")
    print("   3. 🎲 系统没有理解随机性的本质")
    print("   4. ⚖️ 评分系统与实际表现严重脱节")
    print("   5. 🤔 需要重新思考预测随机事件的意义")
    
    print(f"\n这是一个关于人工智能局限性的经典案例：")
    print(f"   再复杂的算法也无法预测真正的随机事件！")

if __name__ == "__main__":
    main()
