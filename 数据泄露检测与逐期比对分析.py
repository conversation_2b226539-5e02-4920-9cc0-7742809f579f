#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据泄露检测与逐期比对分析系统
检测测试集是否存在数据泄露和过拟合问题
生成详细的逐期预测与真实数据比对CSV文件
"""

import pandas as pd
import numpy as np
from datetime import datetime
from collections import defaultdict, Counter
import warnings
warnings.filterwarnings('ignore')

class DataLeakageDetectionSystem:
    """数据泄露检测系统"""
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.optimization_results_file = "优化预测验证结果.csv"
        self.output_file = "逐期预测比对详细分析.csv"
        self.leakage_report_file = "数据泄露检测报告.csv"
        
        # 数据存储
        self.full_data = None
        self.optimization_results = None
        self.detailed_comparison = []
        self.leakage_analysis = []
        
    def load_data(self):
        """加载数据"""
        try:
            # 加载原始数据
            self.full_data = pd.read_csv(self.data_file, encoding='utf-8')
            self.full_data = self.full_data.dropna()
            print(f"✅ 加载原始数据: {len(self.full_data)} 条记录")
            
            # 加载优化结果
            self.optimization_results = pd.read_csv(self.optimization_results_file, encoding='utf-8')
            print(f"✅ 加载优化结果: {len(self.optimization_results)} 条记录")
            
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def detect_data_leakage(self):
        """检测数据泄露"""
        print("\n🔍 检测数据泄露...")
        print("=" * 50)
        
        leakage_issues = []
        
        for idx, result_row in self.optimization_results.iterrows():
            try:
                # 解析期号
                period_str = result_row['预测目标期号']
                period = int(period_str.split('年')[1].split('期')[0])
                
                # 获取训练数据量
                training_size = result_row['训练数据量']
                
                # 检查1: 训练数据是否包含目标期及之后的数据
                target_period_data = self.full_data[
                    (self.full_data['年份'] == 2025) & 
                    (self.full_data['期号'] == period)
                ]
                
                if len(target_period_data) == 0:
                    leakage_issues.append({
                        '期号': period,
                        '问题类型': '目标期数据缺失',
                        '严重程度': '高',
                        '描述': f'2025年{period}期数据在原始数据中不存在'
                    })
                    continue
                
                # 检查2: 训练数据量是否合理递增
                expected_base_size = 515  # 2024-2025前150期的基础数据量
                expected_increment = period - 151  # 从151期开始的增量
                expected_size = expected_base_size + 150 + expected_increment  # 基础 + 验证集 + 测试集增量
                
                if abs(training_size - expected_size) > 2:  # 允许2期的误差
                    leakage_issues.append({
                        '期号': period,
                        '问题类型': '训练数据量异常',
                        '严重程度': '中',
                        '描述': f'期望{expected_size}期，实际{training_size}期，差异{training_size - expected_size}'
                    })
                
                # 检查3: 预测是否使用了未来信息
                # 获取该期之后的所有数据
                future_data = self.full_data[
                    (self.full_data['年份'] == 2025) & 
                    (self.full_data['期号'] > period)
                ]
                
                # 检查预测数字是否与未来数据高度相关
                predicted_numbers = [result_row['预测数字1'], result_row['预测数字2']]
                
                future_correlation_score = 0
                for _, future_row in future_data.iterrows():
                    future_numbers = [future_row[f'数字{i}'] for i in range(1, 7)]
                    overlap = len(set(predicted_numbers) & set(future_numbers))
                    future_correlation_score += overlap
                
                if len(future_data) > 0:
                    avg_future_correlation = future_correlation_score / len(future_data)
                    if avg_future_correlation > 0.5:  # 如果与未来数据相关性过高
                        leakage_issues.append({
                            '期号': period,
                            '问题类型': '未来信息泄露疑似',
                            '严重程度': '高',
                            '描述': f'预测与未来数据平均相关性{avg_future_correlation:.2f}，疑似使用未来信息'
                        })
                
                # 检查4: 预测模式是否过于固定（过拟合迹象）
                if idx >= 5:  # 至少有5期数据才检查
                    recent_predictions = self.optimization_results.iloc[max(0, idx-4):idx+1]
                    pred1_values = recent_predictions['预测数字1'].tolist()
                    pred2_values = recent_predictions['预测数字2'].tolist()
                    
                    # 检查预测是否过于重复
                    pred1_unique = len(set(pred1_values))
                    pred2_unique = len(set(pred2_values))
                    
                    if pred1_unique <= 2 and pred2_unique <= 2:  # 最近5期中预测数字种类过少
                        leakage_issues.append({
                            '期号': period,
                            '问题类型': '过拟合疑似',
                            '严重程度': '中',
                            '描述': f'最近5期预测数字1只有{pred1_unique}种，数字2只有{pred2_unique}种，可能过拟合'
                        })
                
            except Exception as e:
                leakage_issues.append({
                    '期号': period if 'period' in locals() else '未知',
                    '问题类型': '分析错误',
                    '严重程度': '低',
                    '描述': f'分析过程出错: {e}'
                })
        
        self.leakage_analysis = leakage_issues
        
        # 统计问题
        high_severity = len([issue for issue in leakage_issues if issue['严重程度'] == '高'])
        medium_severity = len([issue for issue in leakage_issues if issue['严重程度'] == '中'])
        low_severity = len([issue for issue in leakage_issues if issue['严重程度'] == '低'])
        
        print(f"🔍 数据泄露检测完成:")
        print(f"   高严重程度问题: {high_severity} 个")
        print(f"   中等严重程度问题: {medium_severity} 个")
        print(f"   低严重程度问题: {low_severity} 个")
        print(f"   总问题数: {len(leakage_issues)} 个")
        
        if high_severity > 0:
            print("⚠️ 发现高严重程度问题，可能存在数据泄露")
        elif medium_severity > 5:
            print("⚠️ 发现较多中等问题，可能存在过拟合")
        else:
            print("✅ 未发现明显的数据泄露问题")
        
        return len(leakage_issues)
    
    def generate_detailed_comparison(self):
        """生成详细的逐期比对"""
        print("\n📊 生成逐期详细比对...")
        print("=" * 50)
        
        for idx, result_row in self.optimization_results.iterrows():
            try:
                # 解析期号
                period_str = result_row['预测目标期号']
                period = int(period_str.split('年')[1].split('期')[0])
                
                # 获取真实数据
                actual_data = self.full_data[
                    (self.full_data['年份'] == 2025) & 
                    (self.full_data['期号'] == period)
                ]
                
                if len(actual_data) == 0:
                    continue
                
                actual_row = actual_data.iloc[0]
                actual_numbers = [int(actual_row[f'数字{i}']) for i in range(1, 7)]
                
                # 获取预测数据
                predicted_numbers = [result_row['预测数字1'], result_row['预测数字2']]
                
                # 详细命中分析
                pred_set = set(predicted_numbers)
                actual_set = set(actual_numbers)
                hit_numbers = pred_set & actual_set
                miss_numbers = pred_set - actual_set
                
                # 计算各种指标
                hit_count = len(hit_numbers)
                precision = hit_count / len(pred_set) if len(pred_set) > 0 else 0
                recall = hit_count / len(actual_set) if len(actual_set) > 0 else 0
                f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
                
                # 分析预测质量
                if hit_count == 2:
                    prediction_quality = "优秀"
                elif hit_count == 1:
                    prediction_quality = "良好"
                else:
                    prediction_quality = "需改进"
                
                # 分析预测数字的统计特征
                pred_sum = sum(predicted_numbers)
                pred_range = max(predicted_numbers) - min(predicted_numbers)
                actual_sum = sum(actual_numbers)
                actual_range = max(actual_numbers) - min(actual_numbers)
                
                sum_diff = abs(pred_sum - actual_sum)
                range_diff = abs(pred_range - actual_range)
                
                # 检查预测数字在实际数字中的位置
                position_analysis = []
                for pred_num in predicted_numbers:
                    if pred_num in actual_numbers:
                        position = actual_numbers.index(pred_num) + 1
                        position_analysis.append(f"数字{pred_num}在第{position}位")
                    else:
                        # 找最接近的数字
                        closest_num = min(actual_numbers, key=lambda x: abs(x - pred_num))
                        diff = abs(closest_num - pred_num)
                        position_analysis.append(f"数字{pred_num}未命中,最接近{closest_num}(差{diff})")
                
                # 历史表现分析
                if idx >= 5:
                    recent_results = self.optimization_results.iloc[max(0, idx-4):idx]
                    recent_hit_rate = len(recent_results[recent_results['是否命中'] == '是']) / len(recent_results)
                    trend = "上升" if recent_hit_rate > 0.4 else "下降" if recent_hit_rate < 0.2 else "稳定"
                else:
                    recent_hit_rate = 0
                    trend = "数据不足"
                
                # 构建详细记录
                detailed_record = {
                    '序号': idx + 1,
                    '预测期号': f"2025年{period}期",
                    '预测日期': datetime.now().strftime('%Y-%m-%d'),
                    '训练数据量': result_row['训练数据量'],
                    '预测数字1': predicted_numbers[0],
                    '预测数字2': predicted_numbers[1],
                    '预测数字组合': f"[{predicted_numbers[0]}, {predicted_numbers[1]}]",
                    '预测置信度': result_row['预测置信度'],
                    '实际数字1': actual_numbers[0],
                    '实际数字2': actual_numbers[1],
                    '实际数字3': actual_numbers[2],
                    '实际数字4': actual_numbers[3],
                    '实际数字5': actual_numbers[4],
                    '实际数字6': actual_numbers[5],
                    '实际数字组合': str(actual_numbers),
                    '命中数量': hit_count,
                    '是否命中': '是' if hit_count > 0 else '否',
                    '命中数字': ','.join(map(str, sorted(hit_numbers))) if hit_numbers else '无',
                    '未命中数字': ','.join(map(str, sorted(miss_numbers))) if miss_numbers else '无',
                    '精确度': round(precision, 4),
                    '召回率': round(recall, 4),
                    'F1分数': round(f1_score, 4),
                    '预测质量': prediction_quality,
                    '预测数字和': pred_sum,
                    '实际数字和': actual_sum,
                    '数字和差异': sum_diff,
                    '预测数字范围': pred_range,
                    '实际数字范围': actual_range,
                    '数字范围差异': range_diff,
                    '位置分析': '; '.join(position_analysis),
                    '最近5期命中率': round(recent_hit_rate, 4),
                    '趋势分析': trend,
                    '训练集配置': result_row['训练集配置'],
                    '权重配置': result_row['权重配置'],
                    '频率权重': result_row['频率权重'],
                    '马尔可夫权重': result_row['马尔可夫权重'],
                    '统计权重': result_row['统计权重'],
                    '预测方法': result_row['预测方法'],
                    '数据泄露风险': '低' if period <= 203 else '高',
                    '过拟合风险': '高' if prediction_quality == "需改进" and recent_hit_rate < 0.2 else '中' if recent_hit_rate < 0.3 else '低',
                    '备注': f"基于{result_row['训练集配置']}训练，使用{result_row['权重配置']}权重"
                }
                
                self.detailed_comparison.append(detailed_record)
                
            except Exception as e:
                print(f"⚠️ 处理第{period}期失败: {e}")
                continue
        
        print(f"✅ 生成详细比对记录: {len(self.detailed_comparison)} 条")
    
    def calculate_comprehensive_metrics(self):
        """计算综合指标"""
        print("\n📈 计算综合指标...")
        
        if not self.detailed_comparison:
            return
        
        df = pd.DataFrame(self.detailed_comparison)
        
        # 基本统计
        total_predictions = len(df)
        total_hits = len(df[df['是否命中'] == '是'])
        overall_hit_rate = total_hits / total_predictions if total_predictions > 0 else 0
        
        # 质量分布
        quality_dist = df['预测质量'].value_counts()
        
        # 时间趋势分析
        df['期号'] = df['预测期号'].str.extract(r'(\d+)期').astype(int)
        
        # 前后半段分析
        mid_point = df['期号'].median()
        first_half = df[df['期号'] <= mid_point]
        second_half = df[df['期号'] > mid_point]
        
        first_half_rate = len(first_half[first_half['是否命中'] == '是']) / len(first_half) if len(first_half) > 0 else 0
        second_half_rate = len(second_half[second_half['是否命中'] == '是']) / len(second_half) if len(second_half) > 0 else 0
        
        # 预测多样性分析
        unique_pred1 = df['预测数字1'].nunique()
        unique_pred2 = df['预测数字2'].nunique()
        unique_combinations = df['预测数字组合'].nunique()
        
        # 置信度分析
        avg_confidence = df['预测置信度'].mean()
        confidence_std = df['预测置信度'].std()
        
        # 数字特征分析
        avg_pred_sum = df['预测数字和'].mean()
        avg_actual_sum = df['实际数字和'].mean()
        sum_correlation = df['预测数字和'].corr(df['实际数字和'])
        
        print(f"📊 综合指标分析:")
        print(f"   总预测次数: {total_predictions}")
        print(f"   总命中次数: {total_hits}")
        print(f"   整体命中率: {overall_hit_rate:.1%}")
        print(f"   前半段命中率: {first_half_rate:.1%}")
        print(f"   后半段命中率: {second_half_rate:.1%}")
        print(f"   预测多样性: 数字1有{unique_pred1}种, 数字2有{unique_pred2}种, 组合{unique_combinations}种")
        print(f"   平均置信度: {avg_confidence:.4f} ± {confidence_std:.4f}")
        print(f"   数字和相关性: {sum_correlation:.3f}")
        
        # 过拟合风险评估
        diversity_score = (unique_pred1 + unique_pred2) / (2 * total_predictions) * 100
        if diversity_score < 10:
            overfitting_risk = "高"
        elif diversity_score < 30:
            overfitting_risk = "中"
        else:
            overfitting_risk = "低"
        
        print(f"   多样性评分: {diversity_score:.1f}%")
        print(f"   过拟合风险: {overfitting_risk}")
        
        return {
            'total_predictions': total_predictions,
            'total_hits': total_hits,
            'overall_hit_rate': overall_hit_rate,
            'diversity_score': diversity_score,
            'overfitting_risk': overfitting_risk
        }
    
    def save_results(self):
        """保存结果"""
        try:
            # 保存详细比对结果
            if self.detailed_comparison:
                comparison_df = pd.DataFrame(self.detailed_comparison)
                comparison_df.to_csv(self.output_file, index=False, encoding='utf-8')
                print(f"✅ 详细比对结果已保存到: {self.output_file}")
            
            # 保存数据泄露检测结果
            if self.leakage_analysis:
                leakage_df = pd.DataFrame(self.leakage_analysis)
                leakage_df.to_csv(self.leakage_report_file, index=False, encoding='utf-8')
                print(f"✅ 数据泄露检测报告已保存到: {self.leakage_report_file}")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
            return False
    
    def run_comprehensive_analysis(self):
        """运行综合分析"""
        print("🔍 开始数据泄露检测与逐期比对分析...")
        print("=" * 70)
        
        # 1. 加载数据
        if not self.load_data():
            return False
        
        # 2. 检测数据泄露
        leakage_count = self.detect_data_leakage()
        
        # 3. 生成详细比对
        self.generate_detailed_comparison()
        
        # 4. 计算综合指标
        metrics = self.calculate_comprehensive_metrics()
        
        # 5. 保存结果
        self.save_results()
        
        # 6. 生成最终评估
        print(f"\n🏆 最终评估:")
        print("=" * 50)
        
        if leakage_count == 0:
            data_integrity = "优秀"
        elif leakage_count <= 3:
            data_integrity = "良好"
        elif leakage_count <= 10:
            data_integrity = "一般"
        else:
            data_integrity = "需改进"
        
        print(f"   数据完整性: {data_integrity}")
        print(f"   数据泄露问题: {leakage_count} 个")
        
        if metrics:
            print(f"   预测命中率: {metrics['overall_hit_rate']:.1%}")
            print(f"   预测多样性: {metrics['diversity_score']:.1f}%")
            print(f"   过拟合风险: {metrics['overfitting_risk']}")
        
        # 总体建议
        if leakage_count > 5 or (metrics and metrics['overfitting_risk'] == '高'):
            print(f"   总体建议: ⚠️ 需要重新审查模型和数据")
        elif leakage_count > 0 or (metrics and metrics['overfitting_risk'] == '中'):
            print(f"   总体建议: 🔧 建议进行优化改进")
        else:
            print(f"   总体建议: ✅ 模型表现良好，可以使用")
        
        print(f"\n📁 生成文件:")
        print(f"   {self.output_file} - 详细逐期比对分析")
        print(f"   {self.leakage_report_file} - 数据泄露检测报告")
        
        return True

def main():
    """主函数"""
    print("🔍 数据泄露检测与逐期比对分析系统")
    print("检测测试集数据泄露和过拟合问题")
    print("=" * 80)
    
    system = DataLeakageDetectionSystem()
    
    # 确认执行
    print("⚠️ 注意：此操作将详细分析每期预测结果，检测数据泄露和过拟合问题")
    confirm = input("确认开始分析? (y/n): ").strip().lower()
    
    if confirm != 'y':
        print("❌ 操作已取消")
        return
    
    # 执行分析
    success = system.run_comprehensive_analysis()
    
    if success:
        print(f"\n🎉 数据泄露检测与逐期比对分析完成！")
        print(f"请查看生成的CSV文件了解详细结果")
    else:
        print(f"\n❌ 分析失败")

if __name__ == "__main__":
    main()
