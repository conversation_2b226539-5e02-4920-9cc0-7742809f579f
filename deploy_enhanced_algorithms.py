#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强预测算法部署脚本
Enhanced Prediction Algorithms Deployment Script

用于将增强预测算法集成到现有系统中
支持渐进式部署、A/B测试和性能监控

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0
"""

import os
import sys
import shutil
import json
import pandas as pd
import numpy as np
from datetime import datetime
import logging
import argparse
from algorithm_integration_manager import AlgorithmIntegrationManager
from confidence_system_wrapper import calculate_confidence

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('algorithm_deployment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AlgorithmDeployment:
    """增强预测算法部署管理器"""
    
    def __init__(self, config=None):
        """初始化部署管理器"""
        self.config = config or self._get_default_config()
        self.deployment_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.backup_dir = f"algorithm_backup_{self.deployment_id}"
        self.algorithm_manager = None
        
        logger.info(f"🚀 增强预测算法部署管理器初始化")
        logger.info(f"  部署ID: {self.deployment_id}")
        logger.info(f"  备份目录: {self.backup_dir}")
    
    def _get_default_config(self):
        """获取默认部署配置"""
        return {
            'deployment_mode': 'gradual',  # immediate, gradual, ab_test
            'ab_test_ratio': 0.3,
            'backup_enabled': True,
            'validation_enabled': True,
            'rollback_enabled': True,
            'training_data_file': 'prediction_data.csv',
            'algorithm_config': {
                'ab_test_enabled': True,
                'ab_test_ratio': 0.3,
                'initial_weights': {
                    'enhanced_markov': 0.4,
                    'frequency_analyzer': 0.3,
                    'pattern_matcher': 0.2,
                    'trend_analyzer': 0.1
                }
            },
            'integration_points': [
                'predict_numbers',
                'get_prediction',
                'calculate_prediction'
            ]
        }
    
    def deploy(self):
        """执行完整部署流程"""
        try:
            logger.info("🎯 开始部署增强预测算法")
            
            # 1. 预部署检查
            if not self._pre_deployment_check():
                logger.error("❌ 预部署检查失败，终止部署")
                return False
            
            # 2. 备份现有系统
            if self.config['backup_enabled']:
                if not self._backup_existing_system():
                    logger.error("❌ 系统备份失败，终止部署")
                    return False
            
            # 3. 初始化算法管理器
            if not self._initialize_algorithm_manager():
                logger.error("❌ 算法管理器初始化失败，终止部署")
                return False
            
            # 4. 部署新算法
            if not self._deploy_enhanced_algorithms():
                logger.error("❌ 新算法部署失败，开始回滚")
                self._rollback()
                return False
            
            # 5. 验证部署
            if self.config['validation_enabled']:
                if not self._validate_deployment():
                    logger.error("❌ 部署验证失败，开始回滚")
                    self._rollback()
                    return False
            
            # 6. 生成部署报告
            self._generate_deployment_report()
            
            logger.info("✅ 增强预测算法部署成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 部署过程中发生异常: {e}")
            if self.config['rollback_enabled']:
                self._rollback()
            return False
    
    def _pre_deployment_check(self):
        """预部署检查"""
        logger.info("🔍 执行预部署检查")
        
        checks = []
        
        # 检查依赖文件
        required_files = [
            'enhanced_prediction_algorithms.py',
            'algorithm_integration_manager.py',
            'confidence_system_wrapper.py'
        ]
        
        for file in required_files:
            if os.path.exists(file):
                checks.append(f"✅ 依赖文件 {file} 存在")
            else:
                checks.append(f"❌ 依赖文件 {file} 不存在")
                return False
        
        # 检查训练数据
        if os.path.exists(self.config['training_data_file']):
            checks.append(f"✅ 训练数据文件 {self.config['training_data_file']} 存在")
        else:
            checks.append(f"❌ 训练数据文件 {self.config['training_data_file']} 不存在")
            return False
        
        # 检查Python环境
        try:
            import numpy as np
            import pandas as pd
            checks.append("✅ Python依赖包检查通过")
        except ImportError as e:
            checks.append(f"❌ Python依赖包缺失: {e}")
            return False
        
        # 检查磁盘空间
        import shutil
        free_space = shutil.disk_usage('.').free / (1024**2)  # MB
        if free_space > 100:  # 至少100MB
            checks.append(f"✅ 磁盘空间充足: {free_space:.1f}MB")
        else:
            checks.append(f"❌ 磁盘空间不足: {free_space:.1f}MB")
            return False
        
        for check in checks:
            logger.info(f"  {check}")
        
        return True
    
    def _backup_existing_system(self):
        """备份现有系统"""
        logger.info("💾 备份现有系统")
        
        try:
            # 创建备份目录
            os.makedirs(self.backup_dir, exist_ok=True)
            
            # 备份算法相关文件
            files_to_backup = [
                'enhanced_prediction_algorithms.py',
                'algorithm_integration_manager.py',
                'confidence_system_wrapper.py',
                'algorithm_config.json'
            ]
            
            backed_up_files = []
            for file in files_to_backup:
                if os.path.exists(file):
                    backup_path = os.path.join(self.backup_dir, file)
                    shutil.copy2(file, backup_path)
                    backed_up_files.append(file)
                    logger.info(f"  ✅ 已备份: {file} -> {backup_path}")
            
            # 备份训练数据
            if os.path.exists(self.config['training_data_file']):
                shutil.copy2(
                    self.config['training_data_file'], 
                    os.path.join(self.backup_dir, self.config['training_data_file'])
                )
                backed_up_files.append(self.config['training_data_file'])
                logger.info(f"  ✅ 已备份: {self.config['training_data_file']}")
            
            # 创建备份清单
            backup_manifest = {
                'deployment_id': self.deployment_id,
                'backup_time': datetime.now().isoformat(),
                'backed_up_files': backed_up_files,
                'backup_directory': self.backup_dir
            }
            
            with open(os.path.join(self.backup_dir, 'backup_manifest.json'), 'w') as f:
                json.dump(backup_manifest, f, indent=2)
            
            logger.info(f"✅ 系统备份完成，备份文件数: {len(backed_up_files)}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 系统备份失败: {e}")
            return False
    
    def _initialize_algorithm_manager(self):
        """初始化算法管理器"""
        logger.info("🔧 初始化算法管理器")
        
        try:
            # 创建算法管理器
            self.algorithm_manager = AlgorithmIntegrationManager(
                self.config['algorithm_config']
            )
            
            # 加载训练数据
            training_data = pd.read_csv(self.config['training_data_file'])
            
            # 准备训练数据
            processed_data = self._prepare_training_data(training_data)
            
            # 训练算法
            training_results = self.algorithm_manager.train_all_algorithms(processed_data)
            
            # 检查训练结果
            success_count = sum(1 for result in training_results.values() 
                              if result['status'] == 'success')
            
            if success_count < len(training_results):
                logger.warning(f"⚠️ 部分算法训练失败: {success_count}/{len(training_results)}")
            
            logger.info(f"✅ 算法管理器初始化完成")
            logger.info(f"  成功训练算法: {success_count}/{len(training_results)}")
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"❌ 算法管理器初始化失败: {e}")
            return False
    
    def _prepare_training_data(self, data):
        """准备训练数据"""
        logger.info("🔄 准备训练数据")
        
        try:
            # 检查数据格式
            if '数字1' in data.columns:
                logger.info("  使用标准格式数据")
                return data
            
            # 尝试从prediction_data.csv格式转换
            if all(col in data.columns for col in ['当期期号', '实际数字1', '实际数字2']):
                logger.info("  从prediction_data.csv格式转换数据")
                
                # 提取实际数字列
                processed_data = []
                for _, row in data.iterrows():
                    if pd.notna(row['实际数字1']):
                        period_data = {
                            '期号': row['当期期号'],
                            '数字1': row['实际数字1'],
                            '数字2': row['实际数字2'],
                            '数字3': row['实际数字3'],
                            '数字4': row['实际数字4'],
                            '数字5': row['实际数字5'],
                            '数字6': row['实际数字6']
                        }
                        processed_data.append(period_data)
                
                return pd.DataFrame(processed_data)
            
            # 其他格式处理
            logger.warning("⚠️ 未识别的数据格式，尝试通用处理")
            
            # 尝试提取数字列
            number_columns = [col for col in data.columns if '数字' in col or 'number' in col.lower()]
            if number_columns:
                return data[number_columns]
            
            logger.error("❌ 无法处理的数据格式")
            return data
            
        except Exception as e:
            logger.error(f"❌ 数据准备失败: {e}")
            return data
    
    def _deploy_enhanced_algorithms(self):
        """部署增强算法"""
        logger.info("🚀 部署增强预测算法")

        try:
            # 保存算法配置
            algorithm_config = {
                'deployment_id': self.deployment_id,
                'deployment_time': datetime.now().isoformat(),
                'deployment_mode': self.config['deployment_mode'],
                'ab_test_ratio': self.config['ab_test_ratio'],
                'algorithm_config': self.config['algorithm_config']
            }

            with open('algorithm_config.json', 'w', encoding='utf-8') as f:
                json.dump(algorithm_config, f, indent=2, ensure_ascii=False)

            # 创建算法包装器
            self._create_algorithm_wrapper()

            logger.info("✅ 增强算法部署完成")
            return True

        except Exception as e:
            import traceback
            logger.error(f"❌ 增强算法部署失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def _create_algorithm_wrapper(self):
        """创建算法包装器"""
        # 准备部署信息
        deployment_id = self.deployment_id
        deployment_time = datetime.now().isoformat()

        wrapper_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强预测算法包装器
自动生成的集成代码
"""

import os
import json
from algorithm_integration_manager import AlgorithmIntegrationManager
import logging

logger = logging.getLogger(__name__)

# 全局算法管理器实例
_algorithm_manager = None

def get_algorithm_manager():
    """获取算法管理器实例（单例模式）"""
    global _algorithm_manager

    if _algorithm_manager is None:
        # 加载配置
        config_file = 'algorithm_config.json'
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                algorithm_config = config.get('algorithm_config', {{}})
            except Exception as e:
                logger.warning(f"加载配置失败，使用默认配置: {{e}}")
                algorithm_config = None
        else:
            algorithm_config = None

        # 创建管理器
        _algorithm_manager = AlgorithmIntegrationManager(algorithm_config)

        # 尝试加载训练数据
        try:
            import pandas as pd
            training_file = 'prediction_data.csv'
            if os.path.exists(training_file):
                data = pd.read_csv(training_file)

                # 准备训练数据
                processed_data = []
                for _, row in data.iterrows():
                    if 'actual_numbers' in row:
                        processed_data.append(row['actual_numbers'])
                    elif '实际数字1' in row and pd.notna(row['实际数字1']):
                        numbers = [
                            row['实际数字1'], row['实际数字2'], row['实际数字3'],
                            row['实际数字4'], row['实际数字5'], row['实际数字6']
                        ]
                        processed_data.append(numbers)

                if processed_data:
                    _algorithm_manager.train_all_algorithms(processed_data)
        except Exception as e:
            logger.warning(f"自动训练失败: {{e}}")

    return _algorithm_manager

# 向后兼容的函数别名
def predict_numbers(previous_numbers, context=None):
    """预测数字（向后兼容）"""
    manager = get_algorithm_manager()
    result = manager.predict_ensemble(previous_numbers, context)
    return result['predicted_numbers']

def get_prediction(previous_numbers, context=None):
    """获取预测（向后兼容）"""
    manager = get_algorithm_manager()
    result = manager.predict_ensemble(previous_numbers, context)
    return {{
        'predicted_numbers': result['predicted_numbers'],
        'probabilities': result['probabilities']
    }}

def calculate_prediction(previous_numbers, context=None):
    """计算预测（向后兼容）"""
    return get_prediction(previous_numbers, context)

# 新系统专用函数
def get_enhanced_prediction(previous_numbers, context=None):
    """获取增强预测"""
    manager = get_algorithm_manager()
    return manager.predict_ensemble(previous_numbers, context)

def update_prediction_result(prediction_result, actual_numbers):
    """更新预测结果"""
    manager = get_algorithm_manager()
    return manager.update_performance(prediction_result, actual_numbers)

def get_algorithm_performance():
    """获取算法性能"""
    manager = get_algorithm_manager()
    return manager.get_performance_report()

def export_performance_data(filename):
    """导出性能数据"""
    manager = get_algorithm_manager()
    return manager.export_performance_data(filename)

# 部署信息
DEPLOYMENT_INFO = {{
    'deployment_id': '{deployment_id}',
    'deployment_time': '{deployment_time}',
    'system_version': '1.0'
}}
'''
        
        with open('algorithm_wrapper.py', 'w', encoding='utf-8') as f:
            f.write(wrapper_code)
        
        logger.info("✅ 算法包装器创建完成")
    
    def _validate_deployment(self):
        """验证部署"""
        logger.info("🔍 验证部署结果")
        
        try:
            # 测试基本功能
            from algorithm_wrapper import (
                predict_numbers,
                get_prediction,
                get_enhanced_prediction
            )
            
            # 执行测试预测
            test_numbers = [1, 15, 23, 30, 35, 42]
            test_context = {
                'period_idx': 100,
                'data_source': '测试数据'
            }
            
            # 测试简单预测
            simple_result = predict_numbers(test_numbers, test_context)
            if isinstance(simple_result, list) and len(simple_result) == 2:
                logger.info("✅ 简单预测测试通过")
            else:
                logger.error("❌ 简单预测测试失败")
                return False
            
            # 测试详细预测
            detailed_result = get_prediction(test_numbers, test_context)
            if (isinstance(detailed_result, dict) and 
                'predicted_numbers' in detailed_result and
                'probabilities' in detailed_result):
                logger.info("✅ 详细预测测试通过")
            else:
                logger.error("❌ 详细预测测试失败")
                return False
            
            # 测试增强预测
            enhanced_result = get_enhanced_prediction(test_numbers, test_context)
            if (isinstance(enhanced_result, dict) and 
                'predicted_numbers' in enhanced_result and
                'algorithm_predictions' in enhanced_result):
                logger.info("✅ 增强预测测试通过")
            else:
                logger.error("❌ 增强预测测试失败")
                return False
            
            logger.info("✅ 部署验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 部署验证失败: {e}")
            return False
    
    def _rollback(self):
        """回滚到备份版本"""
        if not self.config['rollback_enabled']:
            logger.warning("⚠️ 回滚功能已禁用")
            return False
        
        logger.info("🔄 开始回滚到备份版本")
        
        try:
            if not os.path.exists(self.backup_dir):
                logger.error("❌ 备份目录不存在，无法回滚")
                return False
            
            # 读取备份清单
            manifest_path = os.path.join(self.backup_dir, 'backup_manifest.json')
            if os.path.exists(manifest_path):
                with open(manifest_path, 'r') as f:
                    manifest = json.load(f)
                
                # 恢复备份文件
                for file in manifest['backed_up_files']:
                    backup_path = os.path.join(self.backup_dir, file)
                    if os.path.exists(backup_path):
                        shutil.copy2(backup_path, file)
                        logger.info(f"  ✅ 已恢复: {file}")
            
            # 删除新系统文件
            new_files = [
                'algorithm_wrapper.py',
                'algorithm_config.json'
            ]
            
            for file in new_files:
                if os.path.exists(file):
                    os.remove(file)
                    logger.info(f"  🗑️ 已删除: {file}")
            
            logger.info("✅ 回滚完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 回滚失败: {e}")
            return False
    
    def _generate_deployment_report(self):
        """生成部署报告"""
        logger.info("📊 生成部署报告")
        
        try:
            # 获取算法状态
            from algorithm_wrapper import get_algorithm_performance
            performance = get_algorithm_performance()
            
            report = {
                'deployment_info': {
                    'deployment_id': self.deployment_id,
                    'deployment_time': datetime.now().isoformat(),
                    'deployment_mode': self.config['deployment_mode'],
                    'ab_test_ratio': self.config['ab_test_ratio']
                },
                'algorithm_performance': performance,
                'deployment_config': self.config,
                'status': 'success'
            }
            
            report_file = f'algorithm_deployment_report_{self.deployment_id}.json'
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 部署报告已生成: {report_file}")
            
        except Exception as e:
            logger.error(f"❌ 生成部署报告失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='增强预测算法部署工具')
    parser.add_argument('--mode', choices=['immediate', 'gradual', 'ab_test'], 
                      default='gradual', help='部署模式')
    parser.add_argument('--ab-ratio', type=float, default=0.3, 
                      help='A/B测试比例 (0-1)')
    parser.add_argument('--training-file', default='prediction_data.csv', 
                      help='训练数据文件')
    parser.add_argument('--no-backup', action='store_true', 
                      help='禁用备份')
    parser.add_argument('--no-validation', action='store_true', 
                      help='禁用验证')
    parser.add_argument('--force', action='store_true', 
                      help='强制部署，忽略错误')
    
    args = parser.parse_args()
    
    print("🚀 增强预测算法部署工具")
    print("=" * 50)
    
    # 创建配置
    config = {
        'deployment_mode': args.mode,
        'ab_test_ratio': args.ab_ratio,
        'backup_enabled': not args.no_backup,
        'validation_enabled': not args.no_validation,
        'rollback_enabled': not args.force,
        'training_data_file': args.training_file,
        'algorithm_config': {
            'ab_test_enabled': args.mode == 'ab_test',
            'ab_test_ratio': args.ab_ratio,
            'initial_weights': {
                'enhanced_markov': 0.4,
                'frequency_analyzer': 0.3,
                'pattern_matcher': 0.2,
                'trend_analyzer': 0.1
            }
        }
    }
    
    # 创建部署管理器
    deployment = AlgorithmDeployment(config)
    
    # 执行部署
    success = deployment.deploy()
    
    if success:
        print("\n✅ 部署成功完成！")
        print("\n📋 后续步骤:")
        print("1. 监控算法性能")
        print("2. 观察预测效果")
        print("3. 根据需要调整算法权重")
        print("4. 定期检查部署报告")
        
        # 显示使用示例
        print("\n💡 使用示例:")
        print("```python")
        print("from algorithm_wrapper import get_enhanced_prediction")
        print("result = get_enhanced_prediction([1, 15, 23, 30, 35, 42])")
        print("print(f'预测数字: {result[\"predicted_numbers\"]}')")
        print("```")
    else:
        print("\n❌ 部署失败！")
        print("请检查日志文件 algorithm_deployment.log 获取详细信息")

if __name__ == "__main__":
    main()
