#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测验证报告系统
对测试集进行预测并验证结果准确性，输出详细验证报告
包括命中期数、组数和整体命中率
"""

import pandas as pd
import numpy as np
from collections import Counter, defaultdict
import json
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from math import comb
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class PredictionValidationSystem:
    """预测验证报告系统"""
    
    def __init__(self, train_data_file='2024年训练数据.csv', test_data_file='2025年测试数据.csv'):
        """初始化预测验证系统"""
        self.train_data_file = train_data_file
        self.test_data_file = test_data_file
        self.train_data = None
        self.test_data = None
        self.predictions = {}
        self.validation_results = {}
        
    def load_data(self):
        """加载数据"""
        try:
            # 加载训练数据（2024年）
            self.train_data = pd.read_csv(self.train_data_file, encoding='utf-8')
            print(f"✅ 成功加载训练数据: {len(self.train_data)} 期")
            
            # 加载测试数据（2025年）
            self.test_data = pd.read_csv(self.test_data_file, encoding='utf-8')
            print(f"✅ 成功加载测试数据: {len(self.test_data)} 期")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def generate_predictions(self):
        """生成多种预测方法的预测结果"""
        print("\n🎯 1. 生成预测结果")
        print("=" * 50)
        
        # 1.1 基于频率的预测
        frequency_predictions = self._frequency_based_prediction()
        self.predictions['frequency_based'] = frequency_predictions
        
        # 1.2 基于马尔可夫链的预测
        markov_predictions = self._markov_based_prediction()
        self.predictions['markov_based'] = markov_predictions
        
        # 1.3 基于贝叶斯的预测
        bayesian_predictions = self._bayesian_based_prediction()
        self.predictions['bayesian_based'] = bayesian_predictions
        
        # 1.4 基于模式的预测
        pattern_predictions = self._pattern_based_prediction()
        self.predictions['pattern_based'] = pattern_predictions
        
        # 1.5 集成预测
        ensemble_predictions = self._ensemble_prediction()
        self.predictions['ensemble'] = ensemble_predictions
        
        print(f"✅ 生成了 {len(self.predictions)} 种预测方法的结果")
        return True
    
    def _frequency_based_prediction(self):
        """基于频率的预测"""
        print("🔢 生成基于频率的预测...")
        
        # 统计训练数据中每个数字的出现频率
        number_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        all_numbers = self.train_data[number_cols].values.flatten()
        number_freq = Counter(all_numbers)
        
        # 按频率排序
        sorted_numbers = sorted(number_freq.items(), key=lambda x: x[1], reverse=True)
        
        predictions = []
        for i in range(len(self.test_data)):
            # 选择频率最高的前2个数字作为预测
            pred_numbers = [sorted_numbers[0][0], sorted_numbers[1][0]]
            predictions.append(pred_numbers)
        
        return {
            'method_name': '基于频率预测',
            'predictions': predictions,
            'description': '选择训练数据中出现频率最高的2个数字'
        }
    
    def _markov_based_prediction(self):
        """基于马尔可夫链的预测"""
        print("🔗 生成基于马尔可夫链的预测...")
        
        # 构建转移矩阵
        transition_matrix = np.zeros((49, 49))
        
        number_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        for i in range(1, len(self.train_data)):
            prev_numbers = [self.train_data.iloc[i-1][col] for col in number_cols]
            curr_numbers = [self.train_data.iloc[i][col] for col in number_cols]
            
            for prev_num in prev_numbers:
                for curr_num in curr_numbers:
                    transition_matrix[prev_num-1, curr_num-1] += 1
        
        # 归一化
        row_sums = transition_matrix.sum(axis=1)
        transition_matrix = transition_matrix / row_sums[:, np.newaxis]
        transition_matrix = np.nan_to_num(transition_matrix)
        
        predictions = []
        for i in range(len(self.test_data)):
            if i == 0:
                # 第一期使用频率最高的数字
                pred_numbers = [5, 15]  # 基于之前的分析
            else:
                # 基于前一期的数字预测
                prev_numbers = [self.test_data.iloc[i-1][col] for col in number_cols]
                
                # 计算每个数字的转移概率
                prob_sum = np.zeros(49)
                for prev_num in prev_numbers:
                    prob_sum += transition_matrix[prev_num-1]
                
                # 选择概率最高的2个数字
                top_indices = np.argsort(prob_sum)[-2:]
                pred_numbers = [idx + 1 for idx in top_indices]
            
            predictions.append(pred_numbers)
        
        return {
            'method_name': '基于马尔可夫链预测',
            'predictions': predictions,
            'description': '基于数字间转移概率进行预测'
        }
    
    def _bayesian_based_prediction(self):
        """基于贝叶斯的预测"""
        print("🧮 生成基于贝叶斯的预测...")
        
        # 使用Beta-Binomial模型
        number_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        all_numbers = self.train_data[number_cols].values.flatten()
        number_counts = Counter(all_numbers)
        
        # 贝叶斯更新
        alpha_prior = 1
        beta_prior = 1
        total_draws = len(all_numbers)
        
        bayesian_probs = {}
        for number in range(1, 50):
            count = number_counts.get(number, 0)
            alpha_posterior = alpha_prior + count
            beta_posterior = beta_prior + total_draws - count
            posterior_mean = alpha_posterior / (alpha_posterior + beta_posterior)
            bayesian_probs[number] = posterior_mean
        
        # 按贝叶斯概率排序
        sorted_numbers = sorted(bayesian_probs.items(), key=lambda x: x[1], reverse=True)
        
        predictions = []
        for i in range(len(self.test_data)):
            # 选择贝叶斯概率最高的2个数字
            pred_numbers = [sorted_numbers[0][0], sorted_numbers[1][0]]
            predictions.append(pred_numbers)
        
        return {
            'method_name': '基于贝叶斯预测',
            'predictions': predictions,
            'description': '使用Beta-Binomial模型的贝叶斯更新'
        }
    
    def _pattern_based_prediction(self):
        """基于模式的预测"""
        print("🔍 生成基于模式的预测...")
        
        # 分析奇偶模式
        number_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        # 统计最常见的奇偶组合
        odd_numbers = [n for n in range(1, 50) if n % 2 == 1]
        even_numbers = [n for n in range(2, 50) if n % 2 == 0]
        
        # 基于训练数据选择最常见的奇数和偶数
        odd_freq = Counter()
        even_freq = Counter()
        
        for _, row in self.train_data.iterrows():
            numbers = [row[col] for col in number_cols]
            for num in numbers:
                if num % 2 == 1:
                    odd_freq[num] += 1
                else:
                    even_freq[num] += 1
        
        most_common_odd = odd_freq.most_common(1)[0][0] if odd_freq else 5
        most_common_even = even_freq.most_common(1)[0][0] if even_freq else 2
        
        predictions = []
        for i in range(len(self.test_data)):
            # 预测一个奇数和一个偶数
            pred_numbers = [most_common_odd, most_common_even]
            predictions.append(pred_numbers)
        
        return {
            'method_name': '基于模式预测',
            'predictions': predictions,
            'description': '基于奇偶模式选择最常见的奇数和偶数'
        }
    
    def _ensemble_prediction(self):
        """集成预测"""
        print("🎯 生成集成预测...")
        
        # 简单的投票集成
        predictions = []
        
        for i in range(len(self.test_data)):
            # 收集所有方法的预测
            all_pred_numbers = []
            
            if 'frequency_based' in self.predictions:
                all_pred_numbers.extend(self.predictions['frequency_based']['predictions'][i])
            if 'markov_based' in self.predictions:
                all_pred_numbers.extend(self.predictions['markov_based']['predictions'][i])
            if 'bayesian_based' in self.predictions:
                all_pred_numbers.extend(self.predictions['bayesian_based']['predictions'][i])
            if 'pattern_based' in self.predictions:
                all_pred_numbers.extend(self.predictions['pattern_based']['predictions'][i])
            
            # 投票选择最常见的2个数字
            if all_pred_numbers:
                vote_counts = Counter(all_pred_numbers)
                most_common = vote_counts.most_common(2)
                pred_numbers = [num for num, _ in most_common]
                
                # 如果不足2个，补充频率最高的数字
                if len(pred_numbers) < 2:
                    pred_numbers.extend([5, 15])  # 基于之前分析的高频数字
                    pred_numbers = pred_numbers[:2]
            else:
                pred_numbers = [5, 15]  # 默认预测
            
            predictions.append(pred_numbers)
        
        return {
            'method_name': '集成预测',
            'predictions': predictions,
            'description': '基于多种方法的投票集成'
        }
    
    def validate_predictions(self):
        """验证预测结果"""
        print("\n📊 2. 验证预测结果")
        print("=" * 50)
        
        number_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        for method_key, method_data in self.predictions.items():
            print(f"\n🔍 验证 {method_data['method_name']}:")
            
            predictions = method_data['predictions']
            
            # 统计命中情况
            hit_details = []
            total_periods = len(self.test_data)
            hit_periods = 0
            total_hit_count = 0
            
            for i, pred_numbers in enumerate(predictions):
                actual_numbers = [self.test_data.iloc[i][col] for col in number_cols]
                
                # 计算命中数
                hits = len(set(pred_numbers) & set(actual_numbers))
                total_hit_count += hits
                
                period_info = {
                    'period': i + 1,
                    'predicted': pred_numbers,
                    'actual': actual_numbers,
                    'hits': hits,
                    'hit_numbers': list(set(pred_numbers) & set(actual_numbers))
                }
                
                hit_details.append(period_info)
                
                if hits > 0:
                    hit_periods += 1
            
            # 计算统计指标
            hit_rate = hit_periods / total_periods if total_periods > 0 else 0
            avg_hits_per_period = total_hit_count / total_periods if total_periods > 0 else 0
            
            # 2数全命中的期数
            two_hit_periods = sum(1 for detail in hit_details if detail['hits'] == 2)
            two_hit_rate = two_hit_periods / total_periods if total_periods > 0 else 0
            
            validation_result = {
                'method_name': method_data['method_name'],
                'total_periods': total_periods,
                'hit_periods': hit_periods,
                'hit_rate': hit_rate,
                'total_hit_count': total_hit_count,
                'avg_hits_per_period': avg_hits_per_period,
                'two_hit_periods': two_hit_periods,
                'two_hit_rate': two_hit_rate,
                'hit_details': hit_details
            }
            
            self.validation_results[method_key] = validation_result
            
            print(f"   总期数: {total_periods}")
            print(f"   命中期数: {hit_periods} (命中率: {hit_rate:.3f})")
            print(f"   总命中数: {total_hit_count}")
            print(f"   平均每期命中数: {avg_hits_per_period:.3f}")
            print(f"   2数全命中期数: {two_hit_periods} (2数全命中率: {two_hit_rate:.3f})")
        
        return True

    def detailed_hit_analysis(self):
        """详细命中分析"""
        print("\n🔍 3. 详细命中分析")
        print("=" * 50)

        detailed_analysis = {}

        for method_key, validation_result in self.validation_results.items():
            print(f"\n📊 {validation_result['method_name']} 详细分析:")

            hit_details = validation_result['hit_details']

            # 3.1 按命中数分组统计
            hit_count_distribution = Counter([detail['hits'] for detail in hit_details])

            print(f"   命中数分布:")
            for hits in range(3):
                count = hit_count_distribution.get(hits, 0)
                rate = count / len(hit_details) if hit_details else 0
                print(f"     {hits}个命中: {count}期 ({rate:.3f})")

            # 3.2 命中期数详情
            hit_periods_list = [detail['period'] for detail in hit_details if detail['hits'] > 0]
            two_hit_periods_list = [detail['period'] for detail in hit_details if detail['hits'] == 2]

            print(f"   命中期数列表 (前10期): {hit_periods_list[:10]}")
            if two_hit_periods_list:
                print(f"   2数全命中期数: {two_hit_periods_list}")
            else:
                print(f"   2数全命中期数: 无")

            # 3.3 命中数字统计
            all_hit_numbers = []
            for detail in hit_details:
                all_hit_numbers.extend(detail['hit_numbers'])

            hit_number_freq = Counter(all_hit_numbers)
            if hit_number_freq:
                most_hit_numbers = hit_number_freq.most_common(5)
                print(f"   最常命中的数字: {most_hit_numbers}")

            # 3.4 时间趋势分析
            period_groups = {
                'Q1': [d for d in hit_details if 1 <= d['period'] <= 45],
                'Q2': [d for d in hit_details if 46 <= d['period'] <= 90],
                'Q3': [d for d in hit_details if 91 <= d['period'] <= 135],
                'Q4': [d for d in hit_details if d['period'] > 135]
            }

            print(f"   季度命中率:")
            for quarter, details in period_groups.items():
                if details:
                    quarter_hits = sum(1 for d in details if d['hits'] > 0)
                    quarter_rate = quarter_hits / len(details)
                    print(f"     {quarter}: {quarter_hits}/{len(details)} ({quarter_rate:.3f})")

            detailed_analysis[method_key] = {
                'hit_count_distribution': dict(hit_count_distribution),
                'hit_periods_list': hit_periods_list,
                'two_hit_periods_list': two_hit_periods_list,
                'hit_number_frequency': dict(hit_number_freq),
                'quarterly_analysis': {q: {'total': len(details),
                                         'hits': sum(1 for d in details if d['hits'] > 0),
                                         'hit_rate': sum(1 for d in details if d['hits'] > 0) / len(details) if details else 0}
                                     for q, details in period_groups.items() if details}
            }

        self.validation_results['detailed_analysis'] = detailed_analysis
        return detailed_analysis

    def generate_comprehensive_report(self):
        """生成综合验证报告"""
        print("\n📋 4. 生成综合验证报告")
        print("=" * 50)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 生成JSON报告
        json_report = {
            'analysis_timestamp': timestamp,
            'system_name': '预测验证报告系统',
            'data_summary': {
                'train_periods': len(self.train_data) if self.train_data is not None else 0,
                'test_periods': len(self.test_data) if self.test_data is not None else 0,
                'prediction_methods': len(self.predictions)
            },
            'predictions': self.predictions,
            'validation_results': self.validation_results
        }

        # 处理JSON序列化问题
        def convert_keys(obj):
            if isinstance(obj, dict):
                return {str(k): convert_keys(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_keys(item) for item in obj]
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            else:
                return obj

        json_report_converted = convert_keys(json_report)

        json_filename = f'预测验证报告结果_{timestamp}.json'
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(json_report_converted, f, ensure_ascii=False, indent=2, default=str)

        # 生成Markdown报告
        md_report = self._generate_markdown_report(timestamp)
        md_filename = f'预测验证报告_{timestamp}.md'
        with open(md_filename, 'w', encoding='utf-8') as f:
            f.write(md_report)

        print(f"✅ JSON报告已保存: {json_filename}")
        print(f"✅ Markdown报告已保存: {md_filename}")

        return json_filename, md_filename

    def _generate_markdown_report(self, timestamp):
        """生成Markdown格式报告"""
        report = f"""# 预测验证分析报告

## 📊 系统概述

**分析时间**: {timestamp}
**系统名称**: 预测验证报告系统
**训练数据**: {len(self.train_data) if self.train_data is not None else 0} 期 (2024年)
**测试数据**: {len(self.test_data) if self.test_data is not None else 0} 期 (2025年)
**预测方法数**: {len(self.predictions)}

## 🎯 1. 预测方法概览

"""

        for method_key, method_data in self.predictions.items():
            report += f"### {method_data['method_name']}\n"
            report += f"- **描述**: {method_data['description']}\n"
            report += f"- **预测数量**: {len(method_data['predictions'])} 期\n\n"

        # 添加验证结果
        report += "## 📊 2. 验证结果汇总\n\n"
        report += "| 预测方法 | 总期数 | 命中期数 | 命中率 | 2数全命中期数 | 2数全命中率 | 平均每期命中数 |\n"
        report += "|----------|--------|----------|--------|---------------|-------------|----------------|\n"

        for method_key, result in self.validation_results.items():
            if method_key not in ['detailed_analysis', 'significance_tests']:
                report += f"| {result['method_name']} | {result['total_periods']} | "
                report += f"{result['hit_periods']} | {result['hit_rate']:.3f} | "
                report += f"{result['two_hit_periods']} | {result['two_hit_rate']:.3f} | "
                report += f"{result['avg_hits_per_period']:.3f} |\n"

        # 添加详细分析
        if 'detailed_analysis' in self.validation_results:
            report += "\n## 🔍 3. 详细命中分析\n\n"

            for method_key, analysis in self.validation_results['detailed_analysis'].items():
                if method_key in self.validation_results and method_key not in ['detailed_analysis', 'significance_tests']:
                    method_name = self.validation_results[method_key]['method_name']
                    report += f"### {method_name}\n\n"

                    # 命中数分布
                    report += "#### 命中数分布\n\n"
                    report += "| 命中数 | 期数 | 比例 |\n"
                    report += "|--------|------|------|\n"

                    hit_dist = analysis['hit_count_distribution']
                    total_periods = sum(hit_dist.values())

                    for hits in range(3):
                        count = hit_dist.get(hits, 0)
                        rate = count / total_periods if total_periods > 0 else 0
                        report += f"| {hits}个命中 | {count} | {rate:.3f} |\n"

                    # 2数全命中期数
                    two_hit_periods = analysis['two_hit_periods_list']
                    if two_hit_periods:
                        report += f"\n#### 2数全命中期数\n{', '.join(map(str, two_hit_periods))}\n\n"
                    else:
                        report += f"\n#### 2数全命中期数\n无\n\n"

                    # 季度分析
                    if 'quarterly_analysis' in analysis:
                        report += "#### 季度表现\n\n"
                        report += "| 季度 | 总期数 | 命中期数 | 命中率 |\n"
                        report += "|------|--------|----------|--------|\n"

                        for quarter, data in analysis['quarterly_analysis'].items():
                            report += f"| {quarter} | {data['total']} | {data['hits']} | {data['hit_rate']:.3f} |\n"

                        report += "\n"

        report += "## 📈 4. 分析结论与建议\n\n"
        report += "### 🎯 主要发现\n\n"

        # 找出最佳方法
        best_method_key = None
        best_hit_rate = 0

        for method_key, result in self.validation_results.items():
            if method_key not in ['detailed_analysis', 'significance_tests']:
                if result['hit_rate'] > best_hit_rate:
                    best_hit_rate = result['hit_rate']
                    best_method_key = method_key

        if best_method_key:
            best_method_name = self.validation_results[best_method_key]['method_name']
            report += f"1. **最佳预测方法**: {best_method_name} (命中率: {best_hit_rate:.3f})\n"

        report += "2. **理论对比**: 所有方法的表现需要与理论基准(23.21%)进行对比\n"
        report += "3. **2数全命中**: 2数全命中的概率较低，符合理论预期(1.28%)\n"
        report += "4. **时间趋势**: 不同季度的表现可能存在差异\n\n"

        report += "### 🚀 优化建议\n\n"
        report += "1. **方法改进**: 基于表现最好的方法进行进一步优化\n"
        report += "2. **特征增强**: 考虑加入更多有效特征提高预测准确性\n"
        report += "3. **集成学习**: 结合多种方法的优势进行集成预测\n"
        report += "4. **动态调整**: 根据最新数据动态调整预测策略\n"
        report += "5. **风险管理**: 基于概率分析制定合理的预期和风险控制\n\n"

        report += f"---\n*报告生成时间: {timestamp}*\n"

        return report

    def run_complete_validation(self):
        """运行完整的预测验证流程"""
        print("🎯 开始预测验证分析...")
        print("=" * 60)

        # 1. 数据加载
        if not self.load_data():
            return False

        # 2. 生成预测
        if not self.generate_predictions():
            return False

        # 3. 验证预测
        if not self.validate_predictions():
            return False

        # 4. 详细分析
        self.detailed_hit_analysis()

        # 5. 生成报告
        json_file, md_file = self.generate_comprehensive_report()

        print("\n" + "=" * 60)
        print("✅ 预测验证分析完成！")
        print("=" * 60)
        print(f"📋 JSON报告: {json_file}")
        print(f"📄 Markdown报告: {md_file}")

        return True

def main():
    """主函数"""
    print("🎯 预测验证报告系统")
    print("对测试集进行预测并验证结果准确性")
    print("输出详细验证报告包括命中期数、组数和整体命中率")
    print("=" * 60)

    # 创建验证系统
    validator = PredictionValidationSystem()

    # 运行完整验证
    success = validator.run_complete_validation()

    if success:
        print("\n🎉 验证成功完成！")
        print("📊 请查看生成的报告文件")
    else:
        print("\n❌ 验证失败，请检查数据文件")

if __name__ == "__main__":
    main()
