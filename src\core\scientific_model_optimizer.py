#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Scientific Model Optimizer for Small Sample High-Randomness Data
Based on first principles analysis of lottery prediction challenges
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.metrics import mean_squared_error, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')


class ScientificModelOptimizer:
    """
    Scientific approach to model optimization for small sample, high-randomness data
    
    Key Principles:
    1. Occam's Razor: Prefer simpler models
    2. Bias-Variance Tradeoff: Control model complexity
    3. Statistical Significance: Require rigorous validation
    4. Realistic Expectations: Acknowledge randomness limits
    """
    
    def __init__(self, random_state: int = 42):
        self.random_state = random_state
        self.models = {}
        self.scalers = {}
        self.feature_selectors = {}
        self.validation_results = {}
        
        # Scientific parameters based on first principles
        self.max_features_ratio = 0.1  # Features should be << samples
        self.min_samples_per_feature = 10  # Statistical rule of thumb
        self.significance_level = 0.01  # Strict significance threshold
        
    def analyze_data_characteristics(self, X: pd.DataFrame, y: pd.Series) -> Dict:
        """
        Analyze data characteristics to inform model selection
        """
        n_samples, n_features = X.shape
        
        analysis = {
            'n_samples': n_samples,
            'n_features': n_features,
            'feature_to_sample_ratio': n_features / n_samples,
            'recommended_max_features': max(1, int(n_samples * self.max_features_ratio)),
            'overfitting_risk': 'high' if n_features / n_samples > 0.1 else 'low',
            'target_variance': y.var(),
            'target_range': (y.min(), y.max()),
            'data_sparsity': X.isnull().sum().sum() / (n_samples * n_features)
        }
        
        # Calculate theoretical random baseline
        # For lottery numbers (1-49), random prediction accuracy
        analysis['random_baseline_mse'] = self._calculate_random_baseline_mse(y)
        analysis['random_baseline_accuracy'] = 1/49  # Single number match probability
        
        return analysis
    
    def _calculate_random_baseline_mse(self, y: pd.Series) -> float:
        """
        Calculate MSE for random predictions
        """
        # Random predictions from uniform distribution over target range
        random_predictions = np.random.uniform(y.min(), y.max(), len(y))
        return mean_squared_error(y, random_predictions)
    
    def select_optimal_algorithm(self, data_analysis: Dict) -> Dict:
        """
        Select optimal algorithm based on data characteristics
        """
        n_samples = data_analysis['n_samples']
        feature_ratio = data_analysis['feature_to_sample_ratio']
        overfitting_risk = data_analysis['overfitting_risk']
        
        recommendations = {
            'primary_algorithm': None,
            'reasoning': [],
            'hyperparameters': {},
            'regularization_strength': 'high'
        }
        
        if n_samples < 200:  # Small sample size
            if feature_ratio > 0.1:  # High dimensional
                recommendations['primary_algorithm'] = 'Ridge'
                recommendations['reasoning'].append(
                    "Ridge regression chosen for small sample + high dimensionality"
                )
                recommendations['hyperparameters'] = {
                    'alpha': 10.0,  # Strong regularization
                    'random_state': self.random_state
                }
            else:  # Low dimensional
                recommendations['primary_algorithm'] = 'ElasticNet'
                recommendations['reasoning'].append(
                    "ElasticNet chosen for small sample + moderate dimensionality"
                )
                recommendations['hyperparameters'] = {
                    'alpha': 1.0,
                    'l1_ratio': 0.5,
                    'random_state': self.random_state,
                    'max_iter': 2000
                }
        else:  # Larger sample size
            if overfitting_risk == 'high':
                recommendations['primary_algorithm'] = 'RandomForest'
                recommendations['reasoning'].append(
                    "Random Forest with strong regularization for larger samples"
                )
                recommendations['hyperparameters'] = {
                    'n_estimators': 50,  # Conservative
                    'max_depth': 3,      # Shallow trees
                    'min_samples_split': 20,
                    'min_samples_leaf': 10,
                    'max_features': 'sqrt',
                    'random_state': self.random_state
                }
            else:
                recommendations['primary_algorithm'] = 'Ridge'
                recommendations['reasoning'].append(
                    "Ridge regression for balanced complexity"
                )
                recommendations['hyperparameters'] = {
                    'alpha': 1.0,
                    'random_state': self.random_state
                }
        
        return recommendations
    
    def implement_feature_selection(self, X: pd.DataFrame, y: pd.Series, 
                                  max_features: int = None) -> Tuple[pd.DataFrame, List[str]]:
        """
        Implement scientific feature selection
        """
        if max_features is None:
            max_features = max(1, int(len(X) * self.max_features_ratio))
        
        # Ensure we don't select more features than available
        max_features = min(max_features, X.shape[1])
        
        if X.shape[1] <= max_features:
            return X, X.columns.tolist()
        
        # Use mutual information for feature selection (handles non-linear relationships)
        selector = SelectKBest(score_func=mutual_info_regression, k=max_features)
        
        try:
            X_selected = selector.fit_transform(X, y)
            selected_features = X.columns[selector.get_support()].tolist()
            
            return pd.DataFrame(X_selected, columns=selected_features, index=X.index), selected_features
        
        except Exception as e:
            print(f"Feature selection failed: {e}")
            # Fallback to correlation-based selection
            correlations = X.corrwith(y).abs().sort_values(ascending=False)
            selected_features = correlations.head(max_features).index.tolist()
            return X[selected_features], selected_features
    
    def train_with_rigorous_validation(self, X: pd.DataFrame, y: pd.Series, 
                                     algorithm: str, hyperparameters: Dict) -> Dict:
        """
        Train model with rigorous cross-validation
        """
        # Time series cross-validation (respects temporal order)
        tscv = TimeSeriesSplit(n_splits=5, test_size=10)  # Small test sets for small data
        
        # Initialize model
        if algorithm == 'Ridge':
            model = Ridge(**hyperparameters)
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            X_for_cv = X_scaled
        elif algorithm == 'ElasticNet':
            model = ElasticNet(**hyperparameters)
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            X_for_cv = X_scaled
        elif algorithm == 'RandomForest':
            model = RandomForestRegressor(**hyperparameters)
            scaler = None
            X_for_cv = X.values
        else:
            raise ValueError(f"Unsupported algorithm: {algorithm}")
        
        # Cross-validation
        cv_scores = cross_val_score(model, X_for_cv, y, cv=tscv, 
                                  scoring='neg_mean_squared_error', n_jobs=-1)
        
        # Train final model on all data
        model.fit(X_for_cv, y)
        
        # Calculate training metrics
        if scaler is not None:
            train_predictions = model.predict(X_scaled)
        else:
            train_predictions = model.predict(X.values)
        
        train_mse = mean_squared_error(y, train_predictions)
        train_mae = mean_absolute_error(y, train_predictions)
        
        results = {
            'model': model,
            'scaler': scaler,
            'cv_scores': cv_scores,
            'cv_mean': -cv_scores.mean(),
            'cv_std': cv_scores.std(),
            'train_mse': train_mse,
            'train_mae': train_mae,
            'overfitting_indicator': train_mse / (-cv_scores.mean()),  # Should be close to 1
            'statistical_significance': self._test_significance(cv_scores)
        }
        
        return results
    
    def _test_significance(self, cv_scores: np.ndarray) -> Dict:
        """
        Test statistical significance of model performance
        """
        from scipy import stats
        
        # Test if CV scores are significantly different from random baseline
        # Using one-sample t-test
        mean_score = -cv_scores.mean()  # Convert back to positive MSE
        
        # Null hypothesis: model performs no better than random
        # For lottery data, random baseline MSE is high
        random_baseline = 200.0  # Approximate random MSE for lottery numbers
        
        t_stat, p_value = stats.ttest_1samp(cv_scores, -random_baseline)
        
        return {
            'mean_cv_mse': mean_score,
            'random_baseline_mse': random_baseline,
            't_statistic': t_stat,
            'p_value': p_value,
            'is_significant': p_value < self.significance_level,
            'improvement_over_random': (random_baseline - mean_score) / random_baseline
        }
    
    def optimize_for_target(self, X: pd.DataFrame, y: pd.Series, 
                          target_name: str) -> Dict:
        """
        Complete optimization pipeline for a single target
        """
        print(f"\n🔬 科学优化 {target_name}")
        
        # Step 1: Analyze data characteristics
        data_analysis = self.analyze_data_characteristics(X, y)
        print(f"  样本数: {data_analysis['n_samples']}")
        print(f"  特征数: {data_analysis['n_features']}")
        print(f"  过拟合风险: {data_analysis['overfitting_risk']}")
        
        # Step 2: Select optimal algorithm
        algorithm_rec = self.select_optimal_algorithm(data_analysis)
        print(f"  推荐算法: {algorithm_rec['primary_algorithm']}")
        print(f"  理由: {'; '.join(algorithm_rec['reasoning'])}")
        
        # Step 3: Feature selection
        max_features = data_analysis['recommended_max_features']
        X_selected, selected_features = self.implement_feature_selection(X, y, max_features)
        print(f"  特征选择: {len(X.columns)} -> {len(selected_features)}")
        
        # Step 4: Train with validation
        training_results = self.train_with_rigorous_validation(
            X_selected, y, 
            algorithm_rec['primary_algorithm'], 
            algorithm_rec['hyperparameters']
        )
        
        # Step 5: Evaluate results
        significance = training_results['statistical_significance']
        print(f"  交叉验证MSE: {training_results['cv_mean']:.2f} ± {training_results['cv_std']:.2f}")
        print(f"  过拟合指标: {training_results['overfitting_indicator']:.2f}")
        print(f"  统计显著性: p={significance['p_value']:.4f}")
        print(f"  相对随机改善: {significance['improvement_over_random']:.1%}")
        
        # Store results
        self.models[target_name] = training_results['model']
        if training_results['scaler'] is not None:
            self.scalers[target_name] = training_results['scaler']
        
        return {
            'target_name': target_name,
            'data_analysis': data_analysis,
            'algorithm_recommendation': algorithm_rec,
            'selected_features': selected_features,
            'training_results': training_results,
            'final_assessment': self._assess_model_quality(training_results)
        }
    
    def _assess_model_quality(self, training_results: Dict) -> Dict:
        """
        Assess overall model quality and provide recommendations
        """
        overfitting_indicator = training_results['overfitting_indicator']
        significance = training_results['statistical_significance']
        
        assessment = {
            'quality_score': 0,
            'issues': [],
            'recommendations': []
        }
        
        # Check overfitting
        if overfitting_indicator > 1.5:
            assessment['issues'].append("Potential overfitting detected")
            assessment['recommendations'].append("Increase regularization or reduce features")
        elif overfitting_indicator < 0.8:
            assessment['issues'].append("Potential underfitting detected")
            assessment['recommendations'].append("Reduce regularization or add features")
        else:
            assessment['quality_score'] += 1
        
        # Check statistical significance
        if significance['is_significant']:
            assessment['quality_score'] += 1
        else:
            assessment['issues'].append("Model performance not statistically significant")
            assessment['recommendations'].append("Collect more data or simplify model")
        
        # Check improvement over random
        if significance['improvement_over_random'] > 0.05:  # 5% improvement
            assessment['quality_score'] += 1
        else:
            assessment['issues'].append("Minimal improvement over random baseline")
            assessment['recommendations'].append("Reconsider modeling approach")
        
        # Overall assessment
        if assessment['quality_score'] >= 2:
            assessment['overall'] = "Acceptable"
        elif assessment['quality_score'] == 1:
            assessment['overall'] = "Marginal"
        else:
            assessment['overall'] = "Poor"
        
        return assessment
