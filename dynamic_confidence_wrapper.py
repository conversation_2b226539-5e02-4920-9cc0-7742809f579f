#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态置信度系统包装器
自动生成的集成代码
"""

import os
import json
import logging
from dynamic_confidence_integration import (
    get_confidence_integration,
    get_integrated_confidence,
    update_prediction_result,
    get_performance_report,
    perform_manual_calibration,
    predict_with_dynamic_confidence
)

logger = logging.getLogger(__name__)

# 全局集成实例
_integration = None

def get_integration():
    """获取集成实例"""
    global _integration
    
    if _integration is None:
        # 加载配置
        config_file = 'dynamic_confidence_config.json'
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                integration_config = config.get('integration_config', {})
                monitoring_config = config.get('monitoring_config', {})
                
                # 合并配置
                full_config = {
                    'integration_enabled': integration_config.get('enabled', True),
                    'integration_mode': integration_config.get('mode', 'hybrid'),
                    'integration_ratio': integration_config.get('ratio', 0.7),
                    'adjuster_config': monitoring_config
                }
                
                _integration = get_confidence_integration(full_config)
            except Exception as e:
                logger.warning(f"加载配置失败，使用默认配置: {e}")
                _integration = get_confidence_integration()
        else:
            _integration = get_confidence_integration()
    
    return _integration

# 主要接口函数
def calculate_dynamic_confidence(predicted_numbers, context=None):
    """计算动态置信度"""
    integration = get_integration()
    return integration.get_integrated_confidence(predicted_numbers, context)

def predict_with_confidence(previous_numbers, context=None):
    """带置信度的预测"""
    return predict_with_dynamic_confidence(previous_numbers, context)

def update_prediction_feedback(prediction_result):
    """更新预测反馈"""
    integration = get_integration()
    return integration.update_prediction_result(prediction_result)

def get_system_performance():
    """获取系统性能"""
    integration = get_integration()
    return integration.get_performance_report()

def calibrate_system():
    """校准系统"""
    integration = get_integration()
    return integration.perform_manual_calibration()

def export_system_data(filename):
    """导出系统数据"""
    integration = get_integration()
    return integration.export_performance_data(filename)

# 向后兼容的函数别名
def get_confidence(predicted_numbers, context=None):
    """获取置信度（向后兼容）"""
    result = calculate_dynamic_confidence(predicted_numbers, context)
    return result['final_confidence']

def calculate_confidence(predicted_numbers, context=None):
    """计算置信度（向后兼容）"""
    result = calculate_dynamic_confidence(predicted_numbers, context)
    return result

# 部署信息
DEPLOYMENT_INFO = {
    'deployment_id': '20250715_183041',
    'deployment_time': '2025-07-15T18:30:41.350225',
    'system_version': '2.0',
    'features': ['dynamic_adjustment', 'real_time_monitoring', 'auto_calibration']
}

# 系统状态检查
def get_system_status():
    """获取系统状态"""
    try:
        integration = get_integration()
        performance = integration.get_performance_report()
        
        return {
            'status': 'active',
            'deployment_info': DEPLOYMENT_INFO,
            'integration_enabled': performance['integration_config']['enabled'],
            'integration_mode': performance['integration_config']['mode'],
            'total_predictions': performance['integration_stats']['total_predictions'],
            'monitoring_active': performance['adjuster_status']['monitoring_enabled'],
            'auto_calibration_active': performance['adjuster_status']['auto_calibration_enabled']
        }
    except Exception as e:
        return {
            'status': 'error',
            'error': str(e),
            'deployment_info': DEPLOYMENT_INFO
        }
