#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测流程详细分析
分析测试集中的预测流程、命中模式和间隔统计
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict, Counter
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class PredictionFlowAnalyzer:
    """预测流程分析器"""
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.full_data = None
        self.train_data = None
        self.test_data = None
        
        # 34.3%方法的最佳参数
        self.optimal_params = {
            'high_freq_boost': 1.15,
            'low_freq_penalty': 0.85,
            'rising_trend_boost': 1.10,
            'falling_trend_penalty': 0.90,
            'perturbation': 0.05
        }
        
        # 数字分类
        self.high_freq_numbers = [5, 15, 3, 40, 30]
        self.low_freq_numbers = [41, 1, 8, 48, 47]
        self.rising_numbers = [30, 39, 4, 8, 22]
        self.falling_numbers = [5, 26, 44, 36, 15]
        
        # 分析结果
        self.prediction_results = []
        self.hit_analysis = {}
        
    def load_data(self):
        """加载数据"""
        print(f"📊 预测流程详细分析")
        print("=" * 60)
        
        try:
            self.full_data = pd.read_csv(self.data_file)
            self.full_data = self.full_data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            # 训练数据：2023-2024年
            self.train_data = self.full_data[
                (self.full_data['年份'] >= 2023) & 
                (self.full_data['年份'] <= 2024)
            ].copy()
            
            # 测试数据：2025年1-179期
            self.test_data = self.full_data[
                (self.full_data['年份'] == 2025) & 
                (self.full_data['期号'] <= 179)
            ].copy()
            
            print(f"✅ 数据加载完成")
            print(f"  训练集: {len(self.train_data)}期 (2023-2024年)")
            print(f"  测试集: {len(self.test_data)}期 (2025年1-179期)")
            
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def build_enhanced_markov_model(self):
        """构建增强马尔可夫模型"""
        print(f"\n🔧 构建34.3%增强马尔可夫模型")
        print("=" * 50)
        
        # 基础马尔可夫转移概率
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 计算基础转移概率
        self.base_markov_prob = {}
        for curr_num in range(1, 50):
            if curr_num in transition_count:
                total = sum(transition_count[curr_num].values())
                smoothed_total = total + 49
                self.base_markov_prob[curr_num] = {}
                
                for next_num in range(1, 50):
                    count = transition_count[curr_num].get(next_num, 0)
                    self.base_markov_prob[curr_num][next_num] = (count + 1) / smoothed_total
            else:
                self.base_markov_prob[curr_num] = {
                    next_num: 1/49 for next_num in range(1, 50)
                }
        
        # 应用增强策略
        self.apply_enhancements()
        
        print(f"✅ 增强马尔可夫模型构建完成")
    
    def apply_enhancements(self):
        """应用增强策略"""
        # 计算权重
        frequency_weights = {}
        trend_weights = {}
        
        for num in range(1, 50):
            if num in self.high_freq_numbers:
                frequency_weights[num] = self.optimal_params['high_freq_boost']
            elif num in self.low_freq_numbers:
                frequency_weights[num] = self.optimal_params['low_freq_penalty']
            else:
                frequency_weights[num] = 1.0
            
            if num in self.rising_numbers:
                trend_weights[num] = self.optimal_params['rising_trend_boost']
            elif num in self.falling_numbers:
                trend_weights[num] = self.optimal_params['falling_trend_penalty']
            else:
                trend_weights[num] = 1.0
        
        # 构建增强马尔可夫概率
        self.enhanced_markov_prob = {}
        for curr_num in self.base_markov_prob:
            self.enhanced_markov_prob[curr_num] = {}
            total_weight = 0
            
            for next_num, base_prob in self.base_markov_prob[curr_num].items():
                freq_weight = frequency_weights[next_num]
                trend_weight = trend_weights[next_num]
                combined_weight = freq_weight * trend_weight
                weighted_prob = base_prob * combined_weight
                
                self.enhanced_markov_prob[curr_num][next_num] = weighted_prob
                total_weight += weighted_prob
            
            # 归一化
            for next_num in self.enhanced_markov_prob[curr_num]:
                self.enhanced_markov_prob[curr_num][next_num] /= total_weight
    
    def enhanced_markov_predict(self, prev_numbers, period_num):
        """增强马尔可夫预测"""
        # 使用期号作为随机种子，确保可重复性
        np.random.seed(42 + period_num)
        
        number_probs = defaultdict(float)
        total_prob = 0.0
        
        for prev_num in prev_numbers:
            if prev_num in self.enhanced_markov_prob:
                for next_num, prob in self.enhanced_markov_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        # 添加随机扰动
        perturbation = self.optimal_params['perturbation']
        for num in number_probs:
            noise = np.random.normal(0, perturbation * number_probs[num])
            number_probs[num] = max(0, number_probs[num] + noise)
        
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            return [num for num, prob in sorted_numbers[:2]]
        else:
            return [1, 2]
    
    def run_sequential_prediction_analysis(self):
        """运行逐期预测分析"""
        print(f"\n🎯 逐期预测流程分析")
        print("=" * 60)
        
        print(f"预测方式: 逐期预测 (使用前一期真实数据)")
        print(f"测试期间: 2025年1-179期")
        print(f"预测方法: 34.3%全面增强马尔可夫")
        
        hits = 0
        total = 0
        hit_periods = []
        miss_periods = []
        
        print(f"\n开始逐期预测...")
        print("=" * 50)
        
        for idx, test_row in self.test_data.iterrows():
            period = test_row['期号']
            year = test_row['年份']
            actual_numbers = [test_row[f'数字{j}'] for j in range(1, 7)]
            
            # 获取前一期数字 (关键：使用真实的前一期数据)
            if idx == self.test_data.index[0]:
                # 第一期测试数据，使用训练集最后一期
                prev_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
                prev_period = f"2024年最后一期"
            else:
                # 使用前一期的真实数据
                prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                prev_row = self.test_data.loc[prev_idx]
                prev_numbers = set([prev_row[f'数字{j}'] for j in range(1, 7)])
                prev_period = f"{prev_row['年份']}年{prev_row['期号']}期"
            
            try:
                # 执行预测
                predicted = self.enhanced_markov_predict(prev_numbers, period)
                predicted_set = set(predicted)
                actual_set = set(actual_numbers)
                
                # 计算命中
                hit_count = len(predicted_set & actual_set)
                is_hit = hit_count >= 1
                
                total += 1
                if is_hit:
                    hits += 1
                    hit_periods.append(period)
                else:
                    miss_periods.append(period)
                
                # 记录详细结果
                result = {
                    'period': period,
                    'year': year,
                    'prev_period': prev_period,
                    'prev_numbers': list(prev_numbers),
                    'predicted': predicted,
                    'actual': actual_numbers,
                    'hit_count': hit_count,
                    'is_hit': is_hit,
                    'hit_numbers': list(predicted_set & actual_set)
                }
                
                self.prediction_results.append(result)
                
                # 打印进度 (每20期)
                if total % 20 == 0:
                    current_rate = hits / total
                    print(f"  已预测{total}期, 命中{hits}期, 当前命中率: {current_rate:.3f}")
                
            except Exception as e:
                print(f"    期号{period}预测失败: {e}")
                total += 1
                miss_periods.append(period)
        
        final_hit_rate = hits / total if total > 0 else 0
        
        print(f"\n📊 预测流程分析完成")
        print("=" * 50)
        print(f"预测方式: 逐期预测 (每期使用前一期真实数据)")
        print(f"总预测期数: {total}")
        print(f"命中期数: {hits}")
        print(f"未命中期数: {total - hits}")
        print(f"最终命中率: {final_hit_rate:.3f} ({final_hit_rate:.1%})")
        
        return {
            'total_periods': total,
            'hit_periods_count': hits,
            'miss_periods_count': total - hits,
            'hit_rate': final_hit_rate,
            'hit_periods': hit_periods,
            'miss_periods': miss_periods
        }
    
    def analyze_hit_intervals(self):
        """分析命中间隔"""
        print(f"\n📈 命中间隔分析")
        print("=" * 50)
        
        hit_periods = [r['period'] for r in self.prediction_results if r['is_hit']]
        
        if len(hit_periods) < 2:
            print("命中期数不足，无法分析间隔")
            return
        
        # 计算命中间隔
        intervals = []
        for i in range(1, len(hit_periods)):
            interval = hit_periods[i] - hit_periods[i-1]
            intervals.append(interval)
        
        # 计算未命中间隔 (连续未命中的期数)
        miss_streaks = []
        current_streak = 0
        
        for result in self.prediction_results:
            if result['is_hit']:
                if current_streak > 0:
                    miss_streaks.append(current_streak)
                    current_streak = 0
            else:
                current_streak += 1
        
        # 如果最后还有未命中序列
        if current_streak > 0:
            miss_streaks.append(current_streak)
        
        # 统计分析
        avg_interval = np.mean(intervals) if intervals else 0
        min_interval = min(intervals) if intervals else 0
        max_interval = max(intervals) if intervals else 0
        
        avg_miss_streak = np.mean(miss_streaks) if miss_streaks else 0
        max_miss_streak = max(miss_streaks) if miss_streaks else 0
        
        print(f"命中间隔统计:")
        print(f"  平均间隔: {avg_interval:.1f}期")
        print(f"  最短间隔: {min_interval}期")
        print(f"  最长间隔: {max_interval}期")
        print(f"  间隔分布: {Counter(intervals)}")
        
        print(f"\n未命中连续期数统计:")
        print(f"  平均连续未命中: {avg_miss_streak:.1f}期")
        print(f"  最长连续未命中: {max_miss_streak}期")
        print(f"  连续未命中分布: {Counter(miss_streaks)}")
        
        # 计算理论期望
        hit_rate = len(hit_periods) / len(self.prediction_results)
        theoretical_avg_interval = 1 / hit_rate if hit_rate > 0 else float('inf')
        
        print(f"\n理论分析:")
        print(f"  命中率: {hit_rate:.3f}")
        print(f"  理论平均间隔: {theoretical_avg_interval:.1f}期")
        print(f"  实际vs理论: {avg_interval/theoretical_avg_interval:.2f}倍" if theoretical_avg_interval != float('inf') else "")
        
        self.hit_analysis = {
            'hit_intervals': intervals,
            'miss_streaks': miss_streaks,
            'avg_interval': avg_interval,
            'min_interval': min_interval,
            'max_interval': max_interval,
            'avg_miss_streak': avg_miss_streak,
            'max_miss_streak': max_miss_streak,
            'theoretical_avg_interval': theoretical_avg_interval
        }
    
    def analyze_prediction_patterns(self):
        """分析预测模式"""
        print(f"\n🔍 预测模式分析")
        print("=" * 50)
        
        # 分析命中的数字模式
        hit_numbers = []
        predicted_numbers = []
        
        for result in self.prediction_results:
            predicted_numbers.extend(result['predicted'])
            if result['is_hit']:
                hit_numbers.extend(result['hit_numbers'])
        
        # 预测数字频率
        predicted_freq = Counter(predicted_numbers)
        hit_freq = Counter(hit_numbers)
        
        print(f"预测数字频率分析:")
        print(f"  最常预测: {predicted_freq.most_common(5)}")
        print(f"  最常命中: {hit_freq.most_common(5)}")
        
        # 分析命中类型
        hit_types = {'single': 0, 'double': 0}
        for result in self.prediction_results:
            if result['is_hit']:
                if result['hit_count'] == 1:
                    hit_types['single'] += 1
                elif result['hit_count'] == 2:
                    hit_types['double'] += 1
        
        print(f"\n命中类型分析:")
        print(f"  单数字命中: {hit_types['single']}次")
        print(f"  双数字命中: {hit_types['double']}次")
        
        # 分析时间模式
        monthly_hits = defaultdict(list)
        for result in self.prediction_results:
            month = ((result['period'] - 1) // 30) % 12 + 1
            monthly_hits[month].append(result['is_hit'])
        
        print(f"\n月度命中率分析:")
        for month in sorted(monthly_hits.keys()):
            month_hit_rate = np.mean(monthly_hits[month])
            month_count = len(monthly_hits[month])
            print(f"  {month}月: {month_hit_rate:.3f} ({month_count}期)")
    
    def create_prediction_visualization(self):
        """创建预测可视化"""
        print(f"\n🎨 生成预测流程可视化")
        print("=" * 50)
        
        fig = plt.figure(figsize=(20, 12))
        
        # 1. 命中时间序列
        ax1 = plt.subplot(2, 3, 1)
        periods = [r['period'] for r in self.prediction_results]
        hits = [1 if r['is_hit'] else 0 for r in self.prediction_results]
        
        ax1.plot(periods, hits, 'o-', markersize=3, alpha=0.7)
        ax1.set_title('命中时间序列', fontsize=12, fontweight='bold')
        ax1.set_xlabel('期号')
        ax1.set_ylabel('是否命中')
        ax1.grid(True, alpha=0.3)
        
        # 2. 命中间隔分布
        ax2 = plt.subplot(2, 3, 2)
        if self.hit_analysis and 'hit_intervals' in self.hit_analysis:
            intervals = self.hit_analysis['hit_intervals']
            ax2.hist(intervals, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            ax2.axvline(x=np.mean(intervals), color='red', linestyle='--', 
                       label=f'平均间隔: {np.mean(intervals):.1f}期')
            ax2.set_title('命中间隔分布', fontsize=12, fontweight='bold')
            ax2.set_xlabel('间隔期数')
            ax2.set_ylabel('频次')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
        
        # 3. 连续未命中分布
        ax3 = plt.subplot(2, 3, 3)
        if self.hit_analysis and 'miss_streaks' in self.hit_analysis:
            miss_streaks = self.hit_analysis['miss_streaks']
            ax3.hist(miss_streaks, bins=15, alpha=0.7, color='lightcoral', edgecolor='black')
            ax3.axvline(x=np.mean(miss_streaks), color='blue', linestyle='--',
                       label=f'平均连续未命中: {np.mean(miss_streaks):.1f}期')
            ax3.set_title('连续未命中期数分布', fontsize=12, fontweight='bold')
            ax3.set_xlabel('连续未命中期数')
            ax3.set_ylabel('频次')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
        
        # 4. 累积命中率
        ax4 = plt.subplot(2, 3, 4)
        cumulative_hits = np.cumsum(hits)
        cumulative_periods = np.arange(1, len(hits) + 1)
        cumulative_rate = cumulative_hits / cumulative_periods
        
        ax4.plot(periods, cumulative_rate, 'g-', linewidth=2)
        ax4.axhline(y=0.343, color='red', linestyle='--', label='目标命中率: 34.3%')
        ax4.set_title('累积命中率趋势', fontsize=12, fontweight='bold')
        ax4.set_xlabel('期号')
        ax4.set_ylabel('累积命中率')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        # 5. 预测数字频率
        ax5 = plt.subplot(2, 3, 5)
        predicted_numbers = []
        for result in self.prediction_results:
            predicted_numbers.extend(result['predicted'])
        
        predicted_freq = Counter(predicted_numbers)
        top_predicted = predicted_freq.most_common(10)
        
        numbers, freqs = zip(*top_predicted)
        ax5.bar(range(len(numbers)), freqs, alpha=0.7, color='gold')
        ax5.set_xticks(range(len(numbers)))
        ax5.set_xticklabels(numbers)
        ax5.set_title('最常预测数字TOP10', fontsize=12, fontweight='bold')
        ax5.set_xlabel('数字')
        ax5.set_ylabel('预测次数')
        ax5.grid(True, alpha=0.3)
        
        # 6. 月度命中率
        ax6 = plt.subplot(2, 3, 6)
        monthly_hits = defaultdict(list)
        for result in self.prediction_results:
            month = ((result['period'] - 1) // 30) % 12 + 1
            monthly_hits[month].append(result['is_hit'])
        
        months = sorted(monthly_hits.keys())
        month_rates = [np.mean(monthly_hits[month]) for month in months]
        
        ax6.bar(months, month_rates, alpha=0.7, color='lightgreen')
        ax6.axhline(y=np.mean(hits), color='red', linestyle='--', label='总体命中率')
        ax6.set_title('月度命中率', fontsize=12, fontweight='bold')
        ax6.set_xlabel('月份')
        ax6.set_ylabel('命中率')
        ax6.legend()
        ax6.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        chart_file = f"预测流程分析_{timestamp}.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"✅ 预测流程可视化已生成: {chart_file}")
    
    def generate_detailed_report(self):
        """生成详细报告"""
        print(f"\n📋 生成详细分析报告")
        print("=" * 60)
        
        # 基本统计
        total_periods = len(self.prediction_results)
        hit_count = sum(1 for r in self.prediction_results if r['is_hit'])
        hit_rate = hit_count / total_periods if total_periods > 0 else 0
        
        # 详细报告
        report = {
            'analysis_time': datetime.now().isoformat(),
            'prediction_method': '34.3%全面增强马尔可夫',
            'prediction_flow': '逐期预测 (使用前一期真实数据)',
            'test_period': '2025年1-179期',
            'basic_statistics': {
                'total_periods': total_periods,
                'hit_periods': hit_count,
                'miss_periods': total_periods - hit_count,
                'hit_rate': hit_rate,
                'hit_percentage': f"{hit_rate:.1%}"
            },
            'interval_analysis': self.hit_analysis,
            'detailed_results': self.prediction_results[:10]  # 前10期详细结果
        }
        
        # 保存报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"预测流程详细报告_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 详细报告已保存: {report_file}")
        
        # 打印核心结论
        print(f"\n🎯 核心结论")
        print("=" * 40)
        print(f"预测方式: 逐期预测 (每期使用前一期真实开奖数据)")
        print(f"总预测期数: {total_periods}期")
        print(f"命中期数: {hit_count}期")
        print(f"命中率: {hit_rate:.3f} ({hit_rate:.1%})")
        
        if self.hit_analysis:
            print(f"平均命中间隔: {self.hit_analysis['avg_interval']:.1f}期")
            print(f"最短间隔: {self.hit_analysis['min_interval']}期")
            print(f"最长间隔: {self.hit_analysis['max_interval']}期")
            print(f"最长连续未命中: {self.hit_analysis['max_miss_streak']}期")
        
        return report

def main():
    """主函数"""
    print("📊 预测流程详细分析")
    print("分析34.3%方法在测试集中的具体预测流程")
    print("=" * 80)
    
    analyzer = PredictionFlowAnalyzer()
    
    # 1. 加载数据
    if not analyzer.load_data():
        return
    
    # 2. 构建模型
    analyzer.build_enhanced_markov_model()
    
    # 3. 运行逐期预测分析
    prediction_summary = analyzer.run_sequential_prediction_analysis()
    
    # 4. 分析命中间隔
    analyzer.analyze_hit_intervals()
    
    # 5. 分析预测模式
    analyzer.analyze_prediction_patterns()
    
    # 6. 创建可视化
    analyzer.create_prediction_visualization()
    
    # 7. 生成详细报告
    analyzer.generate_detailed_report()
    
    print(f"\n🎉 预测流程分析完成")

if __name__ == "__main__":
    main()
