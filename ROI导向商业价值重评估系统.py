#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ROI导向的商业价值重评估系统
基于真实奖金结构重新计算期望收益率
从命中率转向投资回报率评估，提供真实的商业价值分析
"""

import pandas as pd
import numpy as np
import json
from collections import defaultdict
from datetime import datetime
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class ROIBusinessAnalyzer:
    """
    ROI导向的商业价值分析器
    基于真实奖金结构评估预测系统的商业价值
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.data = None
        self.train_data = None
        self.test_data = None
        self.transition_prob = {}
        
        # 彩票奖金结构（假设的典型结构）
        self.prize_structure = {
            'bet_cost': 2,  # 每注成本2元
            'prizes': {
                0: 0,      # 未命中：0元
                1: 5,      # 命中1个：5元
                2: 50,     # 命中2个：50元
                3: 300,    # 命中3个：300元
                4: 3000,   # 命中4个：3000元
                5: 50000,  # 命中5个：50000元
                6: 1000000 # 命中6个：1000000元（大奖）
            }
        }
        
        # 理论基准
        self.theoretical_baseline = 0.232143
        
        self.roi_analysis_results = {}
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            # 使用最优配置
            self.train_data = self.data[(self.data['年份'] >= 2023) & (self.data['年份'] < 2025)].copy()
            self.test_data = self.data[(self.data['年份'] == 2025) & (self.data['期号'] <= 182)].copy()
            
            print(f"✅ 数据加载成功")
            print(f"  训练数据: {len(self.train_data)}期")
            print(f"  测试数据: {len(self.test_data)}期")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def build_markov_model(self):
        """构建马尔可夫模型"""
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        self.transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                self.transition_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        return True
    
    def predict_numbers(self, previous_numbers, num_predictions=2):
        """预测指定数量的数字"""
        if not self.transition_prob:
            return list(range(1, num_predictions + 1))
        
        number_probs = defaultdict(float)
        total_prob = 0.0
        
        for prev_num in previous_numbers:
            if prev_num in self.transition_prob:
                for next_num, prob in self.transition_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        if len(number_probs) >= num_predictions:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            return [num for num, prob in sorted_numbers[:num_predictions]]
        else:
            return list(range(1, num_predictions + 1))
    
    def calculate_roi_for_strategy(self, strategy_name, prediction_count):
        """计算特定策略的ROI"""
        print(f"\n💰 计算{strategy_name}的ROI")
        print("-" * 40)
        
        total_investment = 0
        total_return = 0
        hit_distribution = defaultdict(int)
        
        for idx, test_row in self.test_data.iterrows():
            actual_numbers = set([test_row[f'数字{j}'] for j in range(1, 7)])
            
            # 获取前一期数字
            if idx == self.test_data.index[0]:
                prev_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                prev_numbers = set([self.test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            # 预测
            predicted_numbers = self.predict_numbers(prev_numbers, prediction_count)
            
            # 计算命中数
            hit_count = len(set(predicted_numbers) & actual_numbers)
            hit_distribution[hit_count] += 1
            
            # 计算投资和回报
            investment = self.prize_structure['bet_cost']
            prize = self.prize_structure['prizes'].get(hit_count, 0)
            
            total_investment += investment
            total_return += prize
        
        # 计算ROI指标
        net_profit = total_return - total_investment
        roi_percentage = (net_profit / total_investment) * 100 if total_investment > 0 else 0
        
        print(f"  预测数字数量: {prediction_count}")
        print(f"  总投资: {total_investment}元")
        print(f"  总回报: {total_return}元")
        print(f"  净利润: {net_profit}元")
        print(f"  ROI: {roi_percentage:.2f}%")
        
        print(f"\n  命中分布:")
        for hit_count in sorted(hit_distribution.keys()):
            count = hit_distribution[hit_count]
            percentage = count / len(self.test_data) * 100
            prize = self.prize_structure['prizes'].get(hit_count, 0)
            print(f"    命中{hit_count}个: {count}次 ({percentage:.1f}%) - 单次奖金{prize}元")
        
        return {
            'strategy_name': strategy_name,
            'prediction_count': prediction_count,
            'total_periods': len(self.test_data),
            'total_investment': total_investment,
            'total_return': total_return,
            'net_profit': net_profit,
            'roi_percentage': roi_percentage,
            'hit_distribution': dict(hit_distribution)
        }
    
    def calculate_theoretical_roi(self):
        """计算理论随机ROI"""
        print(f"\n🎲 计算理论随机ROI")
        print("-" * 40)
        
        # 基于理论概率计算期望ROI
        total_numbers = 49
        drawn_numbers = 6
        
        def calculate_hit_probability(prediction_count, hit_count):
            """计算命中指定数量的概率"""
            if hit_count > min(prediction_count, drawn_numbers):
                return 0
            
            # 使用超几何分布
            from math import comb
            
            # 命中hit_count个的概率
            prob = (comb(drawn_numbers, hit_count) * 
                   comb(total_numbers - drawn_numbers, prediction_count - hit_count)) / \
                   comb(total_numbers, prediction_count)
            
            return prob
        
        theoretical_results = {}
        
        for pred_count in [1, 2, 3, 4, 5, 6]:
            expected_return = 0
            hit_probs = {}
            
            for hit_count in range(min(pred_count, drawn_numbers) + 1):
                prob = calculate_hit_probability(pred_count, hit_count)
                prize = self.prize_structure['prizes'].get(hit_count, 0)
                expected_return += prob * prize
                hit_probs[hit_count] = prob
            
            investment = self.prize_structure['bet_cost']
            expected_profit = expected_return - investment
            expected_roi = (expected_profit / investment) * 100
            
            theoretical_results[pred_count] = {
                'prediction_count': pred_count,
                'expected_return': expected_return,
                'expected_profit': expected_profit,
                'expected_roi': expected_roi,
                'hit_probabilities': hit_probs
            }
            
            print(f"  预测{pred_count}个数字:")
            print(f"    期望回报: {expected_return:.2f}元")
            print(f"    期望利润: {expected_profit:.2f}元")
            print(f"    期望ROI: {expected_roi:.2f}%")
        
        return theoretical_results
    
    def comprehensive_roi_analysis(self):
        """综合ROI分析"""
        print(f"\n📊 综合ROI分析")
        print("=" * 60)
        
        # 1. 马尔可夫策略ROI
        markov_results = {}
        for pred_count in [1, 2, 3, 4, 5, 6]:
            result = self.calculate_roi_for_strategy(f"马尔可夫{pred_count}数字", pred_count)
            markov_results[pred_count] = result
        
        # 2. 理论随机ROI
        theoretical_results = self.calculate_theoretical_roi()
        
        # 3. 对比分析
        print(f"\n📈 马尔可夫 vs 理论随机对比")
        print("=" * 60)
        
        comparison_results = {}
        
        for pred_count in [1, 2, 3, 4, 5, 6]:
            markov_roi = markov_results[pred_count]['roi_percentage']
            theoretical_roi = theoretical_results[pred_count]['expected_roi']
            advantage = markov_roi - theoretical_roi
            
            comparison_results[pred_count] = {
                'prediction_count': pred_count,
                'markov_roi': markov_roi,
                'theoretical_roi': theoretical_roi,
                'advantage': advantage
            }
            
            print(f"  预测{pred_count}个数字:")
            print(f"    马尔可夫ROI: {markov_roi:.2f}%")
            print(f"    理论随机ROI: {theoretical_roi:.2f}%")
            print(f"    优势: {advantage:+.2f}个百分点")
        
        # 4. 最优策略推荐
        best_markov = max(markov_results.items(), key=lambda x: x[1]['roi_percentage'])
        best_theoretical = max(theoretical_results.items(), key=lambda x: x[1]['expected_roi'])
        
        print(f"\n🎯 最优策略推荐")
        print("=" * 40)
        print(f"  最佳马尔可夫策略: 预测{best_markov[0]}个数字 (ROI: {best_markov[1]['roi_percentage']:.2f}%)")
        print(f"  最佳理论策略: 预测{best_theoretical[0]}个数字 (ROI: {best_theoretical[1]['expected_roi']:.2f}%)")
        
        return {
            'markov_results': markov_results,
            'theoretical_results': theoretical_results,
            'comparison_results': comparison_results,
            'best_strategies': {
                'markov': best_markov,
                'theoretical': best_theoretical
            }
        }
    
    def business_value_assessment(self, roi_analysis):
        """商业价值评估"""
        print(f"\n💼 商业价值评估")
        print("=" * 60)
        
        # 1. 盈利能力分析
        profitable_strategies = []
        for pred_count, result in roi_analysis['markov_results'].items():
            if result['roi_percentage'] > 0:
                profitable_strategies.append((pred_count, result['roi_percentage']))
        
        print(f"1. 盈利能力分析:")
        if profitable_strategies:
            print(f"  盈利策略数量: {len(profitable_strategies)}")
            for pred_count, roi in profitable_strategies:
                print(f"    预测{pred_count}个数字: ROI {roi:.2f}%")
        else:
            print(f"  ❌ 无盈利策略")
        
        # 2. 风险评估
        print(f"\n2. 风险评估:")
        best_strategy = roi_analysis['best_strategies']['markov']
        best_pred_count = best_strategy[0]
        best_result = best_strategy[1]
        
        # 计算最大损失
        max_loss_per_period = self.prize_structure['bet_cost']
        max_total_loss = max_loss_per_period * len(self.test_data)
        actual_loss = max(0, -best_result['net_profit'])
        
        print(f"  最大可能损失: {max_total_loss}元")
        print(f"  实际损失: {actual_loss}元")
        print(f"  损失比例: {actual_loss/max_total_loss:.1%}")
        
        # 3. 成本效益分析
        print(f"\n3. 成本效益分析:")
        
        # 系统维护成本（假设）
        monthly_maintenance_cost = 1000  # 每月1000元维护成本
        test_months = len(self.test_data) / 30  # 测试期间月数
        total_maintenance_cost = monthly_maintenance_cost * test_months
        
        net_business_value = best_result['net_profit'] - total_maintenance_cost
        
        print(f"  预测收益: {best_result['net_profit']:.0f}元")
        print(f"  系统维护成本: {total_maintenance_cost:.0f}元")
        print(f"  净商业价值: {net_business_value:.0f}元")
        
        # 4. 商业建议
        print(f"\n4. 商业建议:")
        if net_business_value > 0:
            print(f"  ✅ 建议部署：净价值为正")
        elif best_result['roi_percentage'] > 0:
            print(f"  ⚠️ 谨慎考虑：预测盈利但需考虑维护成本")
        else:
            print(f"  ❌ 不建议部署：预测亏损")
        
        return {
            'profitable_strategies': profitable_strategies,
            'risk_assessment': {
                'max_possible_loss': max_total_loss,
                'actual_loss': actual_loss,
                'loss_ratio': actual_loss/max_total_loss
            },
            'cost_benefit': {
                'prediction_profit': best_result['net_profit'],
                'maintenance_cost': total_maintenance_cost,
                'net_business_value': net_business_value
            },
            'recommendation': 'deploy' if net_business_value > 0 else 'caution' if best_result['roi_percentage'] > 0 else 'reject'
        }

def main():
    """主函数"""
    print("🎯 ROI导向的商业价值重评估系统")
    print("基于真实奖金结构的投资回报率分析")
    print("=" * 70)
    
    # 初始化分析器
    analyzer = ROIBusinessAnalyzer()
    
    # 1. 加载数据
    if not analyzer.load_data():
        return
    
    # 2. 构建模型
    analyzer.build_markov_model()
    
    # 3. 综合ROI分析
    roi_analysis = analyzer.comprehensive_roi_analysis()
    
    # 4. 商业价值评估
    business_assessment = analyzer.business_value_assessment(roi_analysis)
    
    # 5. 保存结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"ROI商业价值分析结果_{timestamp}.json"
    
    # 处理numpy类型
    def convert_numpy_types(obj):
        if isinstance(obj, dict):
            return {k: convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(v) for v in obj]
        elif hasattr(obj, 'item'):
            return obj.item()
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, (np.integer, int)):
            return int(obj)
        elif isinstance(obj, (np.floating, float)):
            return float(obj)
        else:
            return obj
    
    comprehensive_results = {
        'prize_structure': analyzer.prize_structure,
        'roi_analysis': convert_numpy_types(roi_analysis),
        'business_assessment': convert_numpy_types(business_assessment),
        'analysis_timestamp': datetime.now().isoformat()
    }
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(comprehensive_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ ROI分析结果已保存: {results_file}")
    
    # 6. 关键结论
    best_strategy = roi_analysis['best_strategies']['markov']
    recommendation = business_assessment['recommendation']
    
    print(f"\n🎉 ROI分析关键结论")
    print("=" * 50)
    print(f"✅ 最佳策略: 预测{best_strategy[0]}个数字")
    print(f"✅ 最佳ROI: {best_strategy[1]['roi_percentage']:.2f}%")
    print(f"✅ 净商业价值: {business_assessment['cost_benefit']['net_business_value']:.0f}元")
    print(f"✅ 商业建议: {recommendation}")
    
    print(f"\n💡 核心洞察:")
    if len(business_assessment['profitable_strategies']) > 0:
        print(f"  1. 发现{len(business_assessment['profitable_strategies'])}个盈利策略")
    else:
        print(f"  1. 所有策略都无法实现盈利")
    
    print(f"  2. 最大损失风险: {business_assessment['risk_assessment']['loss_ratio']:.1%}")
    print(f"  3. 系统维护成本对盈利能力有重要影响")
    print(f"  4. ROI分析比单纯命中率分析更能反映真实价值")

if __name__ == "__main__":
    main()
