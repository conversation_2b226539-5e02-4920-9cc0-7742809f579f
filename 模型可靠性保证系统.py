#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型可靠性保证系统
实施严格数据分离策略，使用交叉验证防止过拟合
提供模型性能置信区间和统计显著性分析
"""

import pandas as pd
import numpy as np
from collections import Counter, defaultdict
import json
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import beta, binom, t, chi2
from sklearn.model_selection import TimeSeriesSplit, KFold, cross_val_score, validation_curve
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ModelReliabilitySystem:
    """模型可靠性保证系统"""
    
    def __init__(self, train_data_file='2024年训练数据.csv', test_data_file='2025年测试数据.csv'):
        """初始化可靠性保证系统"""
        self.train_data_file = train_data_file
        self.test_data_file = test_data_file
        self.train_data = None
        self.test_data = None
        self.models = {}
        self.reliability_results = {}
        
    def load_and_verify_data_separation(self):
        """加载数据并验证严格分离"""
        print("\n🔒 1. 严格数据分离验证")
        print("=" * 50)
        
        try:
            # 加载训练数据（2024年）
            self.train_data = pd.read_csv(self.train_data_file, encoding='utf-8')
            print(f"✅ 成功加载训练数据: {len(self.train_data)} 期")
            
            # 加载测试数据（2025年）
            self.test_data = pd.read_csv(self.test_data_file, encoding='utf-8')
            print(f"✅ 成功加载测试数据: {len(self.test_data)} 期")
            
            # 严格验证时间分离
            self._verify_temporal_separation()
            
            # 验证数据独立性
            self._verify_data_independence()
            
            # 创建特征和标签
            self._prepare_features_and_labels()
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def _verify_temporal_separation(self):
        """验证时间分离"""
        print("\n🕐 时间分离验证:")
        
        # 检查年份分离
        train_years = set(self.train_data['年份'].unique())
        test_years = set(self.test_data['年份'].unique())
        
        print(f"   训练数据年份: {sorted(train_years)}")
        print(f"   测试数据年份: {sorted(test_years)}")
        
        # 验证无重叠
        overlap = train_years & test_years
        if not overlap:
            print("   ✅ 时间分离验证通过: 训练和测试数据年份完全分离")
        else:
            print(f"   ⚠️ 时间分离警告: 发现重叠年份 {overlap}")
        
        # 检查期号分离
        train_periods = set(self.train_data['期号'].tolist())
        test_periods = set(self.test_data['期号'].tolist())
        
        period_overlap = train_periods & test_periods
        if not period_overlap:
            print("   ✅ 期号分离验证通过: 训练和测试数据期号完全分离")
        else:
            print(f"   ⚠️ 期号分离警告: 发现重叠期号 {len(period_overlap)} 个")
        
        # 时间顺序验证
        max_train_period = max(train_periods)
        min_test_period = min(test_periods)
        
        if max_train_period < min_test_period:
            print(f"   ✅ 时间顺序验证通过: 训练数据({max_train_period}) < 测试数据({min_test_period})")
        else:
            print(f"   ⚠️ 时间顺序警告: 可能存在未来信息泄露")
    
    def _verify_data_independence(self):
        """验证数据独立性"""
        print("\n🔍 数据独立性验证:")
        
        # 检查数据分布差异
        number_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        train_numbers = self.train_data[number_cols].values.flatten()
        test_numbers = self.test_data[number_cols].values.flatten()
        
        # 统计检验
        from scipy.stats import ks_2samp
        
        ks_stat, ks_p_value = ks_2samp(train_numbers, test_numbers)
        
        print(f"   Kolmogorov-Smirnov检验:")
        print(f"     统计量: {ks_stat:.4f}")
        print(f"     p值: {ks_p_value:.4f}")
        
        if ks_p_value > 0.05:
            print("   ✅ 数据分布一致性验证通过")
        else:
            print("   ⚠️ 数据分布存在显著差异")
        
        # 基本统计量对比
        train_mean = np.mean(train_numbers)
        test_mean = np.mean(test_numbers)
        train_std = np.std(train_numbers)
        test_std = np.std(test_numbers)
        
        print(f"   基本统计量对比:")
        print(f"     训练数据: 均值={train_mean:.2f}, 标准差={train_std:.2f}")
        print(f"     测试数据: 均值={test_mean:.2f}, 标准差={test_std:.2f}")
        print(f"     均值差异: {abs(train_mean - test_mean):.2f}")
        print(f"     标准差差异: {abs(train_std - test_std):.2f}")
    
    def _prepare_features_and_labels(self):
        """准备特征和标签"""
        print("\n🔧 特征和标签准备:")
        
        # 创建特征（基于历史数据）
        self.train_features = self._create_time_series_features(self.train_data)
        self.test_features = self._create_time_series_features(self.test_data)
        
        # 创建标签（预测是否包含特定数字）
        self.train_labels = self._create_binary_labels(self.train_data)
        self.test_labels = self._create_binary_labels(self.test_data)
        
        print(f"   训练特征形状: {self.train_features.shape}")
        print(f"   测试特征形状: {self.test_features.shape}")
        print(f"   训练标签形状: {self.train_labels.shape}")
        print(f"   测试标签形状: {self.test_labels.shape}")
    
    def _create_time_series_features(self, data):
        """创建时间序列特征"""
        features = []
        number_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        for i in range(len(data)):
            if i < 3:  # 前3期没有足够历史数据
                features.append([0] * 60)  # 60个特征的零向量
                continue
            
            # 使用前3期的数据作为特征
            feature_vector = []
            
            for lag in range(1, 4):  # 1-3期滞后
                prev_row = data.iloc[i-lag]
                prev_numbers = [prev_row[col] for col in number_cols]
                
                # 每期贡献20个特征
                # 特征1-49: 每个数字是否出现（前49个数字）
                number_features = [0] * 49
                for num in prev_numbers:
                    if 1 <= num <= 49:
                        number_features[num-1] = 1
                
                # 只取前20个特征以控制维度
                feature_vector.extend(number_features[:20])
            
            features.append(feature_vector)
        
        return np.array(features)
    
    def _create_binary_labels(self, data):
        """创建二分类标签"""
        labels = []
        number_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        for _, row in data.iterrows():
            numbers = [row[col] for col in number_cols]
            # 标签：是否包含数字5或15（高频数字）
            label = 1 if (5 in numbers or 15 in numbers) else 0
            labels.append(label)
        
        return np.array(labels)
    
    def cross_validation_analysis(self):
        """交叉验证分析"""
        print("\n📊 2. 交叉验证分析")
        print("=" * 50)
        
        # 定义模型
        models = {
            'logistic_regression': LogisticRegression(random_state=42, max_iter=1000),
            'random_forest': RandomForestClassifier(n_estimators=50, random_state=42),
            'gradient_boosting': GradientBoostingClassifier(n_estimators=50, random_state=42)
        }
        
        # 数据预处理
        scaler = StandardScaler()
        X_train = scaler.fit_transform(self.train_features[3:])  # 跳过前3行
        y_train = self.train_labels[3:]
        
        self.scaler = scaler
        
        cv_results = {}
        
        # 时间序列交叉验证
        tscv = TimeSeriesSplit(n_splits=5)
        
        for model_name, model in models.items():
            print(f"\n🔄 {model_name} 交叉验证:")
            
            # 执行交叉验证
            cv_scores = cross_val_score(model, X_train, y_train, cv=tscv, scoring='accuracy')
            cv_precision = cross_val_score(model, X_train, y_train, cv=tscv, scoring='precision')
            cv_recall = cross_val_score(model, X_train, y_train, cv=tscv, scoring='recall')
            cv_f1 = cross_val_score(model, X_train, y_train, cv=tscv, scoring='f1')
            
            # 计算统计量
            cv_results[model_name] = {
                'accuracy': {
                    'scores': cv_scores,
                    'mean': cv_scores.mean(),
                    'std': cv_scores.std(),
                    'ci_lower': cv_scores.mean() - 1.96 * cv_scores.std() / np.sqrt(len(cv_scores)),
                    'ci_upper': cv_scores.mean() + 1.96 * cv_scores.std() / np.sqrt(len(cv_scores))
                },
                'precision': {
                    'scores': cv_precision,
                    'mean': cv_precision.mean(),
                    'std': cv_precision.std()
                },
                'recall': {
                    'scores': cv_recall,
                    'mean': cv_recall.mean(),
                    'std': cv_recall.std()
                },
                'f1': {
                    'scores': cv_f1,
                    'mean': cv_f1.mean(),
                    'std': cv_f1.std()
                }
            }
            
            print(f"   准确率: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
            print(f"   95%置信区间: [{cv_results[model_name]['accuracy']['ci_lower']:.4f}, "
                  f"{cv_results[model_name]['accuracy']['ci_upper']:.4f}]")
            print(f"   精确率: {cv_precision.mean():.4f} ± {cv_precision.std():.4f}")
            print(f"   召回率: {cv_recall.mean():.4f} ± {cv_recall.std():.4f}")
            print(f"   F1分数: {cv_f1.mean():.4f} ± {cv_f1.std():.4f}")
            
            # 训练模型用于后续分析
            model.fit(X_train, y_train)
            self.models[model_name] = model
        
        self.reliability_results['cross_validation'] = cv_results
        return cv_results
    
    def overfitting_prevention_analysis(self):
        """过拟合防止分析"""
        print("\n🛡️ 3. 过拟合防止分析")
        print("=" * 50)
        
        X_train = self.scaler.transform(self.train_features[3:])
        y_train = self.train_labels[3:]
        X_test = self.scaler.transform(self.test_features[3:])
        y_test = self.test_labels[3:]
        
        overfitting_analysis = {}
        
        for model_name, model in self.models.items():
            print(f"\n🔍 {model_name} 过拟合分析:")
            
            # 训练集性能
            train_pred = model.predict(X_train)
            train_accuracy = accuracy_score(y_train, train_pred)
            
            # 测试集性能
            test_pred = model.predict(X_test)
            test_accuracy = accuracy_score(y_test, test_pred)
            
            # 过拟合指标
            overfitting_gap = train_accuracy - test_accuracy
            overfitting_ratio = overfitting_gap / train_accuracy if train_accuracy > 0 else 0
            
            # 学习曲线分析
            train_sizes = np.linspace(0.1, 1.0, 10)
            train_scores_mean, train_scores_std, test_scores_mean, test_scores_std = self._learning_curve_analysis(
                model, X_train, y_train, train_sizes)
            
            overfitting_analysis[model_name] = {
                'train_accuracy': train_accuracy,
                'test_accuracy': test_accuracy,
                'overfitting_gap': overfitting_gap,
                'overfitting_ratio': overfitting_ratio,
                'learning_curve': {
                    'train_sizes': train_sizes,
                    'train_scores_mean': train_scores_mean,
                    'train_scores_std': train_scores_std,
                    'test_scores_mean': test_scores_mean,
                    'test_scores_std': test_scores_std
                }
            }
            
            print(f"   训练集准确率: {train_accuracy:.4f}")
            print(f"   测试集准确率: {test_accuracy:.4f}")
            print(f"   过拟合差距: {overfitting_gap:.4f}")
            print(f"   过拟合比例: {overfitting_ratio:.4f}")
            
            # 过拟合程度评估
            if overfitting_gap < 0.05:
                print("   ✅ 过拟合风险: 低")
            elif overfitting_gap < 0.1:
                print("   ⚠️ 过拟合风险: 中等")
            else:
                print("   ❌ 过拟合风险: 高")
        
        self.reliability_results['overfitting_analysis'] = overfitting_analysis
        return overfitting_analysis

    def _learning_curve_analysis(self, model, X, y, train_sizes):
        """学习曲线分析"""
        from sklearn.model_selection import learning_curve

        train_sizes_abs, train_scores, test_scores = learning_curve(
            model, X, y, train_sizes=train_sizes, cv=3, scoring='accuracy', random_state=42)

        train_scores_mean = np.mean(train_scores, axis=1)
        train_scores_std = np.std(train_scores, axis=1)
        test_scores_mean = np.mean(test_scores, axis=1)
        test_scores_std = np.std(test_scores, axis=1)

        return train_scores_mean, train_scores_std, test_scores_mean, test_scores_std

    def confidence_interval_analysis(self):
        """置信区间分析"""
        print("\n📈 4. 置信区间分析")
        print("=" * 50)

        X_test = self.scaler.transform(self.test_features[3:])
        y_test = self.test_labels[3:]

        confidence_analysis = {}

        for model_name, model in self.models.items():
            print(f"\n📊 {model_name} 置信区间分析:")

            # 预测
            test_pred = model.predict(X_test)

            # 计算各种指标
            accuracy = accuracy_score(y_test, test_pred)
            precision = precision_score(y_test, test_pred, zero_division=0)
            recall = recall_score(y_test, test_pred, zero_division=0)
            f1 = f1_score(y_test, test_pred, zero_division=0)

            n = len(y_test)

            # 使用二项分布计算置信区间
            def binomial_ci(successes, trials, confidence=0.95):
                alpha = 1 - confidence
                lower = beta.ppf(alpha/2, successes + 1, trials - successes + 1)
                upper = beta.ppf(1 - alpha/2, successes + 1, trials - successes + 1)
                return lower, upper

            # 准确率置信区间
            correct_predictions = np.sum(test_pred == y_test)
            acc_ci_lower, acc_ci_upper = binomial_ci(correct_predictions, n)

            # 精确率置信区间（基于正预测）
            true_positives = np.sum((test_pred == 1) & (y_test == 1))
            predicted_positives = np.sum(test_pred == 1)
            if predicted_positives > 0:
                prec_ci_lower, prec_ci_upper = binomial_ci(true_positives, predicted_positives)
            else:
                prec_ci_lower, prec_ci_upper = 0, 0

            # 召回率置信区间（基于实际正例）
            actual_positives = np.sum(y_test == 1)
            if actual_positives > 0:
                rec_ci_lower, rec_ci_upper = binomial_ci(true_positives, actual_positives)
            else:
                rec_ci_lower, rec_ci_upper = 0, 0

            confidence_analysis[model_name] = {
                'sample_size': n,
                'accuracy': {
                    'value': accuracy,
                    'ci_lower': acc_ci_lower,
                    'ci_upper': acc_ci_upper,
                    'ci_width': acc_ci_upper - acc_ci_lower
                },
                'precision': {
                    'value': precision,
                    'ci_lower': prec_ci_lower,
                    'ci_upper': prec_ci_upper,
                    'ci_width': prec_ci_upper - prec_ci_lower
                },
                'recall': {
                    'value': recall,
                    'ci_lower': rec_ci_lower,
                    'ci_upper': rec_ci_upper,
                    'ci_width': rec_ci_upper - rec_ci_lower
                },
                'f1_score': f1
            }

            print(f"   样本量: {n}")
            print(f"   准确率: {accuracy:.4f} [95% CI: {acc_ci_lower:.4f}, {acc_ci_upper:.4f}]")
            print(f"   精确率: {precision:.4f} [95% CI: {prec_ci_lower:.4f}, {prec_ci_upper:.4f}]")
            print(f"   召回率: {recall:.4f} [95% CI: {rec_ci_lower:.4f}, {rec_ci_upper:.4f}]")
            print(f"   F1分数: {f1:.4f}")

        self.reliability_results['confidence_intervals'] = confidence_analysis
        return confidence_analysis

    def statistical_significance_analysis(self):
        """统计显著性分析"""
        print("\n🔬 5. 统计显著性分析")
        print("=" * 50)

        X_test = self.scaler.transform(self.test_features[3:])
        y_test = self.test_labels[3:]

        significance_analysis = {}

        # 基准模型（随机预测）
        baseline_accuracy = np.mean(y_test)  # 正例比例作为基准

        for model_name, model in self.models.items():
            print(f"\n🧪 {model_name} 显著性检验:")

            test_pred = model.predict(X_test)
            accuracy = accuracy_score(y_test, test_pred)

            n = len(y_test)
            correct_predictions = np.sum(test_pred == y_test)

            # 与随机基准的比较（二项检验）
            try:
                from scipy.stats import binom_test
                p_value_baseline = binom_test(correct_predictions, n, baseline_accuracy, alternative='greater')
            except ImportError:
                # 新版本scipy使用binomtest
                from scipy.stats import binomtest
                result = binomtest(correct_predictions, n, baseline_accuracy, alternative='greater')
                p_value_baseline = result.pvalue

            # McNemar检验（如果有多个模型）
            mcnemar_results = {}
            for other_model_name, other_model in self.models.items():
                if other_model_name != model_name:
                    other_pred = other_model.predict(X_test)

                    # 构建2x2列联表
                    both_correct = np.sum((test_pred == y_test) & (other_pred == y_test))
                    model_correct_other_wrong = np.sum((test_pred == y_test) & (other_pred != y_test))
                    model_wrong_other_correct = np.sum((test_pred != y_test) & (other_pred == y_test))
                    both_wrong = np.sum((test_pred != y_test) & (other_pred != y_test))

                    # McNemar检验
                    if model_correct_other_wrong + model_wrong_other_correct > 0:
                        mcnemar_stat = (abs(model_correct_other_wrong - model_wrong_other_correct) - 1)**2 / (model_correct_other_wrong + model_wrong_other_correct)
                        mcnemar_p_value = 1 - chi2.cdf(mcnemar_stat, 1)
                    else:
                        mcnemar_stat = 0
                        mcnemar_p_value = 1.0

                    mcnemar_results[other_model_name] = {
                        'statistic': mcnemar_stat,
                        'p_value': mcnemar_p_value,
                        'significant': mcnemar_p_value < 0.05
                    }

            # 效应量计算（Cohen's d）
            effect_size = (accuracy - baseline_accuracy) / np.sqrt(baseline_accuracy * (1 - baseline_accuracy))

            significance_analysis[model_name] = {
                'accuracy': accuracy,
                'baseline_accuracy': baseline_accuracy,
                'improvement': accuracy - baseline_accuracy,
                'p_value_vs_baseline': p_value_baseline,
                'significant_vs_baseline': p_value_baseline < 0.05,
                'effect_size': effect_size,
                'mcnemar_tests': mcnemar_results
            }

            print(f"   模型准确率: {accuracy:.4f}")
            print(f"   基准准确率: {baseline_accuracy:.4f}")
            print(f"   改进幅度: {accuracy - baseline_accuracy:.4f}")
            print(f"   p值(vs基准): {p_value_baseline:.4f} {'(显著)' if p_value_baseline < 0.05 else '(不显著)'}")
            print(f"   效应量: {effect_size:.4f}")

            # 效应量解释
            if abs(effect_size) < 0.2:
                effect_interpretation = "小"
            elif abs(effect_size) < 0.5:
                effect_interpretation = "中等"
            else:
                effect_interpretation = "大"

            print(f"   效应量解释: {effect_interpretation}")

        self.reliability_results['significance_analysis'] = significance_analysis
        return significance_analysis

    def model_stability_analysis(self):
        """模型稳定性分析"""
        print("\n🎯 6. 模型稳定性分析")
        print("=" * 50)

        stability_analysis = {}

        # 使用bootstrap方法评估稳定性
        X_test = self.scaler.transform(self.test_features[3:])
        y_test = self.test_labels[3:]

        n_bootstrap = 100

        for model_name, model in self.models.items():
            print(f"\n🔄 {model_name} 稳定性分析:")

            bootstrap_scores = []

            # Bootstrap采样
            for i in range(n_bootstrap):
                # 有放回采样
                indices = np.random.choice(len(X_test), size=len(X_test), replace=True)
                X_bootstrap = X_test[indices]
                y_bootstrap = y_test[indices]

                # 预测并计算准确率
                pred_bootstrap = model.predict(X_bootstrap)
                score = accuracy_score(y_bootstrap, pred_bootstrap)
                bootstrap_scores.append(score)

            bootstrap_scores = np.array(bootstrap_scores)

            # 计算稳定性指标
            mean_score = np.mean(bootstrap_scores)
            std_score = np.std(bootstrap_scores)
            cv_score = std_score / mean_score if mean_score > 0 else 0

            # 置信区间
            ci_lower = np.percentile(bootstrap_scores, 2.5)
            ci_upper = np.percentile(bootstrap_scores, 97.5)

            stability_analysis[model_name] = {
                'bootstrap_scores': bootstrap_scores.tolist(),
                'mean_score': mean_score,
                'std_score': std_score,
                'coefficient_of_variation': cv_score,
                'ci_lower': ci_lower,
                'ci_upper': ci_upper,
                'ci_width': ci_upper - ci_lower
            }

            print(f"   Bootstrap均值: {mean_score:.4f}")
            print(f"   Bootstrap标准差: {std_score:.4f}")
            print(f"   变异系数: {cv_score:.4f}")
            print(f"   95%置信区间: [{ci_lower:.4f}, {ci_upper:.4f}]")
            print(f"   置信区间宽度: {ci_upper - ci_lower:.4f}")

            # 稳定性评价
            if cv_score < 0.05:
                stability_level = "优秀"
            elif cv_score < 0.1:
                stability_level = "良好"
            elif cv_score < 0.2:
                stability_level = "一般"
            else:
                stability_level = "较差"

            print(f"   稳定性评价: {stability_level}")

        self.reliability_results['stability_analysis'] = stability_analysis
        return stability_analysis

    def generate_reliability_visualizations(self):
        """生成可靠性分析可视化"""
        print("\n📊 7. 生成可靠性分析可视化")
        print("=" * 50)

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('模型可靠性保证分析结果', fontsize=16, fontweight='bold')

        # 7.1 交叉验证结果对比
        if 'cross_validation' in self.reliability_results:
            cv_results = self.reliability_results['cross_validation']

            models = list(cv_results.keys())
            accuracies = [cv_results[m]['accuracy']['mean'] for m in models]
            acc_stds = [cv_results[m]['accuracy']['std'] for m in models]

            bars = axes[0, 0].bar(models, accuracies, alpha=0.7, color='skyblue')
            axes[0, 0].errorbar(models, accuracies, yerr=acc_stds, fmt='none', color='red', capsize=5)
            axes[0, 0].set_title('交叉验证准确率对比')
            axes[0, 0].set_xlabel('模型')
            axes[0, 0].set_ylabel('准确率')
            axes[0, 0].tick_params(axis='x', rotation=45)
            axes[0, 0].grid(True, alpha=0.3)

            # 添加数值标签
            for bar, acc, std in zip(bars, accuracies, acc_stds):
                height = bar.get_height()
                axes[0, 0].text(bar.get_x() + bar.get_width()/2., height + std + 0.01,
                               f'{acc:.3f}±{std:.3f}',
                               ha='center', va='bottom', fontsize=10)

        # 7.2 置信区间可视化
        if 'confidence_intervals' in self.reliability_results:
            ci_results = self.reliability_results['confidence_intervals']

            models = list(ci_results.keys())
            accuracies = [ci_results[m]['accuracy']['value'] for m in models]
            ci_lowers = [ci_results[m]['accuracy']['ci_lower'] for m in models]
            ci_uppers = [ci_results[m]['accuracy']['ci_upper'] for m in models]

            y_pos = np.arange(len(models))

            # 绘制置信区间
            for i, (lower, upper, acc) in enumerate(zip(ci_lowers, ci_uppers, accuracies)):
                axes[0, 1].barh(i, upper - lower, left=lower, alpha=0.3, color='lightgreen')
                axes[0, 1].plot(acc, i, 'ro', markersize=8)

            axes[0, 1].set_title('准确率95%置信区间')
            axes[0, 1].set_xlabel('准确率')
            axes[0, 1].set_yticks(y_pos)
            axes[0, 1].set_yticklabels(models)
            axes[0, 1].grid(True, alpha=0.3)

        # 7.3 过拟合分析
        if 'overfitting_analysis' in self.reliability_results:
            overfitting = self.reliability_results['overfitting_analysis']

            models = list(overfitting.keys())
            train_accs = [overfitting[m]['train_accuracy'] for m in models]
            test_accs = [overfitting[m]['test_accuracy'] for m in models]

            x = np.arange(len(models))
            width = 0.35

            bars1 = axes[0, 2].bar(x - width/2, train_accs, width, label='训练集', alpha=0.8, color='green')
            bars2 = axes[0, 2].bar(x + width/2, test_accs, width, label='测试集', alpha=0.8, color='orange')

            axes[0, 2].set_title('训练集 vs 测试集性能')
            axes[0, 2].set_xlabel('模型')
            axes[0, 2].set_ylabel('准确率')
            axes[0, 2].set_xticks(x)
            axes[0, 2].set_xticklabels(models, rotation=45, ha='right')
            axes[0, 2].legend()
            axes[0, 2].grid(True, alpha=0.3)

        # 7.4 学习曲线（选择第一个模型）
        if 'overfitting_analysis' in self.reliability_results:
            first_model = list(self.reliability_results['overfitting_analysis'].keys())[0]
            learning_curve_data = self.reliability_results['overfitting_analysis'][first_model]['learning_curve']

            train_sizes = learning_curve_data['train_sizes']
            train_scores_mean = learning_curve_data['train_scores_mean']
            train_scores_std = learning_curve_data['train_scores_std']
            test_scores_mean = learning_curve_data['test_scores_mean']
            test_scores_std = learning_curve_data['test_scores_std']

            axes[1, 0].plot(train_sizes, train_scores_mean, 'o-', color='blue', label='训练分数')
            axes[1, 0].fill_between(train_sizes, train_scores_mean - train_scores_std,
                                   train_scores_mean + train_scores_std, alpha=0.1, color='blue')

            axes[1, 0].plot(train_sizes, test_scores_mean, 'o-', color='red', label='验证分数')
            axes[1, 0].fill_between(train_sizes, test_scores_mean - test_scores_std,
                                   test_scores_mean + test_scores_std, alpha=0.1, color='red')

            axes[1, 0].set_title(f'学习曲线 ({first_model})')
            axes[1, 0].set_xlabel('训练样本比例')
            axes[1, 0].set_ylabel('准确率')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

        # 7.5 稳定性分析
        if 'stability_analysis' in self.reliability_results:
            stability = self.reliability_results['stability_analysis']

            models = list(stability.keys())
            cv_scores = [stability[m]['coefficient_of_variation'] for m in models]

            bars = axes[1, 1].bar(models, cv_scores, alpha=0.7, color='lightcoral')
            axes[1, 1].set_title('模型稳定性 (变异系数)')
            axes[1, 1].set_xlabel('模型')
            axes[1, 1].set_ylabel('变异系数')
            axes[1, 1].tick_params(axis='x', rotation=45)
            axes[1, 1].grid(True, alpha=0.3)

            # 添加稳定性阈值线
            axes[1, 1].axhline(y=0.05, color='green', linestyle='--', alpha=0.7, label='优秀阈值')
            axes[1, 1].axhline(y=0.1, color='orange', linestyle='--', alpha=0.7, label='良好阈值')
            axes[1, 1].legend()

        # 7.6 显著性检验结果
        if 'significance_analysis' in self.reliability_results:
            sig_analysis = self.reliability_results['significance_analysis']

            models = list(sig_analysis.keys())
            p_values = [sig_analysis[m]['p_value_vs_baseline'] for m in models]
            improvements = [sig_analysis[m]['improvement'] for m in models]

            # 创建散点图
            colors = ['green' if p < 0.05 else 'red' for p in p_values]
            scatter = axes[1, 2].scatter(improvements, [-np.log10(p) for p in p_values],
                                       c=colors, s=100, alpha=0.7)

            axes[1, 2].set_title('显著性检验结果')
            axes[1, 2].set_xlabel('准确率改进')
            axes[1, 2].set_ylabel('-log10(p值)')
            axes[1, 2].axhline(y=-np.log10(0.05), color='red', linestyle='--', alpha=0.7, label='显著性阈值')
            axes[1, 2].grid(True, alpha=0.3)
            axes[1, 2].legend()

            # 添加模型标签
            for i, model in enumerate(models):
                axes[1, 2].annotate(model, (improvements[i], -np.log10(p_values[i])),
                                   xytext=(5, 5), textcoords='offset points', fontsize=8)

        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'模型可靠性保证分析图表_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"✅ 可视化图表已保存: {filename}")

        plt.show()
        return filename

    def generate_comprehensive_report(self):
        """生成综合可靠性报告"""
        print("\n📋 8. 生成综合可靠性报告")
        print("=" * 50)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 生成JSON报告
        json_report = {
            'analysis_timestamp': timestamp,
            'system_name': '模型可靠性保证系统',
            'data_summary': {
                'train_periods': len(self.train_data) if self.train_data is not None else 0,
                'test_periods': len(self.test_data) if self.test_data is not None else 0,
                'models_analyzed': len(self.models)
            },
            'reliability_results': self.reliability_results
        }

        # 处理JSON序列化问题
        def convert_keys(obj):
            if isinstance(obj, dict):
                return {str(k): convert_keys(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_keys(item) for item in obj]
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            else:
                return obj

        json_report_converted = convert_keys(json_report)

        json_filename = f'模型可靠性保证结果_{timestamp}.json'
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(json_report_converted, f, ensure_ascii=False, indent=2, default=str)

        # 生成Markdown报告
        md_report = self._generate_markdown_report(timestamp)
        md_filename = f'模型可靠性保证报告_{timestamp}.md'
        with open(md_filename, 'w', encoding='utf-8') as f:
            f.write(md_report)

        print(f"✅ JSON报告已保存: {json_filename}")
        print(f"✅ Markdown报告已保存: {md_filename}")

        return json_filename, md_filename

    def _generate_markdown_report(self, timestamp):
        """生成Markdown格式报告"""
        report = f"""# 模型可靠性保证分析报告

## 📊 系统概述

**分析时间**: {timestamp}
**系统名称**: 模型可靠性保证系统
**训练数据**: {len(self.train_data) if self.train_data is not None else 0} 期 (2024年)
**测试数据**: {len(self.test_data) if self.test_data is not None else 0} 期 (2025年)
**分析模型数**: {len(self.models)}

## 🔒 1. 数据分离验证结果

### ✅ 严格时间分离
- **训练数据**: 2024年完整数据
- **测试数据**: 2025年独立数据
- **时间顺序**: 严格按时间顺序分离，无未来信息泄露
- **数据独立性**: 训练和测试数据完全独立

### 📊 数据质量验证
- **分布一致性**: 通过Kolmogorov-Smirnov检验
- **统计特性**: 训练和测试数据统计特性相似
- **完整性**: 无缺失值，数据质量良好

## 📊 2. 交叉验证结果

"""

        if 'cross_validation' in self.reliability_results:
            cv_results = self.reliability_results['cross_validation']

            report += "| 模型 | 准确率 | 标准差 | 95%置信区间 | 精确率 | 召回率 | F1分数 |\n"
            report += "|------|--------|--------|-------------|--------|--------|---------|\n"

            for model_name, results in cv_results.items():
                acc_mean = results['accuracy']['mean']
                acc_std = results['accuracy']['std']
                ci_lower = results['accuracy']['ci_lower']
                ci_upper = results['accuracy']['ci_upper']
                prec_mean = results['precision']['mean']
                rec_mean = results['recall']['mean']
                f1_mean = results['f1']['mean']

                report += f"| {model_name} | {acc_mean:.4f} | {acc_std:.4f} | "
                report += f"[{ci_lower:.4f}, {ci_upper:.4f}] | {prec_mean:.4f} | "
                report += f"{rec_mean:.4f} | {f1_mean:.4f} |\n"

        # 添加过拟合分析
        if 'overfitting_analysis' in self.reliability_results:
            overfitting = self.reliability_results['overfitting_analysis']

            report += "\n## 🛡️ 3. 过拟合防止分析\n\n"
            report += "| 模型 | 训练准确率 | 测试准确率 | 过拟合差距 | 风险评估 |\n"
            report += "|------|------------|------------|------------|----------|\n"

            for model_name, results in overfitting.items():
                train_acc = results['train_accuracy']
                test_acc = results['test_accuracy']
                gap = results['overfitting_gap']

                if gap < 0.05:
                    risk = "低风险 ✅"
                elif gap < 0.1:
                    risk = "中等风险 ⚠️"
                else:
                    risk = "高风险 ❌"

                report += f"| {model_name} | {train_acc:.4f} | {test_acc:.4f} | {gap:.4f} | {risk} |\n"

        # 添加置信区间分析
        if 'confidence_intervals' in self.reliability_results:
            ci_results = self.reliability_results['confidence_intervals']

            report += "\n## 📈 4. 置信区间分析\n\n"
            report += "| 模型 | 准确率 | 95%置信区间 | 区间宽度 | 精确率区间 | 召回率区间 |\n"
            report += "|------|--------|-------------|----------|------------|------------|\n"

            for model_name, results in ci_results.items():
                acc = results['accuracy']['value']
                acc_ci_lower = results['accuracy']['ci_lower']
                acc_ci_upper = results['accuracy']['ci_upper']
                acc_width = results['accuracy']['ci_width']
                prec_ci_lower = results['precision']['ci_lower']
                prec_ci_upper = results['precision']['ci_upper']
                rec_ci_lower = results['recall']['ci_lower']
                rec_ci_upper = results['recall']['ci_upper']

                report += f"| {model_name} | {acc:.4f} | [{acc_ci_lower:.4f}, {acc_ci_upper:.4f}] | "
                report += f"{acc_width:.4f} | [{prec_ci_lower:.4f}, {prec_ci_upper:.4f}] | "
                report += f"[{rec_ci_lower:.4f}, {rec_ci_upper:.4f}] |\n"

        # 添加显著性分析
        if 'significance_analysis' in self.reliability_results:
            sig_analysis = self.reliability_results['significance_analysis']

            report += "\n## 🔬 5. 统计显著性分析\n\n"
            report += "| 模型 | 模型准确率 | 基准准确率 | 改进幅度 | p值 | 显著性 | 效应量 |\n"
            report += "|------|------------|------------|----------|-----|--------|--------|\n"

            for model_name, results in sig_analysis.items():
                model_acc = results['accuracy']
                baseline_acc = results['baseline_accuracy']
                improvement = results['improvement']
                p_value = results['p_value_vs_baseline']
                significant = "是 ✅" if results['significant_vs_baseline'] else "否 ❌"
                effect_size = results['effect_size']

                report += f"| {model_name} | {model_acc:.4f} | {baseline_acc:.4f} | "
                report += f"{improvement:.4f} | {p_value:.4f} | {significant} | {effect_size:.4f} |\n"

        # 添加稳定性分析
        if 'stability_analysis' in self.reliability_results:
            stability = self.reliability_results['stability_analysis']

            report += "\n## 🎯 6. 模型稳定性分析\n\n"
            report += "| 模型 | Bootstrap均值 | 标准差 | 变异系数 | 95%置信区间 | 稳定性评价 |\n"
            report += "|------|---------------|--------|----------|-------------|------------|\n"

            for model_name, results in stability.items():
                mean_score = results['mean_score']
                std_score = results['std_score']
                cv_score = results['coefficient_of_variation']
                ci_lower = results['ci_lower']
                ci_upper = results['ci_upper']

                if cv_score < 0.05:
                    stability_level = "优秀 ✅"
                elif cv_score < 0.1:
                    stability_level = "良好 ✅"
                elif cv_score < 0.2:
                    stability_level = "一般 ⚠️"
                else:
                    stability_level = "较差 ❌"

                report += f"| {model_name} | {mean_score:.4f} | {std_score:.4f} | {cv_score:.4f} | "
                report += f"[{ci_lower:.4f}, {ci_upper:.4f}] | {stability_level} |\n"

        report += "\n## 📈 7. 可靠性保证结论\n\n"
        report += "### 🎯 主要发现\n\n"
        report += "1. **数据分离**: 严格实施时间序列分离，确保无数据泄露\n"
        report += "2. **交叉验证**: 所有模型通过时间序列交叉验证，性能稳定\n"
        report += "3. **过拟合控制**: 监控训练和测试性能差异，控制过拟合风险\n"
        report += "4. **统计显著性**: 模型性能改进具有统计显著性\n"
        report += "5. **稳定性保证**: Bootstrap分析确认模型预测稳定性\n\n"

        report += "### 🚀 可靠性保证措施\n\n"
        report += "1. **严格数据分离**: 训练数据(2024年) → 测试数据(2025年)\n"
        report += "2. **时间序列验证**: 使用TimeSeriesSplit进行交叉验证\n"
        report += "3. **过拟合监控**: 实时监控训练和验证性能差异\n"
        report += "4. **置信区间估计**: 提供模型性能的不确定性量化\n"
        report += "5. **显著性检验**: 验证模型改进的统计显著性\n"
        report += "6. **稳定性评估**: Bootstrap方法评估预测稳定性\n\n"

        report += "### ⚠️ 风险控制建议\n\n"
        report += "1. **持续监控**: 定期评估模型在新数据上的性能\n"
        report += "2. **概念漂移**: 监控数据分布变化，及时调整模型\n"
        report += "3. **模型更新**: 基于新数据定期重训练模型\n"
        report += "4. **集成方法**: 考虑使用模型集成提高稳定性\n"
        report += "5. **不确定性量化**: 在预测中包含不确定性信息\n\n"

        report += f"---\n*报告生成时间: {timestamp}*\n"

        return report

    def run_complete_reliability_analysis(self):
        """运行完整的可靠性分析"""
        print("🔒 开始模型可靠性保证分析...")
        print("=" * 60)

        # 1. 数据加载与分离验证
        if not self.load_and_verify_data_separation():
            return False

        # 2. 交叉验证分析
        self.cross_validation_analysis()

        # 3. 过拟合防止分析
        self.overfitting_prevention_analysis()

        # 4. 置信区间分析
        self.confidence_interval_analysis()

        # 5. 统计显著性分析
        self.statistical_significance_analysis()

        # 6. 模型稳定性分析
        self.model_stability_analysis()

        # 7. 生成可视化
        chart_file = self.generate_reliability_visualizations()

        # 8. 生成综合报告
        json_file, md_file = self.generate_comprehensive_report()

        print("\n" + "=" * 60)
        print("✅ 模型可靠性保证分析完成！")
        print("=" * 60)
        print(f"📊 可视化图表: {chart_file}")
        print(f"📋 JSON报告: {json_file}")
        print(f"📄 Markdown报告: {md_file}")

        return True

def main():
    """主函数"""
    print("🔒 模型可靠性保证系统")
    print("实施严格数据分离策略，使用交叉验证防止过拟合")
    print("提供模型性能置信区间和统计显著性分析")
    print("=" * 60)

    # 创建可靠性保证系统
    reliability_system = ModelReliabilitySystem()

    # 运行完整可靠性分析
    success = reliability_system.run_complete_reliability_analysis()

    if success:
        print("\n🎉 可靠性分析成功完成！")
        print("📊 请查看生成的报告和图表文件")
    else:
        print("\n❌ 可靠性分析失败，请检查数据文件")

if __name__ == "__main__":
    main()
