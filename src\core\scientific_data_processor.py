#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Scientific Data Processor for Lottery Prediction System
Conservative approach with scientific rigor and realistic expectations
"""

import pandas as pd
import numpy as np
import re
from typing import Tuple, Dict, List
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')


class ScientificDataProcessor:
    """
    Scientific data processor with conservative feature engineering
    
    Principles:
    1. Minimal feature engineering to avoid overfitting
    2. Strict train/test separation (no data leakage)
    3. Conservative feature selection
    4. Realistic baseline comparisons
    """
    
    def __init__(self, data_path: str = None):
        self.data_path = data_path or "data/processed/数据集.txt"
        self.raw_data = None
        self.processed_data = None
        self.feature_columns = []
        self.target_columns = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        # Scientific parameters
        self.train_end_period = 170  # Fixed train/test split
        self.test_start_period = 171
        self.max_lag_features = 2    # Conservative lag features
        self.max_rolling_window = 5  # Conservative rolling windows
    
    def load_and_validate_data(self) -> pd.DataFrame:
        """
        Load and validate lottery data with scientific rigor
        """
        print("📊 加载和验证数据...")

        try:
            # Parse the text format data
            self.raw_data = self._parse_lottery_text_file()

            if self.raw_data is None or len(self.raw_data) == 0:
                raise ValueError("无法加载数据文件或数据为空")

            # Validate data structure
            validation_results = self._validate_data_structure()

            if not validation_results['is_valid']:
                raise ValueError(f"数据验证失败: {validation_results['errors']}")

            print(f"  数据加载成功: {len(self.raw_data)} 期数据")
            print(f"  期号范围: {self.raw_data['期号'].min()} - {self.raw_data['期号'].max()}")

            return self.raw_data

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            raise

    def _parse_lottery_text_file(self) -> pd.DataFrame:
        """
        Parse the lottery text file format
        """
        data_rows = []

        try:
            with open(self.data_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Parse format: "180 期：14，37，28，43，01，41，特码是 29"
                match = re.match(r'(\d+)\s*期：(.+?)，特码是\s*(\d+)', line)
                if match:
                    period = int(match.group(1))
                    numbers_str = match.group(2)
                    special_code = int(match.group(3))

                    # Parse the 6 numbers
                    numbers = [int(x.strip()) for x in numbers_str.split('，')]

                    if len(numbers) == 6:
                        data_rows.append({
                            '期号': period,
                            '数字1': numbers[0],
                            '数字2': numbers[1],
                            '数字3': numbers[2],
                            '数字4': numbers[3],
                            '数字5': numbers[4],
                            '数字6': numbers[5],
                            '特码': special_code
                        })

            # Create DataFrame and sort by period
            df = pd.DataFrame(data_rows)
            df = df.sort_values('期号').reset_index(drop=True)

            print(f"  解析了 {len(df)} 期数据")
            return df

        except Exception as e:
            print(f"  解析数据文件失败: {e}")
            return None
    
    def _validate_data_structure(self) -> Dict:
        """
        Validate data structure and quality
        """
        validation = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }
        
        # Check required columns
        required_columns = ['期号'] + self.target_columns
        missing_columns = [col for col in required_columns if col not in self.raw_data.columns]
        
        if missing_columns:
            validation['is_valid'] = False
            validation['errors'].append(f"缺少必需列: {missing_columns}")
        
        # Check data types and ranges
        for col in self.target_columns:
            if col in self.raw_data.columns:
                # Check if values are in valid range (1-49)
                invalid_values = self.raw_data[
                    (self.raw_data[col] < 1) | (self.raw_data[col] > 49)
                ][col]
                
                if len(invalid_values) > 0:
                    validation['warnings'].append(
                        f"{col} 包含无效值: {invalid_values.tolist()}"
                    )
        
        # Check for duplicates
        duplicate_periods = self.raw_data['期号'].duplicated().sum()
        if duplicate_periods > 0:
            validation['warnings'].append(f"发现 {duplicate_periods} 个重复期号")
        
        # Calculate statistics
        validation['statistics'] = {
            'total_periods': len(self.raw_data),
            'missing_values': self.raw_data.isnull().sum().to_dict(),
            'period_range': (self.raw_data['期号'].min(), self.raw_data['期号'].max()),
            'data_completeness': 1 - self.raw_data.isnull().sum().sum() / self.raw_data.size
        }
        
        return validation
    
    def create_conservative_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Create conservative features to minimize overfitting risk
        """
        print("🔬 创建保守特征集...")
        
        features_df = data.copy()
        
        # Basic statistical features (always useful, low overfitting risk)
        features_df['和值'] = features_df[self.target_columns].sum(axis=1)
        features_df['平均值'] = features_df[self.target_columns].mean(axis=1)
        features_df['极差'] = features_df[self.target_columns].max(axis=1) - features_df[self.target_columns].min(axis=1)
        features_df['标准差'] = features_df[self.target_columns].std(axis=1)
        
        # Count-based features (interpretable and stable)
        features_df['奇数个数'] = features_df[self.target_columns].apply(
            lambda x: sum(num % 2 for num in x), axis=1
        )
        features_df['大数个数'] = features_df[self.target_columns].apply(
            lambda x: sum(num > 25 for num in x), axis=1
        )
        
        # Conservative lag features (only 1-2 lags to avoid overfitting)
        for lag in range(1, self.max_lag_features + 1):
            features_df[f'和值_lag_{lag}'] = features_df['和值'].shift(lag)
            features_df[f'奇数个数_lag_{lag}'] = features_df['奇数个数'].shift(lag)
        
        # Conservative rolling features (small windows only)
        for window in [3, 5]:
            if window <= self.max_rolling_window:
                features_df[f'和值_mean_{window}'] = features_df['和值'].rolling(window=window).mean()
                features_df[f'和值_std_{window}'] = features_df['和值'].rolling(window=window).std()
        
        # Remove rows with NaN values (due to lag and rolling features)
        features_df = features_df.dropna().reset_index(drop=True)
        
        # Store feature column names
        self.feature_columns = [col for col in features_df.columns 
                               if col not in ['期号'] + self.target_columns]
        
        print(f"  创建了 {len(self.feature_columns)} 个保守特征")
        print(f"  有效数据: {len(features_df)} 期")
        
        return features_df
    
    def create_strict_train_test_split(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Create strict train/test split with no data leakage
        """
        print("✂️ 创建严格的训练/测试分割...")
        
        # Strict temporal split
        train_data = data[data['期号'] <= self.train_end_period].copy()
        test_data = data[data['期号'] >= self.test_start_period].copy()
        
        print(f"  训练集: 第1-{self.train_end_period}期，共{len(train_data)}期")
        print(f"  测试集: 第{self.test_start_period}-180期，共{len(test_data)}期")
        
        # Validate split
        if len(train_data) == 0:
            raise ValueError("训练集为空")
        if len(test_data) == 0:
            raise ValueError("测试集为空")
        
        # Check for data leakage (no overlap in periods)
        train_periods = set(train_data['期号'])
        test_periods = set(test_data['期号'])
        overlap = train_periods & test_periods
        
        if overlap:
            raise ValueError(f"训练集和测试集存在重叠期号: {overlap}")
        
        return train_data, test_data
    
    def calculate_random_baselines(self, test_data: pd.DataFrame) -> Dict:
        """
        Calculate random baseline performance for comparison
        """
        print("🎲 计算随机基线性能...")
        
        baselines = {}
        
        for target_col in self.target_columns:
            actual_values = test_data[target_col].values
            
            # Random predictions from uniform distribution (1-49)
            random_predictions = np.random.randint(1, 50, size=len(actual_values))
            
            # Calculate metrics
            mse = np.mean((actual_values - random_predictions) ** 2)
            mae = np.mean(np.abs(actual_values - random_predictions))
            
            # Exact match probability (theoretical)
            exact_match_prob = 1/49
            
            # Any position match probability (empirical)
            any_matches = sum(1 for actual, pred in zip(actual_values, random_predictions) 
                            if actual == pred)
            any_match_rate = any_matches / len(actual_values)
            
            baselines[target_col] = {
                'random_mse': mse,
                'random_mae': mae,
                'theoretical_exact_match': exact_match_prob,
                'empirical_any_match': any_match_rate
            }
        
        # Overall baseline
        all_actual = test_data[self.target_columns].values.flatten()
        all_random = np.random.randint(1, 50, size=len(all_actual))
        
        baselines['overall'] = {
            'random_mse': np.mean((all_actual - all_random) ** 2),
            'random_mae': np.mean(np.abs(all_actual - all_random)),
            'any_match_rate': sum(1 for a, r in zip(all_actual, all_random) if a == r) / len(all_actual)
        }
        
        print(f"  随机基线整体MSE: {baselines['overall']['random_mse']:.2f}")
        print(f"  随机基线命中率: {baselines['overall']['any_match_rate']:.4f}")
        
        return baselines
    
    def get_processed_data(self) -> Dict:
        """
        Get complete processed data with scientific validation
        """
        print("🔬 执行科学数据处理流程...")
        
        # Step 1: Load and validate
        raw_data = self.load_and_validate_data()
        
        # Step 2: Conservative feature engineering
        processed_data = self.create_conservative_features(raw_data)
        
        # Step 3: Strict train/test split
        train_data, test_data = self.create_strict_train_test_split(processed_data)
        
        # Step 4: Calculate baselines
        random_baselines = self.calculate_random_baselines(test_data)
        
        # Store processed data
        self.processed_data = {
            'full_data': processed_data,
            'train_data': train_data,
            'test_data': test_data,
            'feature_columns': self.feature_columns,
            'target_columns': self.target_columns,
            'random_baselines': random_baselines,
            'data_info': {
                'train_periods': (train_data['期号'].min(), train_data['期号'].max()),
                'test_periods': (test_data['期号'].min(), test_data['期号'].max()),
                'n_features': len(self.feature_columns),
                'train_size': len(train_data),
                'test_size': len(test_data)
            }
        }
        
        print("✅ 科学数据处理完成")
        return self.processed_data
