{"analysis_timestamp": "20250815_211636", "system_name": "严格模型训练验证系统", "data_summary": {"train_periods": 366, "test_periods": 179, "feature_dimensions": 50, "models_trained": 3}, "validation_results": {"cross_validation": {"logistic_regression": {"scores": [0.7166666666666667, 0.6833333333333333, 0.7666666666666667, 0.9, 0.7166666666666667], "mean_score": 0.7566666666666666, "std_score": 0.07644896627453143}, "random_forest": {"scores": [0.8, 0.7666666666666667, 0.8333333333333334, 0.85, 0.7666666666666667], "mean_score": 0.8033333333333335, "std_score": 0.033993463423951875}, "gradient_boosting": {"scores": [0.6833333333333333, 0.7666666666666667, 0.6833333333333333, 0.85, 0.7666666666666667], "mean_score": 0.7500000000000001, "std_score": 0.06236095644623235}}, "independent_test": {"logistic_regression": {"accuracy": 0.8314606741573034, "precision": 0.16666666666666666, "recall": 0.16666666666666666, "f1_score": 0.16666666666666666, "predictions": [0, 1, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "probabilities": [0.03218297768444804, 0.5525089369324535, 0.8751470636309603, 0.20915894859156753, 0.03381789785478528, 0.7058766905800354, 0.2035685592516174, 0.7488837961336617, 0.1542917341945382, 0.01144719955398259, 0.2577695137275058, 0.25159784408660385, 0.11714997245007495, 0.031914467009743035, 0.15902837030090405, 0.022910212648220282, 0.03515230516584715, 0.037589073944760056, 0.24717452238220322, 0.05376379197179537, 0.22761171768630198, 0.38175907901035144, 0.09022846239679858, 0.7720203590887335, 0.07832773228537823, 0.04727101275118965, 0.7878214148694017, 0.30892581989621165, 0.22060645456384587, 0.12246300065320023, 0.05918696143802318, 0.1012485104513599, 0.06920498491473374, 0.05693104001487805, 0.016446587593994855, 0.04415430970187592, 0.2608855574083241, 0.30792905551964644, 0.039024790905727705, 0.21600071449045077, 0.11037041256479908, 0.28384748453669756, 0.00934821010248848, 0.6140517008118336, 0.5288279114236014, 0.15336607978026548, 0.7234853629320916, 0.06058489767844121, 0.03811371773549627, 0.26595056315731047, 0.30352765375989327, 0.05932613022725942, 0.1030158775145167, 0.03467453154323896, 0.09238799936416002, 0.024297944040918003, 0.4916405600502041, 0.028934468693694055, 0.12354210557896358, 0.06568895834795022, 0.12866028532019408, 0.10634931911102961, 0.01715279823506712, 0.21728852165473303, 0.023195783202782438, 0.174895974124522, 0.44371110464595936, 0.09688318328662546, 0.016867827120412102, 0.2605925650331092, 0.05670314294823723, 0.037013852957732296, 0.18952916713245152, 0.14503191816183417, 0.048852786124687096, 0.7710871615946291, 0.04139461844368787, 0.27189524083391764, 0.1025586043040973, 0.058817524595512746, 0.08575565545696016, 0.5959041027399609, 0.5926513321414875, 0.11075686907214079, 0.2596141720522494, 0.4504922769364971, 0.4417226323411631, 0.3687078758131414, 0.06670108252383322, 0.018934489431842564, 0.5247940721403429, 0.4835991953600299, 0.2364142039986477, 0.06822289573745485, 0.3219385868338105, 0.10125478401121328, 0.67099870734724, 0.027326451198140922, 0.03184586115688237, 0.08258713536353353, 0.01179321524621014, 0.13846555584802478, 0.27636633485670853, 0.0679664685075257, 0.1994425040429758, 0.5263321179793536, 0.4806763753898495, 0.044022672033741765, 0.14170210691987456, 0.2270731177977251, 0.22681321779543628, 0.32406460295083067, 0.18664314502532672, 0.1654964687893747, 0.10225296978496916, 0.07545294211294146, 0.16169791298724082, 0.05134681880849189, 0.013242807767394163, 0.20415370253032347, 0.06728307843803459, 0.4175620442166486, 0.02375687190581772, 0.09760261488371716, 0.1640662880944966, 0.8773906091398594, 0.22532486261268192, 0.3841161675055625, 0.07428450155247969, 0.15255281486457437, 0.08334759866587671, 0.5747432507881314, 0.2442936870988756, 0.014469283233332233, 0.02223874503292305, 0.0452841308702723, 0.1958973169382464, 0.09270868644336011, 0.043256622540532, 0.4882147344784473, 0.11110156944270574, 0.015644411340757548, 0.040440026065754406, 0.23244605553888717, 0.03471261694171869, 0.33786339948328203, 0.05943776227134096, 0.40450287397397255, 0.017718369223250836, 0.7701947898536298, 0.021361939218796994, 0.015223666227448512, 0.2040480974601907, 0.13980481513914342, 0.012580524401815958, 0.11152115709333997, 0.06277001803008112, 0.014306263532395695, 0.39661872109349555, 0.12751750864825118, 0.026478516182439526, 0.09760932573003663, 0.1847191341220452, 0.11820786225744769, 0.006447138219981267, 0.17367786411065828, 0.027031818092311838, 0.05315125998450573, 0.47800881305097914, 0.08281500544338696, 0.048085713102126316, 0.4456958868669972, 0.2528207891638244, 0.35974037307622114, 0.052071256122837285, 0.20814848270284528, 0.09124660510367691, 0.010641080715194848]}, "random_forest": {"accuracy": 0.8932584269662921, "precision": 0.0, "recall": 0.0, "f1_score": 0.0, "predictions": [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "probabilities": [0.3, 0.51, 0.47, 0.41, 0.35, 0.38, 0.32, 0.33, 0.31, 0.24, 0.28, 0.36, 0.4, 0.1, 0.17, 0.12, 0.05, 0.1, 0.13, 0.12, 0.41, 0.4, 0.18, 0.22, 0.22, 0.13, 0.31, 0.14, 0.15, 0.12, 0.2, 0.12, 0.11, 0.17, 0.06, 0.15, 0.24, 0.3, 0.14, 0.22, 0.17, 0.11, 0.06, 0.19, 0.25, 0.25, 0.46, 0.11, 0.14, 0.26, 0.25, 0.15, 0.25, 0.18, 0.3, 0.02, 0.39, 0.08, 0.28, 0.09, 0.25, 0.24, 0.09, 0.32, 0.07, 0.1, 0.24, 0.21, 0.05, 0.11, 0.12, 0.13, 0.17, 0.26, 0.09, 0.27, 0.19, 0.18, 0.06, 0.13, 0.15, 0.22, 0.29, 0.21, 0.27, 0.27, 0.22, 0.21, 0.11, 0.12, 0.16, 0.18, 0.28, 0.08, 0.14, 0.13, 0.25, 0.13, 0.05, 0.08, 0.08, 0.22, 0.16, 0.09, 0.13, 0.26, 0.31, 0.07, 0.13, 0.17, 0.17, 0.24, 0.22, 0.09, 0.13, 0.11, 0.22, 0.07, 0.16, 0.13, 0.11, 0.17, 0.2, 0.18, 0.1, 0.33, 0.16, 0.23, 0.12, 0.12, 0.2, 0.33, 0.32, 0.11, 0.12, 0.08, 0.32, 0.15, 0.2, 0.19, 0.17, 0.11, 0.2, 0.16, 0.07, 0.34, 0.14, 0.15, 0.08, 0.28, 0.11, 0.08, 0.12, 0.19, 0.07, 0.18, 0.06, 0.13, 0.19, 0.23, 0.14, 0.08, 0.14, 0.2, 0.17, 0.15, 0.1, 0.07, 0.14, 0.13, 0.2, 0.27, 0.21, 0.2, 0.11, 0.17, 0.09, 0.05]}, "gradient_boosting": {"accuracy": 0.8146067415730337, "precision": 0.058823529411764705, "recall": 0.05555555555555555, "f1_score": 0.05714285714285714, "predictions": [0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "probabilities": [0.008091133851802601, 0.03652695544056449, 0.1150723917100388, 0.053484058511636776, 0.7482814663947973, 0.930169091277494, 0.8201058602880522, 0.19933087186874088, 0.023819430940800874, 0.3517936856961476, 0.10648812997255575, 0.20645644131471444, 0.5896579680556241, 0.02527572730487934, 0.10833021061098343, 0.017529608811672356, 0.016039100506109637, 0.06913488174903666, 0.0631828177181681, 0.5692137226712058, 0.07141918946151758, 0.5970880234135427, 0.08106299573270963, 0.11856035807878507, 0.3311510615162244, 0.09953880398852231, 0.2919637377627445, 0.06239048215917412, 0.05628213105187019, 0.05123975041236195, 0.0336305554820826, 0.047945135127005836, 0.06725054494483867, 0.17584475735953006, 0.020411373935260796, 0.3983017364875618, 0.26578425180071596, 0.5584379856874485, 0.012961901833361199, 0.10493146405293689, 0.04193442066480131, 0.5368800989383439, 0.12948570620607627, 0.1625394982818878, 0.42278361885184357, 0.2760702905740006, 0.6534026485012413, 0.18488986477006364, 0.03390238583521874, 0.46148034097609203, 0.265961960788775, 0.07463733137486647, 0.11526661358156882, 0.14691435818745446, 0.16436541211075917, 0.06365433686456914, 0.17464392745178767, 0.07283227634979693, 0.3462514100141844, 0.04353002410872634, 0.42459067152272195, 0.083177026779933, 0.01824110085828112, 0.16881761448869811, 0.06377766064833258, 0.0429243182335594, 0.08733103359819784, 0.03803614491652342, 0.025162969550006617, 0.12899847182354587, 0.055158203558089584, 0.05186661269445935, 0.05146457617835936, 0.20784682242991095, 0.07686907085254638, 0.6917354236581721, 0.04638062457162649, 0.0770465364509293, 0.13980633309728074, 0.06849482150930132, 0.07039051801617544, 0.19102311231235655, 0.4075959435210727, 0.03559890535082786, 0.08056932721758586, 0.17750303391691916, 0.1099949568818209, 0.08233393675433157, 0.03599107269230958, 0.0888046569770076, 0.09824963257788122, 0.0915845046884659, 0.2697904216592838, 0.031249004847159558, 0.18276855268847794, 0.05963195373371074, 0.11113190051173896, 0.032764533402986865, 0.022890095281700003, 0.200346921047204, 0.25675994800748514, 0.0817456432054639, 0.21456835138546348, 0.05500112159005694, 0.04780190465981251, 0.3535741661638758, 0.6232981802567006, 0.05204358574879484, 0.22642846700698824, 0.0892320926594393, 0.040012225757923514, 0.4423174173667869, 0.10319394824212683, 0.1868252709316519, 0.18042705920135932, 0.08934948381420334, 0.19753735243895593, 0.13459293962735502, 0.26072695854350264, 0.5637070003529746, 0.14810253813334828, 0.20031246856168916, 0.09455582554390918, 0.11539952387915776, 0.049197751536204454, 0.6733138027079649, 0.27390571020775684, 0.21861364408056214, 0.42897635816809, 0.11433832617977559, 0.6221066907655025, 0.626032119491629, 0.556803620500278, 0.11419517544189112, 0.17156416438258282, 0.059181716730342275, 0.10965273282344609, 0.10421329868464058, 0.14864438339488176, 0.24303029040751165, 0.07612626628627639, 0.036054306673268595, 0.277408395424437, 0.10295881635968591, 0.023287214195996074, 0.4942493200556479, 0.12635392369034476, 0.15555524336750484, 0.562062340939769, 0.3670957309928067, 0.03232223685566092, 0.045793076796236495, 0.13392206372422613, 0.09345193724636491, 0.04336347218829254, 0.059999898273841175, 0.05335513869528401, 0.03412312356286041, 0.14923893494459087, 0.15600687862925827, 0.11045475917044695, 0.12069296017781683, 0.08935209293528905, 0.1072101709953501, 0.047947033579576595, 0.18699163007917244, 0.24144961529430817, 0.0583156562396641, 0.1263517474063743, 0.09611579960352891, 0.09315393252444885, 0.1280451514903286, 0.1650631775559218, 0.41964348658057976, 0.08641321816694986, 0.06304168433543125, 0.06856300305560488, 0.14506651001844126]}}, "overfitting_analysis": {"logistic_regression": {"train_accuracy": 0.8438356164383561, "test_accuracy": 0.8314606741573034, "overfitting_gap": 0.012374942281052737, "overfitting_ratio": 0.014665110170728081, "overfitting_level": "轻微"}, "random_forest": {"train_accuracy": 1.0, "test_accuracy": 0.8932584269662921, "overfitting_gap": 0.1067415730337079, "overfitting_ratio": 0.1067415730337079, "overfitting_level": "严重"}, "gradient_boosting": {"train_accuracy": 0.9643835616438357, "test_accuracy": 0.8146067415730337, "overfitting_gap": 0.14977682007080195, "overfitting_ratio": 0.1553083503575077, "overfitting_level": "严重"}}}, "models_info": {"logistic_regression": {"name": "逻辑回归", "train_accuracy": 0.8438356164383561}, "random_forest": {"name": "随机森林", "train_accuracy": 1.0}, "gradient_boosting": {"name": "梯度提升", "train_accuracy": 0.9643835616438357}}}