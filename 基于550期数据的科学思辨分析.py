#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于550期数据的科学思辨分析
结合2024-2025年数据规律分析报告，对预测系统进行深度思辨
"""

import pandas as pd
import numpy as np
from datetime import datetime
from collections import defaultdict, Counter
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class ComprehensiveAnalysisSystem:
    """基于550期数据的综合分析系统"""
    
    def __init__(self):
        # 基于报告的关键数据
        self.report_data = {
            # 频率数据
            'high_freq_numbers': [5, 15, 3, 40, 30],
            'low_freq_numbers': [41, 1, 8, 48, 47],
            'freq_difference': 0.47,  # 47%的频率差异
            
            # 统计特征
            'avg_sum': 149.88,
            'sum_std': 33.22,
            'sum_range': (51, 245),
            'avg_odd_count': 3.12,
            'avg_even_count': 2.88,
            'avg_span': 35.35,
            
            # 时间规律
            'monthly_variation': 23.4,
            'highest_month': ('2月', 159.7),
            'lowest_month': ('7月', 136.3),
            'year_trend': +2.09,  # 2025年比2024年高
            
            # 聚类模式
            'cluster_count': 4,
            'silhouette_score': 0.317,
            
            # 相关性
            'time_correlation': 0.037,  # 极弱时间相关性
            'position_independence': True
        }
        
        # 204期预测结果
        self.prediction_204 = {
            'numbers': [25, 21],
            'sum': 46,
            'confidence': 0.330,
            'statistical_z_score': -3.15,
            'p_value': 0.0016
        }
        
        self.analysis_results = {}
    
    def analyze_prediction_vs_patterns(self):
        """分析204期预测与550期规律的符合度"""
        print("🔍 204期预测与550期规律符合度分析")
        print("=" * 60)
        
        try:
            pred_nums = self.prediction_204['numbers']
            pred_sum = self.prediction_204['sum']
            
            # 1. 频率符合度分析
            print("📊 1. 频率符合度分析")
            
            high_freq = self.report_data['high_freq_numbers']
            low_freq = self.report_data['low_freq_numbers']
            
            pred_in_high = sum(1 for num in pred_nums if num in high_freq)
            pred_in_low = sum(1 for num in pred_nums if num in low_freq)
            
            print(f"   高频数字 {high_freq}")
            print(f"   低频数字 {low_freq}")
            print(f"   预测数字 {pred_nums}")
            print(f"   预测中高频数字: {pred_in_high}/2")
            print(f"   预测中低频数字: {pred_in_low}/2")
            
            if pred_in_high > pred_in_low:
                freq_alignment = "✅ 符合高频偏好"
            elif pred_in_low > pred_in_high:
                freq_alignment = "⚠️ 偏向低频数字"
            else:
                freq_alignment = "➖ 中性选择"
            
            print(f"   频率符合度: {freq_alignment}")
            
            # 2. 统计特征符合度分析
            print(f"\n📈 2. 统计特征符合度分析")
            
            avg_sum = self.report_data['avg_sum']
            sum_std = self.report_data['sum_std']
            
            # 计算预测数字和的Z分数
            z_score = (pred_sum - avg_sum) / sum_std
            
            print(f"   550期平均数字和: {avg_sum:.1f} ± {sum_std:.1f}")
            print(f"   204期预测数字和: {pred_sum}")
            print(f"   Z分数: {z_score:.2f}")
            print(f"   统计显著性: {self.prediction_204['p_value']:.4f}")
            
            if abs(z_score) < 1:
                stat_alignment = "✅ 统计正常"
            elif abs(z_score) < 2:
                stat_alignment = "⚠️ 统计偏离"
            else:
                stat_alignment = "❌ 统计异常"
            
            print(f"   统计符合度: {stat_alignment}")
            
            # 3. 奇偶分布符合度
            print(f"\n🔢 3. 奇偶分布符合度分析")
            
            pred_odd_count = sum(1 for num in pred_nums if num % 2 == 1)
            pred_even_count = 2 - pred_odd_count
            
            avg_odd = self.report_data['avg_odd_count']
            expected_odd_in_2 = avg_odd * 2 / 6  # 按比例计算2个数字中的期望奇数个数
            
            print(f"   550期平均奇数: {avg_odd:.2f}/6")
            print(f"   2个数字期望奇数: {expected_odd_in_2:.2f}")
            print(f"   204期预测奇数: {pred_odd_count}/2")
            
            odd_diff = abs(pred_odd_count - expected_odd_in_2)
            if odd_diff < 0.5:
                odd_alignment = "✅ 奇偶合理"
            else:
                odd_alignment = "⚠️ 奇偶偏离"
            
            print(f"   奇偶符合度: {odd_alignment}")
            
            # 4. 综合符合度评估
            print(f"\n🎯 4. 综合符合度评估")
            
            alignment_scores = {
                'frequency': 1 if pred_in_high > pred_in_low else 0,
                'statistics': 1 if abs(z_score) < 2 else 0,
                'odd_even': 1 if odd_diff < 0.5 else 0
            }
            
            total_score = sum(alignment_scores.values())
            alignment_percentage = total_score / len(alignment_scores) * 100
            
            print(f"   频率符合: {'✅' if alignment_scores['frequency'] else '❌'}")
            print(f"   统计符合: {'✅' if alignment_scores['statistics'] else '❌'}")
            print(f"   奇偶符合: {'✅' if alignment_scores['odd_even'] else '❌'}")
            print(f"   综合符合度: {alignment_percentage:.1f}%")
            
            self.analysis_results['pattern_alignment'] = {
                'frequency_score': alignment_scores['frequency'],
                'statistics_score': alignment_scores['statistics'],
                'odd_even_score': alignment_scores['odd_even'],
                'total_score': total_score,
                'percentage': alignment_percentage
            }
            
        except Exception as e:
            print(f"⚠️ 符合度分析失败: {e}")
    
    def evaluate_time_pattern_validity(self):
        """评估时间规律的有效性"""
        print(f"\n⏰ 时间规律有效性评估")
        print("=" * 60)
        
        try:
            # 1. 月度周期性分析
            print("📅 1. 月度周期性分析")
            
            monthly_var = self.report_data['monthly_variation']
            highest_month = self.report_data['highest_month']
            lowest_month = self.report_data['lowest_month']
            
            print(f"   月度变化幅度: {monthly_var}")
            print(f"   最高月份: {highest_month[0]} ({highest_month[1]})")
            print(f"   最低月份: {lowest_month[0]} ({lowest_month[1]})")
            
            # 计算月度效应的统计显著性
            effect_size = monthly_var / self.report_data['sum_std']
            
            print(f"   效应大小: {effect_size:.2f}σ")
            
            if effect_size > 0.5:
                monthly_significance = "✅ 显著月度效应"
            elif effect_size > 0.3:
                monthly_significance = "⚠️ 中等月度效应"
            else:
                monthly_significance = "❌ 微弱月度效应"
            
            print(f"   月度效应评估: {monthly_significance}")
            
            # 2. 年度趋势分析
            print(f"\n📈 2. 年度趋势分析")
            
            year_trend = self.report_data['year_trend']
            trend_significance = abs(year_trend) / self.report_data['sum_std']
            
            print(f"   年度变化: +{year_trend}")
            print(f"   趋势显著性: {trend_significance:.3f}σ")
            
            if trend_significance > 0.1:
                trend_validity = "✅ 有意义的趋势"
            else:
                trend_validity = "❌ 趋势不显著"
            
            print(f"   趋势有效性: {trend_validity}")
            
            # 3. 时间相关性评估
            print(f"\n🔗 3. 时间相关性评估")
            
            time_corr = self.report_data['time_correlation']
            
            print(f"   时间序列相关性: {time_corr:.3f}")
            
            if abs(time_corr) > 0.1:
                corr_strength = "强相关"
            elif abs(time_corr) > 0.05:
                corr_strength = "中等相关"
            else:
                corr_strength = "极弱相关"
            
            print(f"   相关性强度: {corr_strength}")
            
            # 4. 时间规律可利用性评估
            print(f"\n🎯 4. 时间规律可利用性评估")
            
            utilization_factors = {
                'monthly_effect': effect_size > 0.3,
                'yearly_trend': trend_significance > 0.1,
                'time_correlation': abs(time_corr) > 0.05
            }
            
            utilizable_count = sum(utilization_factors.values())
            utilization_score = utilizable_count / len(utilization_factors) * 100
            
            print(f"   月度效应可用: {'✅' if utilization_factors['monthly_effect'] else '❌'}")
            print(f"   年度趋势可用: {'✅' if utilization_factors['yearly_trend'] else '❌'}")
            print(f"   时间相关可用: {'✅' if utilization_factors['time_correlation'] else '❌'}")
            print(f"   时间规律可利用性: {utilization_score:.1f}%")
            
            self.analysis_results['time_pattern_validity'] = {
                'monthly_effect_size': effect_size,
                'yearly_trend_significance': trend_significance,
                'time_correlation': time_corr,
                'utilization_score': utilization_score
            }
            
        except Exception as e:
            print(f"⚠️ 时间规律评估失败: {e}")
    
    def analyze_clustering_effectiveness(self):
        """分析聚类模式的有效性"""
        print(f"\n🎯 聚类模式有效性分析")
        print("=" * 60)
        
        try:
            # 1. 聚类质量评估
            print("📊 1. 聚类质量评估")
            
            cluster_count = self.report_data['cluster_count']
            silhouette = self.report_data['silhouette_score']
            
            print(f"   聚类数量: {cluster_count}")
            print(f"   轮廓系数: {silhouette:.3f}")
            
            if silhouette > 0.5:
                cluster_quality = "✅ 优秀聚类"
            elif silhouette > 0.3:
                cluster_quality = "⚠️ 中等聚类"
            elif silhouette > 0.1:
                cluster_quality = "❌ 较差聚类"
            else:
                cluster_quality = "❌ 无效聚类"
            
            print(f"   聚类质量: {cluster_quality}")
            
            # 2. 模式区分度分析
            print(f"\n🔍 2. 模式区分度分析")
            
            # 基于报告描述的4种模式
            patterns = {
                '低和奇数型': {'sum': 'low', 'odd': 'high', 'span': 'small'},
                '高和奇数型': {'sum': 'high', 'odd': 'high', 'span': 'small'},
                '低和偶数型': {'sum': 'low', 'odd': 'low', 'span': 'large'},
                '高和大跨度型': {'sum': 'high', 'odd': 'high', 'span': 'large'}
            }
            
            print(f"   识别出的4种模式:")
            for i, (name, features) in enumerate(patterns.items()):
                print(f"     模式{i+1}: {name}")
                print(f"       特征: 数字和{features['sum']}, 奇数{features['odd']}, 跨度{features['span']}")
            
            # 3. 模式预测价值评估
            print(f"\n💡 3. 模式预测价值评估")
            
            # 评估204期预测属于哪种模式
            pred_sum = self.prediction_204['sum']
            pred_odd_count = sum(1 for num in self.prediction_204['numbers'] if num % 2 == 1)
            pred_span = max(self.prediction_204['numbers']) - min(self.prediction_204['numbers'])
            
            avg_sum = self.report_data['avg_sum']
            avg_span = self.report_data['avg_span']
            
            # 判断预测属于哪种模式
            sum_level = 'low' if pred_sum < avg_sum else 'high'
            odd_level = 'high' if pred_odd_count >= 1 else 'low'  # 对于2个数字的情况
            span_level = 'small' if pred_span < avg_span else 'large'
            
            pred_pattern = f"{sum_level}和{'奇数' if odd_level == 'high' else '偶数'}型"
            
            print(f"   204期预测特征:")
            print(f"     数字和: {pred_sum} ({sum_level})")
            print(f"     奇数个数: {pred_odd_count} ({odd_level})")
            print(f"     数字跨度: {pred_span} ({span_level})")
            print(f"   预测模式: {pred_pattern}")
            
            # 4. 聚类可利用性评估
            print(f"\n🎯 4. 聚类可利用性评估")
            
            utilization_factors = {
                'cluster_quality': silhouette > 0.3,
                'pattern_distinction': True,  # 4种模式有明确区分
                'prediction_alignment': True  # 可以将预测归类到某种模式
            }
            
            cluster_utilization = sum(utilization_factors.values()) / len(utilization_factors) * 100
            
            print(f"   聚类质量合格: {'✅' if utilization_factors['cluster_quality'] else '❌'}")
            print(f"   模式区分清晰: {'✅' if utilization_factors['pattern_distinction'] else '❌'}")
            print(f"   预测可归类: {'✅' if utilization_factors['prediction_alignment'] else '❌'}")
            print(f"   聚类可利用性: {cluster_utilization:.1f}%")
            
            self.analysis_results['clustering_effectiveness'] = {
                'silhouette_score': silhouette,
                'pattern_count': cluster_count,
                'prediction_pattern': pred_pattern,
                'utilization_score': cluster_utilization
            }
            
        except Exception as e:
            print(f"⚠️ 聚类分析失败: {e}")
    
    def evaluate_frequency_bias_impact(self):
        """评估频率偏差的影响"""
        print(f"\n📊 频率偏差影响评估")
        print("=" * 60)
        
        try:
            # 1. 频率偏差量化
            print("🔢 1. 频率偏差量化分析")
            
            freq_diff = self.report_data['freq_difference']
            high_freq = self.report_data['high_freq_numbers']
            low_freq = self.report_data['low_freq_numbers']
            
            print(f"   最高频与最低频差异: {freq_diff:.1%}")
            print(f"   高频数字: {high_freq}")
            print(f"   低频数字: {low_freq}")
            
            # 计算理论期望频率
            theoretical_freq = 6 / 49  # 每个数字的理论期望频率
            
            print(f"   理论期望频率: {theoretical_freq:.3%}")
            
            # 2. 偏差统计显著性检验
            print(f"\n📈 2. 偏差统计显著性检验")
            
            # 假设最高频数字出现84次，最低频57次，总共550期
            total_draws = 550 * 6  # 总抽取次数
            expected_count = total_draws / 49  # 期望次数
            
            highest_count = 84  # 基于报告数据
            lowest_count = 57
            
            # 卡方检验
            chi2_high = (highest_count - expected_count) ** 2 / expected_count
            chi2_low = (lowest_count - expected_count) ** 2 / expected_count
            
            print(f"   期望出现次数: {expected_count:.1f}")
            print(f"   最高频实际: {highest_count} (χ²={chi2_high:.2f})")
            print(f"   最低频实际: {lowest_count} (χ²={chi2_low:.2f})")
            
            # 判断显著性
            if chi2_high > 3.84 or chi2_low > 3.84:  # α=0.05的临界值
                significance = "✅ 统计显著"
            else:
                significance = "❌ 不显著"
            
            print(f"   偏差显著性: {significance}")
            
            # 3. 对预测系统的影响
            print(f"\n🎯 3. 对预测系统的影响评估")
            
            pred_nums = self.prediction_204['numbers']
            
            # 检查预测数字的频率特征
            pred_25_category = "高频" if 25 in high_freq else "低频" if 25 in low_freq else "中频"
            pred_21_category = "高频" if 21 in high_freq else "低频" if 21 in low_freq else "中频"
            
            print(f"   预测数字25: {pred_25_category}")
            print(f"   预测数字21: {pred_21_category}")
            
            # 评估频率偏差利用情况
            if pred_25_category == "高频" or pred_21_category == "高频":
                freq_utilization = "✅ 利用了频率偏差"
            elif pred_25_category == "低频" or pred_21_category == "低频":
                freq_utilization = "⚠️ 选择了低频数字"
            else:
                freq_utilization = "➖ 中性选择"
            
            print(f"   频率偏差利用: {freq_utilization}")
            
            # 4. 频率偏差可持续性分析
            print(f"\n🔄 4. 频率偏差可持续性分析")
            
            # 基于报告的年度变化数据
            print(f"   数字5: 从最高频变为下降最多(-1.38%)")
            print(f"   数字30: 上升最多(+1.30%)")
            print(f"   频率变化特征: 存在动态调整")
            
            sustainability = "⚠️ 频率偏差存在变化，需动态调整策略"
            print(f"   可持续性评估: {sustainability}")
            
            self.analysis_results['frequency_bias_impact'] = {
                'bias_magnitude': freq_diff,
                'statistical_significance': chi2_high > 3.84 or chi2_low > 3.84,
                'prediction_utilization': freq_utilization,
                'sustainability': 'dynamic'
            }
            
        except Exception as e:
            print(f"⚠️ 频率偏差分析失败: {e}")
    
    def synthesize_scientific_insights(self):
        """综合科学洞察"""
        print(f"\n🧠 综合科学洞察")
        print("=" * 60)
        
        try:
            print("💡 1. 核心发现总结")
            
            # 汇总各项分析结果
            if 'pattern_alignment' in self.analysis_results:
                alignment = self.analysis_results['pattern_alignment']['percentage']
                print(f"   204期预测与550期规律符合度: {alignment:.1f}%")
            
            if 'time_pattern_validity' in self.analysis_results:
                time_util = self.analysis_results['time_pattern_validity']['utilization_score']
                print(f"   时间规律可利用性: {time_util:.1f}%")
            
            if 'clustering_effectiveness' in self.analysis_results:
                cluster_util = self.analysis_results['clustering_effectiveness']['utilization_score']
                print(f"   聚类模式可利用性: {cluster_util:.1f}%")
            
            if 'frequency_bias_impact' in self.analysis_results:
                freq_sig = self.analysis_results['frequency_bias_impact']['statistical_significance']
                print(f"   频率偏差统计显著: {'是' if freq_sig else '否'}")
            
            print(f"\n🎯 2. 预测系统优化建议")
            
            # 基于分析结果的具体建议
            recommendations = []
            
            # 基于符合度分析的建议
            if 'pattern_alignment' in self.analysis_results:
                if self.analysis_results['pattern_alignment']['percentage'] < 50:
                    recommendations.append("调整预测算法以更好符合历史规律")
            
            # 基于时间规律的建议
            if 'time_pattern_validity' in self.analysis_results:
                if self.analysis_results['time_pattern_validity']['utilization_score'] > 50:
                    recommendations.append("增强时间因素在预测中的权重")
            
            # 基于聚类的建议
            if 'clustering_effectiveness' in self.analysis_results:
                if self.analysis_results['clustering_effectiveness']['utilization_score'] > 60:
                    recommendations.append("实施基于模式识别的预测策略")
            
            # 基于频率偏差的建议
            if 'frequency_bias_impact' in self.analysis_results:
                if self.analysis_results['frequency_bias_impact']['statistical_significance']:
                    recommendations.append("动态调整频率权重以适应变化")
            
            for i, rec in enumerate(recommendations, 1):
                print(f"     {i}. {rec}")
            
            print(f"\n🔬 3. 科学方法论评估")
            
            methodology_assessment = {
                '数据充分性': '✅ 550期数据量充足',
                '分析维度': '✅ 多维度综合分析',
                '统计严谨性': '✅ 使用了正态性检验、相关性分析等',
                '时间序列': '✅ 考虑了时间因素',
                '聚类分析': '⚠️ 轮廓系数0.317中等',
                '预测验证': '❌ 缺乏前瞻性验证'
            }
            
            for aspect, assessment in methodology_assessment.items():
                print(f"   {aspect}: {assessment}")
            
            print(f"\n⚠️ 4. 关键风险提示")
            
            risks = [
                "过度拟合历史数据的风险",
                "忽视彩票本质随机性的风险", 
                "时间规律可能是偶然现象",
                "频率偏差可能会回归均值",
                "聚类模式可能不具预测性"
            ]
            
            for i, risk in enumerate(risks, 1):
                print(f"     {i}. {risk}")
            
            print(f"\n🎯 5. 最终科学结论")
            
            print(f"   基于550期数据的分析显示:")
            print(f"   ✅ 存在可观察的统计规律")
            print(f"   ✅ 时间和频率模式具有一定可利用性")
            print(f"   ⚠️ 204期预测存在统计异常")
            print(f"   ❌ 预测系统需要根本性改进")
            print(f"   🔬 建议采用更科学的验证方法")
            
        except Exception as e:
            print(f"⚠️ 综合洞察分析失败: {e}")
    
    def run_comprehensive_analysis(self):
        """运行综合分析"""
        print("🔬 基于550期数据的科学思辨分析")
        print("结合2024-2025年数据规律分析报告")
        print("=" * 80)
        
        # 执行各项分析
        self.analyze_prediction_vs_patterns()
        self.evaluate_time_pattern_validity()
        self.analyze_clustering_effectiveness()
        self.evaluate_frequency_bias_impact()
        self.synthesize_scientific_insights()
        
        return True

def main():
    """主函数"""
    analyzer = ComprehensiveAnalysisSystem()
    
    # 执行综合分析
    success = analyzer.run_comprehensive_analysis()
    
    if success:
        print(f"\n🎉 基于550期数据的科学思辨分析完成！")
    else:
        print(f"\n❌ 分析执行失败")

if __name__ == "__main__":
    main()
