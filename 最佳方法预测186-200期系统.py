#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最佳方法预测186-200期系统
使用29%理论马尔可夫多数字优化方法预测2025年186-200期
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class BestMethodPredictionSystem:
    """最佳方法预测系统"""
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.full_data = None
        self.train_data = None
        self.historical_data = None
        
        # 预测配置
        self.target_periods = list(range(186, 201))  # 186-200期
        self.num_candidates = 10
        self.prediction_results = []
        
    def load_and_prepare_data(self):
        """加载数据并准备训练集"""
        print(f"🎯 最佳方法预测186-200期系统")
        print("=" * 60)
        
        try:
            # 加载完整数据
            self.full_data = pd.read_csv(self.data_file)
            self.full_data = self.full_data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            print(f"✅ 完整数据加载: {len(self.full_data)}期")
            
            # 训练数据：2023-2024年
            self.train_data = self.full_data[
                (self.full_data['年份'] >= 2023) & 
                (self.full_data['年份'] <= 2024)
            ].copy()
            
            # 历史数据：包含2025年1-185期（用于选择策略）
            self.historical_data = self.full_data[
                ((self.full_data['年份'] >= 2023) & (self.full_data['年份'] <= 2024)) |
                ((self.full_data['年份'] == 2025) & (self.full_data['期号'] <= 185))
            ].copy()
            
            print(f"✅ 训练集: {len(self.train_data)}期 (2023-2024年)")
            print(f"✅ 历史数据: {len(self.historical_data)}期 (2023-2024年 + 2025年1-185期)")
            print(f"🎯 预测目标: 2025年186-200期 ({len(self.target_periods)}期)")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def build_markov_model(self):
        """构建29%理论马尔可夫模型"""
        print(f"\n🔧 构建29%理论马尔可夫模型")
        print("=" * 50)
        
        transition_count = defaultdict(lambda: defaultdict(int))
        
        # 使用训练数据构建转移矩阵
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 计算转移概率，使用拉普拉斯平滑
        self.markov_prob = {}
        for curr_num in range(1, 50):
            if curr_num in transition_count:
                total = sum(transition_count[curr_num].values())
                smoothed_total = total + 49
                self.markov_prob[curr_num] = {}
                
                for next_num in range(1, 50):
                    count = transition_count[curr_num].get(next_num, 0)
                    self.markov_prob[curr_num][next_num] = (count + 1) / smoothed_total
            else:
                self.markov_prob[curr_num] = {
                    next_num: 1/49 for next_num in range(1, 50)
                }
        
        print(f"✅ 马尔可夫模型构建完成")
        print(f"  状态数量: 49个完整状态")
        print(f"  平滑方法: 拉普拉斯平滑")
        print(f"  训练样本: {len(self.train_data)}期")
    
    def generate_markov_candidates(self, prev_numbers, period, num_candidates=10):
        """生成马尔可夫候选预测"""
        candidates = []
        
        for seed_offset in range(num_candidates):
            np.random.seed(42 + period + seed_offset)
            
            number_probs = defaultdict(float)
            total_prob = 0.0
            
            for prev_num in prev_numbers:
                if prev_num in self.markov_prob:
                    for next_num, prob in self.markov_prob[prev_num].items():
                        number_probs[next_num] += prob
                        total_prob += prob
            
            if total_prob > 0:
                for num in number_probs:
                    number_probs[num] /= total_prob
            
            # 添加随机扰动
            perturbation = 0.05
            for num in number_probs:
                noise = np.random.normal(0, perturbation * number_probs[num])
                number_probs[num] = max(0, number_probs[num] + noise)
            
            if len(number_probs) >= 2:
                sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
                candidates.append([num for num, prob in sorted_numbers[:2]])
            else:
                candidates.append([1, 2])
        
        return candidates
    
    def select_best_candidate(self, candidates, period):
        """基于历史表现选择最佳候选"""
        if len(self.historical_data) < 10:
            return candidates[0]
        
        # 使用最近10期历史数据评估候选
        recent_data = self.historical_data.tail(10)
        candidate_scores = []
        
        for candidate in candidates:
            score = self.evaluate_candidate_performance(candidate, recent_data)
            candidate_scores.append(score)
        
        # 选择得分最高的候选
        best_idx = np.argmax(candidate_scores)
        return candidates[best_idx], candidate_scores
    
    def evaluate_candidate_performance(self, candidate, historical_data):
        """评估候选在历史数据上的表现"""
        if len(historical_data) == 0:
            return 0.5
        
        hits = 0
        total = len(historical_data)
        
        for _, row in historical_data.iterrows():
            actual_numbers = [row[f'数字{j}'] for j in range(1, 7)]
            predicted_set = set(candidate)
            actual_set = set(actual_numbers)
            
            if len(predicted_set & actual_set) >= 1:
                hits += 1
        
        return hits / total if total > 0 else 0.5
    
    def predict_periods_186_200(self):
        """预测186-200期"""
        print(f"\n🔮 开始预测2025年186-200期")
        print("=" * 60)
        
        # 获取185期的数据作为初始状态
        period_185_data = self.full_data[
            (self.full_data['年份'] == 2025) & 
            (self.full_data['期号'] == 185)
        ]
        
        if len(period_185_data) == 0:
            print("⚠️ 未找到185期数据，使用历史数据最后一期")
            last_period_data = self.historical_data.iloc[-1]
            prev_numbers = set([last_period_data[f'数字{j}'] for j in range(1, 7)])
        else:
            prev_numbers = set([period_185_data.iloc[0][f'数字{j}'] for j in range(1, 7)])
        
        print(f"初始状态(185期): {sorted(list(prev_numbers))}")
        print()
        
        # 逐期预测
        for period in self.target_periods:
            print(f"预测第{period}期:")
            
            # 生成候选预测
            candidates = self.generate_markov_candidates(prev_numbers, period, self.num_candidates)
            
            # 选择最佳候选
            selected_prediction, candidate_scores = self.select_best_candidate(candidates, period)
            
            # 计算置信度
            confidence = max(candidate_scores)
            avg_score = np.mean(candidate_scores)
            
            # 记录预测结果
            prediction_result = {
                'year': 2025,
                'period': period,
                'predicted_numbers': selected_prediction,
                'confidence': confidence,
                'avg_candidate_score': avg_score,
                'all_candidates': candidates,
                'candidate_scores': candidate_scores,
                'prev_numbers': sorted(list(prev_numbers))
            }
            
            self.prediction_results.append(prediction_result)
            
            # 显示预测结果
            print(f"  候选预测: {candidates[:3]}... (共{len(candidates)}个)")
            print(f"  选择预测: {selected_prediction}")
            print(f"  预测置信度: {confidence:.3f}")
            print(f"  候选平均分: {avg_score:.3f}")
            print(f"  基于前期: {sorted(list(prev_numbers))}")
            print()
            
            # 更新前一期数字（使用预测结果作为下一期的输入）
            prev_numbers = set(selected_prediction)
        
        print(f"✅ 186-200期预测完成")
    
    def generate_prediction_summary(self):
        """生成预测汇总"""
        print(f"\n📋 预测结果汇总")
        print("=" * 70)
        
        print(f"{'期号':<6} {'预测数字':<15} {'置信度':<8} {'候选平均分':<10} {'基于前期'}")
        print("-" * 70)
        
        for result in self.prediction_results:
            period = result['period']
            predicted = str(result['predicted_numbers'])
            confidence = result['confidence']
            avg_score = result['avg_candidate_score']
            prev_nums = str(result['prev_numbers'][:3]) + "..."
            
            print(f"{period:<6} {predicted:<15} {confidence:.3f}    {avg_score:.3f}      {prev_nums}")
        
        # 统计分析
        print(f"\n📊 预测统计分析:")
        print("-" * 40)
        
        confidences = [r['confidence'] for r in self.prediction_results]
        avg_scores = [r['avg_candidate_score'] for r in self.prediction_results]
        
        print(f"平均置信度: {np.mean(confidences):.3f}")
        print(f"置信度范围: {np.min(confidences):.3f} - {np.max(confidences):.3f}")
        print(f"平均候选分: {np.mean(avg_scores):.3f}")
        print(f"候选分范围: {np.min(avg_scores):.3f} - {np.max(avg_scores):.3f}")
        
        # 数字分布分析
        all_predicted_numbers = []
        for result in self.prediction_results:
            all_predicted_numbers.extend(result['predicted_numbers'])
        
        from collections import Counter
        number_freq = Counter(all_predicted_numbers)
        most_common = number_freq.most_common(10)
        
        print(f"\n🔢 预测数字频率分析:")
        print("-" * 30)
        print(f"最常预测的数字: {most_common[:5]}")
        
        # 奇偶分析
        odd_count = sum(1 for num in all_predicted_numbers if num % 2 == 1)
        even_count = len(all_predicted_numbers) - odd_count
        print(f"奇数预测: {odd_count}次, 偶数预测: {even_count}次")
        print(f"奇偶比例: {odd_count/(odd_count+even_count):.1%} : {even_count/(odd_count+even_count):.1%}")
    
    def save_predictions(self):
        """保存预测结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存详细结果到JSON
        json_file = f"最佳方法预测186-200期_{timestamp}.json"
        
        def convert_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_types(v) for v in obj]
            elif isinstance(obj, set):
                return list(obj)
            else:
                return obj
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(convert_types(self.prediction_results), f, ensure_ascii=False, indent=2)
        
        # 保存简化结果到CSV
        csv_file = f"最佳方法预测186-200期简化_{timestamp}.csv"
        
        csv_data = []
        for result in self.prediction_results:
            csv_data.append({
                '年份': result['year'],
                '期号': result['period'],
                '预测数字1': result['predicted_numbers'][0],
                '预测数字2': result['predicted_numbers'][1],
                '置信度': result['confidence'],
                '候选平均分': result['avg_candidate_score']
            })
        
        df = pd.DataFrame(csv_data)
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        
        print(f"\n✅ 预测结果已保存:")
        print(f"  详细结果: {json_file}")
        print(f"  简化结果: {csv_file}")
    
    def create_prediction_visualization(self):
        """创建预测可视化"""
        print(f"\n🎨 生成预测可视化图表")
        print("=" * 50)
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        periods = [r['period'] for r in self.prediction_results]
        confidences = [r['confidence'] for r in self.prediction_results]
        avg_scores = [r['avg_candidate_score'] for r in self.prediction_results]
        
        # 1. 置信度趋势
        ax1.plot(periods, confidences, marker='o', linewidth=2, markersize=6, color='blue')
        ax1.set_title('预测置信度趋势', fontsize=14, fontweight='bold')
        ax1.set_xlabel('期号', fontsize=12)
        ax1.set_ylabel('置信度', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 1)
        
        # 添加平均线
        avg_confidence = np.mean(confidences)
        ax1.axhline(y=avg_confidence, color='red', linestyle='--', 
                   label=f'平均置信度: {avg_confidence:.3f}')
        ax1.legend()
        
        # 2. 候选分数趋势
        ax2.plot(periods, avg_scores, marker='s', linewidth=2, markersize=6, color='green')
        ax2.set_title('候选平均分趋势', fontsize=14, fontweight='bold')
        ax2.set_xlabel('期号', fontsize=12)
        ax2.set_ylabel('候选平均分', fontsize=12)
        ax2.grid(True, alpha=0.3)
        
        avg_score = np.mean(avg_scores)
        ax2.axhline(y=avg_score, color='red', linestyle='--', 
                   label=f'平均分: {avg_score:.3f}')
        ax2.legend()
        
        # 3. 预测数字分布
        all_predicted_numbers = []
        for result in self.prediction_results:
            all_predicted_numbers.extend(result['predicted_numbers'])
        
        ax3.hist(all_predicted_numbers, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax3.set_title('预测数字分布', fontsize=14, fontweight='bold')
        ax3.set_xlabel('数字', fontsize=12)
        ax3.set_ylabel('预测频次', fontsize=12)
        ax3.grid(True, alpha=0.3)
        
        # 4. 奇偶分布
        odd_numbers = [num for num in all_predicted_numbers if num % 2 == 1]
        even_numbers = [num for num in all_predicted_numbers if num % 2 == 0]
        
        labels = ['奇数', '偶数']
        sizes = [len(odd_numbers), len(even_numbers)]
        colors = ['lightcoral', 'lightblue']
        
        ax4.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        ax4.set_title('奇偶数分布', fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        chart_file = f"最佳方法预测186-200期图表_{timestamp}.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"✅ 可视化图表已生成: {chart_file}")

def main():
    """主函数"""
    print("🎯 最佳方法预测186-200期系统")
    print("使用29%理论马尔可夫多数字优化方法")
    print("=" * 80)
    
    predictor = BestMethodPredictionSystem()
    
    # 1. 加载和准备数据
    if not predictor.load_and_prepare_data():
        return
    
    # 2. 构建马尔可夫模型
    predictor.build_markov_model()
    
    # 3. 预测186-200期
    predictor.predict_periods_186_200()
    
    # 4. 生成预测汇总
    predictor.generate_prediction_summary()
    
    # 5. 创建可视化
    predictor.create_prediction_visualization()
    
    # 6. 保存预测结果
    predictor.save_predictions()
    
    print(f"\n🎉 186-200期预测完成")
    print("=" * 50)
    print("✅ 使用29%理论马尔可夫多数字优化方法")
    print("✅ 基于2023-2024年训练数据")
    print("✅ 应用历史表现选择策略")
    print("✅ 预测结果已保存并可视化")

if __name__ == "__main__":
    main()
