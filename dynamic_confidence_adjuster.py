#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态置信度调整机制
Dynamic Confidence Adjustment Mechanism

实现根据实时预测表现、数据质量变化、模式识别结果等因素
动态调整置信度的智能系统

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0
"""

import numpy as np
import pandas as pd
from collections import deque, defaultdict
from datetime import datetime, timedelta
import json

class DynamicConfidenceAdjuster:
    """动态置信度调整器"""
    
    def __init__(self, config=None):
        """初始化调整器"""
        self.config = config or self._get_default_config()
        
        # 历史记录
        self.prediction_history = deque(maxlen=self.config['history_window'])
        self.confidence_history = deque(maxlen=self.config['history_window'])
        self.performance_metrics = deque(maxlen=self.config['metrics_window'])
        
        # 调整参数
        self.calibration_factors = {
            'accuracy_factor': 1.0,
            'consistency_factor': 1.0,
            'stability_factor': 1.0,
            'trend_factor': 1.0
        }
        
        # 模式识别
        self.pattern_performance = defaultdict(list)
        self.data_quality_trends = deque(maxlen=50)
        
        print(f"🔧 动态置信度调整器初始化完成")
        print(f"  历史窗口: {self.config['history_window']}")
        print(f"  调整策略: {len(self.config['adjustment_strategies'])}种")
    
    def _get_default_config(self):
        """获取默认配置"""
        return {
            'history_window': 50,
            'metrics_window': 20,
            'adjustment_strategies': [
                'accuracy_based',
                'consistency_based', 
                'stability_based',
                'trend_based',
                'pattern_based'
            ],
            'calibration_params': {
                'min_adjustment': 0.5,
                'max_adjustment': 2.0,
                'learning_rate': 0.1,
                'stability_threshold': 0.1,
                'trend_sensitivity': 0.05
            },
            'confidence_bounds': {
                'min_confidence': 0.05,
                'max_confidence': 0.95,
                'target_range': (0.3, 0.8)
            }
        }
    
    def update_prediction_result(self, prediction_result):
        """更新预测结果"""
        # 添加到历史记录
        self.prediction_history.append(prediction_result)
        
        if 'confidence_details' in prediction_result:
            self.confidence_history.append(prediction_result['confidence_details'])
        
        # 更新性能指标
        self._update_performance_metrics()
        
        # 更新模式性能
        self._update_pattern_performance(prediction_result)
        
        # 更新数据质量趋势
        self._update_data_quality_trends(prediction_result)
        
        # 重新计算校准因子
        self._recalculate_calibration_factors()
    
    def _update_performance_metrics(self):
        """更新性能指标"""
        if len(self.prediction_history) < 5:
            return
        
        recent_predictions = list(self.prediction_history)[-self.config['metrics_window']:]
        
        # 计算准确率
        hits = sum(1 for p in recent_predictions if p.get('is_hit', False))
        accuracy = hits / len(recent_predictions)
        
        # 计算置信度校准
        confidences = [p.get('confidence', 0.5) for p in recent_predictions]
        hit_flags = [1 if p.get('is_hit', False) else 0 for p in recent_predictions]
        
        if len(confidences) > 1:
            calibration = np.corrcoef(confidences, hit_flags)[0, 1]
            calibration = max(-1, min(1, calibration))  # 限制范围
        else:
            calibration = 0
        
        # 计算置信度稳定性
        if len(confidences) > 1:
            stability = 1 - np.std(confidences)  # 标准差越小，稳定性越高
        else:
            stability = 0.5
        
        # 计算趋势
        if len(self.performance_metrics) > 0:
            prev_accuracy = self.performance_metrics[-1]['accuracy']
            trend = accuracy - prev_accuracy
        else:
            trend = 0
        
        metrics = {
            'timestamp': datetime.now(),
            'accuracy': accuracy,
            'calibration': calibration,
            'stability': stability,
            'trend': trend,
            'sample_size': len(recent_predictions)
        }
        
        self.performance_metrics.append(metrics)
    
    def _update_pattern_performance(self, prediction_result):
        """更新模式性能"""
        if 'previous_numbers' not in prediction_result:
            return
        
        # 定义模式
        prev_numbers = prediction_result['previous_numbers']
        pattern_key = self._extract_pattern_key(prev_numbers)
        
        # 记录该模式的表现
        is_hit = prediction_result.get('is_hit', False)
        confidence = prediction_result.get('confidence', 0.5)
        
        self.pattern_performance[pattern_key].append({
            'is_hit': is_hit,
            'confidence': confidence,
            'timestamp': datetime.now()
        })
        
        # 保持最近的记录
        if len(self.pattern_performance[pattern_key]) > 20:
            self.pattern_performance[pattern_key] = self.pattern_performance[pattern_key][-20:]
    
    def _extract_pattern_key(self, numbers):
        """提取模式关键字"""
        if not numbers:
            return "empty"
        
        # 奇偶模式
        odd_count = sum(1 for n in numbers if n % 2 == 1)
        odd_ratio = odd_count / len(numbers)
        
        # 大小模式
        large_count = sum(1 for n in numbers if n > 25)
        large_ratio = large_count / len(numbers)
        
        # 和值模式
        total_sum = sum(numbers)
        sum_level = "low" if total_sum < 120 else "medium" if total_sum < 180 else "high"
        
        return f"odd_{odd_ratio:.1f}_large_{large_ratio:.1f}_sum_{sum_level}"
    
    def _update_data_quality_trends(self, prediction_result):
        """更新数据质量趋势"""
        data_source = prediction_result.get('data_source', '未知')
        
        # 数据质量评分
        quality_scores = {
            '真实数据': 1.0,
            '用户验证': 0.9,
            '自动验证': 0.8,
            '预测数据': 0.6,
            '历史数据': 0.85,
            '用户输入': 0.9
        }
        
        quality_score = quality_scores.get(data_source, 0.5)
        
        self.data_quality_trends.append({
            'timestamp': datetime.now(),
            'quality_score': quality_score,
            'data_source': data_source
        })
    
    def _recalculate_calibration_factors(self):
        """重新计算校准因子"""
        if len(self.performance_metrics) < 3:
            return
        
        # 基于准确率的调整
        self._calculate_accuracy_factor()
        
        # 基于一致性的调整
        self._calculate_consistency_factor()
        
        # 基于稳定性的调整
        self._calculate_stability_factor()
        
        # 基于趋势的调整
        self._calculate_trend_factor()
    
    def _calculate_accuracy_factor(self):
        """计算基于准确率的调整因子"""
        recent_metrics = list(self.performance_metrics)[-5:]
        avg_accuracy = np.mean([m['accuracy'] for m in recent_metrics])
        
        # 目标准确率
        target_accuracy = 0.3
        
        # 调整因子
        if avg_accuracy > target_accuracy:
            # 准确率高，可以适当提高置信度
            factor = 1.0 + (avg_accuracy - target_accuracy) * 2
        else:
            # 准确率低，需要降低置信度
            factor = 1.0 - (target_accuracy - avg_accuracy) * 1.5
        
        # 限制调整幅度
        factor = max(0.5, min(1.5, factor))
        
        # 平滑更新
        learning_rate = self.config['calibration_params']['learning_rate']
        self.calibration_factors['accuracy_factor'] = (
            (1 - learning_rate) * self.calibration_factors['accuracy_factor'] +
            learning_rate * factor
        )
    
    def _calculate_consistency_factor(self):
        """计算基于一致性的调整因子"""
        if len(self.performance_metrics) < 3:
            return
        
        recent_metrics = list(self.performance_metrics)[-5:]
        calibrations = [m['calibration'] for m in recent_metrics]
        
        # 计算校准一致性
        avg_calibration = np.mean(calibrations)
        
        # 目标校准值
        target_calibration = 0.5
        
        # 调整因子
        if avg_calibration > target_calibration:
            factor = 1.0 + (avg_calibration - target_calibration) * 0.5
        else:
            factor = 1.0 - (target_calibration - avg_calibration) * 0.3
        
        # 限制调整幅度
        factor = max(0.7, min(1.3, factor))
        
        # 平滑更新
        learning_rate = self.config['calibration_params']['learning_rate']
        self.calibration_factors['consistency_factor'] = (
            (1 - learning_rate) * self.calibration_factors['consistency_factor'] +
            learning_rate * factor
        )
    
    def _calculate_stability_factor(self):
        """计算基于稳定性的调整因子"""
        if len(self.performance_metrics) < 3:
            return
        
        recent_metrics = list(self.performance_metrics)[-5:]
        stabilities = [m['stability'] for m in recent_metrics]
        
        avg_stability = np.mean(stabilities)
        
        # 稳定性越高，置信度调整越保守
        factor = 0.8 + 0.4 * avg_stability
        
        # 平滑更新
        learning_rate = self.config['calibration_params']['learning_rate']
        self.calibration_factors['stability_factor'] = (
            (1 - learning_rate) * self.calibration_factors['stability_factor'] +
            learning_rate * factor
        )
    
    def _calculate_trend_factor(self):
        """计算基于趋势的调整因子"""
        if len(self.performance_metrics) < 5:
            return
        
        recent_metrics = list(self.performance_metrics)[-5:]
        trends = [m['trend'] for m in recent_metrics]
        
        avg_trend = np.mean(trends)
        
        # 正趋势提高置信度，负趋势降低置信度
        factor = 1.0 + avg_trend * 2
        
        # 限制调整幅度
        factor = max(0.8, min(1.2, factor))
        
        # 平滑更新
        learning_rate = self.config['calibration_params']['learning_rate']
        self.calibration_factors['trend_factor'] = (
            (1 - learning_rate) * self.calibration_factors['trend_factor'] +
            learning_rate * factor
        )
    
    def adjust_confidence(self, base_confidence, prediction_context=None):
        """动态调整置信度"""
        if len(self.performance_metrics) < 2:
            return base_confidence
        
        # 获取调整因子
        factors = self.calibration_factors
        
        # 计算综合调整因子
        combined_factor = (
            0.4 * factors['accuracy_factor'] +
            0.3 * factors['consistency_factor'] +
            0.2 * factors['stability_factor'] +
            0.1 * factors['trend_factor']
        )
        
        # 应用模式特定调整
        if prediction_context and 'previous_numbers' in prediction_context:
            pattern_factor = self._get_pattern_adjustment_factor(
                prediction_context['previous_numbers']
            )
            combined_factor = 0.8 * combined_factor + 0.2 * pattern_factor
        
        # 应用数据质量调整
        if prediction_context and 'data_source' in prediction_context:
            quality_factor = self._get_data_quality_factor(
                prediction_context['data_source']
            )
            combined_factor = 0.9 * combined_factor + 0.1 * quality_factor
        
        # 调整置信度
        adjusted_confidence = base_confidence * combined_factor
        
        # 确保在合理范围内
        min_conf = self.config['confidence_bounds']['min_confidence']
        max_conf = self.config['confidence_bounds']['max_confidence']
        adjusted_confidence = max(min_conf, min(max_conf, adjusted_confidence))
        
        return adjusted_confidence
    
    def _get_pattern_adjustment_factor(self, previous_numbers):
        """获取模式调整因子"""
        pattern_key = self._extract_pattern_key(previous_numbers)
        
        if pattern_key not in self.pattern_performance:
            return 1.0
        
        pattern_history = self.pattern_performance[pattern_key]
        if len(pattern_history) < 3:
            return 1.0
        
        # 计算该模式的历史表现
        hit_rate = sum(1 for p in pattern_history if p['is_hit']) / len(pattern_history)
        
        # 基于历史表现调整
        if hit_rate > 0.4:
            return 1.2  # 该模式表现好，提高置信度
        elif hit_rate < 0.2:
            return 0.8  # 该模式表现差，降低置信度
        else:
            return 1.0  # 中等表现，不调整
    
    def _get_data_quality_factor(self, data_source):
        """获取数据质量调整因子"""
        quality_factors = {
            '真实数据': 1.2,
            '用户验证': 1.1,
            '自动验证': 1.0,
            '预测数据': 0.8,
            '历史数据': 1.05,
            '用户输入': 1.1
        }
        
        return quality_factors.get(data_source, 1.0)
    
    def get_adjustment_report(self):
        """获取调整报告"""
        if len(self.performance_metrics) == 0:
            return {"status": "insufficient_data"}
        
        recent_metrics = list(self.performance_metrics)[-5:]
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'calibration_factors': self.calibration_factors.copy(),
            'recent_performance': {
                'avg_accuracy': np.mean([m['accuracy'] for m in recent_metrics]),
                'avg_calibration': np.mean([m['calibration'] for m in recent_metrics]),
                'avg_stability': np.mean([m['stability'] for m in recent_metrics]),
                'trend': recent_metrics[-1]['trend'] if recent_metrics else 0
            },
            'pattern_count': len(self.pattern_performance),
            'data_quality_trend': np.mean([dq['quality_score'] for dq in list(self.data_quality_trends)[-10:]]) if self.data_quality_trends else 0.5,
            'recommendations': self._generate_recommendations()
        }
        
        return report
    
    def _generate_recommendations(self):
        """生成调整建议"""
        recommendations = []
        
        if len(self.performance_metrics) < 5:
            recommendations.append("需要更多数据来进行有效的动态调整")
            return recommendations
        
        recent_metrics = list(self.performance_metrics)[-5:]
        avg_accuracy = np.mean([m['accuracy'] for m in recent_metrics])
        avg_calibration = np.mean([m['calibration'] for m in recent_metrics])
        
        if avg_accuracy < 0.25:
            recommendations.append("预测准确率偏低，建议检查预测算法")
        
        if avg_calibration < 0.3:
            recommendations.append("置信度校准不佳，建议调整置信度计算方法")
        
        if self.calibration_factors['accuracy_factor'] < 0.8:
            recommendations.append("准确率调整因子偏低，系统正在降低置信度")
        
        if self.calibration_factors['consistency_factor'] < 0.8:
            recommendations.append("一致性调整因子偏低，预测一致性需要改进")
        
        return recommendations
