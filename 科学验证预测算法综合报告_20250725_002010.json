{"system_info": {"name": "Scientific_Validated_Prediction_System", "version": "1.0", "creation_date": "2025-07-25T00:20:10.926320", "based_on_findings": "550期数据科学分析报告"}, "scientific_foundations": {"high_freq_numbers": [5, 15, 3, 40, 30], "low_freq_numbers": [41, 1, 8, 48, 47], "freq_difference": 0.47, "avg_sum": 149.88, "sum_std": 33.22, "avg_odd_count": 3.12, "avg_span": 35.35, "monthly_effect_size": 0.7, "monthly_variation": 23.4, "highest_month": 2, "lowest_month": 7, "cluster_count": 4, "silhouette_score": 0.317}, "data_split": {"total_periods": 1662, "train_periods": 1612, "validation_periods": 30, "test_periods": 20, "data_leakage_check": "PASSED"}, "validation_results": {"cross_validation": {"overall_hit_rate": 0.16, "hit_rate_std": 0.04898979485566356, "overall_confidence": 0.17990422621789334, "fold_results": [{"fold": 0, "hit_rate": 0.15, "confidence": 0.18026343367428355, "predictions": [{"numbers": [25, 23], "confidence": 0.1808675894243253, "hit_count": 1, "hit_rate": 0.5}, {"numbers": [25, 23], "confidence": 0.1808675894243253, "hit_count": 1, "hit_rate": 0.5}, {"numbers": [25, 27], "confidence": 0.18042454758259507, "hit_count": 1, "hit_rate": 0.5}, {"numbers": [24, 27], "confidence": 0.1757121156073684, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.1808675894243253, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.1808675894243253, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.1808675894243253, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.1808675894243253, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 27], "confidence": 0.18042454758259507, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.1808675894243253, "hit_count": 0, "hit_rate": 0.0}]}, {"fold": 1, "hit_rate": 0.25, "confidence": 0.1799099671696127, "predictions": [{"numbers": [25, 23], "confidence": 0.1809122127306636, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.1809122127306636, "hit_count": 1, "hit_rate": 0.5}, {"numbers": [25, 23], "confidence": 0.1809122127306636, "hit_count": 1, "hit_rate": 0.5}, {"numbers": [25, 22], "confidence": 0.1713163538444502, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.1809122127306636, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.1809122127306636, "hit_count": 1, "hit_rate": 0.5}, {"numbers": [25, 23], "confidence": 0.1809122127306636, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.1809122127306636, "hit_count": 1, "hit_rate": 0.5}, {"numbers": [25, 23], "confidence": 0.1809122127306636, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 27], "confidence": 0.18048561600636798, "hit_count": 1, "hit_rate": 0.5}]}, {"fold": 2, "hit_rate": 0.15, "confidence": 0.17858940983760146, "predictions": [{"numbers": [25, 23], "confidence": 0.18095627606674386, "hit_count": 1, "hit_rate": 0.5}, {"numbers": [25, 23], "confidence": 0.18095627606674386, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.18095627606674386, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.18095627606674386, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.18095627606674386, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 27], "confidence": 0.18052874725054754, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.18095627606674386, "hit_count": 1, "hit_rate": 0.5}, {"numbers": [25, 23], "confidence": 0.18095627606674386, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.18095627606674386, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [24, 29], "confidence": 0.15771514259151645, "hit_count": 1, "hit_rate": 0.5}]}, {"fold": 3, "hit_rate": 0.15, "confidence": 0.18087652406141747, "predictions": [{"numbers": [25, 23], "confidence": 0.18096561592437188, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 27], "confidence": 0.18052015660959966, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.18096561592437188, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 27], "confidence": 0.18052015660959966, "hit_count": 1, "hit_rate": 0.5}, {"numbers": [25, 23], "confidence": 0.18096561592437188, "hit_count": 1, "hit_rate": 0.5}, {"numbers": [25, 23], "confidence": 0.18096561592437188, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.18096561592437188, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.18096561592437188, "hit_count": 1, "hit_rate": 0.5}, {"numbers": [25, 23], "confidence": 0.18096561592437188, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.18096561592437188, "hit_count": 0, "hit_rate": 0.0}]}, {"fold": 4, "hit_rate": 0.1, "confidence": 0.1798817963465517, "predictions": [{"numbers": [25, 23], "confidence": 0.18097484674162553, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [24, 27], "confidence": 0.17573269856221083, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [24, 27], "confidence": 0.17573269856221083, "hit_count": 1, "hit_rate": 0.5}, {"numbers": [25, 23], "confidence": 0.18097484674162553, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.18097484674162553, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.18097484674162553, "hit_count": 1, "hit_rate": 0.5}, {"numbers": [25, 23], "confidence": 0.18097484674162553, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.18097484674162553, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 27], "confidence": 0.18052863914971645, "hit_count": 0, "hit_rate": 0.0}, {"numbers": [25, 23], "confidence": 0.18097484674162553, "hit_count": 0, "hit_rate": 0.0}]}]}, "validation_set": {"hit_rate": 0.1, "confidence": 0.1807629304361458, "statistical_reasonable_rate": 1.0, "improvement_vs_baseline": -0.022999999999999993, "results": [{"year": 2025, "period": 154, "predicted": [25, 23], "actual": [37, 18, 45, 25, 47, 42], "confidence": 0.180965022000491, "hit_count": 1, "hit_rate": 0.5, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 155, "predicted": [25, 23], "actual": [6, 34, 33, 12, 37, 20], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 156, "predicted": [25, 23], "actual": [15, 13, 38, 17, 28, 32], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 157, "predicted": [24, 27], "actual": [37, 5, 16, 12, 17, 3], "confidence": 0.17575770277819636, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 51, "sum_deviation": 0.02081665332265811, "statistical_reasonable": true}, {"year": 2025, "period": 158, "predicted": [25, 23], "actual": [15, 48, 4, 17, 42, 21], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 159, "predicted": [25, 23], "actual": [45, 34, 35, 22, 24, 8], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 160, "predicted": [25, 23], "actual": [23, 42, 32, 46, 30, 40], "confidence": 0.180965022000491, "hit_count": 1, "hit_rate": 0.5, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 161, "predicted": [25, 23], "actual": [49, 41, 44, 29, 38, 18], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 162, "predicted": [25, 23], "actual": [38, 2, 22, 48, 15, 12], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 163, "predicted": [25, 27], "actual": [38, 18, 43, 29, 10, 32], "confidence": 0.18053730814646102, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 52, "sum_deviation": 0.04083266613290631, "statistical_reasonable": true}, {"year": 2025, "period": 164, "predicted": [25, 23], "actual": [46, 38, 14, 39, 26, 8], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 165, "predicted": [25, 23], "actual": [23, 37, 17, 8, 42, 49], "confidence": 0.180965022000491, "hit_count": 1, "hit_rate": 0.5, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 166, "predicted": [25, 27], "actual": [30, 38, 18, 14, 42, 16], "confidence": 0.18053730814646102, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 52, "sum_deviation": 0.04083266613290631, "statistical_reasonable": true}, {"year": 2025, "period": 167, "predicted": [25, 23], "actual": [11, 27, 30, 6, 36, 3], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 168, "predicted": [25, 23], "actual": [31, 7, 40, 15, 9, 25], "confidence": 0.180965022000491, "hit_count": 1, "hit_rate": 0.5, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 169, "predicted": [25, 23], "actual": [11, 2, 32, 38, 29, 31], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 170, "predicted": [25, 23], "actual": [22, 20, 36, 44, 31, 19], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 171, "predicted": [25, 23], "actual": [15, 4, 23, 36, 27, 7], "confidence": 0.180965022000491, "hit_count": 1, "hit_rate": 0.5, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 172, "predicted": [25, 23], "actual": [3, 15, 4, 39, 2, 28], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 173, "predicted": [25, 23], "actual": [46, 32, 30, 5, 42, 4], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 174, "predicted": [25, 23], "actual": [11, 36, 22, 24, 20, 12], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 175, "predicted": [25, 23], "actual": [37, 3, 32, 12, 8, 29], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 176, "predicted": [25, 23], "actual": [12, 5, 24, 28, 22, 17], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 177, "predicted": [25, 23], "actual": [31, 30, 11, 49, 22, 41], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 178, "predicted": [25, 23], "actual": [42, 30, 37, 20, 6, 7], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 179, "predicted": [25, 23], "actual": [42, 8, 5, 30, 29, 25], "confidence": 0.180965022000491, "hit_count": 1, "hit_rate": 0.5, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 180, "predicted": [25, 23], "actual": [12, 18, 33, 36, 41, 47], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 181, "predicted": [25, 23], "actual": [10, 21, 40, 37, 2, 39], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 182, "predicted": [25, 23], "actual": [1, 49, 21, 39, 6, 34], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}, {"year": 2025, "period": 183, "predicted": [25, 23], "actual": [12, 39, 38, 7, 28, 45], "confidence": 0.180965022000491, "hit_count": 0, "hit_rate": 0.0, "pred_sum": 48, "sum_deviation": 0.039231385108086485, "statistical_reasonable": true}]}, "overfitting_risk": {"cv_performance": 0.16, "val_performance": 0.1, "performance_gap": 0.06, "relative_gap": 0.375, "risk_level": "高", "overfitting_detected": true}, "test_set": {"hit_rate": 0.15, "confidence": 0.17963467459393634, "hit_count_total": 6, "results": [{"year": 2025, "period": 184, "predicted_numbers": [25, 23], "actual_numbers": [8, 32, 20, 30, 18, 35], "confidence": 0.1809419240999459, "hit_count": 0, "hit_rate": 0.0, "is_hit": false, "pred_sum": 48, "actual_sum": 143, "pred_odd_count": 2, "actual_odd_count": 1, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 185, "predicted_numbers": [25, 23], "actual_numbers": [22, 40, 29, 45, 11, 1], "confidence": 0.1809419240999459, "hit_count": 0, "hit_rate": 0.0, "is_hit": false, "pred_sum": 48, "actual_sum": 148, "pred_odd_count": 2, "actual_odd_count": 4, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 186, "predicted_numbers": [25, 23], "actual_numbers": [9, 13, 17, 20, 22, 40], "confidence": 0.1809419240999459, "hit_count": 0, "hit_rate": 0.0, "is_hit": false, "pred_sum": 48, "actual_sum": 121, "pred_odd_count": 2, "actual_odd_count": 3, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 187, "predicted_numbers": [24, 27], "actual_numbers": [4, 7, 14, 18, 26, 31], "confidence": 0.17571633009079438, "hit_count": 0, "hit_rate": 0.0, "is_hit": false, "pred_sum": 51, "actual_sum": 100, "pred_odd_count": 1, "actual_odd_count": 2, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 188, "predicted_numbers": [25, 23], "actual_numbers": [5, 12, 23, 30, 41, 47], "confidence": 0.1809419240999459, "hit_count": 1, "hit_rate": 0.5, "is_hit": true, "pred_sum": 48, "actual_sum": 158, "pred_odd_count": 2, "actual_odd_count": 4, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 189, "predicted_numbers": [25, 27], "actual_numbers": [3, 11, 36, 39, 41, 48], "confidence": 0.1804949035691271, "hit_count": 0, "hit_rate": 0.0, "is_hit": false, "pred_sum": 52, "actual_sum": 178, "pred_odd_count": 2, "actual_odd_count": 4, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 190, "predicted_numbers": [25, 23], "actual_numbers": [3, 8, 24, 25, 36, 49], "confidence": 0.1809419240999459, "hit_count": 1, "hit_rate": 0.5, "is_hit": true, "pred_sum": 48, "actual_sum": 145, "pred_odd_count": 2, "actual_odd_count": 3, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 191, "predicted_numbers": [25, 27], "actual_numbers": [18, 24, 33, 39, 40, 41], "confidence": 0.1804949035691271, "hit_count": 0, "hit_rate": 0.0, "is_hit": false, "pred_sum": 52, "actual_sum": 195, "pred_odd_count": 2, "actual_odd_count": 3, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 192, "predicted_numbers": [25, 23], "actual_numbers": [3, 10, 19, 20, 31, 46], "confidence": 0.1809419240999459, "hit_count": 0, "hit_rate": 0.0, "is_hit": false, "pred_sum": 48, "actual_sum": 129, "pred_odd_count": 2, "actual_odd_count": 3, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 193, "predicted_numbers": [25, 23], "actual_numbers": [4, 6, 14, 16, 17, 42], "confidence": 0.1809419240999459, "hit_count": 0, "hit_rate": 0.0, "is_hit": false, "pred_sum": 48, "actual_sum": 99, "pred_odd_count": 2, "actual_odd_count": 1, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 194, "predicted_numbers": [25, 23], "actual_numbers": [3, 7, 11, 30, 34, 46], "confidence": 0.1809419240999459, "hit_count": 0, "hit_rate": 0.0, "is_hit": false, "pred_sum": 48, "actual_sum": 131, "pred_odd_count": 2, "actual_odd_count": 3, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 195, "predicted_numbers": [25, 23], "actual_numbers": [6, 8, 12, 22, 27, 42], "confidence": 0.1809419240999459, "hit_count": 0, "hit_rate": 0.0, "is_hit": false, "pred_sum": 48, "actual_sum": 117, "pred_odd_count": 2, "actual_odd_count": 1, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 196, "predicted_numbers": [25, 28], "actual_numbers": [9, 12, 21, 27, 31, 33], "confidence": 0.170483917295094, "hit_count": 0, "hit_rate": 0.0, "is_hit": false, "pred_sum": 53, "actual_sum": 133, "pred_odd_count": 1, "actual_odd_count": 5, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 197, "predicted_numbers": [25, 23], "actual_numbers": [10, 12, 15, 24, 25, 43], "confidence": 0.1809419240999459, "hit_count": 1, "hit_rate": 0.5, "is_hit": true, "pred_sum": 48, "actual_sum": 129, "pred_odd_count": 2, "actual_odd_count": 3, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 198, "predicted_numbers": [25, 23], "actual_numbers": [3, 7, 15, 33, 39, 45], "confidence": 0.1809419240999459, "hit_count": 0, "hit_rate": 0.0, "is_hit": false, "pred_sum": 48, "actual_sum": 142, "pred_odd_count": 2, "actual_odd_count": 6, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 199, "predicted_numbers": [25, 23], "actual_numbers": [5, 11, 13, 21, 32, 37], "confidence": 0.1809419240999459, "hit_count": 0, "hit_rate": 0.0, "is_hit": false, "pred_sum": 48, "actual_sum": 119, "pred_odd_count": 2, "actual_odd_count": 5, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 200, "predicted_numbers": [25, 22], "actual_numbers": [11, 15, 21, 22, 41, 42], "confidence": 0.17137457585539656, "hit_count": 1, "hit_rate": 0.5, "is_hit": true, "pred_sum": 47, "actual_sum": 152, "pred_odd_count": 1, "actual_odd_count": 4, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 201, "predicted_numbers": [25, 23], "actual_numbers": [1, 5, 25, 30, 37, 46], "confidence": 0.1809419240999459, "hit_count": 1, "hit_rate": 0.5, "is_hit": true, "pred_sum": 48, "actual_sum": 144, "pred_odd_count": 2, "actual_odd_count": 4, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 202, "predicted_numbers": [25, 23], "actual_numbers": [5, 9, 17, 33, 44, 48], "confidence": 0.1809419240999459, "hit_count": 0, "hit_rate": 0.0, "is_hit": false, "pred_sum": 48, "actual_sum": 156, "pred_odd_count": 2, "actual_odd_count": 4, "method": "Scientific_Validated_Method"}, {"year": 2025, "period": 203, "predicted_numbers": [25, 23], "actual_numbers": [10, 12, 23, 27, 39, 44], "confidence": 0.1809419240999459, "hit_count": 1, "hit_rate": 0.5, "is_hit": true, "pred_sum": 48, "actual_sum": 155, "pred_odd_count": 2, "actual_odd_count": 3, "method": "Scientific_Validated_Method"}], "baselines_comparison": {"随机预测": 0.10899999999999999, "历史基准": 0.026999999999999996, "改进系统": -0.07600000000000001}}}, "key_improvements": ["基于47%频率偏差的权重调整", "利用4种聚类模式的分类预测", "集成0.70σ月度效应的时间调整", "解决之前0.0%符合度问题", "实施统计约束防止异常预测", "严格的时间序列验证"], "performance_summary": {"test_performance": {"hit_rate": 0.15, "confidence": 0.17963467459393634, "total_hits": 6, "baseline_comparisons": {"随机预测": 0.10899999999999999, "历史基准": 0.026999999999999996, "改进系统": -0.07600000000000001}}, "cross_validation": {"mean_hit_rate": 0.16, "hit_rate_std": 0.04898979485566356, "confidence": 0.17990422621789334}, "validation_set": {"hit_rate": 0.1, "improvement_vs_baseline": -0.022999999999999993, "statistical_reasonable_rate": 1.0}}, "risk_assessment": [{"type": "Overfitting Risk", "level": "高", "detected": true, "details": "CV vs Val performance gap: +37.5%"}, {"type": "Data Quality", "level": "低", "detected": false, "details": "严格的时间序列分割，通过数据泄露检查"}], "recommendations": [{"category": "Performance", "priority": "High", "recommendation": "系统表现不佳，需要重新设计", "rationale": "测试集命中率15.0%低于15%"}, {"category": "Overfitting", "priority": "High", "recommendation": "增强正则化，减少模型复杂度", "rationale": "检测到过拟合风险"}, {"category": "Scientific Findings", "priority": "Medium", "recommendation": "继续监控频率偏差和聚类模式的稳定性", "rationale": "基于550期数据的科学发现可能随时间变化"}]}