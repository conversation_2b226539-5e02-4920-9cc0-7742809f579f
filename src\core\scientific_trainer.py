#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Scientific Model Trainer with Rigorous Validation
Implements strict scientific methodology for model training and evaluation
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.model_selection import TimeSeriesSplit
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

from .scientific_model_optimizer import ScientificModelOptimizer
from .scientific_data_processor import ScientificDataProcessor


class ScientificTrainer:
    """
    Scientific trainer with rigorous validation and realistic expectations
    
    Key Features:
    1. Strict statistical validation
    2. Realistic performance expectations (1-3% accuracy)
    3. Comprehensive baseline comparisons
    4. Overfitting detection and prevention
    5. Statistical significance testing
    """
    
    def __init__(self, random_state: int = 42):
        self.random_state = random_state
        self.data_processor = ScientificDataProcessor()
        self.model_optimizer = ScientificModelOptimizer(random_state)
        
        # Scientific thresholds
        self.max_reasonable_accuracy = 0.03  # 3% maximum reasonable accuracy
        self.min_improvement_over_random = 0.01  # 1% minimum improvement
        self.significance_level = 0.01  # Strict significance level
        
        # Results storage
        self.training_results = {}
        self.validation_results = {}
        self.baseline_comparisons = {}
    
    def train_all_models(self) -> Dict:
        """
        Train models for all targets with scientific rigor
        """
        print("🔬 开始科学模型训练")
        print("=" * 60)
        
        # Step 1: Process data
        processed_data = self.data_processor.get_processed_data()
        
        # Step 2: Train models for each target
        for target_col in processed_data['target_columns']:
            print(f"\n{'='*20} {target_col} {'='*20}")
            
            # Prepare target-specific data
            X_train = processed_data['train_data'][processed_data['feature_columns']]
            y_train = processed_data['train_data'][target_col]
            
            X_test = processed_data['test_data'][processed_data['feature_columns']]
            y_test = processed_data['test_data'][target_col]
            
            # Train and validate model
            model_results = self.model_optimizer.optimize_for_target(
                X_train, y_train, target_col
            )
            
            # Test on hold-out set
            test_results = self._evaluate_on_test_set(
                model_results, X_test, y_test, target_col
            )
            
            # Compare with baselines
            baseline_comparison = self._compare_with_baselines(
                test_results, processed_data['random_baselines'][target_col]
            )
            
            # Store results
            self.training_results[target_col] = model_results
            self.validation_results[target_col] = test_results
            self.baseline_comparisons[target_col] = baseline_comparison
            
            # Scientific assessment
            self._assess_scientific_validity(target_col, test_results, baseline_comparison)
        
        # Overall assessment
        overall_assessment = self._generate_overall_assessment(processed_data)
        
        return {
            'training_results': self.training_results,
            'validation_results': self.validation_results,
            'baseline_comparisons': self.baseline_comparisons,
            'overall_assessment': overall_assessment,
            'data_info': processed_data['data_info']
        }
    
    def _evaluate_on_test_set(self, model_results: Dict, X_test: pd.DataFrame, 
                            y_test: pd.Series, target_col: str) -> Dict:
        """
        Evaluate model on test set with comprehensive metrics
        """
        model = model_results['training_results']['model']
        scaler = model_results['training_results'].get('scaler')
        selected_features = model_results['selected_features']
        
        # Prepare test data
        X_test_selected = X_test[selected_features]
        
        if scaler is not None:
            X_test_scaled = scaler.transform(X_test_selected)
            predictions = model.predict(X_test_scaled)
        else:
            predictions = model.predict(X_test_selected)
        
        # Calculate metrics
        mse = mean_squared_error(y_test, predictions)
        mae = mean_absolute_error(y_test, predictions)
        
        # Calculate accuracy metrics
        exact_matches = sum(1 for actual, pred in zip(y_test, predictions) 
                          if abs(actual - round(pred)) < 0.5)
        accuracy = exact_matches / len(y_test)
        
        # Calculate within-range accuracy (±1, ±2, ±3)
        within_1 = sum(1 for actual, pred in zip(y_test, predictions) 
                      if abs(actual - round(pred)) <= 1) / len(y_test)
        within_2 = sum(1 for actual, pred in zip(y_test, predictions) 
                      if abs(actual - round(pred)) <= 2) / len(y_test)
        within_3 = sum(1 for actual, pred in zip(y_test, predictions) 
                      if abs(actual - round(pred)) <= 3) / len(y_test)
        
        # Prediction distribution analysis
        pred_rounded = [round(p) for p in predictions]
        pred_range = (min(pred_rounded), max(pred_rounded))
        pred_std = np.std(pred_rounded)
        
        return {
            'predictions': predictions,
            'rounded_predictions': pred_rounded,
            'mse': mse,
            'mae': mae,
            'exact_accuracy': accuracy,
            'within_1_accuracy': within_1,
            'within_2_accuracy': within_2,
            'within_3_accuracy': within_3,
            'prediction_range': pred_range,
            'prediction_std': pred_std,
            'test_size': len(y_test)
        }
    
    def _compare_with_baselines(self, test_results: Dict, random_baseline: Dict) -> Dict:
        """
        Compare model performance with random baselines
        """
        model_mse = test_results['mse']
        model_accuracy = test_results['exact_accuracy']
        
        random_mse = random_baseline['random_mse']
        random_accuracy = random_baseline['theoretical_exact_match']
        
        # Calculate improvements
        mse_improvement = (random_mse - model_mse) / random_mse
        accuracy_improvement = model_accuracy - random_accuracy
        
        # Statistical significance test
        # Using binomial test for accuracy
        n_tests = test_results['test_size']
        n_successes = int(model_accuracy * n_tests)
        
        # Null hypothesis: model performs no better than random (p = 1/49)
        p_value = stats.binom_test(n_successes, n_tests, random_accuracy, alternative='greater')
        
        return {
            'model_mse': model_mse,
            'random_mse': random_mse,
            'mse_improvement': mse_improvement,
            'model_accuracy': model_accuracy,
            'random_accuracy': random_accuracy,
            'accuracy_improvement': accuracy_improvement,
            'statistical_test': {
                'n_tests': n_tests,
                'n_successes': n_successes,
                'p_value': p_value,
                'is_significant': p_value < self.significance_level
            }
        }
    
    def _assess_scientific_validity(self, target_col: str, test_results: Dict, 
                                  baseline_comparison: Dict):
        """
        Assess scientific validity of results
        """
        accuracy = test_results['exact_accuracy']
        improvement = baseline_comparison['accuracy_improvement']
        is_significant = baseline_comparison['statistical_test']['is_significant']
        
        print(f"\n📊 {target_col} 科学评估:")
        print(f"  测试准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
        print(f"  随机基线: {baseline_comparison['random_accuracy']:.4f}")
        print(f"  改善幅度: {improvement:.4f} ({improvement*100:.2f}%)")
        print(f"  统计显著性: p={baseline_comparison['statistical_test']['p_value']:.4f}")
        
        # Scientific validity checks
        warnings = []
        
        if accuracy > self.max_reasonable_accuracy:
            warnings.append(f"⚠️ 准确率异常高 ({accuracy:.1%})，可能存在过拟合或数据泄漏")
        
        if improvement < self.min_improvement_over_random:
            warnings.append(f"⚠️ 相对随机基线改善微小 ({improvement:.1%})")
        
        if not is_significant:
            warnings.append("⚠️ 性能改善无统计显著性")
        
        if test_results['test_size'] < 10:
            warnings.append("⚠️ 测试样本量过小，结果可信度低")
        
        if warnings:
            print("  科学性警告:")
            for warning in warnings:
                print(f"    {warning}")
        else:
            print("  ✅ 通过科学性检验")
    
    def _generate_overall_assessment(self, processed_data: Dict) -> Dict:
        """
        Generate overall scientific assessment
        """
        all_accuracies = [results['exact_accuracy'] for results in self.validation_results.values()]
        all_improvements = [comp['accuracy_improvement'] for comp in self.baseline_comparisons.values()]
        all_p_values = [comp['statistical_test']['p_value'] for comp in self.baseline_comparisons.values()]
        
        # Overall statistics
        mean_accuracy = np.mean(all_accuracies)
        mean_improvement = np.mean(all_improvements)
        significant_models = sum(1 for p in all_p_values if p < self.significance_level)
        
        # Scientific assessment
        assessment = {
            'mean_accuracy': mean_accuracy,
            'mean_improvement_over_random': mean_improvement,
            'significant_models': significant_models,
            'total_models': len(all_accuracies),
            'data_characteristics': processed_data['data_info'],
            'scientific_validity': 'unknown'
        }
        
        # Determine overall validity
        if mean_accuracy > self.max_reasonable_accuracy:
            assessment['scientific_validity'] = 'questionable'
            assessment['concerns'] = ['Accuracy too high for random data']
        elif mean_improvement < self.min_improvement_over_random:
            assessment['scientific_validity'] = 'marginal'
            assessment['concerns'] = ['Minimal improvement over random']
        elif significant_models < len(all_accuracies) / 2:
            assessment['scientific_validity'] = 'weak'
            assessment['concerns'] = ['Most models not statistically significant']
        else:
            assessment['scientific_validity'] = 'acceptable'
            assessment['concerns'] = []
        
        # Recommendations
        recommendations = []
        
        if assessment['scientific_validity'] in ['questionable', 'marginal']:
            recommendations.extend([
                "检查是否存在数据泄漏或过拟合",
                "考虑进一步简化模型",
                "增加测试数据量",
                "重新评估特征工程方法"
            ])
        
        if mean_accuracy < 0.005:  # Less than 0.5%
            recommendations.append("考虑模型是否有实际预测价值")
        
        assessment['recommendations'] = recommendations
        
        return assessment
