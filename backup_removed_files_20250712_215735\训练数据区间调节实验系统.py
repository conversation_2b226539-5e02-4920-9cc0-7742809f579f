#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练数据区间调节实验系统
分析不同数据分割比例对马尔可夫预测性能的影响
严格保持时间序列顺序，避免信息泄露
"""

import pandas as pd
import numpy as np
import json
from collections import defaultdict, Counter
from datetime import datetime
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class TrainingIntervalExperiment:
    """
    训练数据区间调节实验系统
    基于生产级马尔可夫预测系统，测试不同训练/测试分割比例
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.data = None
        self.baseline_performance = 0.292  # 29.2%基准性能
        
        # 实验配置
        self.split_ratios = [
            (0.80, 0.20, "80%/20%"),
            (0.85, 0.15, "85%/15%"),
            (0.90, 0.10, "90%/10%"),
            (0.95, 0.05, "95%/5%")
        ]
        
        # 当前基准配置（2023-2024训练，2025测试）
        self.baseline_config = "2023-2024训练/2025测试"
        
        self.results = {}
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            print(f"✅ 数据加载成功: {len(self.data)}期")
            print(f"  数据范围: {self.data['年份'].min()}年-{self.data['年份'].max()}年")
            print(f"  期号范围: {self.data['期号'].min()}-{self.data['期号'].max()}期")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def create_time_series_split(self, train_ratio, test_ratio):
        """创建时间序列分割"""
        total_periods = len(self.data)
        train_size = int(total_periods * train_ratio)
        
        # 严格按时间顺序分割
        train_data = self.data.iloc[:train_size].copy()
        test_data = self.data.iloc[train_size:].copy()
        
        return train_data, test_data
    
    def build_markov_model(self, train_data):
        """构建马尔可夫转移矩阵"""
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(train_data) - 1):
            current_numbers = set([train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 转换为概率
        transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                transition_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        return transition_prob
    
    def predict_next_period(self, previous_numbers, transition_prob):
        """预测下一期的2个数字"""
        if not transition_prob:
            return [1, 2], 0.0
        
        # 计算下一期各数字的概率
        number_probs = defaultdict(float)
        total_prob = 0.0
        
        for prev_num in previous_numbers:
            if prev_num in transition_prob:
                for next_num, prob in transition_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        # 归一化概率
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        # 选择概率最高的2个数字
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            predicted_2digits = [num for num, prob in sorted_numbers[:2]]
            confidence = np.mean([prob for num, prob in sorted_numbers[:2]])
        else:
            # 备选方案：使用频率最高的数字
            all_numbers = []
            for _, row in self.data.tail(10).iterrows():
                for j in range(1, 7):
                    all_numbers.append(row[f'数字{j}'])
            
            number_counts = Counter(all_numbers)
            predicted_2digits = [num for num, count in number_counts.most_common(2)]
            confidence = 0.3
        
        # 确保有2个预测数字
        if len(predicted_2digits) < 2:
            predicted_2digits = [1, 2]
            confidence = 0.1
        
        return predicted_2digits, confidence
    
    def validate_split_performance(self, train_data, test_data, split_name):
        """验证特定分割比例的性能"""
        print(f"\n🔬 验证 {split_name} 分割性能")
        print("-" * 40)
        
        # 构建马尔可夫模型
        transition_prob = self.build_markov_model(train_data)
        
        if not transition_prob:
            print(f"❌ {split_name}: 转移矩阵构建失败")
            return None
        
        print(f"  训练数据: {len(train_data)}期")
        print(f"  测试数据: {len(test_data)}期")
        print(f"  马尔可夫状态: {len(transition_prob)}")
        
        # 预测和评估
        predictions = []
        correct_predictions = 0
        
        for idx, test_row in test_data.iterrows():
            period_num = test_row['期号']
            actual_numbers = set([test_row[f'数字{j}'] for j in range(1, 7)])
            
            # 获取前一期数字
            if idx == test_data.index[0]:
                # 使用训练数据的最后一期
                prev_numbers = set([train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                # 使用测试数据中的前一期
                prev_idx = test_data.index[test_data.index.get_loc(idx) - 1]
                prev_numbers = set([test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            # 预测
            predicted_numbers, confidence = self.predict_next_period(prev_numbers, transition_prob)
            
            # 评估 - 使用统一验证标准：2个预测中至少1个命中
            hit_count = len(set(predicted_numbers) & actual_numbers)
            is_success = hit_count >= 1
            
            if is_success:
                correct_predictions += 1
            
            predictions.append({
                'period': period_num,
                'predicted': predicted_numbers,
                'actual': list(actual_numbers),
                'hits': hit_count,
                'success': is_success,
                'confidence': confidence
            })
        
        # 计算性能指标
        total_periods = len(predictions)
        success_rate = correct_predictions / total_periods if total_periods > 0 else 0
        
        print(f"  成功预测: {correct_predictions}/{total_periods}")
        print(f"  成功率: {success_rate:.3f} ({success_rate*100:.1f}%)")
        print(f"  vs基准: {(success_rate - self.baseline_performance)*100:+.1f}个百分点")
        
        return {
            'split_name': split_name,
            'train_periods': len(train_data),
            'test_periods': len(test_data),
            'markov_states': len(transition_prob),
            'success_rate': success_rate,
            'correct_predictions': correct_predictions,
            'total_predictions': total_periods,
            'improvement_vs_baseline': success_rate - self.baseline_performance,
            'predictions': predictions
        }
    
    def run_baseline_experiment(self):
        """运行基准实验（2023-2024训练，2025测试）"""
        print(f"\n🎯 运行基准实验: {self.baseline_config}")
        print("=" * 60)
        
        # 基准数据分割
        train_data = self.data[(self.data['年份'] >= 2023) & (self.data['年份'] < 2025)].copy()
        test_data = self.data[(self.data['年份'] == 2025) & (self.data['期号'] <= 182)].copy()
        
        baseline_result = self.validate_split_performance(train_data, test_data, self.baseline_config)
        
        if baseline_result:
            self.results['baseline'] = baseline_result
            print(f"✅ 基准实验完成: {baseline_result['success_rate']:.3f}")
        
        return baseline_result is not None
    
    def run_ratio_experiments(self):
        """运行不同分割比例实验"""
        print(f"\n🧪 运行分割比例实验")
        print("=" * 60)
        
        for train_ratio, test_ratio, split_name in self.split_ratios:
            train_data, test_data = self.create_time_series_split(train_ratio, test_ratio)
            
            # 确保测试数据不为空
            if len(test_data) < 10:
                print(f"⚠️ {split_name}: 测试数据过少({len(test_data)}期)，跳过")
                continue
            
            result = self.validate_split_performance(train_data, test_data, split_name)
            
            if result:
                self.results[split_name] = result
        
        print(f"✅ 分割比例实验完成，共{len(self.results)-1}个比例")
    
    def statistical_significance_tests(self):
        """统计显著性检验"""
        print(f"\n📊 统计显著性检验")
        print("=" * 60)
        
        significance_results = {}
        
        for split_name, result in self.results.items():
            if split_name == 'baseline':
                continue
                
            observed_rate = result['success_rate']
            n = result['total_predictions']
            
            if n == 0:
                continue
            
            # 与基准性能比较的二项检验
            try:
                test_result = stats.binomtest(
                    int(observed_rate * n), 
                    n, 
                    self.baseline_performance, 
                    alternative='two-sided'
                )
                p_value = test_result.pvalue
            except AttributeError:
                from scipy.stats import binom
                # 双侧检验
                if observed_rate >= self.baseline_performance:
                    p_value = 2 * (1 - binom.cdf(int(observed_rate * n) - 1, n, self.baseline_performance))
                else:
                    p_value = 2 * binom.cdf(int(observed_rate * n), n, self.baseline_performance)
            
            is_significant = p_value < 0.05
            
            significance_results[split_name] = {
                'p_value': float(p_value),
                'significant': is_significant,
                'direction': 'better' if observed_rate > self.baseline_performance else 'worse'
            }
            
            print(f"  {split_name}:")
            print(f"    观察成功率: {observed_rate:.3f}")
            print(f"    p值: {p_value:.4f}")
            print(f"    显著性: {'是' if is_significant else '否'}")
            print(f"    方向: {'优于' if observed_rate > self.baseline_performance else '劣于'}基准")
        
        return significance_results
    
    def generate_performance_comparison_table(self):
        """生成性能对比表格"""
        print(f"\n📋 性能对比表格")
        print("=" * 80)
        
        # 表头
        print(f"{'分割方式':<20} {'训练期数':<8} {'测试期数':<8} {'成功率':<8} {'vs基准':<10} {'显著性':<8}")
        print("-" * 80)
        
        # 基准结果
        if 'baseline' in self.results:
            baseline = self.results['baseline']
            print(f"{baseline['split_name']:<20} {baseline['train_periods']:<8} {baseline['test_periods']:<8} "
                  f"{baseline['success_rate']:.3f}    {baseline['improvement_vs_baseline']:+.3f}     基准")
        
        # 其他结果
        for split_name, result in self.results.items():
            if split_name == 'baseline':
                continue
                
            significance = self.significance_results.get(split_name, {})
            sig_mark = "✓" if significance.get('significant', False) else "✗"
            
            print(f"{result['split_name']:<20} {result['train_periods']:<8} {result['test_periods']:<8} "
                  f"{result['success_rate']:.3f}    {result['improvement_vs_baseline']:+.3f}     {sig_mark}")
    
    def analyze_optimal_training_interval(self):
        """分析最优训练数据区间"""
        print(f"\n🎯 最优训练数据区间分析")
        print("=" * 60)
        
        # 找到最佳性能
        best_performance = self.baseline_performance
        best_split = self.baseline_config
        
        for split_name, result in self.results.items():
            if result['success_rate'] > best_performance:
                best_performance = result['success_rate']
                best_split = split_name
        
        print(f"最佳性能: {best_performance:.3f} ({best_split})")
        
        # 分析训练数据量与性能的关系
        train_sizes = []
        performances = []
        
        for split_name, result in self.results.items():
            train_sizes.append(result['train_periods'])
            performances.append(result['success_rate'])
        
        # 计算相关性
        if len(train_sizes) > 2:
            correlation = np.corrcoef(train_sizes, performances)[0, 1]
            print(f"训练数据量与性能相关性: {correlation:.3f}")
            
            if abs(correlation) > 0.5:
                trend = "正相关" if correlation > 0 else "负相关"
                print(f"发现{trend}趋势：训练数据量{'增加' if correlation > 0 else '减少'}时性能{'提升' if correlation > 0 else '下降'}")
            else:
                print("训练数据量与性能无明显相关性")
        
        # 过拟合风险评估
        print(f"\n过拟合风险评估:")
        for split_name, result in self.results.items():
            train_ratio = result['train_periods'] / (result['train_periods'] + result['test_periods'])
            if train_ratio > 0.9:
                print(f"  {split_name}: 高风险（训练比例{train_ratio:.1%}）")
            elif train_ratio > 0.85:
                print(f"  {split_name}: 中等风险（训练比例{train_ratio:.1%}）")
            else:
                print(f"  {split_name}: 低风险（训练比例{train_ratio:.1%}）")

def main():
    """主函数"""
    print("🎯 训练数据区间调节实验系统")
    print("分析不同数据分割比例对马尔可夫预测性能的影响")
    print("=" * 70)
    
    # 初始化实验系统
    experiment = TrainingIntervalExperiment()
    
    # 1. 加载数据
    if not experiment.load_data():
        return
    
    # 2. 运行基准实验
    if not experiment.run_baseline_experiment():
        return
    
    # 3. 运行分割比例实验
    experiment.run_ratio_experiments()
    
    # 4. 统计显著性检验
    experiment.significance_results = experiment.statistical_significance_tests()
    
    # 5. 生成性能对比表格
    experiment.generate_performance_comparison_table()
    
    # 6. 分析最优训练区间
    experiment.analyze_optimal_training_interval()
    
    # 7. 保存详细结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"训练数据区间调节实验结果_{timestamp}.json"
    
    # 准备保存的数据（处理numpy类型）
    save_data = {}
    for key, result in experiment.results.items():
        save_data[key] = {
            'split_name': result['split_name'],
            'train_periods': int(result['train_periods']),
            'test_periods': int(result['test_periods']),
            'markov_states': int(result['markov_states']),
            'success_rate': float(result['success_rate']),
            'correct_predictions': int(result['correct_predictions']),
            'total_predictions': int(result['total_predictions']),
            'improvement_vs_baseline': float(result['improvement_vs_baseline'])
        }
    
    # 处理significance_results中的numpy类型
    clean_significance = {}
    for key, value in experiment.significance_results.items():
        clean_significance[key] = {
            'p_value': float(value['p_value']),
            'significant': bool(value['significant']),
            'direction': str(value['direction'])
        }

    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump({
            'experiment_config': {
                'baseline_performance': float(experiment.baseline_performance),
                'baseline_config': str(experiment.baseline_config)
            },
            'results': save_data,
            'significance_tests': clean_significance
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 实验完成！结果已保存: {results_file}")
    
    # 8. 生产系统优化建议
    print(f"\n💡 生产系统优化建议")
    print("=" * 50)
    
    best_split = max(experiment.results.items(), key=lambda x: x[1]['success_rate'])
    best_name, best_result = best_split
    
    if best_result['success_rate'] > experiment.baseline_performance:
        improvement = (best_result['success_rate'] - experiment.baseline_performance) * 100
        print(f"✅ 建议采用 {best_name} 分割方式")
        print(f"  性能提升: +{improvement:.1f}个百分点")
        print(f"  训练数据: {best_result['train_periods']}期")
        print(f"  测试数据: {best_result['test_periods']}期")
    else:
        print(f"⚠️ 当前基准配置已是最优选择")
        print(f"  建议保持现有的 {experiment.baseline_config} 配置")
    
    print(f"\n🔬 关键发现:")
    print(f"  1. 数据分割对性能的影响程度")
    print(f"  2. 最优训练数据区间长度")
    print(f"  3. 过拟合风险评估结果")
    print(f"  4. 统计显著性验证结果")

if __name__ == "__main__":
    main()
