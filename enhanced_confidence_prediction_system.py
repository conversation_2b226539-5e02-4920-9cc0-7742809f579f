#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强置信度预测系统
Enhanced Confidence Prediction System

基于多维度置信度评估框架的高精度预测系统
实现了基础预测置信度、历史表现置信度、模式稳定性置信度、
数据质量置信度和预测一致性置信度的综合评估

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore')

class EnhancedConfidencePredictionSystem:
    """增强置信度预测系统"""
    
    def __init__(self, config=None):
        """初始化系统"""
        self.config = config or self._get_default_config()
        self.train_data = None
        self.test_data = None
        self.transition_prob = {}
        self.prediction_history = []
        self.confidence_history = []
        
        print(f"🚀 增强置信度预测系统初始化完成")
        print(f"  多维度置信度权重: {self.config['confidence_weights']}")
        print(f"  预测方法集成: {len(self.config['ensemble_methods'])}种方法")
    
    def _get_default_config(self):
        """获取默认配置"""
        return {
            'confidence_weights': {
                'base_prediction': 0.25,      # 基础预测置信度
                'historical_performance': 0.25,  # 历史表现置信度
                'pattern_stability': 0.20,    # 模式稳定性置信度
                'data_quality': 0.15,         # 数据质量置信度
                'prediction_consistency': 0.15  # 预测一致性置信度
            },
            'ensemble_methods': [
                'enhanced_markov',
                'frequency_analysis', 
                'pattern_matching',
                'trend_analysis'
            ],
            'confidence_calibration': {
                'enabled': True,
                'window_size': 20,
                'min_confidence': 0.05,
                'max_confidence': 0.95
            },
            'prediction_params': {
                'num_candidates': 15,
                'markov_order': 2,
                'frequency_window': 100,
                'pattern_similarity_threshold': 0.7
            }
        }
    
    def load_data(self, train_file, test_file=None):
        """加载训练和测试数据"""
        print(f"\n📊 加载数据")
        
        # 加载训练数据
        self.train_data = pd.read_csv(train_file, encoding='utf-8')
        print(f"  训练数据: {len(self.train_data)}期")
        
        # 加载测试数据（如果提供）
        if test_file:
            self.test_data = pd.read_csv(test_file, encoding='utf-8')
            print(f"  测试数据: {len(self.test_data)}期")
        
        # 构建马尔可夫模型
        self._build_enhanced_markov_model()
        
        return True
    
    def _build_enhanced_markov_model(self):
        """构建增强的马尔可夫模型"""
        print(f"\n🔧 构建增强马尔可夫模型")
        
        # 一阶马尔可夫转移概率
        first_order_transitions = defaultdict(lambda: defaultdict(int))
        
        # 二阶马尔可夫转移概率
        second_order_transitions = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            # 一阶转移
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    first_order_transitions[curr_num][next_num] += 1
            
            # 二阶转移（如果有足够的历史数据）
            if i >= 1:
                prev_numbers = set([self.train_data.iloc[i-1][f'数字{j}'] for j in range(1, 7)])
                state = tuple(sorted(list(prev_numbers)))
                for next_num in next_numbers:
                    second_order_transitions[state][next_num] += 1
        
        # 计算转移概率
        self.first_order_prob = {}
        for curr_num in first_order_transitions:
            total = sum(first_order_transitions[curr_num].values())
            if total > 0:
                self.first_order_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in first_order_transitions[curr_num].items()
                }
        
        self.second_order_prob = {}
        for state in second_order_transitions:
            total = sum(second_order_transitions[state].values())
            if total > 0:
                self.second_order_prob[state] = {
                    next_num: count / total 
                    for next_num, count in second_order_transitions[state].items()
                }
        
        print(f"✅ 马尔可夫模型构建完成")
        print(f"  一阶状态数: {len(self.first_order_prob)}")
        print(f"  二阶状态数: {len(self.second_order_prob)}")
    
    def calculate_base_prediction_confidence(self, number_probs, predicted_numbers):
        """计算基础预测置信度"""
        if not number_probs or not predicted_numbers:
            return 0.3
        
        # 获取预测数字的概率
        pred_probs = [number_probs.get(num, 0) for num in predicted_numbers]
        
        # 计算基础置信度
        base_conf = np.mean(pred_probs)
        
        # 计算概率分布的集中度
        all_probs = list(number_probs.values())
        if len(all_probs) > 1:
            concentration = np.std(all_probs) * 3  # 放大标准差以增强差异
        else:
            concentration = 0
        
        # 计算预测数字概率与其他数字的差距
        other_probs = [p for n, p in number_probs.items() if n not in predicted_numbers]
        avg_other_prob = np.mean(other_probs) if other_probs else 0
        prob_advantage = max(0, np.mean(pred_probs) - avg_other_prob)
        
        # 综合计算
        final_base_conf = 0.4 * base_conf + 0.3 * concentration + 0.3 * prob_advantage
        
        # 标准化到0.2-0.8范围
        return 0.2 + 0.6 * min(1.0, max(0.0, final_base_conf))
    
    def calculate_historical_performance_confidence(self, predicted_numbers):
        """计算历史表现置信度"""
        if not self.prediction_history:
            return 0.5  # 默认中等置信度
        
        # 计算每个数字的历史命中率
        hit_rates = {}
        for num in range(1, 50):
            predictions = [ph for ph in self.prediction_history if num in ph.get('predicted_numbers', [])]
            hits = [ph for ph in predictions if ph.get('is_hit', False)]
            hit_rates[num] = len(hits) / len(predictions) if predictions else 0.5
        
        # 计算当前预测数字的平均历史命中率
        pred_hit_rates = [hit_rates.get(num, 0.5) for num in predicted_numbers]
        avg_hit_rate = np.mean(pred_hit_rates)
        
        # 计算历史表现置信度
        historical_conf = min(0.9, max(0.1, avg_hit_rate))
        
        return historical_conf
    
    def calculate_pattern_stability_confidence(self, previous_numbers, period_idx):
        """计算模式稳定性置信度"""
        # 定义多维状态
        states = self._define_multi_dimensional_states(previous_numbers)
        
        # 计算状态稳定性
        stability_scores = []
        for state in states:
            stability = self._calculate_state_stability(state)
            stability_scores.append(stability)
        
        # 基于周期性和趋势的稳定性评估
        stability_base = 0.5 + 0.2 * np.sin(period_idx * 0.1)
        stability_trend = 0.1 * np.cos(period_idx * 0.05)
        
        # 综合计算
        if stability_scores:
            stability_conf = 0.6 * np.mean(stability_scores) + 0.4 * (stability_base + stability_trend)
        else:
            stability_conf = stability_base + stability_trend
        
        # 标准化到0.3-0.9范围
        return 0.3 + 0.6 * min(1.0, max(0.0, stability_conf))
    
    def calculate_data_quality_confidence(self, previous_numbers, data_source="预测数据"):
        """计算数据质量置信度"""
        # 数据来源质量评分
        source_quality = {
            "真实数据": 1.0,
            "用户验证": 0.9,
            "自动验证": 0.8,
            "预测数据": 0.6,
            "历史回测数据": 0.85
        }
        
        # 数据完整性评分
        completeness = len(previous_numbers) / 6 if previous_numbers else 0
        
        # 数据一致性评分
        consistency = self._check_data_consistency(previous_numbers)
        
        # 综合计算
        quality_conf = (0.5 * source_quality.get(data_source, 0.6) + 
                       0.3 * completeness + 
                       0.2 * consistency)
        
        # 标准化到0.4-1.0范围
        return 0.4 + 0.6 * quality_conf
    
    def calculate_prediction_consistency_confidence(self, predicted_numbers, all_candidates):
        """计算预测一致性置信度"""
        if not all_candidates or len(all_candidates) < 2:
            return 0.5
        
        # 计算候选预测中包含预测数字的比例
        consistency_scores = []
        for num in predicted_numbers:
            count = sum(1 for candidate in all_candidates if num in candidate)
            consistency = count / len(all_candidates)
            consistency_scores.append(consistency)
        
        # 计算平均一致性
        avg_consistency = np.mean(consistency_scores) if consistency_scores else 0.5
        
        # 计算候选预测的多样性
        diversity = self._calculate_candidates_diversity(all_candidates)
        
        # 综合计算
        consistency_conf = 0.7 * avg_consistency + 0.3 * (1 - diversity)
        
        # 标准化到0.2-0.9范围
        return 0.2 + 0.7 * consistency_conf
    
    def _define_multi_dimensional_states(self, numbers):
        """定义多维状态"""
        if not numbers:
            return []
        
        states = []
        
        # 奇偶状态
        odd_count = sum(1 for n in numbers if n % 2 == 1)
        even_count = len(numbers) - odd_count
        states.append(f"奇偶_{odd_count}_{even_count}")
        
        # 大小状态
        small_count = sum(1 for n in numbers if n <= 25)
        large_count = len(numbers) - small_count
        states.append(f"大小_{small_count}_{large_count}")
        
        # 和值状态
        total_sum = sum(numbers)
        sum_range = "低" if total_sum < 120 else "中" if total_sum < 180 else "高"
        states.append(f"和值_{sum_range}")
        
        return states
    
    def _calculate_state_stability(self, state):
        """计算状态稳定性"""
        # 简化的稳定性计算
        # 在实际应用中，这里会基于历史数据计算该状态的稳定性
        base_stability = 0.6 + 0.2 * np.random.random()
        return min(1.0, max(0.0, base_stability))
    
    def _check_data_consistency(self, numbers):
        """检查数据一致性"""
        if not numbers:
            return 0.0
        
        # 检查数字范围
        range_check = all(1 <= n <= 49 for n in numbers)
        
        # 检查重复
        unique_check = len(numbers) == len(set(numbers))
        
        # 检查数量
        count_check = len(numbers) <= 6
        
        consistency = (range_check + unique_check + count_check) / 3
        return consistency
    
    def _calculate_candidates_diversity(self, candidates):
        """计算候选预测的多样性"""
        if not candidates or len(candidates) < 2:
            return 0.0
        
        # 计算所有候选中不同数字的数量
        all_numbers = set()
        for candidate in candidates:
            all_numbers.update(candidate)
        
        # 多样性 = 不同数字数量 / 理论最大数字数量
        max_possible = min(49, len(candidates) * 2)  # 假设每个候选有2个数字
        diversity = len(all_numbers) / max_possible if max_possible > 0 else 0
        
        return min(1.0, diversity)

    def calculate_multi_dimensional_confidence(self, predicted_numbers, all_candidates,
                                             previous_numbers, number_probs, period_idx,
                                             data_source="预测数据"):
        """计算多维度置信度"""
        weights = self.config['confidence_weights']

        # 计算各维度置信度
        base_conf = self.calculate_base_prediction_confidence(number_probs, predicted_numbers)
        historical_conf = self.calculate_historical_performance_confidence(predicted_numbers)
        stability_conf = self.calculate_pattern_stability_confidence(previous_numbers, period_idx)
        quality_conf = self.calculate_data_quality_confidence(previous_numbers, data_source)
        consistency_conf = self.calculate_prediction_consistency_confidence(predicted_numbers, all_candidates)

        # 加权计算最终置信度
        final_confidence = (
            weights['base_prediction'] * base_conf +
            weights['historical_performance'] * historical_conf +
            weights['pattern_stability'] * stability_conf +
            weights['data_quality'] * quality_conf +
            weights['prediction_consistency'] * consistency_conf
        )

        # 动态调整置信度
        if self.config['confidence_calibration']['enabled']:
            final_confidence = self._dynamic_confidence_adjustment(final_confidence)

        # 确保置信度在合理范围内
        min_conf = self.config['confidence_calibration']['min_confidence']
        max_conf = self.config['confidence_calibration']['max_confidence']
        final_confidence = min(max_conf, max(min_conf, final_confidence))

        # 记录置信度详情
        confidence_details = {
            'base_prediction': base_conf,
            'historical_performance': historical_conf,
            'pattern_stability': stability_conf,
            'data_quality': quality_conf,
            'prediction_consistency': consistency_conf,
            'final_confidence': final_confidence
        }

        return final_confidence, confidence_details

    def _dynamic_confidence_adjustment(self, base_confidence):
        """动态置信度调整"""
        if len(self.confidence_history) < 5:
            return base_confidence

        # 计算最近预测的准确率
        recent_accuracy = self._calculate_recent_accuracy()

        # 计算置信度与实际命中率的相关性
        confidence_hit_correlation = self._calculate_confidence_hit_correlation()

        # 计算校准因子
        calibration_factor = self._calculate_calibration_factor(recent_accuracy, confidence_hit_correlation)

        # 动态调整置信度
        adjusted_confidence = base_confidence * calibration_factor

        return adjusted_confidence

    def _calculate_recent_accuracy(self, window=10):
        """计算最近的预测准确率"""
        if not self.prediction_history:
            return 0.5

        recent_predictions = self.prediction_history[-window:]
        hits = sum(1 for pred in recent_predictions if pred.get('is_hit', False))
        accuracy = hits / len(recent_predictions) if recent_predictions else 0.5

        return accuracy

    def _calculate_confidence_hit_correlation(self):
        """计算置信度与命中率的相关性"""
        if len(self.confidence_history) < 5:
            return 0.5

        confidences = [ch['final_confidence'] for ch in self.confidence_history]
        hits = [1 if ph.get('is_hit', False) else 0 for ph in self.prediction_history[-len(confidences):]]

        if len(confidences) == len(hits) and len(confidences) > 1:
            correlation = np.corrcoef(confidences, hits)[0, 1]
            return max(0.1, min(0.9, (correlation + 1) / 2))  # 标准化到0.1-0.9

        return 0.5

    def _calculate_calibration_factor(self, recent_accuracy, confidence_hit_correlation):
        """计算校准因子"""
        # 基于最近准确率的调整
        accuracy_factor = 0.5 + (recent_accuracy - 0.5) * 0.5

        # 基于相关性的调整
        correlation_factor = 0.5 + (confidence_hit_correlation - 0.5) * 0.3

        # 综合校准因子
        calibration_factor = 0.7 * accuracy_factor + 0.3 * correlation_factor

        # 限制调整幅度
        return min(1.5, max(0.5, calibration_factor))

    def enhanced_markov_prediction(self, previous_numbers):
        """增强马尔可夫预测"""
        number_probs = defaultdict(float)
        total_prob = 0.0

        # 一阶马尔可夫预测
        for prev_num in previous_numbers:
            if prev_num in self.first_order_prob:
                for next_num, prob in self.first_order_prob[prev_num].items():
                    number_probs[next_num] += prob * 0.7  # 一阶权重0.7
                    total_prob += prob * 0.7

        # 二阶马尔可夫预测（如果有足够的历史数据）
        if len(previous_numbers) >= 6:
            state = tuple(sorted(list(previous_numbers)))
            if state in self.second_order_prob:
                for next_num, prob in self.second_order_prob[state].items():
                    number_probs[next_num] += prob * 0.3  # 二阶权重0.3
                    total_prob += prob * 0.3

        # 标准化概率
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob

        return dict(number_probs)

    def frequency_analysis_prediction(self, window=100):
        """频率分析预测"""
        if len(self.train_data) < window:
            window = len(self.train_data)

        # 获取最近时间窗口内的数据
        recent_data = self.train_data.tail(window)

        # 计算每个数字的频率
        frequencies = defaultdict(int)
        for _, row in recent_data.iterrows():
            for j in range(1, 7):
                num = row[f'数字{j}']
                frequencies[num] += 1

        # 计算频率趋势
        trends = self._calculate_frequency_trends(window)

        # 基于频率和趋势生成概率
        number_probs = {}
        total_freq = sum(frequencies.values())

        for num in range(1, 50):
            base_freq = frequencies.get(num, 0) / total_freq if total_freq > 0 else 1/49
            trend_factor = trends.get(num, 1.0)
            number_probs[num] = base_freq * trend_factor

        # 标准化概率
        total_prob = sum(number_probs.values())
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob

        return number_probs

    def _calculate_frequency_trends(self, window):
        """计算频率趋势"""
        trends = {}

        if len(self.train_data) < window * 2:
            return {num: 1.0 for num in range(1, 50)}

        # 计算前半段和后半段的频率
        mid_point = len(self.train_data) - window
        early_data = self.train_data.iloc[mid_point-window//2:mid_point]
        recent_data = self.train_data.tail(window//2)

        early_freq = defaultdict(int)
        recent_freq = defaultdict(int)

        for _, row in early_data.iterrows():
            for j in range(1, 7):
                early_freq[row[f'数字{j}']] += 1

        for _, row in recent_data.iterrows():
            for j in range(1, 7):
                recent_freq[row[f'数字{j}']] += 1

        # 计算趋势因子
        for num in range(1, 50):
            early_rate = early_freq[num] / len(early_data) if len(early_data) > 0 else 0
            recent_rate = recent_freq[num] / len(recent_data) if len(recent_data) > 0 else 0

            if early_rate > 0:
                trend_factor = recent_rate / early_rate
                trends[num] = min(2.0, max(0.5, trend_factor))  # 限制趋势因子范围
            else:
                trends[num] = 1.0

        return trends

    def pattern_matching_prediction(self, previous_numbers):
        """模式匹配预测"""
        if not previous_numbers:
            return {num: 1/49 for num in range(1, 50)}

        # 定义当前模式
        current_pattern = self._define_pattern(previous_numbers)

        # 在历史数据中查找类似模式
        similar_patterns = self._find_similar_patterns(current_pattern)

        # 基于类似模式生成预测概率
        number_probs = defaultdict(float)
        total_weight = 0

        for pattern_data in similar_patterns:
            weight = pattern_data['similarity']
            next_numbers = pattern_data['next_numbers']

            for num in next_numbers:
                number_probs[num] += weight
                total_weight += weight

        # 标准化概率
        if total_weight > 0:
            for num in number_probs:
                number_probs[num] /= total_weight
        else:
            # 如果没有找到类似模式，使用均匀分布
            number_probs = {num: 1/49 for num in range(1, 50)}

        return dict(number_probs)

    def _define_pattern(self, numbers):
        """定义数字模式"""
        if not numbers:
            return {}

        pattern = {}

        # 奇偶模式
        odd_count = sum(1 for n in numbers if n % 2 == 1)
        pattern['odd_ratio'] = odd_count / len(numbers)

        # 大小模式
        large_count = sum(1 for n in numbers if n > 25)
        pattern['large_ratio'] = large_count / len(numbers)

        # 和值模式
        pattern['sum'] = sum(numbers)
        pattern['avg'] = pattern['sum'] / len(numbers)

        # 跨度模式
        pattern['span'] = max(numbers) - min(numbers)

        # 连号模式
        sorted_nums = sorted(numbers)
        consecutive_count = 0
        for i in range(len(sorted_nums) - 1):
            if sorted_nums[i+1] - sorted_nums[i] == 1:
                consecutive_count += 1
        pattern['consecutive_ratio'] = consecutive_count / max(1, len(numbers) - 1)

        return pattern

    def _find_similar_patterns(self, current_pattern, max_results=10):
        """查找类似模式"""
        similar_patterns = []
        threshold = self.config['prediction_params']['pattern_similarity_threshold']

        for i in range(len(self.train_data) - 1):
            # 获取历史期的数字
            historical_numbers = [self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)]
            historical_pattern = self._define_pattern(historical_numbers)

            # 计算模式相似度
            similarity = self._calculate_pattern_similarity(current_pattern, historical_pattern)

            if similarity >= threshold:
                # 获取下一期的数字
                next_numbers = [self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)]

                similar_patterns.append({
                    'similarity': similarity,
                    'historical_numbers': historical_numbers,
                    'next_numbers': next_numbers,
                    'period': i + 1
                })

        # 按相似度排序并返回前max_results个
        similar_patterns.sort(key=lambda x: x['similarity'], reverse=True)
        return similar_patterns[:max_results]

    def _calculate_pattern_similarity(self, pattern1, pattern2):
        """计算模式相似度"""
        if not pattern1 or not pattern2:
            return 0.0

        similarities = []

        # 比较各个特征
        features = ['odd_ratio', 'large_ratio', 'consecutive_ratio']
        for feature in features:
            if feature in pattern1 and feature in pattern2:
                diff = abs(pattern1[feature] - pattern2[feature])
                sim = 1 - diff  # 差异越小，相似度越高
                similarities.append(sim)

        # 比较数值特征（需要标准化）
        if 'sum' in pattern1 and 'sum' in pattern2:
            sum_diff = abs(pattern1['sum'] - pattern2['sum']) / 200  # 假设最大差异为200
            sum_sim = 1 - min(1, sum_diff)
            similarities.append(sum_sim)

        if 'span' in pattern1 and 'span' in pattern2:
            span_diff = abs(pattern1['span'] - pattern2['span']) / 48  # 最大跨度为48
            span_sim = 1 - min(1, span_diff)
            similarities.append(span_sim)

        # 计算平均相似度
        return np.mean(similarities) if similarities else 0.0

    def ensemble_prediction(self, previous_numbers, period_idx, data_source="预测数据"):
        """集成预测"""
        print(f"  🔮 集成预测第{period_idx}期")

        # 获取各模型的预测
        predictions = {}

        # 增强马尔可夫预测
        if 'enhanced_markov' in self.config['ensemble_methods']:
            predictions['markov'] = self.enhanced_markov_prediction(previous_numbers)

        # 频率分析预测
        if 'frequency_analysis' in self.config['ensemble_methods']:
            predictions['frequency'] = self.frequency_analysis_prediction()

        # 模式匹配预测
        if 'pattern_matching' in self.config['ensemble_methods']:
            predictions['pattern'] = self.pattern_matching_prediction(previous_numbers)

        # 集成预测结果
        ensemble_probs = self._combine_predictions(predictions)

        # 生成候选预测
        candidates = self._generate_candidates(ensemble_probs)

        # 选择最佳候选
        best_candidate = self._select_best_candidate(candidates, ensemble_probs)

        # 计算多维度置信度
        confidence, confidence_details = self.calculate_multi_dimensional_confidence(
            best_candidate, candidates, previous_numbers, ensemble_probs,
            period_idx, data_source
        )

        return best_candidate, confidence, confidence_details, candidates

    def _combine_predictions(self, predictions):
        """组合多个预测结果"""
        if not predictions:
            return {num: 1/49 for num in range(1, 50)}

        # 预测方法权重
        weights = {
            'markov': 0.4,
            'frequency': 0.3,
            'pattern': 0.3
        }

        combined_probs = defaultdict(float)

        for method, probs in predictions.items():
            weight = weights.get(method, 1.0 / len(predictions))
            for num, prob in probs.items():
                combined_probs[num] += prob * weight

        return dict(combined_probs)

    def _generate_candidates(self, number_probs):
        """生成候选预测"""
        num_candidates = self.config['prediction_params']['num_candidates']
        candidates = []

        # 按概率排序
        sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)

        # 生成多种候选组合
        for i in range(min(num_candidates, len(sorted_numbers) - 1)):
            for j in range(i + 1, min(i + 10, len(sorted_numbers))):
                candidate = [sorted_numbers[i][0], sorted_numbers[j][0]]
                candidates.append(candidate)

                if len(candidates) >= num_candidates:
                    break

            if len(candidates) >= num_candidates:
                break

        return candidates[:num_candidates]

    def _select_best_candidate(self, candidates, number_probs):
        """选择最佳候选"""
        if not candidates:
            return [1, 2]

        best_candidate = candidates[0]
        best_score = 0

        for candidate in candidates:
            # 计算候选的综合得分
            prob_score = sum(number_probs.get(num, 0) for num in candidate)
            diversity_score = self._calculate_candidate_diversity(candidate)

            total_score = 0.8 * prob_score + 0.2 * diversity_score

            if total_score > best_score:
                best_score = total_score
                best_candidate = candidate

        return best_candidate

    def _calculate_candidate_diversity(self, candidate):
        """计算候选的多样性得分"""
        if len(candidate) < 2:
            return 0.0

        # 奇偶多样性
        odd_count = sum(1 for n in candidate if n % 2 == 1)
        even_count = len(candidate) - odd_count
        parity_diversity = 1 - abs(odd_count - even_count) / len(candidate)

        # 大小多样性
        large_count = sum(1 for n in candidate if n > 25)
        small_count = len(candidate) - large_count
        size_diversity = 1 - abs(large_count - small_count) / len(candidate)

        # 跨度多样性
        span = max(candidate) - min(candidate)
        span_diversity = min(1.0, span / 25)  # 标准化跨度

        return (parity_diversity + size_diversity + span_diversity) / 3

    def predict_single_period(self, previous_numbers, period_idx, data_source="预测数据"):
        """预测单期"""
        print(f"\n🎯 预测第{period_idx}期")
        print(f"  基于前期数字: {sorted(list(previous_numbers))}")
        print(f"  数据来源: {data_source}")

        # 执行集成预测
        predicted_numbers, confidence, confidence_details, candidates = self.ensemble_prediction(
            previous_numbers, period_idx, data_source
        )

        # 记录预测结果
        prediction_result = {
            'period': period_idx,
            'predicted_numbers': predicted_numbers,
            'confidence': confidence,
            'confidence_details': confidence_details,
            'candidates': candidates,
            'previous_numbers': sorted(list(previous_numbers)),
            'data_source': data_source,
            'timestamp': datetime.now().isoformat()
        }

        print(f"  预测数字: {predicted_numbers}")
        print(f"  置信度: {confidence:.3f}")
        print(f"  置信度详情: {confidence_details}")

        return prediction_result

    def validate_prediction(self, prediction_result, actual_numbers):
        """验证预测结果"""
        predicted_numbers = prediction_result['predicted_numbers']

        # 计算命中情况
        hit_numbers = list(set(predicted_numbers) & set(actual_numbers))
        hit_count = len(hit_numbers)
        is_hit = hit_count >= 1

        # 更新预测结果
        prediction_result.update({
            'actual_numbers': sorted(list(actual_numbers)),
            'hit_numbers': hit_numbers,
            'hit_count': hit_count,
            'is_hit': is_hit
        })

        # 添加到历史记录
        self.prediction_history.append(prediction_result)
        self.confidence_history.append(prediction_result['confidence_details'])

        print(f"  实际数字: {sorted(list(actual_numbers))}")
        print(f"  命中数字: {hit_numbers}")
        print(f"  命中情况: {'✅ 成功' if is_hit else '❌ 失败'}")

        return prediction_result

    def batch_predict(self, start_period, end_period, base_data=None):
        """批量预测"""
        print(f"\n🚀 批量预测 {start_period}-{end_period}期")

        predictions = []

        # 获取起始数据
        if base_data is not None:
            previous_numbers = set(base_data)
            data_source = "用户输入"
        else:
            # 使用训练数据的最后一期
            last_row = self.train_data.iloc[-1]
            previous_numbers = set([last_row[f'数字{j}'] for j in range(1, 7)])
            data_source = "历史数据"

        # 逐期预测
        for period in range(start_period, end_period + 1):
            prediction_result = self.predict_single_period(
                previous_numbers, period, data_source
            )
            predictions.append(prediction_result)

            # 更新前期数据为当前预测结果
            previous_numbers = set(prediction_result['predicted_numbers'])
            data_source = "预测数据"

        return predictions

    def evaluate_system_performance(self, test_data=None):
        """评估系统性能"""
        print(f"\n📊 系统性能评估")

        if test_data is None:
            test_data = self.test_data

        if test_data is None:
            print("  ❌ 没有测试数据")
            return None

        predictions = []
        success_count = 0
        total_confidence = 0
        confidence_hit_pairs = []

        # 逐期预测和验证
        for idx, row in test_data.iterrows():
            period_num = row['期号']
            actual_numbers = set([row[f'数字{j}'] for j in range(1, 7)])

            # 获取前期数字
            if idx == test_data.index[0]:
                # 使用训练数据的最后一期
                prev_row = self.train_data.iloc[-1]
                previous_numbers = set([prev_row[f'数字{j}'] for j in range(1, 7)])
                data_source = "历史数据"
            else:
                # 使用测试数据的前一期
                prev_idx = test_data.index[test_data.index.get_loc(idx) - 1]
                prev_row = test_data.loc[prev_idx]
                previous_numbers = set([prev_row[f'数字{j}'] for j in range(1, 7)])
                data_source = "真实数据"

            # 预测
            prediction_result = self.predict_single_period(
                previous_numbers, period_num, data_source
            )

            # 验证
            prediction_result = self.validate_prediction(prediction_result, actual_numbers)
            predictions.append(prediction_result)

            # 统计
            if prediction_result['is_hit']:
                success_count += 1

            total_confidence += prediction_result['confidence']
            confidence_hit_pairs.append((
                prediction_result['confidence'],
                1 if prediction_result['is_hit'] else 0
            ))

        # 计算性能指标
        total_predictions = len(predictions)
        success_rate = success_count / total_predictions if total_predictions > 0 else 0
        avg_confidence = total_confidence / total_predictions if total_predictions > 0 else 0

        # 计算置信度校准
        confidences, hits = zip(*confidence_hit_pairs) if confidence_hit_pairs else ([], [])
        confidence_calibration = np.corrcoef(confidences, hits)[0, 1] if len(confidences) > 1 else 0

        # 性能报告
        performance_report = {
            'total_predictions': total_predictions,
            'success_count': success_count,
            'success_rate': success_rate,
            'avg_confidence': avg_confidence,
            'confidence_calibration': confidence_calibration,
            'predictions': predictions
        }

        print(f"  总预测期数: {total_predictions}")
        print(f"  成功预测: {success_count}")
        print(f"  成功率: {success_rate:.3f} ({success_rate*100:.1f}%)")
        print(f"  平均置信度: {avg_confidence:.3f}")
        print(f"  置信度校准: {confidence_calibration:.3f}")

        return performance_report

    def save_predictions(self, predictions, filename):
        """保存预测结果"""
        print(f"\n💾 保存预测结果到 {filename}")

        # 转换为DataFrame
        data = []
        for pred in predictions:
            row = {
                '期号': pred['period'],
                '预测数字1': pred['predicted_numbers'][0] if len(pred['predicted_numbers']) > 0 else None,
                '预测数字2': pred['predicted_numbers'][1] if len(pred['predicted_numbers']) > 1 else None,
                '置信度': pred['confidence'],
                '基础预测置信度': pred['confidence_details']['base_prediction'],
                '历史表现置信度': pred['confidence_details']['historical_performance'],
                '模式稳定性置信度': pred['confidence_details']['pattern_stability'],
                '数据质量置信度': pred['confidence_details']['data_quality'],
                '预测一致性置信度': pred['confidence_details']['prediction_consistency'],
                '前期数字': str(pred['previous_numbers']),
                '数据来源': pred['data_source'],
                '预测时间': pred['timestamp']
            }

            # 如果有验证结果，添加验证信息
            if 'actual_numbers' in pred:
                row.update({
                    '实际数字1': pred['actual_numbers'][0] if len(pred['actual_numbers']) > 0 else None,
                    '实际数字2': pred['actual_numbers'][1] if len(pred['actual_numbers']) > 1 else None,
                    '实际数字3': pred['actual_numbers'][2] if len(pred['actual_numbers']) > 2 else None,
                    '实际数字4': pred['actual_numbers'][3] if len(pred['actual_numbers']) > 3 else None,
                    '实际数字5': pred['actual_numbers'][4] if len(pred['actual_numbers']) > 4 else None,
                    '实际数字6': pred['actual_numbers'][5] if len(pred['actual_numbers']) > 5 else None,
                    '命中数量': pred['hit_count'],
                    '是否命中': '是' if pred['is_hit'] else '否',
                    '命中数字': str(pred['hit_numbers'])
                })

            data.append(row)

        df = pd.DataFrame(data)
        df.to_csv(filename, index=False, encoding='utf-8')

        print(f"✅ 预测结果已保存，共{len(predictions)}条记录")

        return filename

def main():
    """主函数 - 演示增强置信度预测系统"""
    print("=" * 80)
    print("🚀 增强置信度预测系统演示")
    print("=" * 80)

    # 初始化系统
    system = EnhancedConfidencePredictionSystem()

    # 加载数据
    try:
        system.load_data('2024年训练数据.csv', '2025年测试数据.csv')
    except FileNotFoundError as e:
        print(f"❌ 数据文件未找到: {e}")
        print("请确保以下文件存在:")
        print("  - 2024年训练数据.csv")
        print("  - 2025年测试数据.csv")
        return

    # 系统性能评估
    print("\n" + "="*50)
    print("📊 系统性能评估")
    print("="*50)

    performance = system.evaluate_system_performance()

    if performance:
        # 分析置信度分布
        confidences = [p['confidence'] for p in performance['predictions']]
        hits = [p['is_hit'] for p in performance['predictions']]

        print(f"\n📈 置信度分析:")
        print(f"  置信度范围: {min(confidences):.3f} - {max(confidences):.3f}")
        print(f"  平均置信度: {np.mean(confidences):.3f}")
        print(f"  置信度标准差: {np.std(confidences):.3f}")

        # 按置信度分组分析
        high_conf_predictions = [p for p in performance['predictions'] if p['confidence'] >= 0.6]
        medium_conf_predictions = [p for p in performance['predictions'] if 0.4 <= p['confidence'] < 0.6]
        low_conf_predictions = [p for p in performance['predictions'] if p['confidence'] < 0.4]

        print(f"\n🎯 分层预测效果:")
        if high_conf_predictions:
            high_hit_rate = sum(p['is_hit'] for p in high_conf_predictions) / len(high_conf_predictions)
            print(f"  高置信度(≥0.6): {len(high_conf_predictions)}期, 命中率{high_hit_rate:.3f}")

        if medium_conf_predictions:
            medium_hit_rate = sum(p['is_hit'] for p in medium_conf_predictions) / len(medium_conf_predictions)
            print(f"  中置信度(0.4-0.6): {len(medium_conf_predictions)}期, 命中率{medium_hit_rate:.3f}")

        if low_conf_predictions:
            low_hit_rate = sum(p['is_hit'] for p in low_conf_predictions) / len(low_conf_predictions)
            print(f"  低置信度(<0.4): {len(low_conf_predictions)}期, 命中率{low_hit_rate:.3f}")

        # 保存评估结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        eval_filename = f"enhanced_system_evaluation_{timestamp}.csv"
        system.save_predictions(performance['predictions'], eval_filename)

    # 未来期号预测演示
    print("\n" + "="*50)
    print("🔮 未来期号预测演示")
    print("="*50)

    # 获取最新数据作为基础
    if system.test_data is not None and len(system.test_data) > 0:
        latest_row = system.test_data.iloc[-1]
        latest_numbers = [latest_row[f'数字{j}'] for j in range(1, 7)]
        latest_period = latest_row['期号']

        print(f"基于第{latest_period}期数据: {latest_numbers}")

        # 预测接下来的5期
        future_predictions = system.batch_predict(
            latest_period + 1,
            latest_period + 5,
            latest_numbers
        )

        # 显示预测结果
        print(f"\n🎯 未来5期预测结果:")
        for pred in future_predictions:
            print(f"  第{pred['period']}期: {pred['predicted_numbers']} (置信度: {pred['confidence']:.3f})")

        # 保存未来预测
        future_filename = f"enhanced_future_predictions_{timestamp}.csv"
        system.save_predictions(future_predictions, future_filename)

    # 置信度框架分析
    print("\n" + "="*50)
    print("🧠 多维度置信度框架分析")
    print("="*50)

    if performance and performance['predictions']:
        # 分析各维度置信度的贡献
        dimensions = ['base_prediction', 'historical_performance', 'pattern_stability',
                     'data_quality', 'prediction_consistency']

        print(f"\n📊 各维度置信度统计:")
        for dim in dimensions:
            values = [p['confidence_details'][dim] for p in performance['predictions']]
            print(f"  {dim}: 均值{np.mean(values):.3f}, 标准差{np.std(values):.3f}")

        # 分析维度与命中率的相关性
        print(f"\n🔗 维度与命中率相关性:")
        hits = [1 if p['is_hit'] else 0 for p in performance['predictions']]

        for dim in dimensions:
            dim_values = [p['confidence_details'][dim] for p in performance['predictions']]
            if len(dim_values) > 1:
                correlation = np.corrcoef(dim_values, hits)[0, 1]
                print(f"  {dim}: {correlation:.3f}")

    print("\n" + "="*80)
    print("✅ 增强置信度预测系统演示完成")
    print("="*80)

    # 系统改进建议
    print(f"\n💡 系统改进建议:")
    if performance:
        if performance['success_rate'] < 0.3:
            print("  🔧 建议调整预测方法权重，增强马尔可夫模型比重")
        if performance['confidence_calibration'] < 0.3:
            print("  🔧 建议优化置信度校准机制，提高置信度与命中率的相关性")
        if performance['avg_confidence'] < 0.4:
            print("  🔧 建议调整置信度计算参数，提高整体置信度水平")

        print(f"  📈 当前系统表现: 成功率{performance['success_rate']:.1%}, 平均置信度{performance['avg_confidence']:.3f}")
        print(f"  🎯 优化目标: 成功率35%+, 平均置信度0.6+, 置信度校准0.5+")

if __name__ == "__main__":
    main()
