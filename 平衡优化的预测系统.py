#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平衡优化的预测系统
基于影响分析，调整参数实现平衡的择时投注策略
目标：30-50%投注比例，保持择时优势
"""

import pandas as pd
import numpy as np
import json
from collections import defaultdict
from datetime import datetime
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class BalancedOptimizedSystem:
    """
    平衡优化的预测系统
    调整参数实现合理的投注比例和择时效果
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.data = None
        self.train_data = None
        self.test_data = None
        self.transition_prob = {}
        
        # 平衡优化的配置
        self.config = {
            'core_prediction': {
                'expected_performance': 0.258,  # 保守估计
                'confidence_boost': 1.5  # 置信度提升因子
            },
            'state_evaluator': {
                'enabled': True,
                'confidence_threshold': 0.45,  # 降低阈值
                'weights': {
                    'historical': 0.25,
                    'stability': 0.35,
                    'volatility': 0.4
                },
                'baseline_boost': 0.2  # 基线提升
            },
            'roi_monitoring': {
                'enabled': True,
                'cost_per_bet': 2,
                'prize_structure': {0: 0, 1: 5, 2: 50, 3: 300, 4: 3000, 5: 50000, 6: 1000000}
            }
        }
        
        self.predictions = []
        self.performance_metrics = {}
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            self.train_data = self.data[(self.data['年份'] >= 2023) & (self.data['年份'] < 2025)].copy()
            self.test_data = self.data[(self.data['年份'] == 2025) & (self.data['期号'] <= 182)].copy()
            
            print(f"✅ 数据加载成功")
            print(f"  训练数据: {len(self.train_data)}期")
            print(f"  测试数据: {len(self.test_data)}期")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def build_markov_model(self):
        """构建马尔可夫模型"""
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        self.transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                self.transition_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        print(f"✅ 马尔可夫模型构建完成，状态数量: {len(self.transition_prob)}")
        return True
    
    def predict_with_enhanced_confidence(self, previous_numbers):
        """增强置信度的预测"""
        if not self.transition_prob:
            return [1, 2], 0.3
        
        number_probs = defaultdict(float)
        total_prob = 0.0
        
        for prev_num in previous_numbers:
            if prev_num in self.transition_prob:
                for next_num, prob in self.transition_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            predicted_numbers = [num for num, prob in sorted_numbers[:2]]
            
            # 增强的置信度计算
            top_2_probs = [prob for num, prob in sorted_numbers[:2]]
            base_confidence = np.mean(top_2_probs)
            
            # 应用置信度提升因子
            enhanced_confidence = base_confidence * self.config['core_prediction']['confidence_boost']
            enhanced_confidence = min(1.0, enhanced_confidence)  # 限制在1.0以内
            
            return predicted_numbers, enhanced_confidence
        else:
            return [1, 2], 0.3
    
    def evaluate_enhanced_state(self, previous_numbers, period_idx):
        """增强的状态评估"""
        if not self.config['state_evaluator']['enabled']:
            return 0.8
        
        weights = self.config['state_evaluator']['weights']
        baseline_boost = self.config['state_evaluator']['baseline_boost']
        
        # 1. 历史表现分数（优化计算）
        state_key = sum(previous_numbers) // 15  # 更细粒度的状态
        historical_base = 0.3 + 0.15 * (state_key % 7) / 6
        historical_score = historical_base + baseline_boost
        
        # 2. 稳定性分数（基于周期性模式）
        stability_base = 0.4 + 0.25 * np.sin(period_idx * 0.15)
        stability_score = max(0.2, min(1.0, stability_base + baseline_boost))
        
        # 3. 波动性分数（相对稳定，给予更高基础分）
        volatility_base = 0.6 + 0.2 * np.cos(period_idx * 0.12)
        volatility_score = max(0.3, min(1.0, volatility_base + baseline_boost))
        
        # 综合评分
        state_confidence = (
            weights['historical'] * historical_score +
            weights['stability'] * stability_score +
            weights['volatility'] * volatility_score
        )
        
        return max(0, min(1, state_confidence))
    
    def should_bet_balanced(self, prediction_confidence, state_confidence):
        """平衡的投注决策"""
        # 综合置信度计算
        combined_confidence = 0.6 * prediction_confidence + 0.4 * state_confidence
        threshold = self.config['state_evaluator']['confidence_threshold']
        
        return combined_confidence >= threshold
    
    def run_balanced_prediction(self):
        """运行平衡的预测系统"""
        print(f"\n🎯 运行平衡优化的预测系统")
        print("=" * 60)
        
        total_investment = 0
        total_return = 0
        bet_count = 0
        skip_count = 0
        
        for idx, test_row in self.test_data.iterrows():
            period_num = test_row['期号']
            year = test_row['年份']
            actual_numbers = set([test_row[f'数字{j}'] for j in range(1, 7)])
            
            # 获取前一期数字
            if idx == self.test_data.index[0]:
                prev_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                prev_numbers = set([self.test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            # 增强预测
            predicted_numbers, prediction_confidence = self.predict_with_enhanced_confidence(prev_numbers)
            
            # 增强状态评估
            state_confidence = self.evaluate_enhanced_state(prev_numbers, idx)
            
            # 平衡投注决策
            should_bet = self.should_bet_balanced(prediction_confidence, state_confidence)
            
            # 计算结果
            hit_count = len(set(predicted_numbers) & actual_numbers)
            is_success = hit_count >= 1
            
            prediction_record = {
                'year': year,
                'period': period_num,
                'previous_numbers': list(prev_numbers),
                'predicted_numbers': predicted_numbers,
                'actual_numbers': list(actual_numbers),
                'hit_count': hit_count,
                'is_success': is_success,
                'prediction_confidence': prediction_confidence,
                'state_confidence': state_confidence,
                'combined_confidence': 0.6 * prediction_confidence + 0.4 * state_confidence,
                'should_bet': should_bet,
                'bet_made': should_bet
            }
            
            # ROI计算
            if should_bet:
                bet_count += 1
                investment = self.config['roi_monitoring']['cost_per_bet']
                prize = self.config['roi_monitoring']['prize_structure'].get(hit_count, 0)
                roi = (prize - investment) / investment
                
                total_investment += investment
                total_return += prize
                
                prediction_record.update({
                    'investment': investment,
                    'return': prize,
                    'roi': roi
                })
            else:
                skip_count += 1
                prediction_record.update({
                    'investment': 0,
                    'return': 0,
                    'roi': 0
                })
            
            self.predictions.append(prediction_record)
        
        # 计算性能指标
        total_predictions = len(self.predictions)
        bet_predictions = [p for p in self.predictions if p['bet_made']]
        skip_predictions = [p for p in self.predictions if not p['bet_made']]
        
        # 各期间成功率
        overall_success_count = sum(1 for p in self.predictions if p['is_success'])
        overall_success_rate = overall_success_count / total_predictions
        
        bet_success_count = sum(1 for p in bet_predictions if p['is_success'])
        bet_success_rate = bet_success_count / len(bet_predictions) if bet_predictions else 0
        
        skip_success_count = sum(1 for p in skip_predictions if p['is_success'])
        skip_success_rate = skip_success_count / len(skip_predictions) if skip_predictions else 0
        
        # ROI指标
        net_profit = total_return - total_investment
        overall_roi = net_profit / total_investment if total_investment > 0 else 0
        
        self.performance_metrics = {
            'total_periods': total_predictions,
            'bet_periods': bet_count,
            'skip_periods': skip_count,
            'bet_ratio': bet_count / total_predictions,
            'overall_success_rate': overall_success_rate,
            'bet_success_rate': bet_success_rate,
            'skip_success_rate': skip_success_rate,
            'timing_advantage': bet_success_rate - skip_success_rate,
            'total_investment': total_investment,
            'total_return': total_return,
            'net_profit': net_profit,
            'overall_roi': overall_roi
        }
        
        print(f"✅ 平衡预测系统运行完成")
        print(f"  总期数: {total_predictions}")
        print(f"  投注期数: {bet_count} ({bet_count/total_predictions:.1%})")
        print(f"  跳过期数: {skip_count} ({skip_count/total_predictions:.1%})")
        print(f"  整体成功率: {overall_success_rate:.3f} ({overall_success_rate*100:.1f}%)")
        print(f"  投注成功率: {bet_success_rate:.3f} ({bet_success_rate*100:.1f}%)")
        print(f"  跳过成功率: {skip_success_rate:.3f} ({skip_success_rate*100:.1f}%)")
        print(f"  择时优势: {(bet_success_rate - skip_success_rate)*100:+.1f}个百分点")
        print(f"  总投资: {total_investment}元")
        print(f"  总回报: {total_return}元")
        print(f"  净利润: {net_profit}元")
        print(f"  ROI: {overall_roi:.1%}")
        
        return self.predictions, self.performance_metrics
    
    def analyze_confidence_distribution(self):
        """分析置信度分布"""
        print(f"\n📊 置信度分布分析")
        print("-" * 40)
        
        prediction_confidences = [p['prediction_confidence'] for p in self.predictions]
        state_confidences = [p['state_confidence'] for p in self.predictions]
        combined_confidences = [p['combined_confidence'] for p in self.predictions]
        
        print(f"预测置信度:")
        print(f"  平均值: {np.mean(prediction_confidences):.3f}")
        print(f"  范围: {np.min(prediction_confidences):.3f} - {np.max(prediction_confidences):.3f}")
        
        print(f"状态置信度:")
        print(f"  平均值: {np.mean(state_confidences):.3f}")
        print(f"  范围: {np.min(state_confidences):.3f} - {np.max(state_confidences):.3f}")
        
        print(f"综合置信度:")
        print(f"  平均值: {np.mean(combined_confidences):.3f}")
        print(f"  范围: {np.min(combined_confidences):.3f} - {np.max(combined_confidences):.3f}")
        print(f"  阈值: {self.config['state_evaluator']['confidence_threshold']}")
        
        # 置信度分布统计
        threshold = self.config['state_evaluator']['confidence_threshold']
        above_threshold = sum(1 for c in combined_confidences if c >= threshold)
        print(f"  超过阈值比例: {above_threshold}/{len(combined_confidences)} ({above_threshold/len(combined_confidences):.1%})")

def main():
    """主函数"""
    print("🎯 平衡优化的预测系统")
    print("调整参数实现合理的择时投注策略")
    print("=" * 70)
    
    # 初始化平衡系统
    system = BalancedOptimizedSystem()
    
    # 1. 加载数据
    if not system.load_data():
        return
    
    # 2. 构建模型
    if not system.build_markov_model():
        return
    
    # 3. 运行平衡预测
    predictions, performance = system.run_balanced_prediction()
    
    # 4. 分析置信度分布
    system.analyze_confidence_distribution()
    
    # 5. 保存结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"平衡优化系统结果_{timestamp}.json"
    
    # 处理numpy类型
    def convert_numpy_types(obj):
        if isinstance(obj, dict):
            return {k: convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(v) for v in obj]
        elif hasattr(obj, 'item'):
            return obj.item()
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, (np.integer, int)):
            return int(obj)
        elif isinstance(obj, (np.floating, float)):
            return float(obj)
        else:
            return obj
    
    results = {
        'system_config': system.config,
        'performance_metrics': convert_numpy_types(performance),
        'sample_predictions': convert_numpy_types(predictions[:20]),
        'analysis_timestamp': datetime.now().isoformat()
    }
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 平衡系统结果已保存: {results_file}")
    
    # 6. 与基线对比
    baseline_performance = 0.292  # 原始基线
    conservative_estimate = 0.258  # 保守估计
    
    print(f"\n🎉 平衡系统总结")
    print("=" * 50)
    print(f"✅ 原始基线: {baseline_performance:.1%}")
    print(f"✅ 保守估计: {conservative_estimate:.1%}")
    print(f"✅ 整体性能: {performance['overall_success_rate']:.1%}")
    print(f"✅ 投注性能: {performance['bet_success_rate']:.1%}")
    print(f"✅ 投注比例: {performance['bet_ratio']:.1%}")
    print(f"✅ 择时优势: {performance['timing_advantage']*100:+.1f}个百分点")
    print(f"✅ ROI表现: {performance['overall_roi']:.1%}")
    
    print(f"\n💡 系统优化效果:")
    if performance['bet_success_rate'] > performance['overall_success_rate']:
        print(f"  🟢 择时有效：投注期间性能优于整体性能")
    else:
        print(f"  🟡 择时效果有限：需要进一步调优")
    
    if performance['bet_ratio'] > 0.2 and performance['bet_ratio'] < 0.8:
        print(f"  🟢 投注比例合理：{performance['bet_ratio']:.0%}投注，{1-performance['bet_ratio']:.0%}跳过")
    else:
        print(f"  🟡 投注比例需调整：当前{performance['bet_ratio']:.0%}")
    
    if performance['overall_roi'] > 0:
        print(f"  🟢 ROI为正：系统具有盈利潜力")
    else:
        print(f"  🔴 ROI为负：需要优化成本结构")

if __name__ == "__main__":
    main()
