#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Predictor for Lottery Prediction System
Implements sophisticated ensemble methods and prediction strategies
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
import joblib
import os
from datetime import datetime
from sklearn.ensemble import VotingRegressor, StackingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error
import warnings
warnings.filterwarnings('ignore')


class AdvancedLotteryPredictor:
    """
    Advanced lottery predictor with ensemble methods and prediction diversity
    """
    
    def __init__(self, models_dir: str = 'models', results_dir: str = 'results'):
        self.models_dir = models_dir
        self.results_dir = results_dir
        self.models = {}
        self.scalers = {}
        self.feature_columns = []
        self.ensemble_models = {}
        self.model_features = {}  # Store feature columns for each model

        # Create results directory
        os.makedirs(results_dir, exist_ok=True)
    
    def load_models(self) -> bool:
        """
        Load all trained models and scalers
        """
        try:
            model_files = [f for f in os.listdir(self.models_dir) if f.endswith('.joblib')]
            
            for model_file in model_files:
                model_path = os.path.join(self.models_dir, model_file)
                model_key = model_file.replace('.joblib', '')
                
                if 'scaler' in model_key:
                    self.scalers[model_key] = joblib.load(model_path)
                else:
                    self.models[model_key] = joblib.load(model_path)
            
            print(f"加载了 {len(self.models)} 个模型和 {len(self.scalers)} 个缩放器")
            return len(self.models) > 0
            
        except Exception as e:
            print(f"模型加载失败: {e}")
            return False
    
    def create_ensemble_models(self, target_columns: List[str]) -> Dict:
        """
        Create ensemble models for each target using voting and stacking
        """
        ensemble_results = {}
        
        for target_col in target_columns:
            print(f"\n创建 {target_col} 的集成模型")
            
            # Find models for this target
            target_models = []
            model_names = []
            
            for model_key, model in self.models.items():
                if target_col in model_key:
                    model_name = model_key.replace(f"_{target_col}", "")
                    target_models.append((model_name, model))
                    model_names.append(model_name)
            
            if len(target_models) < 2:
                print(f"  {target_col} 的模型数量不足，跳过集成")
                continue
            
            # Create voting ensemble
            voting_ensemble = VotingRegressor(target_models)
            
            # Create stacking ensemble
            try:
                stacking_ensemble = StackingRegressor(
                    estimators=target_models,
                    final_estimator=LinearRegression(),
                    cv=3
                )
            except:
                stacking_ensemble = None
            
            ensemble_results[target_col] = {
                'voting': voting_ensemble,
                'stacking': stacking_ensemble,
                'individual_models': dict(target_models)
            }
            
            print(f"  创建了投票集成和堆叠集成模型")
        
        self.ensemble_models = ensemble_results
        return ensemble_results
    
    def calculate_model_weights(self, predictions: Dict, actual_values: pd.Series) -> Dict:
        """
        Calculate weights for models based on their performance
        """
        weights = {}
        total_inverse_error = 0
        
        for model_name, pred_values in predictions.items():
            if len(pred_values) == len(actual_values):
                mse = mean_squared_error(actual_values, pred_values)
                # Use inverse of MSE as weight (better models get higher weights)
                weight = 1.0 / (mse + 1e-6)  # Add small epsilon to avoid division by zero
                weights[model_name] = weight
                total_inverse_error += weight
        
        # Normalize weights to sum to 1
        if total_inverse_error > 0:
            for model_name in weights:
                weights[model_name] /= total_inverse_error
        
        return weights
    
    def predict_with_diversity(self, features: pd.DataFrame, target_col: str, 
                              num_predictions: int = 5) -> List[Dict]:
        """
        Generate diverse predictions using different strategies
        """
        predictions = []
        
        # Strategy 1: Weighted ensemble based on historical performance
        pred1 = self.predict_weighted_ensemble(features, target_col)
        predictions.append({
            'prediction': pred1,
            'strategy': 'weighted_ensemble',
            'confidence': 0.8
        })
        
        # Strategy 2: Conservative prediction (median-based)
        pred2 = self.predict_conservative(features, target_col)
        predictions.append({
            'prediction': pred2,
            'strategy': 'conservative',
            'confidence': 0.6
        })
        
        # Strategy 3: Aggressive prediction (best model only)
        pred3 = self.predict_aggressive(features, target_col)
        predictions.append({
            'prediction': pred3,
            'strategy': 'aggressive',
            'confidence': 0.7
        })
        
        # Strategy 4: Random weighted (add some randomness)
        pred4 = self.predict_random_weighted(features, target_col)
        predictions.append({
            'prediction': pred4,
            'strategy': 'random_weighted',
            'confidence': 0.5
        })
        
        # Strategy 5: Range-based prediction
        pred5 = self.predict_range_based(features, target_col)
        predictions.append({
            'prediction': pred5,
            'strategy': 'range_based',
            'confidence': 0.65
        })
        
        return predictions[:num_predictions]
    
    def predict_weighted_ensemble(self, features: pd.DataFrame, target_col: str) -> int:
        """
        Predict using weighted ensemble of all models
        """
        predictions = []
        weights = []

        for model_key, model in self.models.items():
            if target_col in model_key:
                model_name = model_key.replace(f"_{target_col}", "")

                try:
                    # Get the correct features for this model
                    model_features = self.get_model_features(model_key, features.columns.tolist())
                    model_input = features[model_features]

                    # Apply scaling if needed
                    if f"{model_name}_{target_col}" in self.scalers:
                        scaler = self.scalers[f"{model_name}_{target_col}"]
                        features_scaled = scaler.transform(model_input)
                        pred = model.predict(features_scaled)[0]
                    else:
                        pred = model.predict(model_input)[0]

                    predictions.append(pred)
                    weights.append(1.0)  # Simple equal weighting for now

                except Exception as e:
                    print(f"  警告: {model_key} 预测失败: {e}")
                    continue

        if predictions:
            weighted_pred = np.average(predictions, weights=weights)
            return int(np.clip(np.round(weighted_pred), 1, 49))

        return np.random.randint(1, 50)

    def get_model_features(self, model_key: str, available_features: List[str]) -> List[str]:
        """
        Get the correct feature columns for a specific model
        """
        # Try to load feature information from a saved file
        feature_file = os.path.join(self.models_dir, f"{model_key}_features.txt")

        if os.path.exists(feature_file):
            try:
                with open(feature_file, 'r', encoding='utf-8') as f:
                    model_features = [line.strip() for line in f.readlines()]
                # Filter to only include available features
                return [f for f in model_features if f in available_features]
            except:
                pass

        # Fallback: use all available features (may cause issues)
        return available_features
    
    def predict_conservative(self, features: pd.DataFrame, target_col: str) -> int:
        """
        Conservative prediction using median of all models
        """
        predictions = []

        for model_key, model in self.models.items():
            if target_col in model_key:
                model_name = model_key.replace(f"_{target_col}", "")

                try:
                    # Get the correct features for this model
                    model_features = self.get_model_features(model_key, features.columns.tolist())
                    model_input = features[model_features]

                    if f"{model_name}_{target_col}" in self.scalers:
                        scaler = self.scalers[f"{model_name}_{target_col}"]
                        features_scaled = scaler.transform(model_input)
                        pred = model.predict(features_scaled)[0]
                    else:
                        pred = model.predict(model_input)[0]

                    predictions.append(pred)
                except:
                    continue

        if predictions:
            median_pred = np.median(predictions)
            return int(np.clip(np.round(median_pred), 1, 49))

        return np.random.randint(1, 50)
    
    def predict_aggressive(self, features: pd.DataFrame, target_col: str) -> int:
        """
        Aggressive prediction using the best performing model
        """
        # For now, use the first available model (could be improved with performance tracking)
        for model_key, model in self.models.items():
            if target_col in model_key:
                model_name = model_key.replace(f"_{target_col}", "")

                try:
                    # Get the correct features for this model
                    model_features = self.get_model_features(model_key, features.columns.tolist())
                    model_input = features[model_features]

                    if f"{model_name}_{target_col}" in self.scalers:
                        scaler = self.scalers[f"{model_name}_{target_col}"]
                        features_scaled = scaler.transform(model_input)
                        pred = model.predict(features_scaled)[0]
                    else:
                        pred = model.predict(model_input)[0]

                    return int(np.clip(np.round(pred), 1, 49))
                except:
                    continue

        return np.random.randint(1, 50)
    
    def predict_random_weighted(self, features: pd.DataFrame, target_col: str) -> int:
        """
        Random weighted prediction with some noise
        """
        base_pred = self.predict_weighted_ensemble(features, target_col)
        # Add some randomness
        noise = np.random.normal(0, 2)  # Small random noise
        adjusted_pred = base_pred + noise
        return int(np.clip(np.round(adjusted_pred), 1, 49))
    
    def predict_range_based(self, features: pd.DataFrame, target_col: str) -> int:
        """
        Range-based prediction considering typical lottery number ranges
        """
        base_pred = self.predict_weighted_ensemble(features, target_col)
        
        # Adjust based on typical ranges for each position
        position_ranges = {
            '数字1': (1, 15),   # Typically smaller numbers
            '数字2': (5, 25),   # Low-medium range
            '数字3': (10, 35),  # Medium range
            '数字4': (15, 40),  # Medium-high range
            '数字5': (20, 45),  # High range
            '数字6': (25, 49)   # Highest range
        }
        
        if target_col in position_ranges:
            min_val, max_val = position_ranges[target_col]
            # Bias prediction towards the typical range
            if base_pred < min_val:
                adjusted_pred = min_val + np.random.randint(0, 5)
            elif base_pred > max_val:
                adjusted_pred = max_val - np.random.randint(0, 5)
            else:
                adjusted_pred = base_pred
            
            return int(np.clip(adjusted_pred, 1, 49))
        
        return base_pred

    def generate_lottery_predictions(self, data: pd.DataFrame, feature_columns: List[str],
                                   periods: List[int]) -> Dict:
        """
        Generate lottery predictions for multiple periods with diversity
        """
        all_predictions = {}
        target_columns = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']

        for period in periods:
            print(f"\n预测第 {period} 期")

            # Use the latest data for feature extraction
            latest_data = data.tail(10).copy()  # Use last 10 periods for context

            # Create features for prediction
            features = self.create_prediction_features(latest_data, feature_columns)

            period_predictions = {}

            for target_col in target_columns:
                # Generate diverse predictions for this target
                diverse_preds = self.predict_with_diversity(features, target_col, num_predictions=5)

                # Select the best prediction based on confidence
                best_pred = max(diverse_preds, key=lambda x: x['confidence'])
                period_predictions[target_col] = best_pred

            # Ensure no duplicate numbers in the same period
            numbers = [pred['prediction'] for pred in period_predictions.values()]
            numbers = self.ensure_unique_numbers(numbers)

            # Update predictions with unique numbers
            for i, target_col in enumerate(target_columns):
                period_predictions[target_col]['prediction'] = numbers[i]

            all_predictions[period] = {
                'numbers': numbers,
                'details': period_predictions,
                'sum': sum(numbers),
                'odd_count': sum(1 for n in numbers if n % 2 == 1),
                'big_count': sum(1 for n in numbers if n > 25)
            }

            print(f"  预测结果: {numbers}")

        return all_predictions

    def create_prediction_features(self, data: pd.DataFrame, feature_columns: List[str]) -> pd.DataFrame:
        """
        Create features for prediction from the latest data
        """
        # This should match the feature engineering process used in training
        # For now, return the last row of features
        if len(data) > 0:
            return data[feature_columns].tail(1).fillna(0)
        else:
            # Return zeros if no data available
            return pd.DataFrame(0, index=[0], columns=feature_columns)

    def ensure_unique_numbers(self, numbers: List[int]) -> List[int]:
        """
        Ensure all numbers in a lottery draw are unique with better diversity
        """
        unique_numbers = []
        used_numbers = set()

        for i, num in enumerate(numbers):
            if num not in used_numbers:
                unique_numbers.append(num)
                used_numbers.add(num)
            else:
                # Find a replacement number with better diversity
                replacement = self.find_diverse_replacement(num, used_numbers, i)
                unique_numbers.append(replacement)
                used_numbers.add(replacement)

        # Ensure we have exactly 6 numbers
        while len(unique_numbers) < 6:
            available = [i for i in range(1, 50) if i not in used_numbers]
            if available:
                num = np.random.choice(available)
                unique_numbers.append(num)
                used_numbers.add(num)
            else:
                break

        return sorted(unique_numbers[:6])

    def find_diverse_replacement(self, original_num: int, used_numbers: set, position: int) -> int:
        """
        Find a diverse replacement number based on position and historical patterns
        """
        # Define preferred ranges for each position to increase diversity
        position_preferences = {
            0: list(range(1, 20)),    # 数字1: 偏向小数
            1: list(range(5, 30)),    # 数字2: 偏向中小数
            2: list(range(10, 35)),   # 数字3: 偏向中数
            3: list(range(15, 40)),   # 数字4: 偏向中大数
            4: list(range(20, 45)),   # 数字5: 偏向大数
            5: list(range(25, 50))    # 数字6: 偏向最大数
        }

        # Get preferred range for this position
        preferred_range = position_preferences.get(position, list(range(1, 50)))

        # Filter out used numbers
        available_in_range = [n for n in preferred_range if n not in used_numbers]

        if available_in_range:
            # Add some randomness while staying in preferred range
            if len(available_in_range) > 5:
                # Choose from top candidates with some randomness
                candidates = np.random.choice(available_in_range, size=min(5, len(available_in_range)), replace=False)
                return int(np.random.choice(candidates))
            else:
                return int(np.random.choice(available_in_range))

        # Fallback: find any available number
        all_available = [i for i in range(1, 50) if i not in used_numbers]
        if all_available:
            return int(np.random.choice(all_available))

        # Last resort: return original number (shouldn't happen)
        return original_num

    def save_predictions(self, predictions: Dict, filename: str = None) -> str:
        """
        Save predictions to CSV file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"advanced_predictions_{timestamp}.csv"

        filepath = os.path.join(self.results_dir, filename)

        # Convert predictions to DataFrame
        rows = []
        for period, pred_data in predictions.items():
            row = {
                '期号': period,
                '数字1': pred_data['numbers'][0],
                '数字2': pred_data['numbers'][1],
                '数字3': pred_data['numbers'][2],
                '数字4': pred_data['numbers'][3],
                '数字5': pred_data['numbers'][4],
                '数字6': pred_data['numbers'][5],
                '和值': pred_data['sum'],
                '奇数个数': pred_data['odd_count'],
                '大数个数': pred_data['big_count']
            }

            # Add strategy information
            strategies = [pred_data['details'][f'数字{i+1}']['strategy'] for i in range(6)]
            confidences = [pred_data['details'][f'数字{i+1}']['confidence'] for i in range(6)]

            row['主要策略'] = max(set(strategies), key=strategies.count)
            row['平均置信度'] = np.mean(confidences)

            rows.append(row)

        df = pd.DataFrame(rows)
        df.to_csv(filepath, index=False, encoding='utf-8-sig')

        print(f"预测结果已保存到: {filepath}")
        return filepath
