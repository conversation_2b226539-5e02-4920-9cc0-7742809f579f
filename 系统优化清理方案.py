#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统优化清理方案
基于验证实验结果，保留最优方案，删除表现差的方法
确保代码库的清洁性和可维护性
"""

import os
import json
import shutil
from datetime import datetime
from collections import defaultdict

class SystemOptimizationCleaner:
    """
    系统优化清理器
    基于验证结果进行文件清理和优化
    """
    
    def __init__(self):
        self.workspace_root = "."
        self.cleanup_plan = {
            'keep_files': [],      # 保留的文件
            'delete_files': [],    # 删除的文件
            'reasons': {}          # 保留/删除的理由
        }
        
        # 验证结果摘要
        self.validation_results = {
            'single_period': {'accuracy': 0.292, 'credibility': 'A级', 'status': 'KEEP'},
            'rolling': {'accuracy': 0.281, 'credibility': 'A级', 'status': 'KEEP'},
            'probability_dist': {'accuracy': 0.287, 'credibility': 'A级', 'status': 'KEEP'},
            'continuous': {'accuracy': 0.208, 'credibility': 'D级', 'status': 'DELETE'}
        }
    
    def analyze_current_files(self):
        """分析当前文件状态"""
        print(f"📁 分析当前文件状态")
        print("=" * 60)
        
        file_categories = {
            'core_data': [],           # 核心数据文件
            'prediction_algorithms': [], # 预测算法文件
            'validation_reports': [],   # 验证报告
            'experiment_results': [],   # 实验结果
            'temporary_files': [],      # 临时文件
            'documentation': []         # 文档文件
        }
        
        # 扫描所有文件
        for root, dirs, files in os.walk(self.workspace_root):
            for file in files:
                file_path = os.path.join(root, file)
                file_lower = file.lower()
                
                # 分类文件
                if file_lower.endswith('.csv') and 'lottery_data' in file_lower:
                    file_categories['core_data'].append(file_path)
                elif file_lower.endswith('.py') and any(keyword in file_lower for keyword in ['预测', 'prediction']):
                    file_categories['prediction_algorithms'].append(file_path)
                elif file_lower.endswith('.md') and any(keyword in file_lower for keyword in ['验证', 'validation', '报告', 'report']):
                    file_categories['validation_reports'].append(file_path)
                elif file_lower.endswith('.json') and any(keyword in file_lower for keyword in ['结果', 'result', '实验', 'experiment']):
                    file_categories['experiment_results'].append(file_path)
                elif any(file_lower.endswith(ext) for ext in ['.tmp', '.log', '.cache']):
                    file_categories['temporary_files'].append(file_path)
                elif file_lower.endswith('.md'):
                    file_categories['documentation'].append(file_path)
        
        # 显示分类结果
        for category, files in file_categories.items():
            print(f"{category}: {len(files)}个文件")
            for file_path in files[:3]:  # 显示前3个
                print(f"  - {os.path.basename(file_path)}")
            if len(files) > 3:
                print(f"  ... 还有{len(files)-3}个文件")
        
        return file_categories
    
    def create_cleanup_plan(self, file_categories):
        """创建清理方案"""
        print(f"\n📋 创建清理方案")
        print("=" * 60)
        
        # 1. 核心数据文件处理
        for file_path in file_categories['core_data']:
            filename = os.path.basename(file_path)
            if 'lottery_data_clean_no_special.csv' in filename:
                self.cleanup_plan['keep_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "主数据文件，1638期真实数据，A级可信度"
            else:
                self.cleanup_plan['delete_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "临时或重复的数据文件"
        
        # 2. 预测算法文件处理
        for file_path in file_categories['prediction_algorithms']:
            filename = os.path.basename(file_path).lower()
            
            # 保留最优算法
            if any(keyword in filename for keyword in ['单期预测', 'single_period', '正确的单期']):
                self.cleanup_plan['keep_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "单期预测算法，29.2%准确率，A级可信度"
            elif any(keyword in filename for keyword in ['滚动预测', 'rolling']):
                self.cleanup_plan['keep_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "滚动预测算法，28.1%准确率，备选方案"
            elif any(keyword in filename for keyword in ['概率分布', 'probability']):
                self.cleanup_plan['keep_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "概率分布预测，28.7%准确率，风险管理用"
            elif any(keyword in filename for keyword in ['全面预测验证', 'comprehensive']):
                self.cleanup_plan['keep_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "核心验证实验代码，科学价值高"
            elif any(keyword in filename for keyword in ['系统性文件审查', 'systematic']):
                self.cleanup_plan['keep_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "系统审查代码，确保数据一致性"
            # 删除问题算法
            elif any(keyword in filename for keyword in ['连续预测', 'continuous', '20期预测']):
                self.cleanup_plan['delete_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "连续预测算法，20.8%准确率，存在严重缺陷"
            elif any(keyword in filename for keyword in ['重复预测', 'repetitive']):
                self.cleanup_plan['delete_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "导致预测重复的实验代码"
            else:
                # 其他算法文件需要具体分析
                self.cleanup_plan['keep_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "待进一步分析的算法文件"
        
        # 3. 验证报告处理
        for file_path in file_categories['validation_reports']:
            filename = os.path.basename(file_path).lower()
            
            # 保留核心验证报告
            if any(keyword in filename for keyword in ['全面预测方式验证', 'comprehensive_validation']):
                self.cleanup_plan['keep_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "核心验证报告，包含统计显著性检验"
            elif any(keyword in filename for keyword in ['29.2%基线来源验证', 'baseline_verification']):
                self.cleanup_plan['keep_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "基线来源验证报告，确认A级可信度"
            elif any(keyword in filename for keyword in ['系统性文件审查', 'systematic_audit']):
                self.cleanup_plan['keep_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "系统审查报告，确保数据一致性"
            else:
                # 其他验证报告
                self.cleanup_plan['delete_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "中间验证报告，已被最终报告替代"
        
        # 4. 实验结果处理
        for file_path in file_categories['experiment_results']:
            filename = os.path.basename(file_path).lower()
            
            # 检查是否包含错误标记
            if '1181' in filename or '1200' in filename:
                self.cleanup_plan['delete_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "包含错误期数标记（1181-1200期）"
            # 保留最终验证结果
            elif any(keyword in filename for keyword in ['全面预测验证实验结果', 'comprehensive_validation']):
                self.cleanup_plan['keep_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "最终验证实验结果，包含统计检验"
            elif any(keyword in filename for keyword in ['系统性文件审查报告', 'systematic_audit']):
                self.cleanup_plan['keep_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "系统审查结果，确认数据一致性"
            # 删除连续预测相关结果
            elif any(keyword in filename for keyword in ['连续预测', 'continuous']):
                self.cleanup_plan['delete_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "连续预测实验结果，方法已被证实有缺陷"
            else:
                # 其他实验结果
                self.cleanup_plan['delete_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "中间实验结果，已被最终结果替代"
        
        # 5. 临时文件处理
        for file_path in file_categories['temporary_files']:
            self.cleanup_plan['delete_files'].append(file_path)
            self.cleanup_plan['reasons'][file_path] = "临时文件，可以安全删除"
        
        # 6. 文档文件处理
        for file_path in file_categories['documentation']:
            filename = os.path.basename(file_path).lower()
            
            # 保留最终文档
            if any(keyword in filename for keyword in ['最佳实践', 'best_practice', '使用指南', 'guide']):
                self.cleanup_plan['keep_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "最佳实践指南，实用价值高"
            elif any(keyword in filename for keyword in ['readme', '说明']):
                self.cleanup_plan['keep_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "项目说明文档"
            else:
                # 其他文档
                self.cleanup_plan['delete_files'].append(file_path)
                self.cleanup_plan['reasons'][file_path] = "过程文档，已被最终文档替代"
        
        return self.cleanup_plan
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        print(f"\n📊 生成清理报告")
        print("=" * 60)
        
        report = {
            'cleanup_summary': {
                'total_files_analyzed': len(self.cleanup_plan['keep_files']) + len(self.cleanup_plan['delete_files']),
                'files_to_keep': len(self.cleanup_plan['keep_files']),
                'files_to_delete': len(self.cleanup_plan['delete_files']),
                'cleanup_date': datetime.now().isoformat()
            },
            'keep_files': {
                'core_algorithms': [],
                'validation_reports': [],
                'data_files': [],
                'documentation': []
            },
            'delete_files': {
                'failed_methods': [],
                'temporary_files': [],
                'duplicate_files': [],
                'error_files': []
            },
            'optimization_rationale': {
                'performance_based': "基于验证实验结果，保留29.2%+准确率的方法",
                'credibility_based': "基于A级可信度评估，删除D级方法",
                'statistical_based': "基于McNemar检验p=0.0455的统计显著性",
                'practical_based': "基于实际使用价值和维护成本"
            }
        }
        
        # 分类保留文件
        for file_path in self.cleanup_plan['keep_files']:
            filename = os.path.basename(file_path)
            reason = self.cleanup_plan['reasons'][file_path]
            
            if any(keyword in reason for keyword in ['算法', '预测', 'algorithm']):
                report['keep_files']['core_algorithms'].append({
                    'file': filename,
                    'reason': reason
                })
            elif any(keyword in reason for keyword in ['验证', '报告', 'validation', 'report']):
                report['keep_files']['validation_reports'].append({
                    'file': filename,
                    'reason': reason
                })
            elif any(keyword in reason for keyword in ['数据', 'data']):
                report['keep_files']['data_files'].append({
                    'file': filename,
                    'reason': reason
                })
            else:
                report['keep_files']['documentation'].append({
                    'file': filename,
                    'reason': reason
                })
        
        # 分类删除文件
        for file_path in self.cleanup_plan['delete_files']:
            filename = os.path.basename(file_path)
            reason = self.cleanup_plan['reasons'][file_path]
            
            if any(keyword in reason for keyword in ['连续预测', '缺陷', 'continuous']):
                report['delete_files']['failed_methods'].append({
                    'file': filename,
                    'reason': reason
                })
            elif any(keyword in reason for keyword in ['临时', '重复', 'temporary', 'duplicate']):
                report['delete_files']['temporary_files'].append({
                    'file': filename,
                    'reason': reason
                })
            elif any(keyword in reason for keyword in ['错误', 'error']):
                report['delete_files']['error_files'].append({
                    'file': filename,
                    'reason': reason
                })
            else:
                report['delete_files']['duplicate_files'].append({
                    'file': filename,
                    'reason': reason
                })
        
        return report
    
    def display_cleanup_plan(self, report):
        """显示清理方案"""
        print(f"\n📋 系统优化清理方案")
        print("=" * 80)
        
        print(f"清理统计:")
        print(f"  分析文件总数: {report['cleanup_summary']['total_files_analyzed']}")
        print(f"  保留文件数量: {report['cleanup_summary']['files_to_keep']}")
        print(f"  删除文件数量: {report['cleanup_summary']['files_to_delete']}")
        
        print(f"\n🔒 保留文件清单:")
        print("-" * 60)
        
        # 核心算法
        if report['keep_files']['core_algorithms']:
            print(f"\n📊 核心算法文件 ({len(report['keep_files']['core_algorithms'])}个):")
            for item in report['keep_files']['core_algorithms']:
                print(f"  ✅ {item['file']}")
                print(f"     理由: {item['reason']}")
        
        # 验证报告
        if report['keep_files']['validation_reports']:
            print(f"\n📋 验证报告文件 ({len(report['keep_files']['validation_reports'])}个):")
            for item in report['keep_files']['validation_reports']:
                print(f"  ✅ {item['file']}")
                print(f"     理由: {item['reason']}")
        
        # 数据文件
        if report['keep_files']['data_files']:
            print(f"\n💾 数据文件 ({len(report['keep_files']['data_files'])}个):")
            for item in report['keep_files']['data_files']:
                print(f"  ✅ {item['file']}")
                print(f"     理由: {item['reason']}")
        
        # 文档文件
        if report['keep_files']['documentation']:
            print(f"\n📚 文档文件 ({len(report['keep_files']['documentation'])}个):")
            for item in report['keep_files']['documentation']:
                print(f"  ✅ {item['file']}")
                print(f"     理由: {item['reason']}")
        
        print(f"\n🗑️ 删除文件清单:")
        print("-" * 60)
        
        # 失败方法
        if report['delete_files']['failed_methods']:
            print(f"\n❌ 失败方法文件 ({len(report['delete_files']['failed_methods'])}个):")
            for item in report['delete_files']['failed_methods']:
                print(f"  🗑️ {item['file']}")
                print(f"     理由: {item['reason']}")
        
        # 临时文件
        if report['delete_files']['temporary_files']:
            print(f"\n🔄 临时/重复文件 ({len(report['delete_files']['temporary_files'])}个):")
            for item in report['delete_files']['temporary_files']:
                print(f"  🗑️ {item['file']}")
                print(f"     理由: {item['reason']}")
        
        # 错误文件
        if report['delete_files']['error_files']:
            print(f"\n⚠️ 错误文件 ({len(report['delete_files']['error_files'])}个):")
            for item in report['delete_files']['error_files']:
                print(f"  🗑️ {item['file']}")
                print(f"     理由: {item['reason']}")
        
        # 重复文件
        if report['delete_files']['duplicate_files']:
            print(f"\n📄 重复文件 ({len(report['delete_files']['duplicate_files'])}个):")
            for item in report['delete_files']['duplicate_files']:
                print(f"  🗑️ {item['file']}")
                print(f"     理由: {item['reason']}")
        
        print(f"\n💡 优化理由:")
        for key, reason in report['optimization_rationale'].items():
            print(f"  - {reason}")
    
    def execute_cleanup(self, report, dry_run=False):
        """执行清理（默认为实际执行）"""
        if dry_run:
            print(f"\n🔍 试运行模式 - 不会实际删除文件")
            print("=" * 60)
            print(f"如需实际执行清理，请设置 dry_run=False")
            return
        
        print(f"\n🚀 执行实际清理")
        print("=" * 60)
        
        deleted_count = 0
        for file_path in self.cleanup_plan['delete_files']:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"✅ 已删除: {os.path.basename(file_path)}")
                    deleted_count += 1
                else:
                    print(f"⚠️ 文件不存在: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"❌ 删除失败: {os.path.basename(file_path)} - {e}")
        
        print(f"\n清理完成: 成功删除 {deleted_count} 个文件")

def main():
    """主函数"""
    print("🔧 系统优化清理方案")
    print("基于验证实验结果，优化代码库结构")
    print("=" * 80)
    
    # 初始化清理器
    cleaner = SystemOptimizationCleaner()
    
    # 1. 分析当前文件
    file_categories = cleaner.analyze_current_files()
    
    # 2. 创建清理方案
    cleanup_plan = cleaner.create_cleanup_plan(file_categories)
    
    # 3. 生成清理报告
    cleanup_report = cleaner.generate_cleanup_report()
    
    # 4. 显示清理方案
    cleaner.display_cleanup_plan(cleanup_report)
    
    # 5. 保存清理报告
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f"系统优化清理方案_{timestamp}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(cleanup_report, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 清理方案已保存: {report_file}")
    
    # 6. 执行清理（实际执行）
    cleaner.execute_cleanup(cleanup_report, dry_run=False)
    
    print(f"\n🎉 系统优化方案制定完成")
    print("=" * 50)
    print(f"✅ 保留最优方案: 单期预测(29.2%) + 滚动预测(28.1%) + 概率分布(28.7%)")
    print(f"✅ 删除问题方案: 连续预测(20.8%) + 重复预测 + 错误标记文件")
    print(f"✅ 确保数据一致性: 29.2%基线标记统一，A级可信度确认")
    print(f"✅ 代码库优化: 保留核心算法，删除实验代码")

if __name__ == "__main__":
    main()
