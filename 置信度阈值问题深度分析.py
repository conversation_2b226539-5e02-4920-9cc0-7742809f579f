#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
置信度阈值问题深度分析
为什么大部分预测都建议跳过？
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
import json

class ConfidenceThresholdAnalyzer:
    """置信度阈值问题分析器"""
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.data = None
        self.test_data = None
        self.transition_prob = {}
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            # 分割数据
            self.train_data = self.data[self.data['年份'] < 2025].copy()
            self.test_data = self.data[self.data['年份'] == 2025].copy()
            
            print(f"✅ 数据加载成功")
            print(f"  训练数据: {len(self.train_data)}期")
            print(f"  测试数据: {len(self.test_data)}期")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def build_markov_model(self):
        """构建马尔可夫模型"""
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 计算转移概率
        self.transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                self.transition_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        print(f"✅ 马尔可夫模型构建完成，{len(self.transition_prob)}个状态")
        return True
    
    def analyze_confidence_distribution(self):
        """分析置信度分布"""
        print(f"\n🔍 分析置信度分布")
        print("=" * 50)
        
        confidences = []
        predictions = []
        
        for idx, test_row in self.test_data.iterrows():
            if idx == self.test_data.index[0]:
                prev_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                prev_numbers = set([self.test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            # 预测
            predicted_numbers, confidence = self._markov_prediction(prev_numbers)
            confidences.append(confidence)
            predictions.append({
                'period': test_row['期号'],
                'confidence': confidence,
                'predicted': predicted_numbers,
                'actual': [test_row[f'数字{j}'] for j in range(1, 7)]
            })
        
        # 统计分析
        confidences = np.array(confidences)
        
        print(f"置信度统计:")
        print(f"  平均值: {np.mean(confidences):.4f}")
        print(f"  中位数: {np.median(confidences):.4f}")
        print(f"  标准差: {np.std(confidences):.4f}")
        print(f"  最小值: {np.min(confidences):.4f}")
        print(f"  最大值: {np.max(confidences):.4f}")
        
        # 阈值分析
        thresholds = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        
        print(f"\n不同阈值下的投注比例:")
        for threshold in thresholds:
            bet_ratio = np.mean(confidences >= threshold)
            print(f"  阈值{threshold:.1f}: {bet_ratio:.1%} ({int(bet_ratio * len(confidences))}期)")
        
        return predictions, confidences
    
    def _markov_prediction(self, previous_numbers):
        """马尔可夫预测"""
        number_probs = defaultdict(float)
        total_prob = 0.0
        coverage_count = 0
        
        for prev_num in previous_numbers:
            if prev_num in self.transition_prob:
                coverage_count += 1
                for next_num, prob in self.transition_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            predicted_numbers = [num for num, prob in sorted_numbers[:2]]
            
            # 基础置信度计算
            top_2_probs = [prob for num, prob in sorted_numbers[:2]]
            base_confidence = np.mean(top_2_probs)
            
            return predicted_numbers, base_confidence
        else:
            return [1, 2], 0.1
    
    def analyze_threshold_performance(self, predictions, confidences):
        """分析不同阈值下的性能"""
        print(f"\n🎯 分析不同阈值下的性能")
        print("=" * 50)
        
        thresholds = np.arange(0.1, 1.0, 0.05)
        results = []
        
        for threshold in thresholds:
            bet_predictions = [p for p, c in zip(predictions, confidences) if c >= threshold]
            skip_predictions = [p for p, c in zip(predictions, confidences) if c < threshold]

            bet_hits = 0
            skip_hits = 0

            # 计算投注期间的准确率
            if bet_predictions:
                for pred in bet_predictions:
                    actual_set = set(pred['actual'])
                    predicted_set = set(pred['predicted'])
                    if len(actual_set & predicted_set) >= 1:
                        bet_hits += 1
                bet_accuracy = bet_hits / len(bet_predictions)
            else:
                bet_accuracy = 0

            # 计算跳过期间的准确率
            if skip_predictions:
                for pred in skip_predictions:
                    actual_set = set(pred['actual'])
                    predicted_set = set(pred['predicted'])
                    if len(actual_set & predicted_set) >= 1:
                        skip_hits += 1
                skip_accuracy = skip_hits / len(skip_predictions)
            else:
                skip_accuracy = 0

            bet_ratio = len(bet_predictions) / len(predictions)
            overall_accuracy = (bet_hits + skip_hits) / len(predictions)

            results.append({
                'threshold': threshold,
                'bet_ratio': bet_ratio,
                'bet_count': len(bet_predictions),
                'bet_accuracy': bet_accuracy,
                'skip_accuracy': skip_accuracy,
                'overall_accuracy': overall_accuracy
            })
        
        # 找到最优阈值
        best_result = max(results, key=lambda x: x['bet_accuracy'] if x['bet_count'] > 10 else 0)
        
        print(f"阈值性能分析:")
        print(f"{'阈值':<6} {'投注比例':<8} {'投注期数':<8} {'投注准确率':<10} {'跳过准确率':<10}")
        print("-" * 50)
        
        for result in results[::2]:  # 每隔一个显示
            print(f"{result['threshold']:.2f}   {result['bet_ratio']:.1%}      {result['bet_count']:3d}      {result['bet_accuracy']:.3f}      {result['skip_accuracy']:.3f}")
        
        print(f"\n🎯 最优阈值分析:")
        print(f"  当前阈值0.4: 投注{results[6]['bet_ratio']:.1%}, 准确率{results[6]['bet_accuracy']:.3f}")
        print(f"  建议阈值{best_result['threshold']:.2f}: 投注{best_result['bet_ratio']:.1%}, 准确率{best_result['bet_accuracy']:.3f}")
        
        return results
    
    def analyze_confidence_calibration(self, predictions, confidences):
        """分析置信度校准"""
        print(f"\n📊 分析置信度校准")
        print("=" * 50)
        
        # 将置信度分组
        bins = np.arange(0, 1.1, 0.1)
        bin_centers = (bins[:-1] + bins[1:]) / 2
        
        calibration_results = []
        
        for i in range(len(bins) - 1):
            bin_mask = (confidences >= bins[i]) & (confidences < bins[i+1])
            bin_predictions = [p for p, mask in zip(predictions, bin_mask) if mask]
            
            if bin_predictions:
                hits = 0
                for pred in bin_predictions:
                    actual_set = set(pred['actual'])
                    predicted_set = set(pred['predicted'])
                    if len(actual_set & predicted_set) >= 1:
                        hits += 1
                
                actual_accuracy = hits / len(bin_predictions)
                expected_confidence = bin_centers[i]
                calibration_error = abs(actual_accuracy - expected_confidence)
                
                calibration_results.append({
                    'bin_center': bin_centers[i],
                    'count': len(bin_predictions),
                    'expected': expected_confidence,
                    'actual': actual_accuracy,
                    'error': calibration_error
                })
        
        print(f"置信度校准分析:")
        print(f"{'置信度区间':<12} {'期数':<6} {'期望准确率':<10} {'实际准确率':<10} {'校准误差':<8}")
        print("-" * 50)
        
        for result in calibration_results:
            if result['count'] > 0:
                print(f"{result['bin_center']:.1f}          {result['count']:3d}    {result['expected']:.3f}      {result['actual']:.3f}      {result['error']:.3f}")
        
        # 计算平均校准误差
        avg_calibration_error = np.mean([r['error'] for r in calibration_results if r['count'] > 0])
        print(f"\n平均校准误差: {avg_calibration_error:.4f}")
        
        return calibration_results
    
    def theoretical_analysis(self):
        """理论分析"""
        print(f"\n🧠 理论分析：为什么置信度普遍较低？")
        print("=" * 60)
        
        print(f"1. 马尔可夫链的固有特性:")
        print(f"   - 每个数字的转移概率分散到49个目标数字")
        print(f"   - 单个转移概率 ≈ 1/49 ≈ 0.02")
        print(f"   - 即使是最高概率也很难超过0.05")
        
        print(f"\n2. 置信度计算方法:")
        print(f"   - 当前方法：取前2个数字概率的平均值")
        print(f"   - 理论上限：约0.04-0.06")
        print(f"   - 0.4阈值相当于要求20倍于理论期望")
        
        print(f"\n3. 数据稀疏性问题:")
        print(f"   - 历史数据有限（约1000期）")
        print(f"   - 每个状态转移的样本数较少")
        print(f"   - 导致概率估计不稳定")
        
        print(f"\n4. 阈值设置问题:")
        print(f"   - 0.4阈值过于保守")
        print(f"   - 导致几乎所有预测都被跳过")
        print(f"   - 失去了预测系统的实用价值")

def main():
    """主函数"""
    print("🔍 置信度阈值问题深度分析")
    print("为什么大部分预测都建议跳过？")
    print("=" * 80)
    
    analyzer = ConfidenceThresholdAnalyzer()
    
    # 1. 加载数据
    if not analyzer.load_data():
        return
    
    # 2. 构建模型
    if not analyzer.build_markov_model():
        return
    
    # 3. 分析置信度分布
    predictions, confidences = analyzer.analyze_confidence_distribution()
    
    # 4. 分析不同阈值下的性能
    threshold_results = analyzer.analyze_threshold_performance(predictions, confidences)
    
    # 5. 分析置信度校准
    calibration_results = analyzer.analyze_confidence_calibration(predictions, confidences)
    
    # 6. 理论分析
    analyzer.theoretical_analysis()
    
    # 7. 建议
    print(f"\n💡 解决方案建议")
    print("=" * 50)
    print(f"1. 降低阈值：从0.4降低到0.15-0.25")
    print(f"2. 改进置信度计算：使用相对排名而非绝对概率")
    print(f"3. 引入动态阈值：根据历史表现自动调整")
    print(f"4. 多维度评估：结合多个指标而非单一置信度")
    print(f"5. 分层决策：不同置信度区间采用不同策略")

if __name__ == "__main__":
    main()
