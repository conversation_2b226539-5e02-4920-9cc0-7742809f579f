#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2025年数据预测与动态置信度分析
2025 Data Prediction with Dynamic Confidence Analysis

使用新部署的动态置信度调整系统对2025年测试集数据进行预测和分析

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import matplotlib.pyplot as plt
import seaborn as sns
from dynamic_confidence_wrapper import (
    predict_with_confidence,
    calculate_dynamic_confidence,
    update_prediction_feedback,
    get_system_performance,
    get_system_status
)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_and_analyze_data():
    """加载并分析现有数据"""
    print("📊 加载并分析现有数据")
    print("="*50)
    
    # 读取数据
    df = pd.read_csv('prediction_data.csv')
    
    # 基本统计
    print(f"数据总量: {len(df)} 条记录")
    print(f"年份范围: {df['当期年份'].min()} - {df['当期年份'].max()}")
    print(f"期号范围: {df['当期期号'].min()} - {df['当期期号'].max()}")
    
    # 2025年数据统计
    df_2025 = df[df['当期年份'] == 2025]
    print(f"\n2025年数据:")
    print(f"  总记录数: {len(df_2025)}")
    print(f"  期号范围: {df_2025['当期期号'].min()} - {df_2025['当期期号'].max()}")
    
    # 命中率统计
    hit_data = df_2025[df_2025['是否命中'].notna()]
    if len(hit_data) > 0:
        hit_rate = len(hit_data[hit_data['是否命中'] == '是']) / len(hit_data)
        print(f"  历史命中率: {hit_rate:.1%}")
        print(f"  已验证期数: {len(hit_data)}")
    
    # 置信度统计
    confidence_data = df_2025['预测置信度'].dropna()
    if len(confidence_data) > 0:
        print(f"  平均置信度: {confidence_data.mean():.3f}")
        print(f"  置信度范围: {confidence_data.min():.3f} - {confidence_data.max():.3f}")
    
    return df, df_2025

def prepare_training_data(df_2025):
    """准备训练数据"""
    print("\n🔄 准备训练数据")
    print("="*30)
    
    # 提取有实际结果的数据用于训练
    training_data = []
    
    for _, row in df_2025.iterrows():
        # 检查是否有实际数字
        actual_cols = ['实际数字1', '实际数字2', '实际数字3', '实际数字4', '实际数字5', '实际数字6']
        if all(pd.notna(row[col]) for col in actual_cols):
            actual_numbers = [int(row[col]) for col in actual_cols]
            training_data.append(actual_numbers)
    
    print(f"训练数据期数: {len(training_data)}")
    
    return training_data

def predict_next_periods(df_2025, num_predictions=10):
    """预测接下来的期数"""
    print(f"\n🔮 预测接下来的 {num_predictions} 期")
    print("="*50)
    
    # 获取最新的期号
    latest_period = df_2025['当期期号'].max()
    print(f"最新期号: {latest_period}")
    
    # 获取最新的实际数字作为预测基础
    latest_row = df_2025[df_2025['当期期号'] == latest_period].iloc[0]
    
    # 尝试获取最新的实际数字
    actual_cols = ['实际数字1', '实际数字2', '实际数字3', '实际数字4', '实际数字5', '实际数字6']
    if all(pd.notna(latest_row[col]) for col in actual_cols):
        previous_numbers = [int(latest_row[col]) for col in actual_cols]
    else:
        # 如果没有实际数字，使用当期数字
        current_cols = ['当期数字1', '当期数字2', '当期数字3', '当期数字4', '当期数字5', '当期数字6']
        previous_numbers = [int(latest_row[col]) for col in current_cols]
    
    print(f"基础数字: {previous_numbers}")
    
    predictions = []
    
    for i in range(num_predictions):
        period_num = latest_period + i + 1
        
        # 构建预测上下文
        context = {
            'previous_numbers': previous_numbers,
            'data_source': '动态置信度预测',
            'period_idx': period_num,
            'year': 2025,
            'prediction_date': datetime.now().strftime('%Y-%m-%d'),
            'prediction_time': datetime.now().strftime('%H:%M:%S')
        }
        
        # 使用动态置信度系统进行预测
        prediction_result = predict_with_confidence(previous_numbers, context)
        
        # 计算详细的置信度信息
        confidence_details = calculate_dynamic_confidence(
            prediction_result['predicted_numbers'], 
            context
        )
        
        # 构建预测记录
        prediction_record = {
            'period_num': period_num,
            'period_name': f'2025年{period_num}期',
            'previous_numbers': previous_numbers.copy(),
            'predicted_numbers': prediction_result['predicted_numbers'],
            'original_confidence': confidence_details['original_confidence'],
            'adjusted_confidence': confidence_details['adjusted_confidence'],
            'final_confidence': confidence_details['final_confidence'],
            'adjustment_ratio': confidence_details['adjustment_ratio'],
            'integration_mode': confidence_details['integration_mode'],
            'ensemble_method': prediction_result.get('ensemble_method', '未知'),
            'algorithm_weights': prediction_result.get('weights', {}),
            'prediction_timestamp': datetime.now().isoformat()
        }
        
        predictions.append(prediction_record)
        
        print(f"期号 {period_num}: {prediction_result['predicted_numbers']} "
              f"(置信度: {confidence_details['final_confidence']:.3f}, "
              f"调整比例: {confidence_details['adjustment_ratio']:.2f})")
        
        # 更新previous_numbers为当前预测结果（用于下一期预测）
        # 这里我们保持原有的previous_numbers，因为我们没有实际结果
        # previous_numbers = prediction_result['predicted_numbers']
    
    return predictions

def analyze_confidence_patterns(predictions):
    """分析置信度模式"""
    print(f"\n📈 分析置信度模式")
    print("="*30)
    
    # 提取置信度数据
    original_confidences = [p['original_confidence'] for p in predictions]
    adjusted_confidences = [p['adjusted_confidence'] for p in predictions]
    final_confidences = [p['final_confidence'] for p in predictions]
    adjustment_ratios = [p['adjustment_ratio'] for p in predictions]
    
    # 统计分析
    print(f"原始置信度:")
    print(f"  平均值: {np.mean(original_confidences):.3f}")
    print(f"  标准差: {np.std(original_confidences):.3f}")
    print(f"  范围: {np.min(original_confidences):.3f} - {np.max(original_confidences):.3f}")
    
    print(f"\n调整后置信度:")
    print(f"  平均值: {np.mean(adjusted_confidences):.3f}")
    print(f"  标准差: {np.std(adjusted_confidences):.3f}")
    print(f"  范围: {np.min(adjusted_confidences):.3f} - {np.max(adjusted_confidences):.3f}")
    
    print(f"\n最终置信度:")
    print(f"  平均值: {np.mean(final_confidences):.3f}")
    print(f"  标准差: {np.std(final_confidences):.3f}")
    print(f"  范围: {np.min(final_confidences):.3f} - {np.max(final_confidences):.3f}")
    
    print(f"\n调整比例:")
    print(f"  平均值: {np.mean(adjustment_ratios):.3f}")
    print(f"  标准差: {np.std(adjustment_ratios):.3f}")
    print(f"  范围: {np.min(adjustment_ratios):.3f} - {np.max(adjustment_ratios):.3f}")
    
    # 分析调整效果
    improvement_count = sum(1 for p in predictions if p['final_confidence'] > p['original_confidence'])
    print(f"\n调整效果:")
    print(f"  置信度提升的期数: {improvement_count}/{len(predictions)} ({improvement_count/len(predictions):.1%})")
    
    # 集成模式统计
    integration_modes = [p['integration_mode'] for p in predictions]
    mode_counts = {}
    for mode in integration_modes:
        mode_counts[mode] = mode_counts.get(mode, 0) + 1
    
    print(f"\n集成模式分布:")
    for mode, count in mode_counts.items():
        print(f"  {mode}: {count} ({count/len(predictions):.1%})")
    
    return {
        'original_confidences': original_confidences,
        'adjusted_confidences': adjusted_confidences,
        'final_confidences': final_confidences,
        'adjustment_ratios': adjustment_ratios,
        'integration_modes': integration_modes
    }

def create_confidence_visualization(predictions, confidence_stats):
    """创建置信度可视化图表"""
    print(f"\n📊 创建置信度可视化图表")
    print("="*30)
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('2025年预测置信度分析', fontsize=16, fontweight='bold')
    
    # 1. 置信度变化趋势
    ax1 = axes[0, 0]
    periods = [p['period_num'] for p in predictions]
    ax1.plot(periods, confidence_stats['original_confidences'], 'b-o', label='原始置信度', alpha=0.7)
    ax1.plot(periods, confidence_stats['adjusted_confidences'], 'g-s', label='调整置信度', alpha=0.7)
    ax1.plot(periods, confidence_stats['final_confidences'], 'r-^', label='最终置信度', linewidth=2)
    ax1.set_xlabel('期号')
    ax1.set_ylabel('置信度')
    ax1.set_title('置信度变化趋势')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 调整比例分布
    ax2 = axes[0, 1]
    ax2.hist(confidence_stats['adjustment_ratios'], bins=10, alpha=0.7, color='orange', edgecolor='black')
    ax2.axvline(np.mean(confidence_stats['adjustment_ratios']), color='red', linestyle='--', 
                label=f'平均值: {np.mean(confidence_stats["adjustment_ratios"]):.2f}')
    ax2.set_xlabel('调整比例')
    ax2.set_ylabel('频次')
    ax2.set_title('调整比例分布')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 置信度对比箱线图
    ax3 = axes[1, 0]
    confidence_data = [
        confidence_stats['original_confidences'],
        confidence_stats['adjusted_confidences'],
        confidence_stats['final_confidences']
    ]
    ax3.boxplot(confidence_data, labels=['原始', '调整', '最终'])
    ax3.set_ylabel('置信度')
    ax3.set_title('置信度分布对比')
    ax3.grid(True, alpha=0.3)
    
    # 4. 集成模式分布
    ax4 = axes[1, 1]
    mode_counts = {}
    for mode in confidence_stats['integration_modes']:
        mode_counts[mode] = mode_counts.get(mode, 0) + 1
    
    modes = list(mode_counts.keys())
    counts = list(mode_counts.values())
    colors = plt.cm.Set3(np.linspace(0, 1, len(modes)))
    
    wedges, texts, autotexts = ax4.pie(counts, labels=modes, autopct='%1.1f%%', colors=colors)
    ax4.set_title('集成模式分布')
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f'confidence_analysis_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"图表已保存: {filename}")
    
    return filename

def export_predictions(predictions, df_2025):
    """导出预测结果"""
    print(f"\n💾 导出预测结果")
    print("="*30)
    
    # 创建预测结果DataFrame
    prediction_records = []
    
    for pred in predictions:
        record = {
            '预测日期': datetime.now().strftime('%Y-%m-%d'),
            '预测时间': datetime.now().strftime('%H:%M:%S'),
            '当期年份': 2025,
            '当期期号': pred['period_num'] - 1,
            '预测期号': f"2025年{pred['period_num']}期",
            '当期数字1': pred['previous_numbers'][0],
            '当期数字2': pred['previous_numbers'][1],
            '当期数字3': pred['previous_numbers'][2],
            '当期数字4': pred['previous_numbers'][3],
            '当期数字5': pred['previous_numbers'][4],
            '当期数字6': pred['previous_numbers'][5],
            '预测数字1': pred['predicted_numbers'][0],
            '预测数字2': pred['predicted_numbers'][1],
            '原始置信度': pred['original_confidence'],
            '调整置信度': pred['adjusted_confidence'],
            '最终置信度': pred['final_confidence'],
            '调整比例': pred['adjustment_ratio'],
            '集成模式': pred['integration_mode'],
            '预测方法': f"动态置信度-{pred['ensemble_method']}",
            '算法权重': str(pred['algorithm_weights']),
            '实际数字1': None,
            '实际数字2': None,
            '实际数字3': None,
            '实际数字4': None,
            '实际数字5': None,
            '实际数字6': None,
            '命中数量': None,
            '是否命中': None,
            '命中数字': None,
            '备注': '动态置信度预测'
        }
        prediction_records.append(record)
    
    # 创建新的DataFrame
    new_df = pd.DataFrame(prediction_records)
    
    # 导出到新文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f'2025_predictions_dynamic_confidence_{timestamp}.csv'
    new_df.to_csv(filename, index=False, encoding='utf-8-sig')
    
    print(f"预测结果已导出: {filename}")
    print(f"预测期数: {len(predictions)}")
    
    # 同时导出JSON格式的详细数据
    json_filename = f'2025_predictions_detailed_{timestamp}.json'
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump({
            'predictions': predictions,
            'export_timestamp': datetime.now().isoformat(),
            'total_predictions': len(predictions),
            'system_info': get_system_status()
        }, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"详细数据已导出: {json_filename}")
    
    return filename, json_filename

def generate_analysis_report(predictions, confidence_stats, df_2025):
    """生成分析报告"""
    print(f"\n📋 生成分析报告")
    print("="*30)
    
    # 获取系统性能
    system_performance = get_system_performance()
    system_status = get_system_status()
    
    # 创建报告
    report = {
        'report_info': {
            'title': '2025年数据预测与动态置信度分析报告',
            'generation_time': datetime.now().isoformat(),
            'analysis_period': f"2025年{predictions[0]['period_num']}期 - 2025年{predictions[-1]['period_num']}期",
            'total_predictions': len(predictions)
        },
        'data_summary': {
            'historical_data_count': len(df_2025),
            'latest_period': df_2025['当期期号'].max(),
            'prediction_start_period': predictions[0]['period_num'],
            'prediction_end_period': predictions[-1]['period_num']
        },
        'confidence_analysis': {
            'original_confidence': {
                'mean': float(np.mean(confidence_stats['original_confidences'])),
                'std': float(np.std(confidence_stats['original_confidences'])),
                'min': float(np.min(confidence_stats['original_confidences'])),
                'max': float(np.max(confidence_stats['original_confidences']))
            },
            'adjusted_confidence': {
                'mean': float(np.mean(confidence_stats['adjusted_confidences'])),
                'std': float(np.std(confidence_stats['adjusted_confidences'])),
                'min': float(np.min(confidence_stats['adjusted_confidences'])),
                'max': float(np.max(confidence_stats['adjusted_confidences']))
            },
            'final_confidence': {
                'mean': float(np.mean(confidence_stats['final_confidences'])),
                'std': float(np.std(confidence_stats['final_confidences'])),
                'min': float(np.min(confidence_stats['final_confidences'])),
                'max': float(np.max(confidence_stats['final_confidences']))
            },
            'adjustment_ratio': {
                'mean': float(np.mean(confidence_stats['adjustment_ratios'])),
                'std': float(np.std(confidence_stats['adjustment_ratios'])),
                'min': float(np.min(confidence_stats['adjustment_ratios'])),
                'max': float(np.max(confidence_stats['adjustment_ratios']))
            }
        },
        'system_performance': system_performance,
        'system_status': system_status,
        'predictions': predictions
    }
    
    # 保存报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f'2025_prediction_analysis_report_{timestamp}.json'
    
    with open(report_filename, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"分析报告已生成: {report_filename}")
    
    # 生成简要文本报告
    text_report = f"""
2025年数据预测与动态置信度分析报告
{'='*50}

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
分析期间: 2025年{predictions[0]['period_num']}期 - 2025年{predictions[-1]['period_num']}期
预测期数: {len(predictions)}

置信度分析:
{'='*30}
原始置信度: {np.mean(confidence_stats['original_confidences']):.3f} ± {np.std(confidence_stats['original_confidences']):.3f}
调整置信度: {np.mean(confidence_stats['adjusted_confidences']):.3f} ± {np.std(confidence_stats['adjusted_confidences']):.3f}
最终置信度: {np.mean(confidence_stats['final_confidences']):.3f} ± {np.std(confidence_stats['final_confidences']):.3f}
平均调整比例: {np.mean(confidence_stats['adjustment_ratios']):.3f}

系统状态:
{'='*30}
系统版本: {system_status.get('deployment_info', {}).get('system_version', '未知')}
集成模式: {system_status.get('integration_mode', '未知')}
监控状态: {'活跃' if system_status.get('monitoring_active', False) else '非活跃'}
自动校准: {'启用' if system_status.get('auto_calibration_active', False) else '禁用'}

预测结果:
{'='*30}
"""
    
    for i, pred in enumerate(predictions, 1):
        text_report += f"{i:2d}. 期号{pred['period_num']}: {pred['predicted_numbers']} (置信度: {pred['final_confidence']:.3f})\n"
    
    text_filename = f'2025_prediction_summary_{timestamp}.txt'
    with open(text_filename, 'w', encoding='utf-8') as f:
        f.write(text_report)
    
    print(f"简要报告已生成: {text_filename}")
    
    return report_filename, text_filename

def main():
    """主函数"""
    print("🔮 2025年数据预测与动态置信度分析")
    print("="*60)
    
    try:
        # 1. 加载和分析数据
        df, df_2025 = load_and_analyze_data()
        
        # 2. 准备训练数据
        training_data = prepare_training_data(df_2025)
        
        # 3. 进行预测
        predictions = predict_next_periods(df_2025, num_predictions=10)
        
        # 4. 分析置信度模式
        confidence_stats = analyze_confidence_patterns(predictions)
        
        # 5. 创建可视化图表
        chart_filename = create_confidence_visualization(predictions, confidence_stats)
        
        # 6. 导出预测结果
        csv_filename, json_filename = export_predictions(predictions, df_2025)
        
        # 7. 生成分析报告
        report_filename, summary_filename = generate_analysis_report(predictions, confidence_stats, df_2025)
        
        print("\n" + "="*60)
        print("✅ 2025年数据预测与分析完成！")
        print("="*60)
        
        print(f"\n📊 生成的文件:")
        print(f"  预测结果 (CSV): {csv_filename}")
        print(f"  详细数据 (JSON): {json_filename}")
        print(f"  可视化图表: {chart_filename}")
        print(f"  分析报告: {report_filename}")
        print(f"  简要报告: {summary_filename}")
        
        print(f"\n🎯 关键指标:")
        print(f"  预测期数: {len(predictions)}")
        print(f"  平均最终置信度: {np.mean(confidence_stats['final_confidences']):.3f}")
        print(f"  平均调整比例: {np.mean(confidence_stats['adjustment_ratios']):.3f}")
        print(f"  置信度提升率: {sum(1 for p in predictions if p['final_confidence'] > p['original_confidence'])/len(predictions):.1%}")
        
        print(f"\n🚀 动态置信度系统已成功应用于2025年数据预测！")
        
    except Exception as e:
        print(f"\n❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
