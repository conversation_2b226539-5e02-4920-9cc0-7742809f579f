#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测一致性测试
Test prediction consistency after fix
"""

import sys
sys.path.append('.')

from 集成评分系统的预测系统 import IntegratedPredictionSystem

def test_consistency():
    """测试预测一致性"""
    print("🧪 测试预测一致性")
    print("="*30)
    
    # 创建系统实例
    system = IntegratedPredictionSystem()
    
    # 初始化系统
    if not system.initialize_system():
        print("❌ 系统初始化失败")
        return
    
    # 测试输入
    test_input = [10, 12, 15, 24, 25, 43]
    
    print(f"测试输入: {test_input}")
    print("\n一致性模式测试 (use_fixed_seed=True):")
    
    # 一致性模式测试
    system.use_fixed_seed = True
    predictions_consistent = []
    
    for i in range(3):
        pred, confidence = system.predict_next_period(test_input)
        predictions_consistent.append((pred, confidence))
        print(f"  第{i+1}次: {pred} (置信度: {confidence:.6f})")
    
    # 检查一致性
    unique_consistent = len(set(tuple(p[0]) for p in predictions_consistent))
    print(f"一致性结果: {'✅ 一致' if unique_consistent == 1 else '❌ 不一致'}")
    
    print("\n多样性模式测试 (use_fixed_seed=False):")
    
    # 多样性模式测试
    system.use_fixed_seed = False
    predictions_diverse = []
    
    for i in range(3):
        pred, confidence = system.predict_next_period(test_input)
        predictions_diverse.append((pred, confidence))
        print(f"  第{i+1}次: {pred} (置信度: {confidence:.6f})")
    
    # 检查多样性
    unique_diverse = len(set(tuple(p[0]) for p in predictions_diverse))
    print(f"多样性结果: {'✅ 多样' if unique_diverse > 1 else '⚠️ 仍然一致'}")

if __name__ == "__main__":
    test_consistency()
