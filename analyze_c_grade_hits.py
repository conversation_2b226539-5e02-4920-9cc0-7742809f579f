#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析为什么很多命中的预测显示C评分
Analyze why many successful predictions show C grade
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def analyze_hit_predictions_scores():
    """分析命中预测的评分分布"""
    print("🔍 分析命中预测的评分分布")
    print("="*50)
    
    # 加载数据
    df = pd.read_csv('prediction_data.csv')
    
    # 筛选命中的预测
    hit_df = df[df['是否命中'] == '是'].copy()
    hit_df['评分'] = pd.to_numeric(hit_df['预测评分'])
    
    print(f"命中预测总数: {len(hit_df)}")
    print(f"命中预测平均评分: {hit_df['评分'].mean():.1f}分")
    print(f"命中预测评分范围: {hit_df['评分'].min():.1f} - {hit_df['评分'].max():.1f}分")
    
    # 评分分布
    print(f"\n命中预测的评分分布:")
    print(f"≥50分: {len(hit_df[hit_df['评分']>=50])}期 ({len(hit_df[hit_df['评分']>=50])/len(hit_df):.1%})")
    print(f"40-49分: {len(hit_df[(hit_df['评分']>=40)&(hit_df['评分']<50)])}期 ({len(hit_df[(hit_df['评分']>=40)&(hit_df['评分']<50)])/len(hit_df):.1%})")
    print(f"30-39分: {len(hit_df[(hit_df['评分']>=30)&(hit_df['评分']<40)])}期 ({len(hit_df[(hit_df['评分']>=30)&(hit_df['评分']<40)])/len(hit_df):.1%})")
    print(f"20-29分: {len(hit_df[(hit_df['评分']>=20)&(hit_df['评分']<30)])}期 ({len(hit_df[(hit_df['评分']>=20)&(hit_df['评分']<30)])/len(hit_df):.1%})")
    print(f"<20分: {len(hit_df[hit_df['评分']<20])}期 ({len(hit_df[hit_df['评分']<20])/len(hit_df):.1%})")
    
    # C评分的命中预测
    c_grade_hits = hit_df[hit_df['评分等级'].str.contains('C', na=False)]
    print(f"\nC评分命中预测: {len(c_grade_hits)}期 ({len(c_grade_hits)/len(hit_df):.1%})")
    
    return hit_df, c_grade_hits

def analyze_all_predictions_scores():
    """分析所有预测的评分分布"""
    print(f"\n🔍 分析所有预测的评分分布")
    print("="*50)
    
    df = pd.read_csv('prediction_data.csv')
    df['评分'] = pd.to_numeric(df['预测评分'])
    
    print(f"总预测数: {len(df)}")
    print(f"平均评分: {df['评分'].mean():.1f}分")
    print(f"评分范围: {df['评分'].min():.1f} - {df['评分'].max():.1f}分")
    
    # 评分分布
    print(f"\n所有预测的评分分布:")
    print(f"≥50分: {len(df[df['评分']>=50])}期 ({len(df[df['评分']>=50])/len(df):.1%})")
    print(f"40-49分: {len(df[(df['评分']>=40)&(df['评分']<50)])}期 ({len(df[(df['评分']>=40)&(df['评分']<50)])/len(df):.1%})")
    print(f"30-39分: {len(df[(df['评分']>=30)&(df['评分']<40)])}期 ({len(df[(df['评分']>=30)&(df['评分']<40)])/len(df):.1%})")
    print(f"20-29分: {len(df[(df['评分']>=20)&(df['评分']<30)])}期 ({len(df[(df['评分']>=20)&(df['评分']<30)])/len(df):.1%})")
    print(f"<20分: {len(df[df['评分']<20])}期 ({len(df[df['评分']<20])/len(df):.1%})")
    
    return df

def analyze_score_vs_hit_rate():
    """分析评分与命中率的关系"""
    print(f"\n🔍 分析评分与命中率的关系")
    print("="*50)
    
    df = pd.read_csv('prediction_data.csv')
    df['评分'] = pd.to_numeric(df['预测评分'])
    
    # 按评分区间分析命中率
    score_ranges = [
        (50, 100, "≥50分"),
        (40, 50, "40-49分"),
        (30, 40, "30-39分"),
        (20, 30, "20-29分"),
        (0, 20, "<20分")
    ]
    
    print("评分区间与命中率关系:")
    for min_score, max_score, label in score_ranges:
        range_df = df[(df['评分'] >= min_score) & (df['评分'] < max_score)]
        if len(range_df) > 0:
            hits = len(range_df[range_df['是否命中'] == '是'])
            hit_rate = hits / len(range_df)
            print(f"{label}: {hits}/{len(range_df)} = {hit_rate:.1%}")
        else:
            print(f"{label}: 无数据")

def analyze_c_grade_examples():
    """分析C评分命中的具体例子"""
    print(f"\n🔍 分析C评分命中的具体例子")
    print("="*50)
    
    df = pd.read_csv('prediction_data.csv')
    hit_df = df[df['是否命中'] == '是'].copy()
    
    # C评分的命中预测
    c_grade_hits = hit_df[hit_df['评分等级'].str.contains('C', na=False)]
    
    print(f"C评分命中预测示例 (前10个):")
    for i, (_, row) in enumerate(c_grade_hits.head(10).iterrows(), 1):
        print(f"{i:2d}. {row['预测期号']}: 预测[{row['预测数字1']},{row['预测数字2']}] "
              f"{row['预测评分']}分 {row['评分等级']} ✅")
    
    # 分析C评分命中预测的特征
    c_grade_hits['评分'] = pd.to_numeric(c_grade_hits['预测评分'])
    print(f"\nC评分命中预测特征:")
    print(f"数量: {len(c_grade_hits)}期")
    print(f"平均评分: {c_grade_hits['评分'].mean():.1f}分")
    print(f"评分范围: {c_grade_hits['评分'].min():.1f} - {c_grade_hits['评分'].max():.1f}分")
    
    # 分析预测数字特征
    pred_nums = []
    for _, row in c_grade_hits.iterrows():
        pred_nums.extend([row['预测数字1'], row['预测数字2']])
    
    from collections import Counter
    num_freq = Counter(pred_nums)
    print(f"\nC评分命中预测中最常见的数字:")
    for num, freq in num_freq.most_common(10):
        print(f"数字{num}: {freq}次")

def analyze_scoring_system_bias():
    """分析评分系统的偏差"""
    print(f"\n🔍 分析评分系统的偏差")
    print("="*50)
    
    df = pd.read_csv('prediction_data.csv')
    df['评分'] = pd.to_numeric(df['预测评分'])
    
    # 计算理论命中率 vs 实际命中率
    overall_hit_rate = len(df[df['是否命中'] == '是']) / len(df)
    print(f"整体命中率: {overall_hit_rate:.1%}")
    
    # 分析评分系统的校准情况
    print(f"\n评分系统校准分析:")
    
    # 按评分分组，看预测概率vs实际命中率
    score_bins = [0, 20, 30, 40, 50, 100]
    for i in range(len(score_bins)-1):
        min_score, max_score = score_bins[i], score_bins[i+1]
        bin_df = df[(df['评分'] >= min_score) & (df['评分'] < max_score)]
        
        if len(bin_df) > 0:
            actual_hit_rate = len(bin_df[bin_df['是否命中'] == '是']) / len(bin_df)
            avg_predicted_prob = pd.to_numeric(bin_df['评分概率']).mean()
            
            print(f"{min_score}-{max_score}分: 预测概率{avg_predicted_prob:.1%}, "
                  f"实际命中率{actual_hit_rate:.1%}, "
                  f"差异{actual_hit_rate-avg_predicted_prob:+.1%}")

def generate_insights():
    """生成深度洞察"""
    print(f"\n💡 深度洞察分析")
    print("="*50)
    
    df = pd.read_csv('prediction_data.csv')
    df['评分'] = pd.to_numeric(df['预测评分'])
    hit_df = df[df['是否命中'] == '是']
    
    # 关键发现
    total_hits = len(hit_df)
    c_grade_hits = len(hit_df[hit_df['评分等级'].str.contains('C', na=False)])
    c_grade_percentage = c_grade_hits / total_hits
    
    avg_hit_score = hit_df['评分'].mean()
    avg_all_score = df['评分'].mean()
    
    print(f"关键发现:")
    print(f"1. {c_grade_percentage:.1%}的命中预测是C评分 ({c_grade_hits}/{total_hits})")
    print(f"2. 命中预测平均评分({avg_hit_score:.1f}分) 仅略高于总体平均({avg_all_score:.1f}分)")
    print(f"3. 评分系统整体偏保守，最高评分仅{df['评分'].max():.1f}分")
    
    # 可能的原因分析
    print(f"\n可能原因分析:")
    print(f"1. 评分系统训练时过于保守，避免过度自信")
    print(f"2. 彩票预测本身具有高度随机性，难以准确评分")
    print(f"3. 评分特征可能无法完全捕捉预测成功的关键因素")
    print(f"4. 训练数据中成功预测样本相对较少，模型学习不充分")
    
    # 系统表现评估
    high_score_df = df[df['评分'] >= 40]
    if len(high_score_df) > 0:
        high_score_hit_rate = len(high_score_df[high_score_df['是否命中'] == '是']) / len(high_score_df)
        print(f"\n系统表现:")
        print(f"≥40分预测命中率: {high_score_hit_rate:.1%}")
        print(f"这说明评分系统{'有一定' if high_score_hit_rate > 0.4 else '缺乏'}区分能力")

def main():
    """主函数"""
    print("🤔 思辨分析：为什么很多命中的预测显示C评分")
    print("="*70)
    
    # 1. 分析命中预测的评分分布
    hit_df, c_grade_hits = analyze_hit_predictions_scores()
    
    # 2. 分析所有预测的评分分布
    all_df = analyze_all_predictions_scores()
    
    # 3. 分析评分与命中率的关系
    analyze_score_vs_hit_rate()
    
    # 4. 分析C评分命中的具体例子
    analyze_c_grade_examples()
    
    # 5. 分析评分系统的偏差
    analyze_scoring_system_bias()
    
    # 6. 生成深度洞察
    generate_insights()
    
    print(f"\n🎯 结论:")
    print(f"C评分命中预测多的主要原因是评分系统整体偏保守，")
    print(f"大部分预测都被评为低分，但这并不意味着评分系统无效。")
    print(f"关键是要理解评分的相对意义，而不是绝对数值。")

if __name__ == "__main__":
    main()
