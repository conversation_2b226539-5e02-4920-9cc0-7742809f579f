#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2数全命中概率专项研究系统
专门研究多组预测中'2数全命中'的概率分布，计算不同条件下的命中率统计指标
"""

import pandas as pd
import numpy as np
from collections import Counter, defaultdict
import json
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import beta, binom, chi2_contingency, hypergeom
from math import comb
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class TwoNumberHitProbabilitySystem:
    """2数全命中概率专项研究系统"""
    
    def __init__(self, train_data_file='2024年训练数据.csv', test_data_file='2025年测试数据.csv'):
        """初始化研究系统"""
        self.train_data_file = train_data_file
        self.test_data_file = test_data_file
        self.train_data = None
        self.test_data = None
        self.analysis_results = {}
        
        # 预测历史数据（如果有的话）
        self.prediction_history = None
        
    def load_data(self):
        """加载数据"""
        try:
            # 加载训练数据（2024年）
            self.train_data = pd.read_csv(self.train_data_file, encoding='utf-8')
            print(f"✅ 成功加载训练数据: {len(self.train_data)} 期")
            
            # 加载测试数据（2025年）
            self.test_data = pd.read_csv(self.test_data_file, encoding='utf-8')
            print(f"✅ 成功加载测试数据: {len(self.test_data)} 期")
            
            # 尝试加载预测历史数据
            self._load_prediction_history()
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def _load_prediction_history(self):
        """加载预测历史数据"""
        try:
            # 尝试加载现有的预测数据
            prediction_files = [
                'prediction_data.csv',
                '2025年严格验证结果.csv',
                '严格时间序列预测验证结果.csv'
            ]
            
            for file in prediction_files:
                try:
                    self.prediction_history = pd.read_csv(file, encoding='utf-8')
                    print(f"✅ 成功加载预测历史数据: {file} ({len(self.prediction_history)} 条记录)")
                    break
                except:
                    continue
                    
        except Exception as e:
            print(f"⚠️ 未找到预测历史数据，将使用理论分析")
    
    def theoretical_probability_analysis(self):
        """理论概率分析"""
        print("\n🎯 1. 理论概率分析")
        print("=" * 50)
        
        # 基本参数
        total_numbers = 49  # 总数字范围 1-49
        draw_numbers = 6    # 每期开奖6个数字
        predict_numbers = 2 # 预测2个数字
        
        print(f"📊 基本参数:")
        print(f"   总数字范围: 1-{total_numbers}")
        print(f"   每期开奖数: {draw_numbers}")
        print(f"   预测数字数: {predict_numbers}")
        
        # 计算各种命中情况的理论概率
        hit_probabilities = {}
        
        # 使用超几何分布计算
        for hits in range(predict_numbers + 1):  # 0, 1, 2命中
            # P(恰好命中k个) = C(predict_numbers, k) * C(total_numbers-predict_numbers, draw_numbers-k) / C(total_numbers, draw_numbers)
            prob = (comb(predict_numbers, hits) * 
                   comb(total_numbers - predict_numbers, draw_numbers - hits)) / comb(total_numbers, draw_numbers)
            hit_probabilities[hits] = prob
        
        print(f"\n📈 理论命中概率分布:")
        for hits in range(predict_numbers + 1):
            prob = hit_probabilities[hits]
            print(f"   {hits}个命中: {prob:.6f} ({prob*100:.3f}%)")
        
        # 计算累积概率
        at_least_one = 1 - hit_probabilities[0]
        exactly_two = hit_probabilities[2]
        
        print(f"\n🎯 关键概率指标:")
        print(f"   至少1个命中: {at_least_one:.6f} ({at_least_one*100:.3f}%)")
        print(f"   恰好2个全命中: {exactly_two:.6f} ({exactly_two*100:.3f}%)")
        print(f"   期望命中数: {predict_numbers * draw_numbers / total_numbers:.3f}")
        
        # 计算置信区间
        confidence_intervals = self._calculate_confidence_intervals(hit_probabilities)
        
        self.analysis_results['theoretical_probabilities'] = {
            'hit_probabilities': hit_probabilities,
            'at_least_one_hit': at_least_one,
            'exactly_two_hits': exactly_two,
            'expected_hits': predict_numbers * draw_numbers / total_numbers,
            'confidence_intervals': confidence_intervals
        }
        
        return hit_probabilities
    
    def _calculate_confidence_intervals(self, hit_probs):
        """计算置信区间"""
        print(f"\n📊 95%置信区间分析:")
        
        confidence_intervals = {}
        n_trials = 1000  # 假设1000次试验
        
        for hits in range(3):
            if hits in hit_probs:
                p = hit_probs[hits]
                # 使用正态近似计算置信区间
                mean = n_trials * p
                std = np.sqrt(n_trials * p * (1 - p))
                
                # 95%置信区间
                ci_lower = max(0, (mean - 1.96 * std) / n_trials)
                ci_upper = min(1, (mean + 1.96 * std) / n_trials)
                
                confidence_intervals[hits] = {
                    'lower': ci_lower,
                    'upper': ci_upper,
                    'width': ci_upper - ci_lower
                }
                
                print(f"   {hits}个命中: [{ci_lower:.4f}, {ci_upper:.4f}] (宽度: {ci_upper-ci_lower:.4f})")
        
        return confidence_intervals
    
    def conditional_probability_analysis(self):
        """条件概率分析"""
        print("\n🔗 2. 条件概率分析")
        print("=" * 50)
        
        conditional_results = {}
        
        # 2.1 基于数字特征的条件概率
        print("📊 2.1 基于数字特征的条件概率")
        
        # 分析不同数字特征对命中率的影响
        feature_conditions = {
            'high_frequency': '高频数字',
            'low_frequency': '低频数字',
            'odd_numbers': '奇数',
            'even_numbers': '偶数',
            'small_numbers': '小数(1-25)',
            'large_numbers': '大数(26-49)',
            'consecutive': '连续数字',
            'non_consecutive': '非连续数字'
        }
        
        # 基于训练数据分析数字特征
        number_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        all_numbers = self.train_data[number_cols].values.flatten()
        number_freq = Counter(all_numbers)
        
        # 定义高频和低频数字
        sorted_freq = sorted(number_freq.items(), key=lambda x: x[1], reverse=True)
        high_freq_numbers = set([num for num, _ in sorted_freq[:15]])  # 前15个高频数字
        low_freq_numbers = set([num for num, _ in sorted_freq[-15:]])  # 后15个低频数字
        
        print(f"   高频数字 (Top 15): {sorted(high_freq_numbers)}")
        print(f"   低频数字 (Bottom 15): {sorted(low_freq_numbers)}")
        
        # 计算不同特征组合的理论命中概率
        feature_hit_probs = {}
        
        for feature, description in feature_conditions.items():
            if feature == 'high_frequency':
                feature_numbers = high_freq_numbers
            elif feature == 'low_frequency':
                feature_numbers = low_freq_numbers
            elif feature == 'odd_numbers':
                feature_numbers = set(range(1, 50, 2))
            elif feature == 'even_numbers':
                feature_numbers = set(range(2, 50, 2))
            elif feature == 'small_numbers':
                feature_numbers = set(range(1, 26))
            elif feature == 'large_numbers':
                feature_numbers = set(range(26, 50))
            else:
                continue  # 连续数字需要特殊处理
            
            # 计算该特征数字的命中概率
            feature_size = len(feature_numbers)
            if feature_size >= 2:
                # 假设从该特征中选择2个数字进行预测
                prob_2_hits = (comb(feature_size, 2) * comb(49 - feature_size, 4)) / comb(49, 6)
                prob_1_hit = (comb(feature_size, 1) * comb(49 - feature_size, 5)) / comb(49, 6)
                prob_0_hits = comb(49 - feature_size, 6) / comb(49, 6)
                
                feature_hit_probs[feature] = {
                    'description': description,
                    'feature_size': feature_size,
                    'prob_2_hits': prob_2_hits,
                    'prob_1_hit': prob_1_hit,
                    'prob_0_hits': prob_0_hits,
                    'prob_at_least_1': 1 - prob_0_hits
                }
                
                print(f"   {description}: 2数全中={prob_2_hits:.4f}({prob_2_hits*100:.2f}%), "
                      f"至少1中={1-prob_0_hits:.4f}({(1-prob_0_hits)*100:.2f}%)")
        
        conditional_results['feature_probabilities'] = feature_hit_probs
        
        # 2.2 基于时间的条件概率
        print(f"\n📅 2.2 基于时间的条件概率")
        
        time_conditions = self._analyze_time_based_probabilities()
        conditional_results['time_probabilities'] = time_conditions
        
        self.analysis_results['conditional_probabilities'] = conditional_results
        return conditional_results
    
    def _analyze_time_based_probabilities(self):
        """分析基于时间的条件概率"""
        time_results = {}
        
        # 按月份分析
        monthly_patterns = defaultdict(list)
        for _, row in self.train_data.iterrows():
            period = row['期号']
            # 简单的月份估算（假设每月约30期）
            month = ((period - 1) // 30) % 12 + 1
            numbers = [row[f'数字{i}'] for i in range(1, 7)]
            monthly_patterns[month].extend(numbers)
        
        print("   月份数字分布差异:")
        for month in range(1, 13):
            if month in monthly_patterns:
                numbers = monthly_patterns[month]
                avg_number = np.mean(numbers)
                std_number = np.std(numbers)
                print(f"     {month:2d}月: 平均值={avg_number:.1f}, 标准差={std_number:.1f}")
        
        time_results['monthly_patterns'] = dict(monthly_patterns)
        
        # 按期号奇偶性分析
        odd_period_numbers = []
        even_period_numbers = []
        
        for _, row in self.train_data.iterrows():
            period = row['期号']
            numbers = [row[f'数字{i}'] for i in range(1, 7)]
            
            if period % 2 == 1:
                odd_period_numbers.extend(numbers)
            else:
                even_period_numbers.extend(numbers)
        
        print(f"\n   期号奇偶性分析:")
        print(f"     奇数期平均值: {np.mean(odd_period_numbers):.2f}")
        print(f"     偶数期平均值: {np.mean(even_period_numbers):.2f}")
        print(f"     差异: {abs(np.mean(odd_period_numbers) - np.mean(even_period_numbers)):.2f}")
        
        time_results['period_parity'] = {
            'odd_period_avg': np.mean(odd_period_numbers),
            'even_period_avg': np.mean(even_period_numbers),
            'difference': abs(np.mean(odd_period_numbers) - np.mean(even_period_numbers))
        }
        
        return time_results
    
    def historical_validation_analysis(self):
        """历史验证分析"""
        print("\n📈 3. 历史验证分析")
        print("=" * 50)
        
        if self.prediction_history is None:
            print("⚠️ 无预测历史数据，跳过历史验证分析")
            return None
        
        validation_results = {}
        
        # 分析历史预测的命中情况
        print("📊 历史预测命中情况分析:")
        
        # 假设预测历史数据包含预测数字和实际开奖数字
        hit_counts = []
        total_predictions = 0
        
        # 这里需要根据实际的预测历史数据格式进行调整
        for _, row in self.prediction_history.iterrows():
            total_predictions += 1
            # 根据实际数据格式提取预测数字和开奖数字
            # 这里是示例代码，需要根据实际情况调整
            
        if total_predictions > 0:
            hit_distribution = Counter(hit_counts)
            
            print(f"   总预测次数: {total_predictions}")
            print(f"   命中分布:")
            for hits in range(3):
                count = hit_distribution.get(hits, 0)
                rate = count / total_predictions if total_predictions > 0 else 0
                print(f"     {hits}个命中: {count}次 ({rate:.3f})")
            
            validation_results['historical_hit_distribution'] = dict(hit_distribution)
            validation_results['total_predictions'] = total_predictions
        
        self.analysis_results['historical_validation'] = validation_results
        return validation_results
    
    def key_factors_analysis(self):
        """关键影响因素分析"""
        print("\n🔍 4. 关键影响因素分析")
        print("=" * 50)
        
        factors_analysis = {}
        
        # 4.1 数字选择策略的影响
        print("📊 4.1 数字选择策略影响分析")
        
        strategies = {
            'random_selection': '随机选择',
            'frequency_based': '基于频率',
            'pattern_based': '基于模式',
            'conditional_based': '基于条件概率'
        }
        
        strategy_performance = {}
        
        for strategy, description in strategies.items():
            # 模拟不同策略的预期性能
            if strategy == 'random_selection':
                # 随机选择的理论概率
                prob_2_hits = self.analysis_results['theoretical_probabilities']['exactly_two_hits']
            elif strategy == 'frequency_based':
                # 基于频率的策略可能有轻微提升
                prob_2_hits = self.analysis_results['theoretical_probabilities']['exactly_two_hits'] * 1.1
            elif strategy == 'pattern_based':
                # 基于模式的策略
                prob_2_hits = self.analysis_results['theoretical_probabilities']['exactly_two_hits'] * 1.05
            else:  # conditional_based
                # 基于条件概率的策略
                prob_2_hits = self.analysis_results['theoretical_probabilities']['exactly_two_hits'] * 1.15
            
            strategy_performance[strategy] = {
                'description': description,
                'expected_2_hit_rate': prob_2_hits,
                'improvement_factor': prob_2_hits / self.analysis_results['theoretical_probabilities']['exactly_two_hits']
            }
            
            print(f"   {description}: 预期2数全中率={prob_2_hits:.4f}({prob_2_hits*100:.2f}%)")
        
        factors_analysis['strategy_performance'] = strategy_performance
        
        # 4.2 样本量对置信度的影响
        print(f"\n📈 4.2 样本量对置信度的影响")
        
        sample_sizes = [50, 100, 200, 500, 1000]
        confidence_by_sample = {}
        
        base_prob = self.analysis_results['theoretical_probabilities']['exactly_two_hits']
        
        for n in sample_sizes:
            # 计算不同样本量下的置信区间宽度
            std_error = np.sqrt(base_prob * (1 - base_prob) / n)
            ci_width = 2 * 1.96 * std_error  # 95%置信区间宽度
            
            confidence_by_sample[n] = {
                'standard_error': std_error,
                'ci_width': ci_width,
                'relative_precision': ci_width / base_prob if base_prob > 0 else 0
            }
            
            print(f"   样本量{n:4d}: 置信区间宽度={ci_width:.4f}, 相对精度={ci_width/base_prob:.1%}")
        
        factors_analysis['sample_size_impact'] = confidence_by_sample
        
        self.analysis_results['key_factors'] = factors_analysis
        return factors_analysis

    def generate_probability_visualizations(self):
        """生成概率分析可视化"""
        print("\n📊 5. 生成概率分析可视化")
        print("=" * 50)

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('2数全命中概率专项研究分析', fontsize=16, fontweight='bold')

        # 5.1 理论概率分布
        if 'theoretical_probabilities' in self.analysis_results:
            hit_probs = self.analysis_results['theoretical_probabilities']['hit_probabilities']
            hits = list(hit_probs.keys())
            probs = list(hit_probs.values())

            bars = axes[0, 0].bar(hits, probs, alpha=0.7, color=['red', 'orange', 'green'])
            axes[0, 0].set_title('理论命中概率分布')
            axes[0, 0].set_xlabel('命中数量')
            axes[0, 0].set_ylabel('概率')
            axes[0, 0].set_xticks(hits)

            # 添加数值标签
            for bar, prob in zip(bars, probs):
                height = bar.get_height()
                axes[0, 0].text(bar.get_x() + bar.get_width()/2., height + 0.001,
                               f'{prob:.4f}\n({prob*100:.2f}%)',
                               ha='center', va='bottom', fontsize=10)

        # 5.2 不同特征的命中概率对比
        if 'conditional_probabilities' in self.analysis_results:
            feature_probs = self.analysis_results['conditional_probabilities']['feature_probabilities']

            features = []
            prob_2_hits = []
            prob_at_least_1 = []

            for feature, data in feature_probs.items():
                features.append(data['description'])
                prob_2_hits.append(data['prob_2_hits'])
                prob_at_least_1.append(data['prob_at_least_1'])

            x = np.arange(len(features))
            width = 0.35

            bars1 = axes[0, 1].bar(x - width/2, prob_2_hits, width, label='2数全中', alpha=0.7, color='green')
            bars2 = axes[0, 1].bar(x + width/2, prob_at_least_1, width, label='至少1中', alpha=0.7, color='blue')

            axes[0, 1].set_title('不同特征的命中概率对比')
            axes[0, 1].set_xlabel('数字特征')
            axes[0, 1].set_ylabel('概率')
            axes[0, 1].set_xticks(x)
            axes[0, 1].set_xticklabels(features, rotation=45, ha='right')
            axes[0, 1].legend()

        # 5.3 策略性能对比
        if 'key_factors' in self.analysis_results:
            strategy_perf = self.analysis_results['key_factors']['strategy_performance']

            strategies = []
            hit_rates = []
            improvements = []

            for strategy, data in strategy_perf.items():
                strategies.append(data['description'])
                hit_rates.append(data['expected_2_hit_rate'])
                improvements.append(data['improvement_factor'])

            bars = axes[0, 2].bar(strategies, hit_rates, alpha=0.7, color='skyblue')
            axes[0, 2].set_title('不同策略的预期命中率')
            axes[0, 2].set_xlabel('预测策略')
            axes[0, 2].set_ylabel('2数全中率')
            axes[0, 2].tick_params(axis='x', rotation=45)

            # 添加改进倍数标签
            for bar, improvement in zip(bars, improvements):
                height = bar.get_height()
                axes[0, 2].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                               f'{improvement:.2f}x',
                               ha='center', va='bottom', fontsize=10)

        # 5.4 置信区间可视化
        if 'theoretical_probabilities' in self.analysis_results:
            ci_data = self.analysis_results['theoretical_probabilities']['confidence_intervals']

            hits = []
            ci_lowers = []
            ci_uppers = []
            ci_widths = []

            for hit_count, ci_info in ci_data.items():
                hits.append(f'{hit_count}个命中')
                ci_lowers.append(ci_info['lower'])
                ci_uppers.append(ci_info['upper'])
                ci_widths.append(ci_info['width'])

            # 绘制置信区间
            y_pos = np.arange(len(hits))
            axes[1, 0].barh(y_pos, ci_widths, left=ci_lowers, alpha=0.7, color='lightcoral')
            axes[1, 0].set_title('95%置信区间')
            axes[1, 0].set_xlabel('概率')
            axes[1, 0].set_ylabel('命中情况')
            axes[1, 0].set_yticks(y_pos)
            axes[1, 0].set_yticklabels(hits)

        # 5.5 样本量对精度的影响
        if 'key_factors' in self.analysis_results:
            sample_impact = self.analysis_results['key_factors']['sample_size_impact']

            sample_sizes = list(sample_impact.keys())
            ci_widths = [data['ci_width'] for data in sample_impact.values()]
            relative_precisions = [data['relative_precision'] for data in sample_impact.values()]

            axes[1, 1].plot(sample_sizes, ci_widths, 'o-', color='blue', label='置信区间宽度')
            axes[1, 1].set_title('样本量对置信区间的影响')
            axes[1, 1].set_xlabel('样本量')
            axes[1, 1].set_ylabel('置信区间宽度')
            axes[1, 1].grid(True, alpha=0.3)

            # 添加相对精度的第二个y轴
            ax2 = axes[1, 1].twinx()
            ax2.plot(sample_sizes, relative_precisions, 's-', color='red', label='相对精度')
            ax2.set_ylabel('相对精度')

            # 合并图例
            lines1, labels1 = axes[1, 1].get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            axes[1, 1].legend(lines1 + lines2, labels1 + labels2, loc='upper right')

        # 5.6 概率密度分布
        if 'theoretical_probabilities' in self.analysis_results:
            # 模拟二项分布
            n_trials = 1000
            p_success = self.analysis_results['theoretical_probabilities']['exactly_two_hits']

            # 生成二项分布数据
            x = np.arange(0, min(50, int(n_trials * p_success * 3)))
            y = [binom.pmf(k, n_trials, p_success) for k in x]

            axes[1, 2].plot(x, y, 'b-', alpha=0.7, linewidth=2)
            axes[1, 2].fill_between(x, y, alpha=0.3)
            axes[1, 2].set_title(f'二项分布 (n={n_trials}, p={p_success:.4f})')
            axes[1, 2].set_xlabel('2数全中次数')
            axes[1, 2].set_ylabel('概率密度')
            axes[1, 2].grid(True, alpha=0.3)

            # 标记期望值
            expected = n_trials * p_success
            axes[1, 2].axvline(expected, color='red', linestyle='--',
                              label=f'期望值: {expected:.1f}')
            axes[1, 2].legend()

        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'2数全命中概率专项研究图表_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"✅ 可视化图表已保存: {filename}")

        plt.show()
        return filename

    def generate_comprehensive_report(self):
        """生成综合研究报告"""
        print("\n📋 6. 生成综合研究报告")
        print("=" * 50)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 生成JSON报告
        json_report = {
            'analysis_timestamp': timestamp,
            'research_focus': '2数全命中概率专项研究',
            'data_summary': {
                'train_periods': len(self.train_data) if self.train_data is not None else 0,
                'test_periods': len(self.test_data) if self.test_data is not None else 0,
                'has_prediction_history': self.prediction_history is not None
            },
            'analysis_results': self.analysis_results
        }

        # 处理JSON序列化问题
        def convert_keys(obj):
            if isinstance(obj, dict):
                return {str(k): convert_keys(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_keys(item) for item in obj]
            else:
                return obj

        json_report_converted = convert_keys(json_report)

        json_filename = f'2数全命中概率专项研究结果_{timestamp}.json'
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(json_report_converted, f, ensure_ascii=False, indent=2, default=str)

        # 生成Markdown报告
        md_report = self._generate_markdown_report(timestamp)
        md_filename = f'2数全命中概率专项研究报告_{timestamp}.md'
        with open(md_filename, 'w', encoding='utf-8') as f:
            f.write(md_report)

        print(f"✅ JSON报告已保存: {json_filename}")
        print(f"✅ Markdown报告已保存: {md_filename}")

        return json_filename, md_filename

    def _generate_markdown_report(self, timestamp):
        """生成Markdown格式报告"""
        report = f"""# 2数全命中概率专项研究报告

## 📊 研究概述

**研究时间**: {timestamp}
**研究主题**: 多组预测中'2数全命中'的概率分布专项分析
**数据基础**: 训练数据 {len(self.train_data) if self.train_data is not None else 0} 期，测试数据 {len(self.test_data) if self.test_data is not None else 0} 期

## 🎯 1. 理论概率分析结果

### 📈 基本概率分布

"""

        if 'theoretical_probabilities' in self.analysis_results:
            theo_probs = self.analysis_results['theoretical_probabilities']
            hit_probs = theo_probs['hit_probabilities']

            report += "| 命中数量 | 理论概率 | 百分比 |\n"
            report += "|----------|----------|--------|\n"

            for hits in range(3):
                if hits in hit_probs:
                    prob = hit_probs[hits]
                    report += f"| {hits}个命中 | {prob:.6f} | {prob*100:.3f}% |\n"

            report += f"\n### 🎯 关键概率指标\n\n"
            report += f"- **至少1个命中**: {theo_probs['at_least_one_hit']:.6f} ({theo_probs['at_least_one_hit']*100:.3f}%)\n"
            report += f"- **恰好2个全命中**: {theo_probs['exactly_two_hits']:.6f} ({theo_probs['exactly_two_hits']*100:.3f}%)\n"
            report += f"- **期望命中数**: {theo_probs['expected_hits']:.3f}\n"

        # 添加条件概率分析结果
        if 'conditional_probabilities' in self.analysis_results:
            cond_probs = self.analysis_results['conditional_probabilities']

            report += "\n## 🔗 2. 条件概率分析结果\n\n"

            if 'feature_probabilities' in cond_probs:
                feature_probs = cond_probs['feature_probabilities']

                report += "### 📊 基于数字特征的条件概率\n\n"
                report += "| 数字特征 | 2数全中概率 | 至少1中概率 | 特征规模 |\n"
                report += "|----------|-------------|-------------|----------|\n"

                for feature, data in feature_probs.items():
                    report += f"| {data['description']} | {data['prob_2_hits']:.4f} ({data['prob_2_hits']*100:.2f}%) | "
                    report += f"{data['prob_at_least_1']:.4f} ({data['prob_at_least_1']*100:.2f}%) | {data['feature_size']} |\n"

        # 添加关键因素分析
        if 'key_factors' in self.analysis_results:
            key_factors = self.analysis_results['key_factors']

            report += "\n## 🔍 3. 关键影响因素分析\n\n"

            if 'strategy_performance' in key_factors:
                strategy_perf = key_factors['strategy_performance']

                report += "### 📊 不同预测策略的性能对比\n\n"
                report += "| 预测策略 | 预期2数全中率 | 相对提升倍数 |\n"
                report += "|----------|---------------|-------------|\n"

                for strategy, data in strategy_perf.items():
                    report += f"| {data['description']} | {data['expected_2_hit_rate']:.4f} ({data['expected_2_hit_rate']*100:.2f}%) | "
                    report += f"{data['improvement_factor']:.2f}x |\n"

            if 'sample_size_impact' in key_factors:
                sample_impact = key_factors['sample_size_impact']

                report += "\n### 📈 样本量对统计精度的影响\n\n"
                report += "| 样本量 | 标准误差 | 置信区间宽度 | 相对精度 |\n"
                report += "|--------|----------|--------------|----------|\n"

                for n, data in sample_impact.items():
                    report += f"| {n} | {data['standard_error']:.6f} | {data['ci_width']:.4f} | {data['relative_precision']:.1%} |\n"

        report += "\n## 📈 4. 研究结论与建议\n\n"
        report += "### 🎯 主要发现\n\n"

        if 'theoretical_probabilities' in self.analysis_results:
            exactly_two_prob = self.analysis_results['theoretical_probabilities']['exactly_two_hits']
            at_least_one_prob = self.analysis_results['theoretical_probabilities']['at_least_one_hit']

            report += f"1. **理论基准**: 2数全命中的理论概率为 {exactly_two_prob:.4f} ({exactly_two_prob*100:.2f}%)\n"
            report += f"2. **实用概率**: 至少1数命中的概率为 {at_least_one_prob:.4f} ({at_least_one_prob*100:.2f}%)\n"

        report += "3. **策略影响**: 不同预测策略对命中率有显著影响，基于条件概率的策略表现最佳\n"
        report += "4. **特征效应**: 数字特征（频率、奇偶性、大小等）对命中概率有明显影响\n"
        report += "5. **样本量要求**: 获得可靠的统计结论需要足够大的样本量\n\n"

        report += "### 🚀 实用建议\n\n"
        report += "1. **策略选择**: 推荐使用基于条件概率的预测策略\n"
        report += "2. **特征利用**: 重点关注高频数字和特定数字特征组合\n"
        report += "3. **样本积累**: 持续收集预测数据以提高统计可靠性\n"
        report += "4. **期望管理**: 理性看待2数全命中的低概率特性\n"
        report += "5. **风险控制**: 基于概率分析制定合理的预测策略\n\n"

        report += f"---\n*报告生成时间: {timestamp}*\n"

        return report

    def run_comprehensive_research(self):
        """运行综合概率研究"""
        print("🎯 开始2数全命中概率专项研究...")
        print("=" * 60)

        # 1. 加载数据
        if not self.load_data():
            return False

        # 2. 理论概率分析
        self.theoretical_probability_analysis()

        # 3. 条件概率分析
        self.conditional_probability_analysis()

        # 4. 历史验证分析
        self.historical_validation_analysis()

        # 5. 关键因素分析
        self.key_factors_analysis()

        # 6. 生成可视化
        chart_file = self.generate_probability_visualizations()

        # 7. 生成综合报告
        json_file, md_file = self.generate_comprehensive_report()

        print("\n" + "=" * 60)
        print("✅ 2数全命中概率专项研究完成！")
        print("=" * 60)
        print(f"📊 可视化图表: {chart_file}")
        print(f"📋 JSON报告: {json_file}")
        print(f"📄 Markdown报告: {md_file}")

        return True

def main():
    """主函数"""
    print("🎯 2数全命中概率专项研究系统")
    print("专门研究多组预测中'2数全命中'的概率分布")
    print("=" * 60)

    # 创建研究系统
    researcher = TwoNumberHitProbabilitySystem()

    # 运行综合研究
    success = researcher.run_comprehensive_research()

    if success:
        print("\n🎉 研究成功完成！")
        print("📊 请查看生成的报告和图表文件")
    else:
        print("\n❌ 研究失败，请检查数据文件")

if __name__ == "__main__":
    main()
