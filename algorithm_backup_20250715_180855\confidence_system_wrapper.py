#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
置信度评估系统包装器
自动生成的集成代码
"""

from confidence_integration_adapter import (
    calculate_prediction_confidence,
    update_prediction_result,
    get_system_migration_stats
)

# 向后兼容的函数别名
def calculate_confidence(predicted_numbers, context=None):
    """计算预测置信度（向后兼容）"""
    return calculate_prediction_confidence(predicted_numbers, context)

def evaluate_prediction_confidence(predicted_numbers, context=None):
    """评估预测置信度（向后兼容）"""
    result = calculate_prediction_confidence(predicted_numbers, context)
    return result['final_confidence']

def get_prediction_confidence(predicted_numbers, context=None):
    """获取预测置信度（向后兼容）"""
    result = calculate_prediction_confidence(predicted_numbers, context)
    return result['final_confidence']

# 新系统专用函数
def get_detailed_confidence(predicted_numbers, context=None):
    """获取详细置信度信息"""
    return calculate_prediction_confidence(predicted_numbers, context)

def update_prediction_feedback(prediction_result):
    """更新预测反馈"""
    return update_prediction_result(prediction_result)

def get_system_stats():
    """获取系统统计信息"""
    return get_system_migration_stats()

# 部署信息
DEPLOYMENT_INFO = {
    'deployment_id': '20250715_174915',
    'deployment_time': '2025-07-15T17:49:15.367266',
    'system_version': '1.0'
}
