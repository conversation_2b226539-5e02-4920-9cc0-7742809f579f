#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
两阶段数字选择策略分析系统
第一阶段：预测12个候选数字
第二阶段：从12个数字中选择最终的2个数字
基于Occam's razor原则，保持简单可解释性
"""

import pandas as pd
import numpy as np
import json
from collections import defaultdict, Counter
from datetime import datetime
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class TwoStagePredictor:
    """
    两阶段预测系统
    阶段1：使用马尔可夫方法预测12个候选数字
    阶段2：使用二级过滤机制选择最终2个数字
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.data = None
        self.train_data = None
        self.test_data = None
        self.transition_prob = {}
        
        # 系统配置
        self.config = {
            'method_name': '两阶段预测方法',
            'stage1_method': '马尔可夫扩展预测12数字',
            'stage2_method': '概率加权二级过滤',
            'confidence_level': '实验级',
            'theoretical_basis': '两阶段优化选择',
            'target_improvement': '超越29.2%基准'
        }
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            print(f"✅ 数据加载成功: {len(self.data)}期")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def prepare_data_split(self):
        """准备训练和测试数据"""
        print("\n📊 准备数据分割")
        print("=" * 50)
        
        # 训练数据：2023-2024年
        self.train_data = self.data[(self.data['年份'] >= 2023) & (self.data['年份'] < 2025)].copy()
        
        # 测试数据：2025年前182期
        self.test_data = self.data[(self.data['年份'] == 2025) & (self.data['期号'] <= 182)].copy()
        
        print(f"训练数据: {len(self.train_data)}期 (2023-2024年)")
        print(f"测试数据: {len(self.test_data)}期 (2025年1-182期)")
        print("✅ 严格时间序列分割，确保无信息泄露")
        
        return len(self.train_data) > 0 and len(self.test_data) > 0
    
    def build_markov_transition_matrix(self):
        """构建马尔可夫转移矩阵"""
        print("\n🔬 构建马尔可夫转移矩阵")
        
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 转换为概率
        self.transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                self.transition_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        print(f"✅ 转移矩阵构建完成，状态数量: {len(self.transition_prob)}")
        return len(self.transition_prob) > 0
    
    def stage1_predict_12_candidates(self, previous_numbers):
        """阶段1：预测12个候选数字"""
        if not self.transition_prob:
            # 备选方案：使用频率统计
            all_numbers = []
            for _, row in self.train_data.tail(20).iterrows():
                for j in range(1, 7):
                    all_numbers.append(row[f'数字{j}'])
            
            number_counts = Counter(all_numbers)
            candidates = [num for num, count in number_counts.most_common(12)]
            
            if len(candidates) < 12:
                # 补充随机数字
                remaining = [i for i in range(1, 50) if i not in candidates]
                candidates.extend(remaining[:12-len(candidates)])
            
            return candidates[:12], 0.3
        
        # 计算各数字的转移概率
        number_probs = defaultdict(float)
        total_prob = 0.0
        
        for prev_num in previous_numbers:
            if prev_num in self.transition_prob:
                for next_num, prob in self.transition_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        # 归一化概率
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        # 选择概率最高的12个数字
        if len(number_probs) >= 12:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            candidates = [num for num, prob in sorted_numbers[:12]]
            avg_confidence = np.mean([prob for num, prob in sorted_numbers[:12]])
        else:
            # 补充高频数字
            all_numbers = []
            for _, row in self.train_data.tail(20).iterrows():
                for j in range(1, 7):
                    all_numbers.append(row[f'数字{j}'])
            
            number_counts = Counter(all_numbers)
            high_freq_numbers = [num for num, count in number_counts.most_common(20)]
            
            candidates = list(number_probs.keys())
            for num in high_freq_numbers:
                if len(candidates) >= 12:
                    break
                if num not in candidates:
                    candidates.append(num)
            
            avg_confidence = 0.4
        
        return candidates[:12], avg_confidence
    
    def stage2_select_final_2(self, candidates, previous_numbers):
        """阶段2：从12个候选数字中选择最终2个"""
        if len(candidates) < 2:
            return candidates + [1, 2][:2-len(candidates)], 0.1
        
        # 二级评分机制
        scores = {}
        
        for num in candidates:
            score = 0.0
            
            # 评分1：马尔可夫转移概率
            markov_score = 0.0
            for prev_num in previous_numbers:
                if prev_num in self.transition_prob and num in self.transition_prob[prev_num]:
                    markov_score += self.transition_prob[prev_num][num]
            scores[num] = markov_score * 0.4
            
            # 评分2：历史频率（最近20期）
            recent_count = 0
            for _, row in self.train_data.tail(20).iterrows():
                for j in range(1, 7):
                    if row[f'数字{j}'] == num:
                        recent_count += 1
            freq_score = recent_count / 120  # 20期 * 6数字
            scores[num] += freq_score * 0.3
            
            # 评分3：数字特性（奇偶平衡）
            balance_score = 0.5 if num % 2 == 1 else 0.5  # 简单的奇偶平衡
            scores[num] += balance_score * 0.2
            
            # 评分4：数字范围分布
            if 1 <= num <= 16:
                range_score = 0.33
            elif 17 <= num <= 33:
                range_score = 0.34
            else:
                range_score = 0.33
            scores[num] += range_score * 0.1
        
        # 选择得分最高的2个数字
        sorted_candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        final_2 = [num for num, score in sorted_candidates[:2]]
        
        # 计算置信度
        top_scores = [score for num, score in sorted_candidates[:2]]
        confidence = np.mean(top_scores) if top_scores else 0.1
        
        return final_2, confidence
    
    def predict_single_period(self, previous_numbers):
        """预测单期的2个数字"""
        # 阶段1：预测12个候选数字
        candidates, stage1_confidence = self.stage1_predict_12_candidates(previous_numbers)
        
        # 阶段2：选择最终2个数字
        final_2, stage2_confidence = self.stage2_select_final_2(candidates, previous_numbers)
        
        # 综合置信度
        overall_confidence = (stage1_confidence + stage2_confidence) / 2
        
        return {
            'final_prediction': final_2,
            'candidates': candidates,
            'stage1_confidence': stage1_confidence,
            'stage2_confidence': stage2_confidence,
            'overall_confidence': overall_confidence
        }
    
    def validate_two_stage_method(self):
        """验证两阶段方法性能"""
        print("\n🔬 验证两阶段预测方法")
        print("=" * 50)
        
        predictions = []
        correct_predictions = 0
        stage1_hits = 0  # 12个候选中的命中数
        
        for idx, test_row in self.test_data.iterrows():
            period_num = test_row['期号']
            actual_numbers = set([test_row[f'数字{j}'] for j in range(1, 7)])
            
            # 获取前一期数字
            if idx == self.test_data.index[0]:
                prev_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                prev_numbers = set([self.test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            # 预测
            result = self.predict_single_period(prev_numbers)
            
            # 评估阶段1（12个候选）
            stage1_hit_count = len(set(result['candidates']) & actual_numbers)
            stage1_hits += stage1_hit_count
            
            # 评估阶段2（最终2个预测）
            final_hit_count = len(set(result['final_prediction']) & actual_numbers)
            is_success = final_hit_count >= 1
            
            if is_success:
                correct_predictions += 1
            
            predictions.append({
                'period': period_num,
                'predicted_2': result['final_prediction'],
                'candidates_12': result['candidates'],
                'actual': list(actual_numbers),
                'stage1_hits': stage1_hit_count,
                'final_hits': final_hit_count,
                'success': is_success,
                'overall_confidence': result['overall_confidence']
            })
        
        # 计算性能指标
        total_periods = len(predictions)
        success_rate = correct_predictions / total_periods
        avg_stage1_hits = stage1_hits / total_periods
        
        print(f"✅ 两阶段方法验证完成")
        print(f"  测试期数: {total_periods}")
        print(f"  阶段1平均命中: {avg_stage1_hits:.2f}/6 ({avg_stage1_hits/6*100:.1f}%)")
        print(f"  最终成功预测: {correct_predictions}")
        print(f"  最终成功率: {success_rate:.3f} ({success_rate*100:.1f}%)")
        print(f"  基准性能: 29.2%")
        print(f"  性能提升: {(success_rate - 0.292)*100:+.1f}个百分点")
        
        return {
            'predictions': predictions,
            'success_rate': success_rate,
            'avg_stage1_hits': avg_stage1_hits,
            'improvement_vs_baseline': success_rate - 0.292
        }

def main():
    """主函数"""
    print("🎯 两阶段数字选择策略分析系统")
    print("阶段1: 马尔可夫预测12个候选数字")
    print("阶段2: 概率加权选择最终2个数字")
    print("=" * 60)
    
    # 初始化系统
    predictor = TwoStagePredictor()
    
    # 1. 加载数据
    if not predictor.load_data():
        return
    
    # 2. 准备数据分割
    if not predictor.prepare_data_split():
        return
    
    # 3. 构建马尔可夫转移矩阵
    if not predictor.build_markov_transition_matrix():
        return
    
    # 4. 验证两阶段方法
    results = predictor.validate_two_stage_method()
    
    if results:
        # 5. 统计显著性检验
        baseline_rate = 0.292
        observed_rate = results['success_rate']
        n = len(results['predictions'])
        
        # 使用二项检验
        try:
            # 尝试新版本的函数
            result = stats.binomtest(int(observed_rate * n), n, baseline_rate, alternative='greater')
            p_value = result.pvalue
        except AttributeError:
            # 回退到旧版本或手动计算
            from scipy.stats import binom
            p_value = 1 - binom.cdf(int(observed_rate * n) - 1, n, baseline_rate)
        
        print(f"\n📊 统计显著性检验")
        print(f"  观察成功率: {observed_rate:.3f}")
        print(f"  基准成功率: {baseline_rate:.3f}")
        print(f"  样本数量: {n}")
        print(f"  p值: {p_value:.4f}")
        print(f"  显著性(p<0.05): {'是' if p_value < 0.05 else '否'}")
        
        # 6. 保存结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f"两阶段预测验证结果_{timestamp}.csv"

        # 保存为CSV格式避免JSON序列化问题
        df_results = pd.DataFrame(results['predictions'])
        df_results.to_csv(results_file, index=False, encoding='utf-8')

        # 保存摘要结果
        summary_file = f"两阶段预测摘要_{timestamp}.txt"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(f"两阶段预测方法验证摘要\n")
            f.write(f"=" * 40 + "\n")
            f.write(f"成功率: {results['success_rate']:.3f} ({results['success_rate']*100:.1f}%)\n")
            f.write(f"阶段1平均命中: {results['avg_stage1_hits']:.2f}/6\n")
            f.write(f"相对基准提升: {results['improvement_vs_baseline']*100:+.1f}个百分点\n")
            f.write(f"统计显著性p值: {float(p_value):.4f}\n")
            f.write(f"显著性(p<0.05): {'是' if p_value < 0.05 else '否'}\n")

        print(f"\n✅ 结果已保存:")
        print(f"  详细结果: {results_file}")
        print(f"  摘要结果: {summary_file}")
        
        # 7. 结论
        print(f"\n🎯 两阶段预测方法分析结论")
        print("=" * 50)
        if results['improvement_vs_baseline'] > 0 and p_value < 0.05:
            print("✅ 两阶段方法显著优于基准方法")
        elif results['improvement_vs_baseline'] > 0:
            print("⚠️ 两阶段方法略优于基准，但未达到统计显著性")
        else:
            print("❌ 两阶段方法未能改善基准性能")

if __name__ == "__main__":
    main()
