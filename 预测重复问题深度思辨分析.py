#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测重复问题深度思辨分析
分析为什么181-200期预测出现高度重复的根本原因
探讨马尔可夫链预测的局限性和改进方向
"""

import pandas as pd
import numpy as np
import json
from collections import defaultdict, Counter
from datetime import datetime
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class PredictionRepetitionAnalyzer:
    """
    预测重复问题分析器
    深度分析预测重复的根本原因
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.data = None
        self.train_data = None
        self.transition_prob = {}
        
        # 分析结果
        self.analysis_results = {}
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            self.train_data = self.data[
                ((self.data['年份'] >= 2023) & (self.data['年份'] < 2025)) |
                ((self.data['年份'] == 2025) & (self.data['期号'] <= 179))
            ].copy()
            
            print(f"✅ 数据加载成功，训练数据: {len(self.train_data)}期")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def build_markov_model(self):
        """构建马尔可夫模型"""
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        self.transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                self.transition_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        print(f"✅ 马尔可夫模型构建完成")
        return True
    
    def analyze_179_period_transition(self):
        """分析179期的转移概率"""
        print(f"\n🔍 分析179期转移概率")
        print("=" * 60)
        
        # 获取179期数字
        period_179 = self.train_data[self.train_data['期号'] == 179].iloc[0]
        numbers_179 = [period_179[f'数字{j}'] for j in range(1, 7)]
        
        print(f"179期开奖数字: {numbers_179}")
        
        # 分析每个数字的转移概率
        transition_analysis = {}
        
        for num in numbers_179:
            if num in self.transition_prob:
                transitions = self.transition_prob[num]
                # 按概率排序
                sorted_transitions = sorted(transitions.items(), key=lambda x: x[1], reverse=True)
                
                transition_analysis[num] = {
                    'total_transitions': len(transitions),
                    'top_5_transitions': sorted_transitions[:5],
                    'max_prob': sorted_transitions[0][1] if sorted_transitions else 0,
                    'entropy': self._calculate_entropy(list(transitions.values()))
                }
                
                print(f"\n数字 {num} 的转移分析:")
                print(f"  总转移数: {len(transitions)}")
                print(f"  最高概率: {sorted_transitions[0][1]:.4f} -> {sorted_transitions[0][0]}")
                print(f"  熵值: {transition_analysis[num]['entropy']:.4f}")
                print(f"  前5个转移: {sorted_transitions[:5]}")
        
        return transition_analysis
    
    def _calculate_entropy(self, probabilities):
        """计算熵值"""
        if not probabilities:
            return 0
        
        entropy = 0
        for p in probabilities:
            if p > 0:
                entropy -= p * np.log2(p)
        return entropy
    
    def simulate_prediction_process(self):
        """模拟预测过程"""
        print(f"\n🎯 模拟预测过程")
        print("=" * 60)
        
        # 获取179期数字作为起始
        period_179 = self.train_data[self.train_data['期号'] == 179].iloc[0]
        current_numbers = set([period_179[f'数字{j}'] for j in range(1, 7)])
        
        print(f"起始数字 (179期): {sorted(list(current_numbers))}")
        
        simulation_results = []
        
        for period in range(181, 201):
            # 预测过程
            predicted_numbers, details = self._detailed_prediction(current_numbers)
            
            simulation_results.append({
                'period': period,
                'input_numbers': sorted(list(current_numbers)),
                'predicted_numbers': predicted_numbers,
                'prediction_details': details
            })
            
            # 更新当前数字（这里是问题所在！）
            # 原始代码：current_numbers = set(predicted_numbers + list(current_numbers)[:4])
            # 这种更新方式导致了重复！
            
            print(f"期号 {period}:")
            print(f"  输入: {sorted(list(current_numbers))}")
            print(f"  预测: {predicted_numbers}")
            print(f"  详情: {details}")
            
            # 模拟不同的更新策略
            if period <= 182:
                # 前两期使用原始策略
                current_numbers = set(predicted_numbers + list(current_numbers)[:4])
            else:
                # 后续期使用固定策略（导致重复）
                current_numbers = set(predicted_numbers + list(current_numbers)[:4])
            
            print(f"  更新后: {sorted(list(current_numbers))}")
            print()
        
        return simulation_results
    
    def _detailed_prediction(self, previous_numbers):
        """详细的预测过程"""
        if not self.transition_prob:
            return [1, 2], "无转移概率"
        
        number_probs = defaultdict(float)
        total_prob = 0.0
        coverage_count = 0
        
        details = {
            'covered_numbers': [],
            'uncovered_numbers': [],
            'aggregated_probs': {}
        }
        
        for prev_num in previous_numbers:
            if prev_num in self.transition_prob:
                coverage_count += 1
                details['covered_numbers'].append(prev_num)
                
                for next_num, prob in self.transition_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
            else:
                details['uncovered_numbers'].append(prev_num)
        
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        # 记录聚合概率
        details['aggregated_probs'] = dict(number_probs)
        details['coverage_ratio'] = coverage_count / len(previous_numbers)
        
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            predicted_numbers = [num for num, prob in sorted_numbers[:2]]
            
            details['top_predictions'] = sorted_numbers[:5]
            details['selected'] = sorted_numbers[:2]
            
            return predicted_numbers, details
        else:
            return [1, 2], details
    
    def analyze_repetition_causes(self):
        """分析重复的根本原因"""
        print(f"\n🧠 分析重复的根本原因")
        print("=" * 60)
        
        causes = {
            'markov_limitation': {
                'description': '马尔可夫链的记忆局限性',
                'analysis': self._analyze_markov_memory_limitation(),
                'severity': 'High'
            },
            'update_strategy': {
                'description': '数字更新策略的问题',
                'analysis': self._analyze_update_strategy_problem(),
                'severity': 'Critical'
            },
            'probability_concentration': {
                'description': '概率分布的集中化',
                'analysis': self._analyze_probability_concentration(),
                'severity': 'Medium'
            },
            'feedback_loop': {
                'description': '预测反馈循环',
                'analysis': self._analyze_feedback_loop(),
                'severity': 'High'
            }
        }
        
        for cause_name, cause_info in causes.items():
            print(f"\n{cause_name.upper()} ({cause_info['severity']} 严重性):")
            print(f"  描述: {cause_info['description']}")
            print(f"  分析: {cause_info['analysis']}")
        
        return causes
    
    def _analyze_markov_memory_limitation(self):
        """分析马尔可夫记忆局限性"""
        # 一阶马尔可夫只记住前一个状态
        return ("一阶马尔可夫链只能记住前一期的状态，无法捕捉更长期的模式。"
                "当输入状态固定时，输出也会趋于固定。")
    
    def _analyze_update_strategy_problem(self):
        """分析更新策略问题"""
        return ("当前更新策略 current_numbers = set(predicted_numbers + list(current_numbers)[:4]) "
                "导致输入状态快速收敛到固定模式，从而产生重复预测。")
    
    def _analyze_probability_concentration(self):
        """分析概率集中化"""
        # 计算概率分布的集中度
        period_179 = self.train_data[self.train_data['期号'] == 179].iloc[0]
        numbers_179 = [period_179[f'数字{j}'] for j in range(1, 7)]
        
        concentration_scores = []
        for num in numbers_179:
            if num in self.transition_prob:
                probs = list(self.transition_prob[num].values())
                # 计算基尼系数
                gini = self._calculate_gini_coefficient(probs)
                concentration_scores.append(gini)
        
        avg_concentration = np.mean(concentration_scores) if concentration_scores else 0
        
        return (f"概率分布集中度 (基尼系数): {avg_concentration:.4f}。"
                f"高集中度导致预测偏向少数高概率数字。")
    
    def _calculate_gini_coefficient(self, values):
        """计算基尼系数"""
        if not values:
            return 0
        
        sorted_values = sorted(values)
        n = len(sorted_values)
        cumsum = np.cumsum(sorted_values)
        
        return (n + 1 - 2 * sum((n + 1 - i) * y for i, y in enumerate(sorted_values))) / (n * sum(sorted_values))
    
    def _analyze_feedback_loop(self):
        """分析反馈循环"""
        return ("预测结果被用作下一期的输入，形成闭环反馈。"
                "在没有外部真实数据注入的情况下，系统会陷入自我强化的循环。")
    
    def propose_solutions(self):
        """提出解决方案"""
        print(f"\n💡 解决方案建议")
        print("=" * 60)
        
        solutions = {
            'solution_1': {
                'name': '引入随机扰动',
                'description': '在预测过程中引入适度的随机性',
                'implementation': '在转移概率中加入噪声，或随机选择次优解',
                'pros': '增加预测多样性，避免完全重复',
                'cons': '可能降低预测准确性',
                'feasibility': 'High'
            },
            'solution_2': {
                'name': '改进状态更新策略',
                'description': '使用更智能的状态更新方法',
                'implementation': '基于历史模式或概率分布更新状态',
                'pros': '保持状态多样性，减少收敛',
                'cons': '增加系统复杂性',
                'feasibility': 'Medium'
            },
            'solution_3': {
                'name': '多阶马尔可夫链',
                'description': '使用二阶或三阶马尔可夫链',
                'implementation': '考虑前2-3期的状态进行预测',
                'pros': '更丰富的记忆，更复杂的模式',
                'cons': '数据稀疏性问题，计算复杂度增加',
                'feasibility': 'Medium'
            },
            'solution_4': {
                'name': '集成多种方法',
                'description': '结合多种预测方法的结果',
                'implementation': '马尔可夫 + 频率分析 + 趋势分析',
                'pros': '综合多种信息源，提高鲁棒性',
                'cons': '系统复杂性大幅增加',
                'feasibility': 'Low'
            },
            'solution_5': {
                'name': '动态阈值调整',
                'description': '根据预测重复度动态调整选择策略',
                'implementation': '检测重复时自动选择次优或随机解',
                'pros': '自适应避免重复，保持多样性',
                'cons': '需要复杂的重复检测机制',
                'feasibility': 'High'
            }
        }
        
        for sol_id, solution in solutions.items():
            print(f"\n{solution['name']} (可行性: {solution['feasibility']}):")
            print(f"  描述: {solution['description']}")
            print(f"  实现: {solution['implementation']}")
            print(f"  优点: {solution['pros']}")
            print(f"  缺点: {solution['cons']}")
        
        return solutions
    
    def theoretical_reflection(self):
        """理论反思"""
        print(f"\n🤔 理论反思")
        print("=" * 60)
        
        reflections = {
            'markov_assumption': {
                'question': '马尔可夫假设在彩票预测中是否合理？',
                'analysis': ('马尔可夫假设认为未来只依赖于当前状态，不依赖历史。'
                           '但彩票的随机性可能不完全符合这一假设。'),
                'implication': '需要考虑更复杂的依赖关系或完全随机的模型。'
            },
            'prediction_paradox': {
                'question': '预测本身是否改变了系统的行为？',
                'analysis': ('在我们的系统中，预测结果被用作下一期的输入，'
                           '这创造了一个人工的反馈循环。'),
                'implication': '真实预测应该基于真实的历史数据，而非预测数据。'
            },
            'randomness_vs_pattern': {
                'question': '我们是在寻找不存在的模式吗？',
                'analysis': ('彩票设计为随机，我们的模式识别可能是'
                           '对随机噪声的过度拟合。'),
                'implication': '需要更严格的统计检验来验证模式的真实性。'
            },
            'system_limitations': {
                'question': '当前系统的根本局限性是什么？',
                'analysis': ('一阶马尔可夫、固定更新策略、缺乏外部验证'
                           '都限制了系统的预测能力。'),
                'implication': '需要重新设计系统架构，引入更多信息源。'
            }
        }
        
        for ref_id, reflection in reflections.items():
            print(f"\n{reflection['question']}")
            print(f"  分析: {reflection['analysis']}")
            print(f"  启示: {reflection['implication']}")
        
        return reflections

def main():
    """主函数"""
    print("🧠 预测重复问题深度思辨分析")
    print("分析181-200期预测重复的根本原因")
    print("=" * 80)
    
    # 初始化分析器
    analyzer = PredictionRepetitionAnalyzer()
    
    # 1. 加载数据和构建模型
    if not analyzer.load_data():
        return
    
    if not analyzer.build_markov_model():
        return
    
    # 2. 分析179期转移概率
    transition_analysis = analyzer.analyze_179_period_transition()
    
    # 3. 模拟预测过程
    simulation_results = analyzer.simulate_prediction_process()
    
    # 4. 分析重复原因
    repetition_causes = analyzer.analyze_repetition_causes()
    
    # 5. 提出解决方案
    solutions = analyzer.propose_solutions()
    
    # 6. 理论反思
    reflections = analyzer.theoretical_reflection()
    
    # 7. 总结
    print(f"\n🎯 核心发现总结")
    print("=" * 50)
    print(f"✅ 重复原因识别: 4个主要原因")
    print(f"✅ 解决方案提出: 5种可行方案")
    print(f"✅ 理论反思: 4个深层问题")
    
    print(f"\n💡 关键洞察:")
    print(f"  1. 马尔可夫链的记忆局限导致状态收敛")
    print(f"  2. 预测反馈循环创造了人工模式")
    print(f"  3. 缺乏真实数据注入导致系统封闭")
    print(f"  4. 需要重新设计预测架构")
    
    print(f"\n🚀 建议的改进方向:")
    print(f"  1. 引入随机扰动机制")
    print(f"  2. 改进状态更新策略")
    print(f"  3. 实施动态阈值调整")
    print(f"  4. 基于真实数据进行验证")

if __name__ == "__main__":
    main()
