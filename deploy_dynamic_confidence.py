#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态置信度调整机制部署脚本
Dynamic Confidence Adjustment Deployment Script

用于启用和部署动态置信度调整机制
支持实时监控、自动校准和性能优化

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0
"""

import os
import sys
import shutil
import json
import pandas as pd
import numpy as np
from datetime import datetime
import logging
import argparse
from dynamic_confidence_integration import DynamicConfidenceIntegration

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dynamic_confidence_deployment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DynamicConfidenceDeployment:
    """动态置信度调整机制部署管理器"""
    
    def __init__(self, config=None):
        """初始化部署管理器"""
        self.config = config or self._get_default_config()
        self.deployment_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.backup_dir = f"dynamic_confidence_backup_{self.deployment_id}"
        self.integration = None
        
        logger.info(f"🚀 动态置信度调整机制部署管理器初始化")
        logger.info(f"  部署ID: {self.deployment_id}")
        logger.info(f"  备份目录: {self.backup_dir}")
    
    def _get_default_config(self):
        """获取默认部署配置"""
        return {
            'deployment_mode': 'production',  # production, testing, monitoring
            'integration_enabled': True,
            'integration_mode': 'hybrid',  # hybrid, override, weighted
            'integration_ratio': 0.7,
            'backup_enabled': True,
            'validation_enabled': True,
            'rollback_enabled': True,
            'monitoring_config': {
                'real_time_monitoring': {
                    'enabled': True,
                    'check_interval': 30,
                    'alert_thresholds': {
                        'low_accuracy': 0.15,
                        'poor_calibration': 0.1,
                        'high_variance': 0.3
                    }
                },
                'auto_calibration': {
                    'enabled': True,
                    'interval_minutes': 60,
                    'min_predictions': 10
                }
            },
            'performance_targets': {
                'min_accuracy': 0.25,
                'target_calibration': 0.5,
                'max_variance': 0.2
            }
        }
    
    def deploy(self):
        """执行完整部署流程"""
        try:
            logger.info("🎯 开始部署动态置信度调整机制")
            
            # 1. 预部署检查
            if not self._pre_deployment_check():
                logger.error("❌ 预部署检查失败，终止部署")
                return False
            
            # 2. 备份现有系统
            if self.config['backup_enabled']:
                if not self._backup_existing_system():
                    logger.error("❌ 系统备份失败，终止部署")
                    return False
            
            # 3. 初始化动态置信度集成
            if not self._initialize_integration():
                logger.error("❌ 集成初始化失败，终止部署")
                return False
            
            # 4. 部署动态调整机制
            if not self._deploy_dynamic_adjustment():
                logger.error("❌ 动态调整机制部署失败，开始回滚")
                self._rollback()
                return False
            
            # 5. 验证部署
            if self.config['validation_enabled']:
                if not self._validate_deployment():
                    logger.error("❌ 部署验证失败，开始回滚")
                    self._rollback()
                    return False
            
            # 6. 启动监控
            if not self._start_monitoring():
                logger.warning("⚠️ 监控启动失败，但部署继续")
            
            # 7. 生成部署报告
            self._generate_deployment_report()
            
            logger.info("✅ 动态置信度调整机制部署成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 部署过程中发生异常: {e}")
            if self.config['rollback_enabled']:
                self._rollback()
            return False
    
    def _pre_deployment_check(self):
        """预部署检查"""
        logger.info("🔍 执行预部署检查")
        
        checks = []
        
        # 检查依赖文件
        required_files = [
            'enhanced_dynamic_confidence_adjuster.py',
            'dynamic_confidence_integration.py',
            'confidence_system_wrapper.py',
            'algorithm_wrapper.py'
        ]
        
        for file in required_files:
            if os.path.exists(file):
                checks.append(f"✅ 依赖文件 {file} 存在")
            else:
                checks.append(f"❌ 依赖文件 {file} 不存在")
                return False
        
        # 检查现有系统状态
        try:
            from confidence_system_wrapper import calculate_confidence
            from algorithm_wrapper import get_enhanced_prediction
            
            # 测试现有系统
            test_result = calculate_confidence([5, 40])
            if test_result:
                checks.append("✅ 现有置信度系统正常")
            else:
                checks.append("❌ 现有置信度系统异常")
                return False
            
            test_prediction = get_enhanced_prediction([1, 15, 23, 30, 35, 42])
            if test_prediction and 'predicted_numbers' in test_prediction:
                checks.append("✅ 现有预测系统正常")
            else:
                checks.append("❌ 现有预测系统异常")
                return False
                
        except Exception as e:
            checks.append(f"❌ 现有系统检查失败: {e}")
            return False
        
        # 检查Python环境
        try:
            import numpy as np
            import pandas as pd
            import threading
            checks.append("✅ Python依赖包检查通过")
        except ImportError as e:
            checks.append(f"❌ Python依赖包缺失: {e}")
            return False
        
        # 检查磁盘空间
        import shutil
        free_space = shutil.disk_usage('.').free / (1024**2)  # MB
        if free_space > 50:  # 至少50MB
            checks.append(f"✅ 磁盘空间充足: {free_space:.1f}MB")
        else:
            checks.append(f"❌ 磁盘空间不足: {free_space:.1f}MB")
            return False
        
        for check in checks:
            logger.info(f"  {check}")
        
        return True
    
    def _backup_existing_system(self):
        """备份现有系统"""
        logger.info("💾 备份现有系统")
        
        try:
            # 创建备份目录
            os.makedirs(self.backup_dir, exist_ok=True)
            
            # 备份相关文件
            files_to_backup = [
                'confidence_system_wrapper.py',
                'algorithm_wrapper.py',
                'confidence_config.json',
                'algorithm_config.json'
            ]
            
            backed_up_files = []
            for file in files_to_backup:
                if os.path.exists(file):
                    backup_path = os.path.join(self.backup_dir, file)
                    shutil.copy2(file, backup_path)
                    backed_up_files.append(file)
                    logger.info(f"  ✅ 已备份: {file} -> {backup_path}")
            
            # 创建备份清单
            backup_manifest = {
                'deployment_id': self.deployment_id,
                'backup_time': datetime.now().isoformat(),
                'backed_up_files': backed_up_files,
                'backup_directory': self.backup_dir
            }
            
            with open(os.path.join(self.backup_dir, 'backup_manifest.json'), 'w') as f:
                json.dump(backup_manifest, f, indent=2)
            
            logger.info(f"✅ 系统备份完成，备份文件数: {len(backed_up_files)}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 系统备份失败: {e}")
            return False
    
    def _initialize_integration(self):
        """初始化动态置信度集成"""
        logger.info("🔧 初始化动态置信度集成")
        
        try:
            # 准备集成配置
            integration_config = {
                'integration_enabled': self.config['integration_enabled'],
                'integration_mode': self.config['integration_mode'],
                'integration_ratio': self.config['integration_ratio'],
                'adjuster_config': self.config['monitoring_config']
            }
            
            # 创建集成实例
            self.integration = DynamicConfidenceIntegration(integration_config)
            
            logger.info(f"✅ 动态置信度集成初始化完成")
            logger.info(f"  集成模式: {self.config['integration_mode']}")
            logger.info(f"  集成比例: {self.config['integration_ratio']:.1%}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 集成初始化失败: {e}")
            return False
    
    def _deploy_dynamic_adjustment(self):
        """部署动态调整机制"""
        logger.info("🚀 部署动态调整机制")
        
        try:
            # 保存配置
            dynamic_config = {
                'deployment_id': self.deployment_id,
                'deployment_time': datetime.now().isoformat(),
                'deployment_mode': self.config['deployment_mode'],
                'integration_config': {
                    'enabled': self.config['integration_enabled'],
                    'mode': self.config['integration_mode'],
                    'ratio': self.config['integration_ratio']
                },
                'monitoring_config': self.config['monitoring_config'],
                'performance_targets': self.config['performance_targets']
            }
            
            with open('dynamic_confidence_config.json', 'w', encoding='utf-8') as f:
                json.dump(dynamic_config, f, indent=2, ensure_ascii=False)
            
            # 创建动态置信度包装器
            self._create_dynamic_wrapper()
            
            logger.info("✅ 动态调整机制部署完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 动态调整机制部署失败: {e}")
            return False
    
    def _create_dynamic_wrapper(self):
        """创建动态置信度包装器"""
        # 准备部署信息
        deployment_id = self.deployment_id
        deployment_time = datetime.now().isoformat()
        
        wrapper_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态置信度系统包装器
自动生成的集成代码
"""

import os
import json
import logging
from dynamic_confidence_integration import (
    get_confidence_integration,
    get_integrated_confidence,
    update_prediction_result,
    get_performance_report,
    perform_manual_calibration,
    predict_with_dynamic_confidence
)

logger = logging.getLogger(__name__)

# 全局集成实例
_integration = None

def get_integration():
    """获取集成实例"""
    global _integration
    
    if _integration is None:
        # 加载配置
        config_file = 'dynamic_confidence_config.json'
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                integration_config = config.get('integration_config', {{}})
                monitoring_config = config.get('monitoring_config', {{}})
                
                # 合并配置
                full_config = {{
                    'integration_enabled': integration_config.get('enabled', True),
                    'integration_mode': integration_config.get('mode', 'hybrid'),
                    'integration_ratio': integration_config.get('ratio', 0.7),
                    'adjuster_config': monitoring_config
                }}
                
                _integration = get_confidence_integration(full_config)
            except Exception as e:
                logger.warning(f"加载配置失败，使用默认配置: {{e}}")
                _integration = get_confidence_integration()
        else:
            _integration = get_confidence_integration()
    
    return _integration

# 主要接口函数
def calculate_dynamic_confidence(predicted_numbers, context=None):
    """计算动态置信度"""
    integration = get_integration()
    return integration.get_integrated_confidence(predicted_numbers, context)

def predict_with_confidence(previous_numbers, context=None):
    """带置信度的预测"""
    return predict_with_dynamic_confidence(previous_numbers, context)

def update_prediction_feedback(prediction_result):
    """更新预测反馈"""
    integration = get_integration()
    return integration.update_prediction_result(prediction_result)

def get_system_performance():
    """获取系统性能"""
    integration = get_integration()
    return integration.get_performance_report()

def calibrate_system():
    """校准系统"""
    integration = get_integration()
    return integration.perform_manual_calibration()

def export_system_data(filename):
    """导出系统数据"""
    integration = get_integration()
    return integration.export_performance_data(filename)

# 向后兼容的函数别名
def get_confidence(predicted_numbers, context=None):
    """获取置信度（向后兼容）"""
    result = calculate_dynamic_confidence(predicted_numbers, context)
    return result['final_confidence']

def calculate_confidence(predicted_numbers, context=None):
    """计算置信度（向后兼容）"""
    result = calculate_dynamic_confidence(predicted_numbers, context)
    return result

# 部署信息
DEPLOYMENT_INFO = {{
    'deployment_id': '{deployment_id}',
    'deployment_time': '{deployment_time}',
    'system_version': '2.0',
    'features': ['dynamic_adjustment', 'real_time_monitoring', 'auto_calibration']
}}

# 系统状态检查
def get_system_status():
    """获取系统状态"""
    try:
        integration = get_integration()
        performance = integration.get_performance_report()
        
        return {{
            'status': 'active',
            'deployment_info': DEPLOYMENT_INFO,
            'integration_enabled': performance['integration_config']['enabled'],
            'integration_mode': performance['integration_config']['mode'],
            'total_predictions': performance['integration_stats']['total_predictions'],
            'monitoring_active': performance['adjuster_status']['monitoring_enabled'],
            'auto_calibration_active': performance['adjuster_status']['auto_calibration_enabled']
        }}
    except Exception as e:
        return {{
            'status': 'error',
            'error': str(e),
            'deployment_info': DEPLOYMENT_INFO
        }}
'''
        
        with open('dynamic_confidence_wrapper.py', 'w', encoding='utf-8') as f:
            f.write(wrapper_code)
        
        logger.info("✅ 动态置信度包装器创建完成")
    
    def _validate_deployment(self):
        """验证部署"""
        logger.info("🔍 验证部署结果")
        
        try:
            # 测试基本功能
            from dynamic_confidence_wrapper import (
                calculate_dynamic_confidence,
                predict_with_confidence,
                get_system_status
            )
            
            # 执行测试
            test_numbers = [5, 40]
            test_context = {
                'previous_numbers': [1, 15, 23, 30, 35, 42],
                'data_source': '测试数据'
            }
            
            # 测试动态置信度计算
            confidence_result = calculate_dynamic_confidence(test_numbers, test_context)
            if (isinstance(confidence_result, dict) and 
                'final_confidence' in confidence_result and
                'original_confidence' in confidence_result and
                'adjusted_confidence' in confidence_result):
                logger.info("✅ 动态置信度计算测试通过")
            else:
                logger.error("❌ 动态置信度计算测试失败")
                return False
            
            # 测试集成预测
            prediction_result = predict_with_confidence(test_context['previous_numbers'], test_context)
            if (isinstance(prediction_result, dict) and 
                'predicted_numbers' in prediction_result and
                'confidence' in prediction_result and
                'confidence_details' in prediction_result):
                logger.info("✅ 集成预测测试通过")
            else:
                logger.error("❌ 集成预测测试失败")
                return False
            
            # 测试系统状态
            status = get_system_status()
            if isinstance(status, dict) and status.get('status') == 'active':
                logger.info("✅ 系统状态测试通过")
            else:
                logger.error("❌ 系统状态测试失败")
                return False
            
            logger.info("✅ 部署验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 部署验证失败: {e}")
            return False
    
    def _start_monitoring(self):
        """启动监控"""
        logger.info("🔍 启动实时监控")
        
        try:
            if self.integration:
                # 监控已在集成初始化时启动
                logger.info("✅ 实时监控已启动")
                return True
            else:
                logger.warning("⚠️ 集成实例不存在，无法启动监控")
                return False
                
        except Exception as e:
            logger.error(f"❌ 启动监控失败: {e}")
            return False
    
    def _rollback(self):
        """回滚到备份版本"""
        if not self.config['rollback_enabled']:
            logger.warning("⚠️ 回滚功能已禁用")
            return False
        
        logger.info("🔄 开始回滚到备份版本")
        
        try:
            if not os.path.exists(self.backup_dir):
                logger.error("❌ 备份目录不存在，无法回滚")
                return False
            
            # 读取备份清单
            manifest_path = os.path.join(self.backup_dir, 'backup_manifest.json')
            if os.path.exists(manifest_path):
                with open(manifest_path, 'r') as f:
                    manifest = json.load(f)
                
                # 恢复备份文件
                for file in manifest['backed_up_files']:
                    backup_path = os.path.join(self.backup_dir, file)
                    if os.path.exists(backup_path):
                        shutil.copy2(backup_path, file)
                        logger.info(f"  ✅ 已恢复: {file}")
            
            # 删除新系统文件
            new_files = [
                'dynamic_confidence_wrapper.py',
                'dynamic_confidence_config.json'
            ]
            
            for file in new_files:
                if os.path.exists(file):
                    os.remove(file)
                    logger.info(f"  🗑️ 已删除: {file}")
            
            logger.info("✅ 回滚完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 回滚失败: {e}")
            return False
    
    def _generate_deployment_report(self):
        """生成部署报告"""
        logger.info("📊 生成部署报告")
        
        try:
            # 获取系统状态
            from dynamic_confidence_wrapper import get_system_status, get_system_performance
            
            system_status = get_system_status()
            system_performance = get_system_performance()
            
            report = {
                'deployment_info': {
                    'deployment_id': self.deployment_id,
                    'deployment_time': datetime.now().isoformat(),
                    'deployment_mode': self.config['deployment_mode'],
                    'integration_config': {
                        'enabled': self.config['integration_enabled'],
                        'mode': self.config['integration_mode'],
                        'ratio': self.config['integration_ratio']
                    }
                },
                'system_status': system_status,
                'system_performance': system_performance,
                'deployment_config': self.config,
                'status': 'success'
            }
            
            report_file = f'dynamic_confidence_deployment_report_{self.deployment_id}.json'
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 部署报告已生成: {report_file}")
            
        except Exception as e:
            logger.error(f"❌ 生成部署报告失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='动态置信度调整机制部署工具')
    parser.add_argument('--mode', choices=['production', 'testing', 'monitoring'], 
                      default='production', help='部署模式')
    parser.add_argument('--integration-mode', choices=['hybrid', 'override', 'weighted'], 
                      default='hybrid', help='集成模式')
    parser.add_argument('--integration-ratio', type=float, default=0.7, 
                      help='集成比例 (0-1)')
    parser.add_argument('--no-backup', action='store_true', 
                      help='禁用备份')
    parser.add_argument('--no-validation', action='store_true', 
                      help='禁用验证')
    parser.add_argument('--no-monitoring', action='store_true', 
                      help='禁用实时监控')
    parser.add_argument('--force', action='store_true', 
                      help='强制部署，忽略错误')
    
    args = parser.parse_args()
    
    print("🚀 动态置信度调整机制部署工具")
    print("=" * 50)
    
    # 创建配置
    config = {
        'deployment_mode': args.mode,
        'integration_enabled': True,
        'integration_mode': args.integration_mode,
        'integration_ratio': args.integration_ratio,
        'backup_enabled': not args.no_backup,
        'validation_enabled': not args.no_validation,
        'rollback_enabled': not args.force,
        'monitoring_config': {
            'real_time_monitoring': {
                'enabled': not args.no_monitoring,
                'check_interval': 30,
                'alert_thresholds': {
                    'low_accuracy': 0.15,
                    'poor_calibration': 0.1,
                    'high_variance': 0.3
                }
            },
            'auto_calibration': {
                'enabled': True,
                'interval_minutes': 60,
                'min_predictions': 10
            }
        },
        'performance_targets': {
            'min_accuracy': 0.25,
            'target_calibration': 0.5,
            'max_variance': 0.2
        }
    }
    
    # 创建部署管理器
    deployment = DynamicConfidenceDeployment(config)
    
    # 执行部署
    success = deployment.deploy()
    
    if success:
        print("\n✅ 部署成功完成！")
        print("\n📋 后续步骤:")
        print("1. 监控系统运行状态")
        print("2. 观察置信度调整效果")
        print("3. 根据需要调整集成参数")
        print("4. 定期检查性能报告")
        print("5. 执行手动校准（如需要）")
        
        # 显示使用示例
        print("\n💡 使用示例:")
        print("```python")
        print("from dynamic_confidence_wrapper import predict_with_confidence")
        print("result = predict_with_confidence([1, 15, 23, 30, 35, 42])")
        print("print(f'预测: {result[\"predicted_numbers\"]}')")
        print("print(f'置信度: {result[\"confidence\"]:.3f}')")
        print("```")
    else:
        print("\n❌ 部署失败！")
        print("请检查日志文件 dynamic_confidence_deployment.log 获取详细信息")

if __name__ == "__main__":
    main()
