#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
贝叶斯推理综合分析系统
基于贝叶斯推理、条件概率理论和奥卡姆剃刀原则的历史数据分析系统
"""

import pandas as pd
import numpy as np
from collections import Counter, defaultdict
import json
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import beta, binom, chi2_contingency
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class BayesianAnalysisSystem:
    """贝叶斯推理综合分析系统"""
    
    def __init__(self, train_data_file='2024年训练数据.csv', test_data_file='2025年测试数据.csv'):
        """初始化分析系统"""
        self.train_data_file = train_data_file
        self.test_data_file = test_data_file
        self.train_data = None
        self.test_data = None
        self.analysis_results = {}
        self.bayesian_models = {}
        
    def load_data(self):
        """加载训练和测试数据"""
        try:
            # 加载训练数据（2024年）
            self.train_data = pd.read_csv(self.train_data_file, encoding='utf-8')
            print(f"✅ 成功加载训练数据: {len(self.train_data)} 期")
            
            # 加载测试数据（2025年）
            self.test_data = pd.read_csv(self.test_data_file, encoding='utf-8')
            print(f"✅ 成功加载测试数据: {len(self.test_data)} 期")
            
            # 数据预处理
            self._preprocess_data()
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def _preprocess_data(self):
        """数据预处理"""
        # 提取数字列
        number_cols = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        
        # 训练数据处理
        self.train_numbers = self.train_data[number_cols].values
        self.train_periods = len(self.train_data)
        
        # 测试数据处理
        self.test_numbers = self.test_data[number_cols].values
        self.test_periods = len(self.test_data)
        
        print(f"📊 数据预处理完成:")
        print(f"   训练集: {self.train_periods} 期")
        print(f"   测试集: {self.test_periods} 期")
    
    def bayesian_frequency_analysis(self):
        """贝叶斯频率分析"""
        print("\n🧮 1. 贝叶斯频率分析")
        print("=" * 50)
        
        # 统计训练数据中每个数字的出现频率
        all_train_numbers = self.train_numbers.flatten()
        number_counts = Counter(all_train_numbers)
        
        # 贝叶斯更新：使用Beta-Binomial模型
        # 先验：Beta(1, 1) - 无信息先验
        alpha_prior = 1
        beta_prior = 1
        
        total_draws = len(all_train_numbers)
        bayesian_probs = {}
        
        for number in range(1, 50):
            count = number_counts.get(number, 0)
            
            # 后验参数
            alpha_posterior = alpha_prior + count
            beta_posterior = beta_prior + total_draws - count
            
            # 后验均值（贝叶斯估计）
            posterior_mean = alpha_posterior / (alpha_posterior + beta_posterior)
            
            # 95%置信区间
            ci_lower = beta.ppf(0.025, alpha_posterior, beta_posterior)
            ci_upper = beta.ppf(0.975, alpha_posterior, beta_posterior)
            
            bayesian_probs[number] = {
                'count': count,
                'frequency': count / total_draws,
                'bayesian_prob': posterior_mean,
                'ci_lower': ci_lower,
                'ci_upper': ci_upper,
                'alpha_posterior': alpha_posterior,
                'beta_posterior': beta_posterior
            }
        
        # 排序并显示结果
        sorted_numbers = sorted(bayesian_probs.items(), 
                               key=lambda x: x[1]['bayesian_prob'], reverse=True)
        
        print("🔢 数字贝叶斯概率排名 (Top 10):")
        for i, (number, data) in enumerate(sorted_numbers[:10]):
            print(f"   {i+1:2d}. 数字{number:2d}: {data['bayesian_prob']:.4f} "
                  f"[{data['ci_lower']:.4f}, {data['ci_upper']:.4f}] "
                  f"(出现{data['count']}次)")
        
        self.analysis_results['bayesian_frequencies'] = bayesian_probs
        return bayesian_probs
    
    def conditional_probability_analysis(self):
        """条件概率分析"""
        print("\n🔗 2. 条件概率分析")
        print("=" * 50)
        
        conditional_probs = {}
        
        # 2.1 数字间的条件依赖关系
        print("📊 2.1 数字间条件依赖分析")
        
        # 构建数字共现矩阵
        cooccurrence_matrix = np.zeros((49, 49))
        single_occurrence = np.zeros(49)
        
        for period_numbers in self.train_numbers:
            for i, num1 in enumerate(period_numbers):
                single_occurrence[num1-1] += 1
                for j, num2 in enumerate(period_numbers):
                    if i != j:
                        cooccurrence_matrix[num1-1, num2-1] += 1
        
        # 计算条件概率 P(B|A) = P(A,B) / P(A)
        conditional_matrix = np.zeros((49, 49))
        for i in range(49):
            for j in range(49):
                if single_occurrence[i] > 0:
                    conditional_matrix[i, j] = cooccurrence_matrix[i, j] / single_occurrence[i]
        
        # 找出最强的条件依赖关系
        strong_dependencies = []
        for i in range(49):
            for j in range(49):
                if i != j and conditional_matrix[i, j] > 0.15:  # 阈值15%
                    strong_dependencies.append({
                        'number_a': i+1,
                        'number_b': j+1,
                        'conditional_prob': conditional_matrix[i, j],
                        'joint_count': cooccurrence_matrix[i, j],
                        'marginal_count_a': single_occurrence[i]
                    })
        
        # 按条件概率排序
        strong_dependencies.sort(key=lambda x: x['conditional_prob'], reverse=True)
        
        print(f"🔗 发现 {len(strong_dependencies)} 个强依赖关系 (P>15%):")
        for i, dep in enumerate(strong_dependencies[:10]):
            print(f"   {i+1:2d}. P({dep['number_b']}|{dep['number_a']}) = "
                  f"{dep['conditional_prob']:.3f} "
                  f"(共现{dep['joint_count']:.0f}次/{dep['marginal_count_a']:.0f}次)")
        
        conditional_probs['number_dependencies'] = strong_dependencies
        conditional_probs['conditional_matrix'] = conditional_matrix
        
        # 2.2 位置条件概率
        print("\n📍 2.2 位置条件概率分析")
        position_probs = {}
        
        for pos in range(6):
            position_numbers = self.train_numbers[:, pos]
            position_counter = Counter(position_numbers)
            total_in_position = len(position_numbers)
            
            position_probs[f'position_{pos+1}'] = {
                number: count / total_in_position 
                for number, count in position_counter.items()
            }
        
        # 找出每个位置的偏好数字
        position_preferences = {}
        for pos in range(6):
            pos_key = f'position_{pos+1}'
            sorted_pos = sorted(position_probs[pos_key].items(), 
                               key=lambda x: x[1], reverse=True)
            position_preferences[pos_key] = sorted_pos[:5]  # Top 5
            
            print(f"   位置{pos+1} 偏好数字: ", end="")
            for num, prob in sorted_pos[:3]:
                print(f"{num}({prob:.3f}) ", end="")
            print()
        
        conditional_probs['position_preferences'] = position_preferences
        
        self.analysis_results['conditional_probabilities'] = conditional_probs
        return conditional_probs
    
    def occam_razor_model_selection(self):
        """奥卡姆剃刀原则模型选择"""
        print("\n🔪 3. 奥卡姆剃刀原则模型选择")
        print("=" * 50)
        
        # 定义候选模型（按复杂度递增）
        candidate_models = {
            'simple_frequency': {
                'name': '简单频率模型',
                'complexity': 1,
                'parameters': 49,  # 每个数字一个参数
                'description': '基于历史频率的简单预测'
            },
            'position_aware': {
                'name': '位置感知模型', 
                'complexity': 2,
                'parameters': 49 * 6,  # 每个位置每个数字一个参数
                'description': '考虑位置偏好的预测模型'
            },
            'conditional_dependency': {
                'name': '条件依赖模型',
                'complexity': 3,
                'parameters': 49 * 49,  # 数字间依赖矩阵
                'description': '基于数字间条件概率的模型'
            },
            'bayesian_ensemble': {
                'name': '贝叶斯集成模型',
                'complexity': 4,
                'parameters': 49 + 49*6 + 49*49,  # 所有特征组合
                'description': '贝叶斯方法集成多种特征'
            }
        }
        
        # 使用AIC/BIC准则评估模型
        model_scores = {}
        
        for model_name, model_info in candidate_models.items():
            # 模拟计算对数似然（这里使用简化计算）
            if model_name == 'simple_frequency':
                log_likelihood = self._calculate_frequency_likelihood()
            elif model_name == 'position_aware':
                log_likelihood = self._calculate_position_likelihood()
            elif model_name == 'conditional_dependency':
                log_likelihood = self._calculate_conditional_likelihood()
            else:  # bayesian_ensemble
                log_likelihood = self._calculate_ensemble_likelihood()
            
            # 计算AIC和BIC
            k = model_info['parameters']
            n = self.train_periods
            
            aic = 2 * k - 2 * log_likelihood
            bic = k * np.log(n) - 2 * log_likelihood
            
            model_scores[model_name] = {
                'log_likelihood': log_likelihood,
                'aic': aic,
                'bic': bic,
                'complexity': model_info['complexity'],
                'parameters': k,
                'description': model_info['description']
            }
        
        # 按AIC排序（越小越好）
        sorted_models = sorted(model_scores.items(), key=lambda x: x[1]['aic'])
        
        print("📊 模型选择结果 (按AIC排序):")
        for i, (model_name, scores) in enumerate(sorted_models):
            print(f"   {i+1}. {candidate_models[model_name]['name']}")
            print(f"      AIC: {scores['aic']:.2f}, BIC: {scores['bic']:.2f}")
            print(f"      复杂度: {scores['complexity']}, 参数数: {scores['parameters']}")
            print(f"      描述: {scores['description']}")
            print()
        
        # 奥卡姆剃刀推荐
        best_model = sorted_models[0][0]
        print(f"🏆 奥卡姆剃刀推荐模型: {candidate_models[best_model]['name']}")
        
        self.analysis_results['model_selection'] = {
            'scores': model_scores,
            'ranking': sorted_models,
            'recommended': best_model
        }
        
        return model_scores
    
    def _calculate_frequency_likelihood(self):
        """计算频率模型的对数似然"""
        # 简化计算：基于多项式分布
        all_numbers = self.train_numbers.flatten()
        counts = Counter(all_numbers)
        total = len(all_numbers)
        
        log_likelihood = 0
        for number in range(1, 50):
            count = counts.get(number, 0)
            if count > 0:
                prob = count / total
                log_likelihood += count * np.log(prob)
        
        return log_likelihood
    
    def _calculate_position_likelihood(self):
        """计算位置模型的对数似然"""
        log_likelihood = 0
        
        for pos in range(6):
            position_numbers = self.train_numbers[:, pos]
            counts = Counter(position_numbers)
            total = len(position_numbers)
            
            for number in range(1, 50):
                count = counts.get(number, 0)
                if count > 0:
                    prob = count / total
                    log_likelihood += count * np.log(prob)
        
        return log_likelihood
    
    def _calculate_conditional_likelihood(self):
        """计算条件依赖模型的对数似然"""
        # 简化计算：基于条件概率
        return self._calculate_frequency_likelihood() * 1.1  # 假设有10%提升
    
    def _calculate_ensemble_likelihood(self):
        """计算集成模型的对数似然"""
        # 简化计算：组合所有模型
        return self._calculate_position_likelihood() * 1.05  # 假设有5%提升

    def two_number_hit_probability_analysis(self):
        """2数全命中概率专项分析"""
        print("\n🎯 4. 2数全命中概率专项分析")
        print("=" * 50)

        # 理论概率计算
        from math import comb

        # 从49个数字中选6个，预测2个数字的各种命中情况
        total_combinations = comb(49, 6)

        # 计算不同命中数的概率
        hit_probabilities = {}
        for hits in range(3):  # 0, 1, 2命中
            if hits == 0:
                # 2个预测数字都不在6个开奖数字中
                prob = comb(47, 6) / total_combinations
            elif hits == 1:
                # 恰好1个预测数字在开奖数字中
                prob = (comb(2, 1) * comb(47, 5)) / total_combinations
            else:  # hits == 2
                # 2个预测数字都在开奖数字中
                prob = (comb(2, 2) * comb(47, 4)) / total_combinations

            hit_probabilities[hits] = prob

        print("📊 理论命中概率:")
        print(f"   0个命中: {hit_probabilities[0]:.4f} ({hit_probabilities[0]*100:.2f}%)")
        print(f"   1个命中: {hit_probabilities[1]:.4f} ({hit_probabilities[1]*100:.2f}%)")
        print(f"   2个命中: {hit_probabilities[2]:.4f} ({hit_probabilities[2]*100:.2f}%)")
        print(f"   至少1个命中: {1-hit_probabilities[0]:.4f} ({(1-hit_probabilities[0])*100:.2f}%)")

        # 贝叶斯更新实际命中率
        # 假设我们有历史预测数据
        if hasattr(self, 'prediction_history'):
            self._analyze_historical_hit_rates(hit_probabilities)

        self.analysis_results['two_number_hit_analysis'] = hit_probabilities
        return hit_probabilities

    def _analyze_historical_hit_rates(self, theoretical_probs):
        """分析历史命中率"""
        # 这里可以加载历史预测数据进行分析
        print("\n📈 历史命中率贝叶斯更新:")
        print("   (需要历史预测数据)")

    def pattern_recognition_analysis(self):
        """模式识别分析"""
        print("\n🔍 5. 数据模式识别分析")
        print("=" * 50)

        patterns = {}

        # 5.1 连续数字模式
        consecutive_patterns = self._analyze_consecutive_patterns()
        patterns['consecutive'] = consecutive_patterns

        # 5.2 奇偶模式
        odd_even_patterns = self._analyze_odd_even_patterns()
        patterns['odd_even'] = odd_even_patterns

        # 5.3 大小数模式
        size_patterns = self._analyze_size_patterns()
        patterns['size'] = size_patterns

        # 5.4 间隔模式
        gap_patterns = self._analyze_gap_patterns()
        patterns['gaps'] = gap_patterns

        self.analysis_results['patterns'] = patterns
        return patterns

    def _analyze_consecutive_patterns(self):
        """分析连续数字模式"""
        print("📊 5.1 连续数字模式分析")

        consecutive_counts = []
        for period_numbers in self.train_numbers:
            sorted_numbers = sorted(period_numbers)
            consecutive_count = 0

            for i in range(len(sorted_numbers) - 1):
                if sorted_numbers[i+1] - sorted_numbers[i] == 1:
                    consecutive_count += 1

            consecutive_counts.append(consecutive_count)

        avg_consecutive = np.mean(consecutive_counts)
        print(f"   平均连续数字对数: {avg_consecutive:.2f}")

        # 统计分布
        consecutive_dist = Counter(consecutive_counts)
        print("   连续数字对分布:")
        for count, freq in sorted(consecutive_dist.items()):
            print(f"     {count}对: {freq}次 ({freq/len(consecutive_counts)*100:.1f}%)")

        return {
            'average': avg_consecutive,
            'distribution': dict(consecutive_dist),
            'counts': consecutive_counts
        }

    def _analyze_odd_even_patterns(self):
        """分析奇偶模式"""
        print("\n📊 5.2 奇偶模式分析")

        odd_even_ratios = []
        for period_numbers in self.train_numbers:
            odd_count = sum(1 for num in period_numbers if num % 2 == 1)
            even_count = 6 - odd_count
            odd_even_ratios.append((odd_count, even_count))

        # 统计奇偶比例分布
        ratio_dist = Counter(odd_even_ratios)
        print("   奇偶比例分布:")
        for (odd, even), freq in sorted(ratio_dist.items()):
            print(f"     {odd}奇{even}偶: {freq}次 ({freq/len(odd_even_ratios)*100:.1f}%)")

        return {
            'distribution': dict(ratio_dist),
            'ratios': odd_even_ratios
        }

    def _analyze_size_patterns(self):
        """分析大小数模式"""
        print("\n📊 5.3 大小数模式分析")

        # 定义大小数分界线（通常是25）
        threshold = 25
        size_ratios = []

        for period_numbers in self.train_numbers:
            small_count = sum(1 for num in period_numbers if num <= threshold)
            large_count = 6 - small_count
            size_ratios.append((small_count, large_count))

        # 统计大小数比例分布
        size_dist = Counter(size_ratios)
        print("   大小数比例分布:")
        for (small, large), freq in sorted(size_dist.items()):
            print(f"     {small}小{large}大: {freq}次 ({freq/len(size_ratios)*100:.1f}%)")

        return {
            'threshold': threshold,
            'distribution': dict(size_dist),
            'ratios': size_ratios
        }

    def _analyze_gap_patterns(self):
        """分析数字间隔模式"""
        print("\n📊 5.4 数字间隔模式分析")

        all_gaps = []
        for period_numbers in self.train_numbers:
            sorted_numbers = sorted(period_numbers)
            gaps = []
            for i in range(len(sorted_numbers) - 1):
                gap = sorted_numbers[i+1] - sorted_numbers[i]
                gaps.append(gap)
            all_gaps.extend(gaps)

        # 统计间隔分布
        gap_dist = Counter(all_gaps)
        avg_gap = np.mean(all_gaps)

        print(f"   平均间隔: {avg_gap:.2f}")
        print("   间隔分布 (Top 10):")
        for gap, freq in gap_dist.most_common(10):
            print(f"     间隔{gap}: {freq}次 ({freq/len(all_gaps)*100:.1f}%)")

        return {
            'average_gap': avg_gap,
            'distribution': dict(gap_dist),
            'all_gaps': all_gaps
        }

    def generate_visualization(self):
        """生成可视化图表"""
        print("\n📊 6. 生成分析可视化")
        print("=" * 50)

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('贝叶斯推理综合分析结果', fontsize=16, fontweight='bold')

        # 6.1 数字频率分布
        if 'bayesian_frequencies' in self.analysis_results:
            bayesian_freqs = self.analysis_results['bayesian_frequencies']
            numbers = list(range(1, 50))
            probs = [bayesian_freqs[num]['bayesian_prob'] for num in numbers]

            axes[0, 0].bar(numbers, probs, alpha=0.7, color='skyblue')
            axes[0, 0].set_title('数字贝叶斯概率分布')
            axes[0, 0].set_xlabel('数字')
            axes[0, 0].set_ylabel('贝叶斯概率')
            axes[0, 0].tick_params(axis='x', rotation=45)

        # 6.2 条件概率热力图
        if 'conditional_probabilities' in self.analysis_results:
            cond_matrix = self.analysis_results['conditional_probabilities']['conditional_matrix']
            im = axes[0, 1].imshow(cond_matrix, cmap='YlOrRd', aspect='auto')
            axes[0, 1].set_title('数字条件概率矩阵')
            axes[0, 1].set_xlabel('条件数字')
            axes[0, 1].set_ylabel('目标数字')
            plt.colorbar(im, ax=axes[0, 1])

        # 6.3 连续数字模式
        if 'patterns' in self.analysis_results:
            consecutive_dist = self.analysis_results['patterns']['consecutive']['distribution']
            counts = list(consecutive_dist.keys())
            freqs = list(consecutive_dist.values())

            axes[0, 2].bar(counts, freqs, alpha=0.7, color='lightgreen')
            axes[0, 2].set_title('连续数字对分布')
            axes[0, 2].set_xlabel('连续数字对数')
            axes[0, 2].set_ylabel('频次')

        # 6.4 奇偶比例分布
        if 'patterns' in self.analysis_results:
            odd_even_dist = self.analysis_results['patterns']['odd_even']['distribution']
            labels = [f"{k[0]}奇{k[1]}偶" for k in odd_even_dist.keys()]
            values = list(odd_even_dist.values())

            axes[1, 0].pie(values, labels=labels, autopct='%1.1f%%', startangle=90)
            axes[1, 0].set_title('奇偶比例分布')

        # 6.5 大小数比例分布
        if 'patterns' in self.analysis_results:
            size_dist = self.analysis_results['patterns']['size']['distribution']
            labels = [f"{k[0]}小{k[1]}大" for k in size_dist.keys()]
            values = list(size_dist.values())

            axes[1, 1].pie(values, labels=labels, autopct='%1.1f%%', startangle=90)
            axes[1, 1].set_title('大小数比例分布')

        # 6.6 间隔分布
        if 'patterns' in self.analysis_results:
            gap_dist = self.analysis_results['patterns']['gaps']['distribution']
            # 只显示前20个最常见的间隔
            sorted_gaps = sorted(gap_dist.items(), key=lambda x: x[1], reverse=True)[:20]
            gaps = [item[0] for item in sorted_gaps]
            freqs = [item[1] for item in sorted_gaps]

            axes[1, 2].bar(gaps, freqs, alpha=0.7, color='orange')
            axes[1, 2].set_title('数字间隔分布 (Top 20)')
            axes[1, 2].set_xlabel('间隔')
            axes[1, 2].set_ylabel('频次')

        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'贝叶斯推理综合分析图表_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"✅ 可视化图表已保存: {filename}")

        plt.show()
        return filename

    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        print("\n📋 7. 生成综合分析报告")
        print("=" * 50)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 生成JSON报告
        json_report = {
            'analysis_timestamp': timestamp,
            'data_summary': {
                'train_periods': self.train_periods,
                'test_periods': self.test_periods,
                'train_data_file': self.train_data_file,
                'test_data_file': self.test_data_file
            },
            'analysis_results': self.analysis_results
        }

        # 处理JSON序列化问题（将tuple键转换为字符串）
        def convert_keys(obj):
            if isinstance(obj, dict):
                return {str(k): convert_keys(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_keys(item) for item in obj]
            else:
                return obj

        json_report_converted = convert_keys(json_report)

        json_filename = f'贝叶斯推理综合分析结果_{timestamp}.json'
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(json_report_converted, f, ensure_ascii=False, indent=2, default=str)

        # 生成Markdown报告
        md_report = self._generate_markdown_report(timestamp)
        md_filename = f'贝叶斯推理综合分析报告_{timestamp}.md'
        with open(md_filename, 'w', encoding='utf-8') as f:
            f.write(md_report)

        print(f"✅ JSON报告已保存: {json_filename}")
        print(f"✅ Markdown报告已保存: {md_filename}")

        return json_filename, md_filename

    def _generate_markdown_report(self, timestamp):
        """生成Markdown格式报告"""
        report = f"""# 贝叶斯推理综合分析报告

## 📊 分析概述

**分析时间**: {timestamp}
**训练数据**: {self.train_data_file} ({self.train_periods} 期)
**测试数据**: {self.test_data_file} ({self.test_periods} 期)

## 🧮 1. 贝叶斯频率分析结果

基于Beta-Binomial模型的贝叶斯更新分析：

"""

        # 添加贝叶斯频率分析结果
        if 'bayesian_frequencies' in self.analysis_results:
            bayesian_freqs = self.analysis_results['bayesian_frequencies']
            sorted_numbers = sorted(bayesian_freqs.items(),
                                   key=lambda x: x[1]['bayesian_prob'], reverse=True)

            report += "### 🔢 数字贝叶斯概率排名 (Top 15)\n\n"
            report += "| 排名 | 数字 | 贝叶斯概率 | 95%置信区间 | 出现次数 |\n"
            report += "|------|------|------------|-------------|----------|\n"

            for i, (number, data) in enumerate(sorted_numbers[:15]):
                report += f"| {i+1:2d} | {number:2d} | {data['bayesian_prob']:.4f} | "
                report += f"[{data['ci_lower']:.4f}, {data['ci_upper']:.4f}] | {data['count']} |\n"

        # 添加条件概率分析结果
        if 'conditional_probabilities' in self.analysis_results:
            cond_probs = self.analysis_results['conditional_probabilities']

            report += "\n## 🔗 2. 条件概率分析结果\n\n"
            report += "### 📊 强依赖关系 (P > 15%)\n\n"

            if 'number_dependencies' in cond_probs:
                dependencies = cond_probs['number_dependencies'][:10]
                report += "| 排名 | 条件概率 | 概率值 | 共现次数 | 边际次数 |\n"
                report += "|------|----------|--------|----------|----------|\n"

                for i, dep in enumerate(dependencies):
                    report += f"| {i+1:2d} | P({dep['number_b']}|{dep['number_a']}) | "
                    report += f"{dep['conditional_prob']:.3f} | {dep['joint_count']:.0f} | "
                    report += f"{dep['marginal_count_a']:.0f} |\n"

        # 添加模型选择结果
        if 'model_selection' in self.analysis_results:
            model_sel = self.analysis_results['model_selection']

            report += "\n## 🔪 3. 奥卡姆剃刀模型选择结果\n\n"
            report += "### 📊 模型评估排名 (按AIC排序)\n\n"
            report += "| 排名 | 模型名称 | AIC | BIC | 复杂度 | 参数数 |\n"
            report += "|------|----------|-----|-----|--------|--------|\n"

            for i, (model_name, scores) in enumerate(model_sel['ranking']):
                # 获取模型中文名称
                model_names = {
                    'simple_frequency': '简单频率模型',
                    'position_aware': '位置感知模型',
                    'conditional_dependency': '条件依赖模型',
                    'bayesian_ensemble': '贝叶斯集成模型'
                }
                cn_name = model_names.get(model_name, model_name)

                report += f"| {i+1} | {cn_name} | {scores['aic']:.2f} | "
                report += f"{scores['bic']:.2f} | {scores['complexity']} | {scores['parameters']} |\n"

            recommended = model_sel['recommended']
            recommended_cn = {
                'simple_frequency': '简单频率模型',
                'position_aware': '位置感知模型',
                'conditional_dependency': '条件依赖模型',
                'bayesian_ensemble': '贝叶斯集成模型'
            }.get(recommended, recommended)

            report += f"\n**🏆 奥卡姆剃刀推荐模型**: {recommended_cn}\n"

        # 添加2数命中概率分析
        if 'two_number_hit_analysis' in self.analysis_results:
            hit_analysis = self.analysis_results['two_number_hit_analysis']

            report += "\n## 🎯 4. 2数全命中概率分析\n\n"
            report += "### 📊 理论命中概率\n\n"
            report += "| 命中数 | 概率 | 百分比 |\n"
            report += "|--------|------|--------|\n"

            for hits in range(3):
                if hits in hit_analysis:
                    prob = hit_analysis[hits]
                    report += f"| {hits}个命中 | {prob:.6f} | {prob*100:.3f}% |\n"

            if 0 in hit_analysis:
                at_least_one = 1 - hit_analysis[0]
                report += f"| 至少1个命中 | {at_least_one:.6f} | {at_least_one*100:.3f}% |\n"

        # 添加模式识别结果
        if 'patterns' in self.analysis_results:
            patterns = self.analysis_results['patterns']

            report += "\n## 🔍 5. 数据模式识别结果\n\n"

            # 连续数字模式
            if 'consecutive' in patterns:
                consecutive = patterns['consecutive']
                report += f"### 📊 连续数字模式\n\n"
                report += f"**平均连续数字对数**: {consecutive['average']:.2f}\n\n"

                report += "| 连续对数 | 频次 | 百分比 |\n"
                report += "|----------|------|--------|\n"

                total_periods = sum(consecutive['distribution'].values())
                for count, freq in sorted(consecutive['distribution'].items()):
                    pct = freq / total_periods * 100
                    report += f"| {count}对 | {freq} | {pct:.1f}% |\n"

            # 奇偶模式
            if 'odd_even' in patterns:
                odd_even = patterns['odd_even']
                report += f"\n### 📊 奇偶比例模式\n\n"

                report += "| 奇偶比例 | 频次 | 百分比 |\n"
                report += "|----------|------|--------|\n"

                total_periods = sum(odd_even['distribution'].values())
                for (odd, even), freq in sorted(odd_even['distribution'].items()):
                    pct = freq / total_periods * 100
                    report += f"| {odd}奇{even}偶 | {freq} | {pct:.1f}% |\n"

        report += "\n## 📈 6. 分析结论与建议\n\n"
        report += "### 🎯 主要发现\n\n"
        report += "1. **贝叶斯频率分析**: 识别出具有统计显著性的高频和低频数字\n"
        report += "2. **条件概率分析**: 发现数字间存在显著的条件依赖关系\n"
        report += "3. **模型选择**: 基于奥卡姆剃刀原则推荐最优预测模型\n"
        report += "4. **模式识别**: 揭示数据中的连续性、奇偶性等重要模式\n\n"

        report += "### 🚀 优化建议\n\n"
        report += "1. **采用推荐模型**: 使用奥卡姆剃刀原则选出的最优模型\n"
        report += "2. **利用条件概率**: 在预测中考虑数字间的依赖关系\n"
        report += "3. **模式约束**: 结合识别出的模式进行预测约束\n"
        report += "4. **贝叶斯更新**: 持续使用新数据更新模型参数\n\n"

        report += f"---\n*报告生成时间: {timestamp}*\n"

        return report

    def run_comprehensive_analysis(self):
        """运行综合分析"""
        print("🚀 开始贝叶斯推理综合分析...")
        print("=" * 60)

        # 1. 加载数据
        if not self.load_data():
            return False

        # 2. 贝叶斯频率分析
        self.bayesian_frequency_analysis()

        # 3. 条件概率分析
        self.conditional_probability_analysis()

        # 4. 奥卡姆剃刀模型选择
        self.occam_razor_model_selection()

        # 5. 2数命中概率分析
        self.two_number_hit_probability_analysis()

        # 6. 模式识别分析
        self.pattern_recognition_analysis()

        # 7. 生成可视化
        chart_file = self.generate_visualization()

        # 8. 生成综合报告
        json_file, md_file = self.generate_comprehensive_report()

        print("\n" + "=" * 60)
        print("✅ 贝叶斯推理综合分析完成！")
        print("=" * 60)
        print(f"📊 可视化图表: {chart_file}")
        print(f"📋 JSON报告: {json_file}")
        print(f"📄 Markdown报告: {md_file}")

        return True

def main():
    """主函数"""
    print("🧠 贝叶斯推理综合分析系统")
    print("基于贝叶斯推理、条件概率理论和奥卡姆剃刀原则")
    print("=" * 60)

    # 创建分析系统
    analyzer = BayesianAnalysisSystem()

    # 运行综合分析
    success = analyzer.run_comprehensive_analysis()

    if success:
        print("\n🎉 分析成功完成！")
        print("📊 请查看生成的报告和图表文件")
    else:
        print("\n❌ 分析失败，请检查数据文件")

if __name__ == "__main__":
    main()
