#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测结果深度分析系统
基于生产优化2025年181-200期预测结果.csv进行全面分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import json
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class PredictionResultAnalyzer:
    """预测结果深度分析器"""
    
    def __init__(self):
        self.prediction_file = "生产优化2025年181-200期预测结果.csv"
        self.real_data_file = "data/processed/lottery_data_clean_no_special.csv"
        
        self.predictions = None
        self.real_data = None
        self.analysis_results = {}
        
    def load_data(self):
        """加载预测数据和真实数据"""
        try:
            # 加载预测数据
            self.predictions = pd.read_csv(self.prediction_file)
            print(f"✅ 预测数据加载成功: {len(self.predictions)}期")
            
            # 加载真实数据
            real_data_full = pd.read_csv(self.real_data_file)
            # 筛选2025年181期之后的数据
            self.real_data = real_data_full[
                (real_data_full['年份'] == 2025) & 
                (real_data_full['期号'] >= 181)
            ].copy()
            
            print(f"✅ 真实数据加载成功: {len(self.real_data)}期 (2025年181期开始)")
            print(f"  预测数据范围: 181-200期")
            print(f"  真实数据范围: 181-{self.real_data['期号'].max()}期")
            
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def analyze_prediction_schemes(self):
        """分析预测方案对比"""
        print(f"\n🔍 预测方案对比分析")
        print("=" * 60)
        
        # 合并预测数据和真实数据
        merged_data = pd.merge(
            self.predictions, 
            self.real_data, 
            left_on=['年份', '期号'], 
            right_on=['年份', '期号'], 
            how='inner'
        )
        
        print(f"可验证期数: {len(merged_data)}期")
        
        if len(merged_data) == 0:
            print("⚠️ 没有可验证的数据")
            return None
        
        # 定义预测方案
        schemes = {
            '预测数字1': 'prediction1',
            '预测数字2': 'prediction2', 
            '基础预测1': 'base1',
            '基础预测2': 'base2'
        }
        
        results = {}
        
        print(f"\n{'方案':<12} {'总期数':<8} {'命中期数':<8} {'命中率':<8} {'命中详情'}")
        print("-" * 60)
        
        for scheme_name, scheme_key in schemes.items():
            hits = []
            hit_count = 0
            
            for _, row in merged_data.iterrows():
                # 获取预测数字
                if scheme_name == '预测数字1':
                    pred_nums = [row['预测数字1'], row['预测数字2']]
                elif scheme_name == '预测数字2':
                    pred_nums = [row['预测数字1'], row['预测数字2']]
                elif scheme_name == '基础预测1':
                    pred_nums = [row['基础预测1'], row['基础预测2']]
                elif scheme_name == '基础预测2':
                    pred_nums = [row['基础预测1'], row['基础预测2']]
                
                # 获取真实开奖数字
                real_nums = [row[f'数字{i}'] for i in range(1, 7)]
                
                # 计算命中
                pred_set = set(pred_nums)
                real_set = set(real_nums)
                hit_nums = pred_set & real_set
                is_hit = len(hit_nums) >= 1
                
                hits.append({
                    'period': row['期号'],
                    'predicted': pred_nums,
                    'actual': real_nums,
                    'hit_numbers': list(hit_nums),
                    'hit_count': len(hit_nums),
                    'is_hit': is_hit
                })
                
                if is_hit:
                    hit_count += 1
            
            # 计算统计结果
            total_periods = len(merged_data)
            hit_rate = hit_count / total_periods if total_periods > 0 else 0
            
            results[scheme_name] = {
                'total_periods': total_periods,
                'hit_periods': hit_count,
                'hit_rate': hit_rate,
                'hits_detail': hits
            }
            
            # 显示结果
            hit_periods_str = f"{hit_count}/{total_periods}"
            print(f"{scheme_name:<12} {total_periods:<8} {hit_count:<8} {hit_rate:.3f}    {hit_periods_str}")
        
        self.analysis_results['schemes'] = results
        return results
    
    def analyze_confidence_calibration(self):
        """分析置信度校准"""
        print(f"\n📊 置信度校准验证")
        print("=" * 60)
        
        # 合并数据
        merged_data = pd.merge(
            self.predictions, 
            self.real_data, 
            left_on=['年份', '期号'], 
            right_on=['年份', '期号'], 
            how='inner'
        )
        
        if len(merged_data) == 0:
            print("⚠️ 没有可验证的数据")
            return None
        
        # 分析最终置信度和基础置信度
        confidence_types = ['最终置信度', '基础置信度']
        
        for conf_type in confidence_types:
            print(f"\n{conf_type}校准分析:")
            
            # 计算每期的命中情况
            hit_results = []
            for _, row in merged_data.iterrows():
                pred_nums = [row['预测数字1'], row['预测数字2']]
                real_nums = [row[f'数字{i}'] for i in range(1, 7)]
                
                pred_set = set(pred_nums)
                real_set = set(real_nums)
                is_hit = len(pred_set & real_set) >= 1
                
                hit_results.append({
                    'confidence': row[conf_type],
                    'is_hit': is_hit,
                    'period': row['期号']
                })
            
            hit_df = pd.DataFrame(hit_results)
            
            # 置信度分组分析
            bins = [0, 0.5, 0.6, 0.7, 0.8, 1.0]
            bin_labels = ['0.0-0.5', '0.5-0.6', '0.6-0.7', '0.7-0.8', '0.8-1.0']
            
            hit_df['conf_bin'] = pd.cut(hit_df['confidence'], bins=bins, labels=bin_labels, include_lowest=True)
            
            calibration_results = hit_df.groupby('conf_bin').agg({
                'is_hit': ['count', 'sum', 'mean'],
                'confidence': 'mean'
            }).round(3)
            
            print(f"  {'置信度区间':<12} {'期数':<6} {'命中':<6} {'命中率':<8} {'平均置信度'}")
            print("  " + "-" * 50)
            
            for bin_label in bin_labels:
                if bin_label in calibration_results.index:
                    row = calibration_results.loc[bin_label]
                    count = int(row[('is_hit', 'count')])
                    hits = int(row[('is_hit', 'sum')])
                    hit_rate = row[('is_hit', 'mean')]
                    avg_conf = row[('confidence', 'mean')]
                    
                    print(f"  {bin_label:<12} {count:<6} {hits:<6} {hit_rate:.3f}    {avg_conf:.3f}")
            
            # 相关性分析
            correlation = hit_df['confidence'].corr(hit_df['is_hit'].astype(int))
            print(f"  置信度与命中率相关性: {correlation:.3f}")
        
        self.analysis_results['confidence'] = hit_df
        return hit_df
    
    def deep_thinking_analysis(self):
        """深度思辨分析"""
        print(f"\n🧠 深度思辨分析")
        print("=" * 60)
        
        if 'schemes' not in self.analysis_results:
            print("⚠️ 需要先进行预测方案分析")
            return
        
        schemes_results = self.analysis_results['schemes']
        
        print(f"1. 预测方案性能对比:")
        print(f"   {'方案':<12} {'命中率':<8} {'相对表现'}")
        print(f"   " + "-" * 35)
        
        # 按命中率排序
        sorted_schemes = sorted(schemes_results.items(), key=lambda x: x[1]['hit_rate'], reverse=True)
        
        best_rate = sorted_schemes[0][1]['hit_rate']
        
        for scheme_name, result in sorted_schemes:
            hit_rate = result['hit_rate']
            relative_perf = "最佳" if hit_rate == best_rate else f"{hit_rate/best_rate:.1%}"
            print(f"   {scheme_name:<12} {hit_rate:.3f}    {relative_perf}")
        
        print(f"\n2. 基础预测 vs 最终预测分析:")
        
        # 对比基础预测和最终预测
        base_schemes = ['基础预测1', '基础预测2']
        final_schemes = ['预测数字1', '预测数字2']
        
        base_avg = np.mean([schemes_results[s]['hit_rate'] for s in base_schemes])
        final_avg = np.mean([schemes_results[s]['hit_rate'] for s in final_schemes])
        
        print(f"   基础预测平均命中率: {base_avg:.3f}")
        print(f"   最终预测平均命中率: {final_avg:.3f}")
        print(f"   性能差异: {final_avg - base_avg:+.3f}")
        
        if final_avg > base_avg:
            print(f"   结论: 最终预测优于基础预测")
        elif final_avg < base_avg:
            print(f"   结论: 基础预测优于最终预测 (可能存在过度优化)")
        else:
            print(f"   结论: 两者性能相当")
        
        print(f"\n3. 策略配置效果分析:")
        
        # 分析不同策略配置的效果
        merged_data = pd.merge(
            self.predictions, 
            self.real_data, 
            left_on=['年份', '期号'], 
            right_on=['年份', '期号'], 
            how='inner'
        )
        
        if len(merged_data) > 0:
            # 按策略配置分组
            strategy_groups = merged_data.groupby('策略配置')
            
            print(f"   {'策略配置':<15} {'期数':<6} {'平均置信度':<10} {'说明'}")
            print(f"   " + "-" * 45)
            
            for strategy, group in strategy_groups:
                count = len(group)
                avg_conf = group['最终置信度'].mean()
                
                # 计算该策略的命中率
                hit_count = 0
                for _, row in group.iterrows():
                    pred_nums = [row['预测数字1'], row['预测数字2']]
                    real_nums = [row[f'数字{i}'] for i in range(1, 7)]
                    if len(set(pred_nums) & set(real_nums)) >= 1:
                        hit_count += 1
                
                hit_rate = hit_count / count if count > 0 else 0
                
                print(f"   {strategy:<15} {count:<6} {avg_conf:.3f}      命中率{hit_rate:.3f}")
    
    def baseline_comparison(self):
        """与基线对比"""
        print(f"\n📈 与29.2%基线对比")
        print("=" * 60)
        
        if 'schemes' not in self.analysis_results:
            print("⚠️ 需要先进行预测方案分析")
            return
        
        baseline_rate = 0.292  # 29.2%单期预测基线
        schemes_results = self.analysis_results['schemes']
        
        print(f"基线: 29.2%单期预测方法")
        print(f"")
        print(f"{'方案':<12} {'命中率':<8} {'vs基线':<10} {'统计显著性'}")
        print("-" * 50)
        
        for scheme_name, result in schemes_results.items():
            hit_rate = result['hit_rate']
            diff = hit_rate - baseline_rate
            
            # 简单的二项检验
            n = result['total_periods']
            k = result['hit_periods']
            
            if n > 0:
                # 计算p值 (简化版本)
                expected_hits = n * baseline_rate
                z_score = (k - expected_hits) / np.sqrt(n * baseline_rate * (1 - baseline_rate))
                p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
                
                significance = "显著" if p_value < 0.05 else "不显著"
                vs_baseline = f"{diff:+.3f}"
                
                print(f"{scheme_name:<12} {hit_rate:.3f}    {vs_baseline:<10} {significance}")
    
    def generate_detailed_report(self):
        """生成详细报告"""
        print(f"\n📋 详细验证报告")
        print("=" * 60)
        
        # 合并数据
        merged_data = pd.merge(
            self.predictions, 
            self.real_data, 
            left_on=['年份', '期号'], 
            right_on=['年份', '期号'], 
            how='inner'
        )
        
        if len(merged_data) == 0:
            print("⚠️ 没有可验证的数据")
            return
        
        print(f"逐期验证结果:")
        print(f"{'期号':<6} {'预测1':<8} {'预测2':<8} {'基础1':<8} {'基础2':<8} {'实际开奖':<20} {'命中情况'}")
        print("-" * 80)
        
        for _, row in merged_data.iterrows():
            period = row['期号']
            pred1 = f"{row['预测数字1']},{row['预测数字2']}"
            pred2 = f"{row['预测数字1']},{row['预测数字2']}"  # 同一组预测
            base1 = f"{row['基础预测1']},{row['基础预测2']}"
            base2 = f"{row['基础预测1']},{row['基础预测2']}"  # 同一组基础预测
            
            real_nums = [row[f'数字{i}'] for i in range(1, 7)]
            real_str = str(real_nums)
            
            # 计算命中
            pred_set = {row['预测数字1'], row['预测数字2']}
            base_set = {row['基础预测1'], row['基础预测2']}
            real_set = set(real_nums)
            
            pred_hits = pred_set & real_set
            base_hits = base_set & real_set
            
            hit_info = f"预测:{len(pred_hits)} 基础:{len(base_hits)}"
            
            print(f"{period:<6} {pred1:<8} {pred2:<8} {base1:<8} {base2:<8} {real_str:<20} {hit_info}")
    
    def save_analysis_results(self):
        """保存分析结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存到JSON文件
        results_file = f"预测结果分析_{timestamp}.json"
        
        # 处理numpy类型
        def convert_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_types(v) for v in obj]
            else:
                return obj
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(convert_types(self.analysis_results), f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 分析结果已保存: {results_file}")

def main():
    """主函数"""
    print("🔍 预测结果深度分析系统")
    print("基于生产优化2025年181-200期预测结果.csv")
    print("=" * 80)
    
    analyzer = PredictionResultAnalyzer()
    
    # 1. 加载数据
    if not analyzer.load_data():
        return
    
    # 2. 预测方案对比分析
    analyzer.analyze_prediction_schemes()
    
    # 3. 置信度校准验证
    analyzer.analyze_confidence_calibration()
    
    # 4. 深度思辨分析
    analyzer.deep_thinking_analysis()
    
    # 5. 与基线对比
    analyzer.baseline_comparison()
    
    # 6. 生成详细报告
    analyzer.generate_detailed_report()
    
    # 7. 保存结果
    analyzer.save_analysis_results()
    
    print(f"\n🎉 深度分析完成")

if __name__ == "__main__":
    main()
