{"system_info": {"name": "Production Lottery Prediction System", "version": "1.0.0", "last_updated": "2025-07-12T21:57:35.569369", "optimization_date": "2025-07-12"}, "optimal_method": {"method_name": "生产级马尔可夫预测系统", "training_period": "2023-2024年", "test_period": "2025年1-182期", "performance": "29.2%", "confidence_level": "A级可信度", "file_name": "生产级马尔可夫预测系统.py", "data_file": "data/processed/lottery_data_clean_no_special.csv"}, "performance_metrics": {"baseline_performance": "29.2%", "validation_dataset": "2025年1-182期", "evaluation_standard": "2个预测中至少1个命中", "statistical_significance": "p<0.05验证通过"}, "data_configuration": {"training_data": "2023-2024年 (731期)", "test_data": "2025年1-182期 (178期)", "data_file": "data/processed/lottery_data_clean_no_special.csv", "data_validation": "已验证无特码污染"}, "removed_methods": {"reason": "基于训练数据区间调节实验，性能显著劣于基准", "removed_files": ["两阶段预测系统.py", "改进两阶段预测系统.py", "简化两阶段预测系统.py", "两阶段预测验证结果_20250712_213026.json", "两阶段预测验证结果_20250712_213049.json", "两阶段预测验证结果_20250712_213120.csv", "两阶段预测摘要_20250712_213120.txt", "改进两阶段预测验证结果_20250712_213319.csv", "改进两阶段预测摘要_20250712_213319.txt", "简化两阶段预测验证结果_20250712_213517.csv", "简化两阶段预测摘要_20250712_213517.txt", "优化数据策略备选系统.py", "精英特征工程方法深度分析.py", "重复数值机制深度思辨分析.py", "重复机制融合马尔可夫基准_可行性分析.py", "训练数据区间调节实验系统.py", "训练数据区间调节可视化分析.py"], "backup_location": "backup_removed_files_20250712_215735"}, "usage_instructions": {"main_script": "生产级马尔可夫预测系统.py", "command": "python 生产级马尔可夫预测系统.py", "expected_performance": "29.2%成功率", "maintenance": "建议定期使用滚动窗口更新训练数据"}}