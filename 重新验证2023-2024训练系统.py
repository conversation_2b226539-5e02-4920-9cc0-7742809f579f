#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新验证系统：2023-2024年训练，2025年1-179期测试
深入分析29%马尔可夫方法差异
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class RevalidationSystem:
    """重新验证系统"""
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.full_data = None
        self.train_data = None
        self.test_data = None
        
        # 预测方案配置
        self.prediction_schemes = {
            '标准马尔可夫': self._standard_markov_prediction,
            '29%理论马尔可夫': self._theoretical_29_markov_prediction,
            '增强马尔可夫': self._enhanced_markov_prediction,
            '奇偶平衡': self._odd_even_prediction,
            '间隔分析': self._interval_analysis_prediction,
            '热冷数字': self._hot_cold_prediction,
            '频率分析': self._frequency_analysis_prediction
        }
        
        self.results = {}
        
    def load_and_prepare_data(self):
        """加载数据并按新要求分割"""
        print(f"🔄 重新验证：2023-2024训练 vs 2025年1-179期测试")
        print("=" * 60)
        
        try:
            # 加载完整数据
            self.full_data = pd.read_csv(self.data_file)
            self.full_data = self.full_data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            print(f"✅ 完整数据加载: {len(self.full_data)}期")
            
            # 新的数据分割：2023-2024年作为训练集
            self.train_data = self.full_data[
                (self.full_data['年份'] >= 2023) & 
                (self.full_data['年份'] <= 2024)
            ].copy()
            
            # 测试集：2025年1-179期
            self.test_data = self.full_data[
                (self.full_data['年份'] == 2025) & 
                (self.full_data['期号'] <= 179)
            ].copy()
            
            print(f"✅ 训练集: {len(self.train_data)}期 (2023-2024年)")
            print(f"✅ 测试集: {len(self.test_data)}期 (2025年1-179期)")
            print(f"🔒 数据分割检查: 训练期间2023-2024年，测试期间2025年")
            
            # 验证数据分割的合理性
            train_years = sorted(self.train_data['年份'].unique())
            test_years = sorted(self.test_data['年份'].unique())
            print(f"📊 训练集年份: {train_years}")
            print(f"📊 测试集年份: {test_years}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def _build_markov_models(self):
        """构建不同版本的马尔可夫模型"""
        print(f"\n🔧 构建多版本马尔可夫模型")
        print("=" * 50)
        
        # 1. 标准马尔可夫模型
        self._build_standard_markov()
        
        # 2. 29%理论马尔可夫模型
        self._build_theoretical_29_markov()
        
        # 3. 增强马尔可夫模型
        self._build_enhanced_markov()
        
        print(f"✅ 多版本马尔可夫模型构建完成")
    
    def _build_standard_markov(self):
        """构建标准马尔可夫模型"""
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 计算转移概率
        self._standard_markov_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                self._standard_markov_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        print(f"  标准马尔可夫: {len(self._standard_markov_prob)}个状态")
    
    def _build_theoretical_29_markov(self):
        """构建29%理论马尔可夫模型（尝试复现29.2%结果）"""
        # 使用更精细的参数设置，尝试达到29%的理论水平
        transition_count = defaultdict(lambda: defaultdict(int))
        
        # 使用全部训练数据，包括更多历史信息
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 计算转移概率，使用平滑处理
        self._theoretical_29_markov_prob = {}
        for curr_num in range(1, 50):  # 确保所有数字都有概率
            if curr_num in transition_count:
                total = sum(transition_count[curr_num].values())
                # 拉普拉斯平滑
                smoothed_total = total + 49  # 49个可能的下一个数字
                self._theoretical_29_markov_prob[curr_num] = {}
                
                for next_num in range(1, 50):
                    count = transition_count[curr_num].get(next_num, 0)
                    # 平滑概率
                    self._theoretical_29_markov_prob[curr_num][next_num] = (count + 1) / smoothed_total
            else:
                # 如果没有历史数据，使用均匀分布
                self._theoretical_29_markov_prob[curr_num] = {
                    next_num: 1/49 for next_num in range(1, 50)
                }
        
        print(f"  29%理论马尔可夫: 49个状态(完整)")
    
    def _build_enhanced_markov(self):
        """构建增强马尔可夫模型"""
        # 考虑更多因素：时间权重、频率调整等
        transition_count = defaultdict(lambda: defaultdict(int))
        
        # 使用时间衰减权重
        total_periods = len(self.train_data)
        for i in range(len(self.train_data) - 1):
            # 时间权重：越近的数据权重越大
            time_weight = (i + 1) / total_periods
            
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += time_weight
        
        # 计算转移概率
        self._enhanced_markov_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                self._enhanced_markov_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        print(f"  增强马尔可夫: {len(self._enhanced_markov_prob)}个状态")
    
    def _standard_markov_prediction(self, prev_numbers, period_info):
        """标准马尔可夫预测"""
        if not hasattr(self, '_standard_markov_prob'):
            self._build_markov_models()
        
        return self._markov_predict(prev_numbers, self._standard_markov_prob, perturbation=0.08)
    
    def _theoretical_29_markov_prediction(self, prev_numbers, period_info):
        """29%理论马尔可夫预测"""
        if not hasattr(self, '_theoretical_29_markov_prob'):
            self._build_markov_models()
        
        return self._markov_predict(prev_numbers, self._theoretical_29_markov_prob, perturbation=0.05)
    
    def _enhanced_markov_prediction(self, prev_numbers, period_info):
        """增强马尔可夫预测"""
        if not hasattr(self, '_enhanced_markov_prob'):
            self._build_markov_models()
        
        return self._markov_predict(prev_numbers, self._enhanced_markov_prob, perturbation=0.06)
    
    def _markov_predict(self, previous_numbers, transition_prob, perturbation=0.08):
        """通用马尔可夫预测方法"""
        number_probs = defaultdict(float)
        total_prob = 0.0
        coverage_count = 0
        
        for prev_num in previous_numbers:
            if prev_num in transition_prob:
                coverage_count += 1
                for next_num, prob in transition_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        # 添加随机扰动
        for num in number_probs:
            noise = np.random.normal(0, perturbation * number_probs[num])
            number_probs[num] = max(0, number_probs[num] + noise)
        
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            return [num for num, prob in sorted_numbers[:2]]
        else:
            return [1, 2]
    
    def _odd_even_prediction(self, prev_numbers, period_info):
        """奇偶平衡预测"""
        odd_candidates = [n for n in range(1, 50) if n % 2 == 1]
        even_candidates = [n for n in range(1, 50) if n % 2 == 0]
        
        odd_choice = np.random.choice(odd_candidates)
        even_choice = np.random.choice(even_candidates)
        
        return [odd_choice, even_choice]
    
    def _interval_analysis_prediction(self, prev_numbers, period_info):
        """间隔分析预测"""
        if not hasattr(self, '_interval_stats'):
            self._build_interval_stats()
        
        candidates = []
        for num in range(1, 50):
            if num in self._interval_stats and len(self._interval_stats[num]) > 0:
                avg_interval = np.mean(self._interval_stats[num])
                if 1 <= avg_interval <= 10:
                    candidates.append(num)
        
        if len(candidates) >= 2:
            return np.random.choice(candidates, 2, replace=False).tolist()
        else:
            return [1, 2]
    
    def _build_interval_stats(self):
        """构建间隔统计"""
        self._interval_stats = defaultdict(list)
        number_positions = defaultdict(list)
        
        for idx, row in self.train_data.iterrows():
            for j in range(1, 7):
                number_positions[row[f'数字{j}']].append(idx)
        
        for num, positions in number_positions.items():
            if len(positions) > 1:
                intervals = [positions[i] - positions[i-1] for i in range(1, len(positions))]
                self._interval_stats[num] = intervals
    
    def _hot_cold_prediction(self, prev_numbers, period_info):
        """热冷数字预测"""
        if not hasattr(self, '_hot_cold_stats'):
            self._build_hot_cold_stats()
        
        sorted_hot = sorted(self._hot_cold_stats.items(), key=lambda x: x[1], reverse=True)
        return [num for num, count in sorted_hot[:2]]
    
    def _build_hot_cold_stats(self):
        """构建热冷统计"""
        self._hot_cold_stats = defaultdict(int)
        recent_data = self.train_data.tail(50)
        
        for _, row in recent_data.iterrows():
            for j in range(1, 7):
                self._hot_cold_stats[row[f'数字{j}']] += 1
    
    def _frequency_analysis_prediction(self, prev_numbers, period_info):
        """频率分析预测"""
        if not hasattr(self, '_freq_stats'):
            self._build_frequency_stats()
        
        sorted_freq = sorted(self._freq_stats.items(), key=lambda x: x[1], reverse=True)
        return [num for num, freq in sorted_freq[:2]]
    
    def _build_frequency_stats(self):
        """构建频率统计"""
        self._freq_stats = defaultdict(int)
        
        for _, row in self.train_data.iterrows():
            for j in range(1, 7):
                self._freq_stats[row[f'数字{j}']] += 1
    
    def run_comprehensive_validation(self):
        """运行全面验证"""
        print(f"\n🔍 开始重新验证分析")
        print("=" * 60)
        
        # 设置随机种子确保可重现性
        np.random.seed(42)
        
        # 初始化结果存储
        for scheme_name in self.prediction_schemes.keys():
            self.results[scheme_name] = {
                'predictions': [],
                'hits': [],
                'total_periods': 0,
                'hit_periods': 0,
                'hit_rate': 0.0
            }
        
        # 逐期预测验证
        print(f"预测期数: {len(self.test_data)}期")
        print(f"{'期号':<6} {'实际开奖':<25} {'马尔可夫对比':<60}")
        print("-" * 95)
        
        for idx, test_row in self.test_data.iterrows():
            period = test_row['期号']
            actual_numbers = [test_row[f'数字{j}'] for j in range(1, 7)]
            
            # 获取前一期数字作为预测输入
            if idx == self.test_data.index[0]:
                # 第一期使用训练集最后一期
                prev_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                # 使用测试集中的前一期
                prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                prev_numbers = set([self.test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            period_info = {'year': test_row['年份'], 'period': period}
            
            # 对每个预测方案进行预测
            period_predictions = {}
            for scheme_name, prediction_func in self.prediction_schemes.items():
                try:
                    predicted = prediction_func(prev_numbers, period_info)
                    period_predictions[scheme_name] = predicted
                    
                    # 计算命中情况
                    predicted_set = set(predicted)
                    actual_set = set(actual_numbers)
                    hit_count = len(predicted_set & actual_set)
                    is_hit = hit_count >= 1
                    
                    # 记录结果
                    self.results[scheme_name]['predictions'].append(predicted)
                    self.results[scheme_name]['hits'].append(is_hit)
                    self.results[scheme_name]['total_periods'] += 1
                    if is_hit:
                        self.results[scheme_name]['hit_periods'] += 1
                        
                except Exception as e:
                    print(f"⚠️ {scheme_name}预测失败: {e}")
                    period_predictions[scheme_name] = [1, 2]
            
            # 显示前10期的马尔可夫方法对比
            if idx - self.test_data.index[0] < 10:
                markov_comparison = " | ".join([
                    f"{name}:{pred}" for name, pred in period_predictions.items() 
                    if '马尔可夫' in name
                ])
                print(f"{period:<6} {str(actual_numbers):<25} {markov_comparison}")
        
        if len(self.test_data) > 10:
            print(f"... (共{len(self.test_data)}期)")
        
        # 计算最终命中率
        for scheme_name in self.results:
            result = self.results[scheme_name]
            if result['total_periods'] > 0:
                result['hit_rate'] = result['hit_periods'] / result['total_periods']
        
        print(f"\n✅ 重新验证完成")
    
    def analyze_markov_differences(self):
        """深入分析马尔可夫方法差异"""
        print(f"\n🧠 马尔可夫方法深度分析")
        print("=" * 60)
        
        # 提取马尔可夫相关结果
        markov_results = {
            name: result for name, result in self.results.items() 
            if '马尔可夫' in name
        }
        
        print(f"📊 马尔可夫方法性能对比:")
        print(f"{'方法':<20} {'命中率':<10} {'命中期数':<12} {'vs29%理论'}")
        print("-" * 60)
        
        theoretical_29_rate = 0.292
        
        for method_name, result in markov_results.items():
            hit_rate = result['hit_rate']
            hit_periods = result['hit_periods']
            total_periods = result['total_periods']
            diff_from_29 = hit_rate - theoretical_29_rate
            
            print(f"{method_name:<20} {hit_rate:.3f}      {hit_periods}/{total_periods}      {diff_from_29:+.3f}")
        
        # 分析差异原因
        print(f"\n🔍 差异原因分析:")
        print("-" * 40)
        
        standard_rate = markov_results.get('标准马尔可夫', {}).get('hit_rate', 0)
        theoretical_rate = markov_results.get('29%理论马尔可夫', {}).get('hit_rate', 0)
        enhanced_rate = markov_results.get('增强马尔可夫', {}).get('hit_rate', 0)
        
        print(f"1. 标准马尔可夫 vs 29%理论:")
        print(f"   实际差异: {theoretical_rate - standard_rate:+.3f}")
        print(f"   可能原因: 平滑处理、参数调整、实现细节")
        
        print(f"\n2. 增强马尔可夫 vs 标准马尔可夫:")
        print(f"   实际差异: {enhanced_rate - standard_rate:+.3f}")
        print(f"   改进效果: 时间权重{'有效' if enhanced_rate > standard_rate else '无效'}")
        
        print(f"\n3. 与29.2%理论基线的总体差异:")
        for method_name, result in markov_results.items():
            diff = result['hit_rate'] - theoretical_29_rate
            print(f"   {method_name}: {diff:+.3f} ({diff*100:+.1f}个百分点)")
        
        # 数据集影响分析
        print(f"\n📈 数据集影响分析:")
        print("-" * 40)
        print(f"训练期间: 2023-2024年 ({len(self.train_data)}期)")
        print(f"测试期间: 2025年1-179期 ({len(self.test_data)}期)")
        print(f"数据特点: 更近期的训练数据，可能更符合当前趋势")
        print(f"理论预期: 应该比使用更早期数据的模型表现更好")
        
        actual_best_markov = max(markov_results.items(), key=lambda x: x[1]['hit_rate'])
        print(f"实际最佳: {actual_best_markov[0]} ({actual_best_markov[1]['hit_rate']:.3f})")
    
    def generate_comprehensive_report(self):
        """生成综合报告"""
        print(f"\n📊 重新验证综合报告")
        print("=" * 70)
        
        # 按命中率排序
        sorted_results = sorted(self.results.items(), key=lambda x: x[1]['hit_rate'], reverse=True)
        
        print(f"{'排名':<4} {'预测方案':<20} {'总期数':<8} {'命中期数':<8} {'命中率':<10} {'性能等级'}")
        print("-" * 75)
        
        for rank, (scheme_name, result) in enumerate(sorted_results, 1):
            hit_rate = result['hit_rate']
            total_periods = result['total_periods']
            hit_periods = result['hit_periods']
            
            # 性能等级评定
            if hit_rate >= 0.35:
                level = "⭐⭐⭐⭐⭐"
            elif hit_rate >= 0.3:
                level = "⭐⭐⭐⭐"
            elif hit_rate >= 0.25:
                level = "⭐⭐⭐"
            elif hit_rate >= 0.2:
                level = "⭐⭐"
            else:
                level = "⭐"
            
            medal = "🥇" if rank == 1 else "🥈" if rank == 2 else "🥉" if rank == 3 else "  "
            
            print(f"{medal:<4} {scheme_name:<20} {total_periods:<8} {hit_periods:<8} {hit_rate:.3f}      {level}")
        
        # 与之前结果对比
        print(f"\n📈 与之前验证结果对比:")
        print("-" * 50)
        
        # 之前的结果（2024训练，2025年1-185期测试）
        previous_results = {
            '奇偶平衡': 0.266,
            '间隔分析': 0.250,
            '马尔可夫基线': 0.234,
            '热冷数字': 0.234,
            '频率分析': 0.174
        }
        
        print(f"{'方案':<20} {'之前结果':<10} {'当前结果':<10} {'差异'}")
        print("-" * 50)
        
        for scheme_name, result in sorted_results:
            current_rate = result['hit_rate']
            # 映射方案名称
            prev_name = scheme_name
            if '马尔可夫' in scheme_name:
                prev_name = '马尔可夫基线'
            
            if prev_name in previous_results:
                prev_rate = previous_results[prev_name]
                diff = current_rate - prev_rate
                print(f"{scheme_name:<20} {prev_rate:.3f}      {current_rate:.3f}      {diff:+.3f}")
        
        # 统计分析
        print(f"\n📊 统计分析:")
        print("-" * 30)
        
        hit_rates = [result['hit_rate'] for result in self.results.values()]
        print(f"平均命中率: {np.mean(hit_rates):.3f}")
        print(f"最高命中率: {np.max(hit_rates):.3f}")
        print(f"最低命中率: {np.min(hit_rates):.3f}")
        print(f"标准差: {np.std(hit_rates):.3f}")
        
        # 基线对比
        random_baseline = 2/49 * 2
        print(f"\n📊 基线对比:")
        print("-" * 30)
        print(f"理论随机基线: {random_baseline:.3f} ({random_baseline*100:.1f}%)")
        print(f"29.2%理论基线: 0.292 (29.2%)")
        
        best_result = sorted_results[0]
        print(f"最佳方案: {best_result[0]} ({best_result[1]['hit_rate']:.3f})")
        print(f"vs随机基线: +{best_result[1]['hit_rate'] - random_baseline:.3f}")
        print(f"vs29.2%基线: {best_result[1]['hit_rate'] - 0.292:+.3f}")

def main():
    """主函数"""
    print("🔄 重新验证系统：2023-2024训练 vs 2025年1-179期测试")
    print("深入分析29%马尔可夫方法差异")
    print("=" * 80)
    
    validator = RevalidationSystem()
    
    # 1. 加载和准备数据
    if not validator.load_and_prepare_data():
        return
    
    # 2. 运行全面验证
    validator.run_comprehensive_validation()
    
    # 3. 分析马尔可夫方法差异
    validator.analyze_markov_differences()
    
    # 4. 生成综合报告
    validator.generate_comprehensive_report()
    
    print(f"\n🎉 重新验证分析完成")

if __name__ == "__main__":
    main()
