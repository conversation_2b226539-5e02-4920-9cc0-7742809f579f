#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态权重自主预测系统演示脚本
展示系统的主要功能和使用方法
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime
from 动态权重自主预测系统 import DynamicWeightPredictionSystem

def demo_system():
    """演示系统功能"""
    print("🎯 动态权重自主预测系统演示")
    print("=" * 60)
    
    # 创建系统实例
    system = DynamicWeightPredictionSystem()
    
    # 初始化系统
    print("\n1️⃣ 系统初始化演示")
    print("-" * 30)
    if system.initialize_system():
        print("✅ 系统初始化成功")
    else:
        print("❌ 系统初始化失败")
        return
    
    # 显示系统状态
    print("\n2️⃣ 系统状态演示")
    print("-" * 30)
    system.show_system_status()
    
    # 模拟输入数据并预测
    print("\n3️⃣ 预测功能演示")
    print("-" * 30)
    
    # 模拟当期数据
    demo_data = {
        'year': 2025,
        'period': 205,
        'numbers': [5, 12, 23, 31, 40, 45]
    }
    
    print(f"模拟输入数据: {demo_data['year']}年{demo_data['period']}期 {demo_data['numbers']}")
    
    # 进行预测
    prediction_result = system.make_prediction_and_save(demo_data)
    
    # 显示预测历史
    print("\n4️⃣ 预测历史演示")
    print("-" * 30)
    system.show_recent_predictions()
    
    # 模拟验证预测
    print("\n5️⃣ 预测验证演示")
    print("-" * 30)
    
    # 模拟实际开奖数据
    actual_numbers = [8, 15, 22, 32, 41, 47]
    print(f"模拟实际开奖: {demo_data['year']}年{demo_data['period'] + 1}期 {actual_numbers}")
    
    # 验证预测
    system.verify_prediction(demo_data['year'], demo_data['period'] + 1, actual_numbers)
    
    # 再次显示预测历史（包含验证结果）
    print("\n6️⃣ 验证后的预测历史")
    print("-" * 30)
    system.show_recent_predictions()
    
    print("\n🎉 演示完成！")
    print("您现在可以运行 'python 动态权重自主预测系统.py' 开始使用系统")

def create_sample_data():
    """创建示例数据（如果主数据文件不存在）"""
    data_file = "data/processed/lottery_data_clean_no_special.csv"
    
    if os.path.exists(data_file):
        print(f"✅ 主数据文件已存在: {data_file}")
        return True
    
    print(f"⚠️ 主数据文件不存在，创建示例数据...")
    
    # 创建目录
    os.makedirs(os.path.dirname(data_file), exist_ok=True)
    
    # 生成示例数据
    sample_data = []
    
    # 生成2024年数据（200期）
    for period in range(1, 201):
        numbers = sorted(np.random.choice(range(1, 50), 6, replace=False))
        sample_data.append({
            '年份': 2024,
            '期号': period,
            '数字1': numbers[0],
            '数字2': numbers[1],
            '数字3': numbers[2],
            '数字4': numbers[3],
            '数字5': numbers[4],
            '数字6': numbers[5]
        })
    
    # 生成2025年数据（204期）
    for period in range(1, 205):
        numbers = sorted(np.random.choice(range(1, 50), 6, replace=False))
        sample_data.append({
            '年份': 2025,
            '期号': period,
            '数字1': numbers[0],
            '数字2': numbers[1],
            '数字3': numbers[2],
            '数字4': numbers[3],
            '数字5': numbers[4],
            '数字6': numbers[5]
        })
    
    # 保存到CSV文件
    df = pd.DataFrame(sample_data)
    df.to_csv(data_file, index=False, encoding='utf-8')
    
    print(f"✅ 示例数据已创建: {len(sample_data)} 期数据")
    return True

def show_usage_guide():
    """显示使用指南"""
    print("📖 动态权重自主预测系统使用指南")
    print("=" * 60)
    
    print("\n🚀 快速开始:")
    print("1. 运行系统: python 动态权重自主预测系统.py")
    print("2. 选择功能: 根据菜单选择相应功能")
    print("3. 输入数据: 按提示输入开奖数据")
    print("4. 查看结果: 系统自动生成预测并保存")
    
    print("\n📋 主要功能:")
    print("1️⃣ 输入当期数据并预测下期")
    print("   - 输入年份、期号和6个开奖数字")
    print("   - 系统自动更新权重并生成预测")
    print("   - 预测结果保存到CSV文件")
    
    print("\n2️⃣ 查看预测历史")
    print("   - 显示最近10条预测记录")
    print("   - 包含预测时间、数据、结果等信息")
    print("   - 显示预测统计和命中率")
    
    print("\n3️⃣ 验证预测结果")
    print("   - 输入实际开奖数据验证预测")
    print("   - 自动计算命中情况")
    print("   - 更新预测记录状态")
    
    print("\n4️⃣ 查看系统状态")
    print("   - 显示系统初始化状态")
    print("   - 显示数据文件信息")
    print("   - 显示权重和预测统计")
    
    print("\n💡 使用技巧:")
    print("• 支持多种输入格式: '5 12 23 31 40 45' 或 '5,12,23,31,40,45'")
    print("• 系统每5期自动更新一次权重")
    print("• 预测置信度越高，预测越可靠")
    print("• 及时验证预测结果以获得准确的统计信息")
    
    print("\n📁 输出文件:")
    print("• dynamic_weight_predictions.csv - 预测结果文件")
    print("• dynamic_weight_state.json - 系统状态文件")
    
    print("\n⚠️ 注意事项:")
    print("• 确保主数据文件存在且格式正确")
    print("• 输入数据必须是6个不重复的1-49数字")
    print("• 预测结果仅供参考，不保证准确性")

def main():
    """主函数"""
    print("🎯 动态权重自主预测系统")
    print("基于严格验证的动态权重调整算法")
    print("=" * 70)
    
    while True:
        print("\n📋 选择操作:")
        print("1. 运行系统演示")
        print("2. 查看使用指南")
        print("3. 创建示例数据")
        print("4. 启动预测系统")
        print("5. 退出")
        
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == '1':
            # 确保数据文件存在
            if not create_sample_data():
                continue
            demo_system()
        
        elif choice == '2':
            show_usage_guide()
        
        elif choice == '3':
            create_sample_data()
        
        elif choice == '4':
            print("\n🚀 启动预测系统...")
            print("请运行: python 动态权重自主预测系统.py")
            break
        
        elif choice == '5':
            print("👋 再见！")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
