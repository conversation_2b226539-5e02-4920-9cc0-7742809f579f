#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批判性预测分析：为何200期后还预测2,3？
Critical analysis: Why still predicting 2,3 after 200 periods?
"""

import pandas as pd
import numpy as np
from collections import Counter
from datetime import datetime
import calendar

def analyze_prediction_patterns():
    """分析预测模式的固化问题"""
    print("🔍 预测模式固化分析")
    print("="*60)
    
    try:
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        
        # 统计预测数字频率
        all_predictions = []
        for _, row in df.iterrows():
            if pd.notna(row['预测数字1']) and pd.notna(row['预测数字2']):
                try:
                    num1 = int(float(row['预测数字1']))
                    num2 = int(float(row['预测数字2']))
                    all_predictions.extend([num1, num2])
                except (ValueError, TypeError):
                    continue
        
        pred_counter = Counter(all_predictions)
        total_predictions = len(all_predictions)
        
        print(f"预测数字频率分析 (总预测次数: {total_predictions}):")
        print(f"{'数字':<6} {'次数':<6} {'频率':<8} {'期望频率':<10} {'偏差':<8}")
        print("-" * 50)
        
        # 理论上每个数字的期望频率
        expected_freq = total_predictions / 49  # 假设1-49均匀分布
        
        # 显示前15个最频繁的数字
        for num, count in pred_counter.most_common(15):
            freq_pct = (count / total_predictions) * 100
            expected_pct = (expected_freq / total_predictions) * 100
            bias = freq_pct - expected_pct
            
            print(f"{num:<6} {count:<6} {freq_pct:<7.1f}% {expected_pct:<9.1f}% {bias:<+7.1f}%")
        
        # 分析2和3的特殊情况
        print(f"\n🚨 数字2和3的特殊分析:")
        num_2_count = pred_counter.get(2, 0)
        num_3_count = pred_counter.get(3, 0)
        
        print(f"   数字2预测次数: {num_2_count} ({num_2_count/total_predictions*100:.1f}%)")
        print(f"   数字3预测次数: {num_3_count} ({num_3_count/total_predictions*100:.1f}%)")
        print(f"   2和3合计占比: {(num_2_count+num_3_count)/total_predictions*100:.1f}%")
        
        # 分析最近的预测趋势
        print(f"\n📈 最近50期预测趋势:")
        recent_predictions = []
        recent_data = df.tail(50)
        
        for _, row in recent_data.iterrows():
            if pd.notna(row['预测数字1']) and pd.notna(row['预测数字2']):
                try:
                    num1 = int(float(row['预测数字1']))
                    num2 = int(float(row['预测数字2']))
                    recent_predictions.extend([num1, num2])
                except (ValueError, TypeError):
                    continue
        
        recent_counter = Counter(recent_predictions)
        print("   最近50期最频繁预测:")
        for num, count in recent_counter.most_common(10):
            print(f"     数字{num}: {count}次")
        
        return pred_counter, recent_counter
        
    except Exception as e:
        print(f"❌ 预测模式分析失败: {e}")
        return None, None

def analyze_monthly_hit_rates():
    """分析每月命中率"""
    print(f"\n📅 每月命中率分析")
    print("="*50)
    
    try:
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        
        # 过滤有效数据
        valid_data = df[
            df['是否命中'].notna() & 
            (df['是否命中'] != '') &
            df['预测时间'].notna()
        ].copy()
        
        # 解析日期
        valid_data['日期'] = pd.to_datetime(valid_data['预测时间'], format='%Y-%m-%d,%H:%M:%S', errors='coerce')
        valid_data = valid_data[valid_data['日期'].notna()]
        
        # 提取年月
        valid_data['年月'] = valid_data['日期'].dt.to_period('M')
        
        print(f"有效数据: {len(valid_data)}条")
        print(f"时间范围: {valid_data['日期'].min()} - {valid_data['日期'].max()}")
        
        # 按月统计命中率
        monthly_stats = []
        
        for period in valid_data['年月'].unique():
            month_data = valid_data[valid_data['年月'] == period]
            
            total_predictions = len(month_data)
            hit_count = len(month_data[month_data['是否命中'] == '是'])
            hit_rate = (hit_count / total_predictions * 100) if total_predictions > 0 else 0
            
            monthly_stats.append({
                'year_month': str(period),
                'year': period.year,
                'month': period.month,
                'total': total_predictions,
                'hits': hit_count,
                'hit_rate': hit_rate
            })
        
        # 排序并显示
        monthly_stats.sort(key=lambda x: (x['year'], x['month']))
        
        print(f"\n每月命中率详情:")
        print(f"{'年月':<10} {'预测数':<8} {'命中数':<8} {'命中率':<8} {'月份特征':<15}")
        print("-" * 60)
        
        total_all = 0
        hits_all = 0
        
        for stat in monthly_stats:
            month_name = calendar.month_name[stat['month']][:3]
            year_month = f"{stat['year']}-{stat['month']:02d}"
            
            print(f"{year_month:<10} {stat['total']:<8} {stat['hits']:<8} {stat['hit_rate']:<7.1f}% {month_name:<15}")
            
            total_all += stat['total']
            hits_all += stat['hits']
        
        # 总体统计
        overall_hit_rate = (hits_all / total_all * 100) if total_all > 0 else 0
        print(f"{'总计':<10} {total_all:<8} {hits_all:<8} {overall_hit_rate:<7.1f}% {'全期间':<15}")
        
        # 分析月度趋势
        print(f"\n📊 月度趋势分析:")
        
        if len(monthly_stats) > 1:
            # 计算趋势
            hit_rates = [stat['hit_rate'] for stat in monthly_stats]
            
            # 最好和最差月份
            best_month = max(monthly_stats, key=lambda x: x['hit_rate'])
            worst_month = min(monthly_stats, key=lambda x: x['hit_rate'])
            
            print(f"   最佳月份: {best_month['year']}-{best_month['month']:02d} ({best_month['hit_rate']:.1f}%)")
            print(f"   最差月份: {worst_month['year']}-{worst_month['month']:02d} ({worst_month['hit_rate']:.1f}%)")
            
            # 计算标准差
            hit_rate_std = np.std(hit_rates)
            print(f"   命中率标准差: {hit_rate_std:.1f}%")
            
            if hit_rate_std > 10:
                print("   ⚠️ 月度命中率波动较大")
            else:
                print("   ✅ 月度命中率相对稳定")
        
        return monthly_stats
        
    except Exception as e:
        print(f"❌ 月度分析失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def analyze_why_still_predicting_2_3():
    """分析为什么还在预测2和3"""
    print(f"\n🤔 为什么200期后还预测2,3？深度分析")
    print("="*60)
    
    print("可能的原因分析:")
    
    print(f"\n1. 🔄 算法固化问题:")
    print("   - 马尔可夫链模型可能过度依赖历史转移概率")
    print("   - 如果2,3在历史数据中频繁出现，模型会持续预测")
    print("   - 缺乏足够的随机性和探索机制")
    
    print(f"\n2. 📊 特征工程偏差:")
    print("   - 小数字(1-10)被赋予了过高权重")
    print("   - 评分算法中的'小数字特征'加成1.08倍")
    print("   - 导致系统偏向预测小数字")
    
    print(f"\n3. 🎯 置信度计算问题:")
    print("   - 置信度计算可能偏向某些数字组合")
    print("   - [2,3]组合可能在算法中获得较高置信度")
    print("   - 缺乏多样性约束机制")
    
    print(f"\n4. 🔍 历史数据偏差:")
    print("   - 如果训练数据中2,3确实高频出现")
    print("   - 模型会'学习'到这种模式")
    print("   - 但实际开奖可能并非如此")
    
    print(f"\n5. ⚙️ 系统设计缺陷:")
    print("   - 缺乏有效的多样性机制")
    print("   - 没有'禁止重复'或'强制探索'机制")
    print("   - 评分系统可能强化了某些偏好")

def test_prediction_diversity():
    """测试预测多样性"""
    print(f"\n🎲 预测多样性测试")
    print("="*40)
    
    try:
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        
        # 统计不同数字组合
        combinations = set()
        valid_predictions = 0
        
        for _, row in df.iterrows():
            if pd.notna(row['预测数字1']) and pd.notna(row['预测数字2']):
                try:
                    num1 = int(float(row['预测数字1']))
                    num2 = int(float(row['预测数字2']))
                    # 标准化组合（小数在前）
                    combo = tuple(sorted([num1, num2]))
                    combinations.add(combo)
                    valid_predictions += 1
                except (ValueError, TypeError):
                    continue
        
        print(f"预测多样性统计:")
        print(f"   总预测次数: {valid_predictions}")
        print(f"   不同组合数: {len(combinations)}")
        print(f"   多样性比例: {len(combinations)/valid_predictions*100:.1f}%")
        
        # 理论最大组合数 (C(49,2) = 1176)
        max_combinations = 49 * 48 // 2
        diversity_potential = len(combinations) / max_combinations * 100
        
        print(f"   多样性潜力: {diversity_potential:.1f}% (相对于理论最大值)")
        
        # 分析最频繁的组合
        combo_counter = Counter()
        for _, row in df.iterrows():
            if pd.notna(row['预测数字1']) and pd.notna(row['预测数字2']):
                try:
                    num1 = int(float(row['预测数字1']))
                    num2 = int(float(row['预测数字2']))
                    combo = tuple(sorted([num1, num2]))
                    combo_counter[combo] += 1
                except (ValueError, TypeError):
                    continue
        
        print(f"\n最频繁的预测组合:")
        for combo, count in combo_counter.most_common(10):
            percentage = (count / valid_predictions) * 100
            print(f"   {combo}: {count}次 ({percentage:.1f}%)")
        
        # 检查[2,3]组合的频率
        combo_2_3 = tuple(sorted([2, 3]))
        count_2_3 = combo_counter.get(combo_2_3, 0)
        if count_2_3 > 0:
            print(f"\n🚨 [2,3]组合分析:")
            print(f"   预测次数: {count_2_3}")
            print(f"   占比: {count_2_3/valid_predictions*100:.1f}%")
            print(f"   这确实是一个异常高频的组合！")
        
        return len(combinations), valid_predictions, combo_counter
        
    except Exception as e:
        print(f"❌ 多样性测试失败: {e}")
        return 0, 0, Counter()

def critical_system_evaluation():
    """批判性系统评估"""
    print(f"\n⚖️ 批判性系统评估")
    print("="*50)
    
    print("系统存在的根本问题:")
    
    print(f"\n1. 🔒 算法僵化:")
    print("   - 200期预测后仍然偏向相同数字")
    print("   - 缺乏学习和适应机制")
    print("   - 没有从失败预测中学习")
    
    print(f"\n2. 📊 统计偏差:")
    print("   - 过度依赖历史频率")
    print("   - 忽略了真实随机性")
    print("   - 可能存在数据选择偏差")
    
    print(f"\n3. 🎯 目标错位:")
    print("   - 追求高评分而非高命中率")
    print("   - 评分算法与实际表现脱节")
    print("   - 缺乏真实的反馈机制")
    
    print(f"\n4. 🔄 缺乏进化:")
    print("   - 系统不会从错误中学习")
    print("   - 没有动态调整机制")
    print("   - 参数固化，缺乏适应性")
    
    print(f"\n5. 🎲 随机性理解不足:")
    print("   - 试图在纯随机中找规律")
    print("   - 过度拟合历史模式")
    print("   - 忽略了彩票的本质特征")

def generate_improvement_recommendations():
    """生成改进建议"""
    print(f"\n💡 根本性改进建议")
    print("="*40)
    
    print("短期改进 (立即可行):")
    print("   1. 🎲 增加强制随机性:")
    print("      - 每10期强制选择不同数字区间")
    print("      - 避免连续预测相同组合")
    print("      - 引入'禁用列表'机制")
    
    print("   2. 📊 重新校准权重:")
    print("      - 降低小数字的特殊权重")
    print("      - 平衡各数字区间的选择概率")
    print("      - 定期重新评估特征重要性")
    
    print("   3. 🔄 引入反馈机制:")
    print("      - 根据近期命中率调整参数")
    print("      - 惩罚连续失败的预测模式")
    print("      - 奖励多样化的预测策略")
    
    print(f"\n中期改进 (需要重构):")
    print("   1. 🧠 多模型集成:")
    print("      - 结合多种不同的预测算法")
    print("      - 动态权重分配")
    print("      - 模型竞争和淘汰机制")
    
    print("   2. 📈 在线学习:")
    print("      - 实时更新模型参数")
    print("      - 滑动窗口训练")
    print("      - 自适应学习率")
    
    print(f"\n长期改进 (根本重思):")
    print("   1. 🤔 重新定义目标:")
    print("      - 从'预测准确'转向'决策支持'")
    print("      - 提供多个候选方案")
    print("      - 量化不确定性")
    
    print("   2. 🎯 接受随机性:")
    print("      - 承认预测的根本局限")
    print("      - 专注于风险管理")
    print("      - 提供概率分布而非点预测")

def main():
    """主函数"""
    print("🔍 批判性预测分析：为何200期后还预测2,3？")
    print("="*80)
    
    # 1. 分析预测模式固化
    pred_counter, recent_counter = analyze_prediction_patterns()
    
    # 2. 分析每月命中率
    monthly_stats = analyze_monthly_hit_rates()
    
    # 3. 分析为什么还预测2,3
    analyze_why_still_predicting_2_3()
    
    # 4. 测试预测多样性
    unique_combos, total_preds, combo_counter = test_prediction_diversity()
    
    # 5. 批判性系统评估
    critical_system_evaluation()
    
    # 6. 改进建议
    generate_improvement_recommendations()
    
    print(f"\n🎯 核心结论")
    print("="*60)
    print("经过200期预测还在预测[2,3]暴露了系统的根本问题:")
    print("   1. ❌ 算法过度依赖历史模式，缺乏适应性")
    print("   2. ❌ 特征工程存在偏差，偏向小数字")
    print("   3. ❌ 缺乏有效的多样性约束机制")
    print("   4. ❌ 没有从预测失败中学习的能力")
    print("   5. ❌ 评分系统与实际命中率脱节")
    
    print(f"\n这不是一个技术bug，而是系统设计的根本缺陷！")
    print(f"需要从哲学层面重新思考预测随机事件的意义和方法。")

if __name__ == "__main__":
    main()
