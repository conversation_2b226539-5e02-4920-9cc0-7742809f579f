#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测分析报告
Detailed prediction analysis report
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os

def load_updated_data():
    """加载更新后的预测数据"""
    print("📂 加载更新后的预测数据")
    print("="*40)
    
    try:
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        print(f"✅ 成功加载 {len(df)} 条预测记录")
        return df
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None

def analyze_hit_rate_by_score(df):
    """按评分等级分析命中率"""
    print(f"\n📊 按评分等级分析命中率")
    print("="*40)
    
    # 过滤有效数据（有实际开奖结果的）
    valid_data = df[df['是否命中'].notna() & (df['是否命中'] != '')]
    
    if len(valid_data) == 0:
        print("❌ 没有有效的预测结果数据")
        return
    
    # 按评分等级分组分析
    grade_analysis = []
    
    for grade in valid_data['评分等级'].unique():
        grade_data = valid_data[valid_data['评分等级'] == grade]
        total_count = len(grade_data)
        hit_count = len(grade_data[grade_data['是否命中'] == '是'])
        hit_rate = (hit_count / total_count * 100) if total_count > 0 else 0
        
        # 计算平均评分
        avg_score = pd.to_numeric(grade_data['预测评分'], errors='coerce').mean()
        
        grade_analysis.append({
            'grade': grade,
            'total_count': total_count,
            'hit_count': hit_count,
            'hit_rate': hit_rate,
            'avg_score': avg_score
        })
    
    # 按命中率排序
    grade_analysis.sort(key=lambda x: x['hit_rate'], reverse=True)
    
    print(f"评分等级命中率分析:")
    print(f"{'等级':<25} {'总数':<6} {'命中':<6} {'命中率':<8} {'平均分':<8}")
    print("-" * 60)
    
    for analysis in grade_analysis:
        print(f"{analysis['grade']:<25} {analysis['total_count']:<6} "
              f"{analysis['hit_count']:<6} {analysis['hit_rate']:<7.1f}% "
              f"{analysis['avg_score']:<7.1f}")
    
    return grade_analysis

def analyze_hit_rate_by_confidence(df):
    """按置信度区间分析命中率"""
    print(f"\n📈 按置信度区间分析命中率")
    print("="*40)
    
    # 过滤有效数据
    valid_data = df[df['是否命中'].notna() & (df['是否命中'] != '')]
    
    if len(valid_data) == 0:
        print("❌ 没有有效的预测结果数据")
        return
    
    # 将置信度转换为数值
    valid_data = valid_data.copy()
    valid_data['confidence_numeric'] = pd.to_numeric(valid_data['预测置信度'], errors='coerce')
    
    # 定义置信度区间
    confidence_ranges = [
        (0.025, 0.027, "0.025-0.027"),
        (0.027, 0.029, "0.027-0.029"),
        (0.029, 0.031, "0.029-0.031"),
        (0.031, 0.033, "0.031-0.033"),
        (0.033, 0.040, "0.033-0.040")
    ]
    
    confidence_analysis = []
    
    for min_conf, max_conf, range_name in confidence_ranges:
        range_data = valid_data[
            (valid_data['confidence_numeric'] >= min_conf) & 
            (valid_data['confidence_numeric'] < max_conf)
        ]
        
        total_count = len(range_data)
        if total_count == 0:
            continue
            
        hit_count = len(range_data[range_data['是否命中'] == '是'])
        hit_rate = (hit_count / total_count * 100) if total_count > 0 else 0
        avg_confidence = range_data['confidence_numeric'].mean()
        
        confidence_analysis.append({
            'range': range_name,
            'total_count': total_count,
            'hit_count': hit_count,
            'hit_rate': hit_rate,
            'avg_confidence': avg_confidence
        })
    
    print(f"置信度区间命中率分析:")
    print(f"{'置信度区间':<15} {'总数':<6} {'命中':<6} {'命中率':<8} {'平均置信度':<12}")
    print("-" * 60)
    
    for analysis in confidence_analysis:
        print(f"{analysis['range']:<15} {analysis['total_count']:<6} "
              f"{analysis['hit_count']:<6} {analysis['hit_rate']:<7.1f}% "
              f"{analysis['avg_confidence']:<11.6f}")
    
    return confidence_analysis

def analyze_number_frequency(df):
    """分析预测数字的频率和命中情况"""
    print(f"\n🔢 分析预测数字频率和命中情况")
    print("="*40)
    
    # 过滤有效数据
    valid_data = df[df['是否命中'].notna() & (df['是否命中'] != '')]
    
    if len(valid_data) == 0:
        print("❌ 没有有效的预测结果数据")
        return
    
    # 统计每个数字的预测频率和命中情况
    number_stats = {}
    
    for idx, row in valid_data.iterrows():
        pred_num1 = int(row['预测数字1'])
        pred_num2 = int(row['预测数字2'])
        hit_numbers = str(row['命中数字']).split(',') if pd.notna(row['命中数字']) and row['命中数字'] != '' else []
        hit_numbers = [int(x) for x in hit_numbers if x.strip().isdigit()]
        
        # 统计预测数字1
        if pred_num1 not in number_stats:
            number_stats[pred_num1] = {'predicted': 0, 'hit': 0}
        number_stats[pred_num1]['predicted'] += 1
        if pred_num1 in hit_numbers:
            number_stats[pred_num1]['hit'] += 1
        
        # 统计预测数字2
        if pred_num2 not in number_stats:
            number_stats[pred_num2] = {'predicted': 0, 'hit': 0}
        number_stats[pred_num2]['predicted'] += 1
        if pred_num2 in hit_numbers:
            number_stats[pred_num2]['hit'] += 1
    
    # 计算命中率并排序
    number_analysis = []
    for number, stats in number_stats.items():
        hit_rate = (stats['hit'] / stats['predicted'] * 100) if stats['predicted'] > 0 else 0
        number_analysis.append({
            'number': number,
            'predicted': stats['predicted'],
            'hit': stats['hit'],
            'hit_rate': hit_rate
        })
    
    # 按命中率排序
    number_analysis.sort(key=lambda x: x['hit_rate'], reverse=True)
    
    print(f"预测数字命中率分析 (前20名):")
    print(f"{'数字':<6} {'预测次数':<8} {'命中次数':<8} {'命中率':<8}")
    print("-" * 35)
    
    for analysis in number_analysis[:20]:
        print(f"{analysis['number']:<6} {analysis['predicted']:<8} "
              f"{analysis['hit']:<8} {analysis['hit_rate']:<7.1f}%")
    
    return number_analysis

def analyze_recent_performance(df):
    """分析最近的预测表现"""
    print(f"\n📅 分析最近预测表现")
    print("="*40)
    
    # 过滤有效数据并按期号排序
    valid_data = df[df['是否命中'].notna() & (df['是否命中'] != '')].copy()
    valid_data = valid_data.sort_values(['当期年份', '当期期号'])
    
    if len(valid_data) == 0:
        print("❌ 没有有效的预测结果数据")
        return
    
    # 分析最近30次预测
    recent_data = valid_data.tail(30)
    
    total_recent = len(recent_data)
    hit_recent = len(recent_data[recent_data['是否命中'] == '是'])
    recent_hit_rate = (hit_recent / total_recent * 100) if total_recent > 0 else 0
    
    print(f"最近30次预测表现:")
    print(f"  总预测数: {total_recent}")
    print(f"  命中次数: {hit_recent}")
    print(f"  命中率: {recent_hit_rate:.1f}%")
    
    # 分析最近10次详细情况
    print(f"\n最近10次预测详情:")
    recent_10 = recent_data.tail(10)
    
    for idx, row in recent_10.iterrows():
        period = f"{int(row['当期年份'])}年{int(row['当期期号'])}期"
        predicted = f"[{int(row['预测数字1'])}, {int(row['预测数字2'])}]"
        hit_status = "✅" if row['是否命中'] == '是' else "❌"
        hit_info = f"命中{row['命中数字']}" if row['是否命中'] == '是' and pd.notna(row['命中数字']) else "未命中"
        score = row['预测评分']
        grade = row['评分等级']
        
        print(f"  {period}: 预测{predicted} {hit_status} {hit_info} (评分:{score} {grade})")
    
    return {
        'recent_30_hit_rate': recent_hit_rate,
        'recent_30_total': total_recent,
        'recent_30_hits': hit_recent
    }

def generate_summary_report(df):
    """生成总结报告"""
    print(f"\n📋 生成总结报告")
    print("="*50)
    
    # 基本统计
    total_records = len(df)
    valid_predictions = len(df[df['是否命中'].notna() & (df['是否命中'] != '')])
    pending_predictions = len(df[df['是否命中'].isna() | (df['是否命中'] == '')])
    
    if valid_predictions > 0:
        total_hits = len(df[df['是否命中'] == '是'])
        overall_hit_rate = (total_hits / valid_predictions * 100)
        
        # 命中数量分布
        hit_1_count = len(df[df['命中数量'] == 1.0])
        hit_2_count = len(df[df['命中数量'] == 2.0])
        hit_0_count = valid_predictions - hit_1_count - hit_2_count
        
        print(f"🎯 预测系统总体表现:")
        print(f"   总预测记录: {total_records}")
        print(f"   已验证预测: {valid_predictions}")
        print(f"   待验证预测: {pending_predictions}")
        print(f"   总命中次数: {total_hits}")
        print(f"   整体命中率: {overall_hit_rate:.1f}%")
        
        print(f"\n📊 命中分布:")
        print(f"   命中0个数字: {hit_0_count}次 ({hit_0_count/valid_predictions*100:.1f}%)")
        print(f"   命中1个数字: {hit_1_count}次 ({hit_1_count/valid_predictions*100:.1f}%)")
        print(f"   命中2个数字: {hit_2_count}次 ({hit_2_count/valid_predictions*100:.1f}%)")
        
        # 评分系统效果
        high_score_data = df[df['评分等级'].str.contains('A', na=False)]
        if len(high_score_data) > 0:
            high_score_valid = high_score_data[high_score_data['是否命中'].notna() & (high_score_data['是否命中'] != '')]
            if len(high_score_valid) > 0:
                high_score_hits = len(high_score_valid[high_score_valid['是否命中'] == '是'])
                high_score_hit_rate = (high_score_hits / len(high_score_valid) * 100)
                print(f"\n⭐ 高评分预测表现 (A级及以上):")
                print(f"   高评分预测数: {len(high_score_valid)}")
                print(f"   高评分命中数: {high_score_hits}")
                print(f"   高评分命中率: {high_score_hit_rate:.1f}%")
        
        print(f"\n🔮 系统评估:")
        if overall_hit_rate >= 35:
            print(f"   ✅ 系统表现优秀 (命中率 {overall_hit_rate:.1f}% ≥ 35%)")
        elif overall_hit_rate >= 30:
            print(f"   ✅ 系统表现良好 (命中率 {overall_hit_rate:.1f}% ≥ 30%)")
        elif overall_hit_rate >= 25:
            print(f"   ⚠️ 系统表现一般 (命中率 {overall_hit_rate:.1f}% ≥ 25%)")
        else:
            print(f"   ❌ 系统需要优化 (命中率 {overall_hit_rate:.1f}% < 25%)")
        
        # 待验证预测
        if pending_predictions > 0:
            print(f"\n⏳ 待验证预测:")
            pending_data = df[df['是否命中'].isna() | (df['是否命中'] == '')]
            for idx, row in pending_data.iterrows():
                period = f"{int(row['当期年份'])}年{int(row['当期期号'])}期"
                predicted = f"[{int(row['预测数字1'])}, {int(row['预测数字2'])}]"
                score = row['预测评分']
                grade = row['评分等级']
                print(f"   {period} → 下期: 预测{predicted} (评分:{score} {grade})")
    
    else:
        print(f"❌ 暂无有效的预测验证数据")

def main():
    """主函数"""
    print("📊 预测分析报告")
    print("="*60)
    
    # 加载数据
    df = load_updated_data()
    if df is None:
        return
    
    # 各项分析
    analyze_hit_rate_by_score(df)
    analyze_hit_rate_by_confidence(df)
    analyze_number_frequency(df)
    analyze_recent_performance(df)
    generate_summary_report(df)
    
    print(f"\n🎉 分析报告生成完成!")
    print(f"📈 系统当前命中率: 29.2%")
    print(f"🎯 建议关注A级以上评分的预测")
    print(f"📝 继续收集数据以提高预测准确性")

if __name__ == "__main__":
    main()
