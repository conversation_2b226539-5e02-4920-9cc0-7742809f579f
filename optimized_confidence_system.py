#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版动态置信度调整系统
Optimized Dynamic Confidence Adjustment System

基于回测验证结果的改进建议进行优化

作者: AI Assistant
创建时间: 2025-07-15
版本: 2.1 Optimized
"""

import pandas as pd
import numpy as np
from datetime import datetime
from sklearn.isotonic import IsotonicRegression
from sklearn.linear_model import LogisticRegression
import json
import warnings
warnings.filterwarnings('ignore')

class OptimizedConfidenceAdjuster:
    """优化版动态置信度调整器"""
    
    def __init__(self, config=None):
        """初始化优化版调整器"""
        self.config = config or self._get_optimized_config()
        self.historical_performance = []
        self.calibration_model = None
        self.is_calibrated = False
        
        print("🔧 优化版动态置信度调整器初始化")
        print("="*50)
        print(f"  置信度范围: {self.config['confidence_bounds']['min_confidence']:.1f} - {self.config['confidence_bounds']['max_confidence']:.1f}")
        print(f"  调整因子范围: {self.config['adjustment_bounds']['min_factor']:.1f} - {self.config['adjustment_bounds']['max_factor']:.1f}")
        print(f"  校准方法: {self.config['calibration_method']}")
    
    def _get_optimized_config(self):
        """获取优化配置"""
        return {
            'confidence_bounds': {
                'min_confidence': 0.1,    # 从0.01提升到0.1
                'max_confidence': 0.8,    # 从0.95降低到0.8
                'target_range': (0.2, 0.6)  # 更合理的目标范围
            },
            'adjustment_bounds': {
                'min_factor': 0.3,        # 允许更大的下调
                'max_factor': 5.0         # 允许更大的上调
            },
            'calibration_method': 'isotonic',  # isotonic, platt, hybrid
            'calibration_params': {
                'min_samples': 20,        # 最小校准样本数
                'update_frequency': 10,   # 每10期更新一次校准
                'smoothing_factor': 0.1   # 平滑因子
            },
            'adjustment_factors': {
                'accuracy_weight': 0.4,   # 准确率权重
                'calibration_weight': 0.3, # 校准质量权重
                'stability_weight': 0.2,  # 稳定性权重
                'trend_weight': 0.1       # 趋势权重
            },
            'confidence_intervals': [
                (0.1, 0.2), (0.2, 0.3), (0.3, 0.4), 
                (0.4, 0.5), (0.5, 0.6), (0.6, 0.7), (0.7, 0.8)
            ]
        }
    
    def update_historical_performance(self, prediction_result):
        """更新历史性能数据"""
        self.historical_performance.append({
            'period': prediction_result.get('period'),
            'original_confidence': prediction_result.get('original_confidence'),
            'adjusted_confidence': prediction_result.get('adjusted_confidence'),
            'is_hit': prediction_result.get('is_hit'),
            'hit_count': prediction_result.get('hit_count', 0),
            'timestamp': datetime.now()
        })
        
        # 保持历史数据在合理范围内
        if len(self.historical_performance) > 200:
            self.historical_performance = self.historical_performance[-200:]
        
        # 定期更新校准模型
        if (len(self.historical_performance) % 
            self.config['calibration_params']['update_frequency'] == 0):
            self._update_calibration_model()
    
    def _update_calibration_model(self):
        """更新校准模型"""
        if len(self.historical_performance) < self.config['calibration_params']['min_samples']:
            return
        
        # 准备校准数据
        confidences = [p['adjusted_confidence'] for p in self.historical_performance[-50:]]
        hit_flags = [1 if p['is_hit'] else 0 for p in self.historical_performance[-50:]]
        
        try:
            if self.config['calibration_method'] == 'isotonic':
                self.calibration_model = IsotonicRegression(out_of_bounds='clip')
                self.calibration_model.fit(confidences, hit_flags)
            elif self.config['calibration_method'] == 'platt':
                # Platt Scaling (Logistic Regression)
                confidences_reshaped = np.array(confidences).reshape(-1, 1)
                self.calibration_model = LogisticRegression()
                self.calibration_model.fit(confidences_reshaped, hit_flags)
            
            self.is_calibrated = True
            print(f"📊 校准模型已更新 (样本数: {len(confidences)})")
            
        except Exception as e:
            print(f"⚠️ 校准模型更新失败: {e}")
    
    def calculate_enhanced_adjustment_factors(self):
        """计算增强调整因子"""
        if len(self.historical_performance) < 10:
            return {
                'accuracy_factor': 1.0,
                'calibration_factor': 1.0,
                'stability_factor': 1.0,
                'trend_factor': 1.0,
                'composite_factor': 1.0
            }
        
        recent_data = self.historical_performance[-30:]  # 最近30期
        
        # 1. 准确率因子
        hit_rate = np.mean([p['is_hit'] for p in recent_data])
        target_hit_rate = 0.35  # 基于历史数据的目标命中率
        accuracy_factor = 1.0 + (hit_rate - target_hit_rate) * 2.0
        
        # 2. 校准因子
        if len(recent_data) > 5:
            confidences = [p['adjusted_confidence'] for p in recent_data]
            hit_flags = [p['is_hit'] for p in recent_data]
            
            # 计算校准误差
            mean_confidence = np.mean(confidences)
            actual_hit_rate = np.mean(hit_flags)
            calibration_error = abs(actual_hit_rate - mean_confidence)
            
            # 校准因子：校准误差越小，因子越接近1
            calibration_factor = 1.0 + (0.1 - calibration_error) * 0.5
        else:
            calibration_factor = 1.0
        
        # 3. 稳定性因子
        if len(recent_data) > 10:
            confidences = [p['adjusted_confidence'] for p in recent_data]
            confidence_std = np.std(confidences)
            stability_factor = 1.0 + (0.1 - confidence_std) * 0.3
        else:
            stability_factor = 1.0
        
        # 4. 趋势因子
        if len(recent_data) > 15:
            hit_rates = []
            window_size = 5
            for i in range(window_size, len(recent_data)):
                window_hits = [p['is_hit'] for p in recent_data[i-window_size:i]]
                hit_rates.append(np.mean(window_hits))
            
            if len(hit_rates) > 2:
                trend_slope = np.polyfit(range(len(hit_rates)), hit_rates, 1)[0]
                trend_factor = 1.0 + trend_slope * 2.0
            else:
                trend_factor = 1.0
        else:
            trend_factor = 1.0
        
        # 限制因子范围
        factors = {
            'accuracy_factor': max(0.5, min(2.0, accuracy_factor)),
            'calibration_factor': max(0.5, min(2.0, calibration_factor)),
            'stability_factor': max(0.5, min(2.0, stability_factor)),
            'trend_factor': max(0.5, min(2.0, trend_factor))
        }
        
        # 计算复合因子
        weights = self.config['adjustment_factors']
        composite_factor = (
            weights['accuracy_weight'] * factors['accuracy_factor'] +
            weights['calibration_weight'] * factors['calibration_factor'] +
            weights['stability_weight'] * factors['stability_factor'] +
            weights['trend_weight'] * factors['trend_factor']
        )
        
        factors['composite_factor'] = composite_factor
        
        return factors
    
    def adjust_confidence_optimized(self, base_confidence, prediction_context=None):
        """优化版置信度调整"""
        # 1. 计算调整因子
        adjustment_factors = self.calculate_enhanced_adjustment_factors()
        
        # 2. 应用复合调整
        adjusted_confidence = base_confidence * adjustment_factors['composite_factor']
        
        # 3. 应用调整边界
        min_factor = self.config['adjustment_bounds']['min_factor']
        max_factor = self.config['adjustment_bounds']['max_factor']
        
        adjustment_ratio = adjusted_confidence / base_confidence if base_confidence > 0 else 1.0
        adjustment_ratio = max(min_factor, min(max_factor, adjustment_ratio))
        
        adjusted_confidence = base_confidence * adjustment_ratio
        
        # 4. 应用置信度边界
        min_conf = self.config['confidence_bounds']['min_confidence']
        max_conf = self.config['confidence_bounds']['max_confidence']
        adjusted_confidence = max(min_conf, min(max_conf, adjusted_confidence))
        
        # 5. 应用校准模型（如果可用）
        if self.is_calibrated and self.calibration_model is not None:
            try:
                if self.config['calibration_method'] == 'isotonic':
                    calibrated_confidence = self.calibration_model.predict([adjusted_confidence])[0]
                elif self.config['calibration_method'] == 'platt':
                    calibrated_confidence = self.calibration_model.predict_proba([[adjusted_confidence]])[0][1]
                
                # 平滑处理
                smoothing = self.config['calibration_params']['smoothing_factor']
                final_confidence = (1 - smoothing) * adjusted_confidence + smoothing * calibrated_confidence
            except:
                final_confidence = adjusted_confidence
        else:
            final_confidence = adjusted_confidence
        
        # 6. 最终边界检查
        final_confidence = max(min_conf, min(max_conf, final_confidence))
        
        return {
            'original_confidence': base_confidence,
            'adjusted_confidence': adjusted_confidence,
            'final_confidence': final_confidence,
            'adjustment_factors': adjustment_factors,
            'adjustment_ratio': adjustment_ratio,
            'calibration_applied': self.is_calibrated,
            'confidence_interval': self._get_confidence_interval(final_confidence)
        }
    
    def _get_confidence_interval(self, confidence):
        """获取置信度区间"""
        for i, (low, high) in enumerate(self.config['confidence_intervals']):
            if low <= confidence < high:
                return f"区间{i+1}: {low:.1f}-{high:.1f}"
        return f"区间外: {confidence:.3f}"
    
    def get_confidence_interpretation(self, confidence):
        """获取置信度解释"""
        if confidence >= 0.7:
            return "高置信度 - 预测可靠性很高"
        elif confidence >= 0.5:
            return "中高置信度 - 预测可靠性较高"
        elif confidence >= 0.3:
            return "中等置信度 - 预测可靠性一般"
        elif confidence >= 0.2:
            return "中低置信度 - 预测可靠性较低"
        else:
            return "低置信度 - 预测可靠性很低"
    
    def export_optimization_report(self, filename):
        """导出优化报告"""
        report = {
            'optimization_info': {
                'version': '2.1 Optimized',
                'optimization_date': datetime.now().isoformat(),
                'config': self.config,
                'calibration_status': self.is_calibrated
            },
            'historical_performance': self.historical_performance[-20:],  # 最近20期
            'current_factors': self.calculate_enhanced_adjustment_factors() if self.historical_performance else {},
            'improvements': [
                '扩大置信度范围 (0.1-0.8)',
                '增加调整因子范围 (0.3-5.0)',
                '引入Isotonic Regression校准',
                '多维度调整因子权重优化',
                '置信度区间细分',
                '自适应校准模型更新'
            ]
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"📊 优化报告已导出: {filename}")
        return filename

def load_historical_data_for_optimization():
    """加载历史数据用于优化"""
    print("📊 加载历史数据进行优化")
    print("="*30)
    
    df = pd.read_csv('prediction_data.csv')
    df_clean = df[df['实际数字1'].notna()].copy()
    df_clean = df_clean.sort_values('当期期号').reset_index(drop=True)
    
    # 使用第1-194期作为历史数据
    historical_data = df_clean[df_clean['当期期号'] <= 194].copy()
    
    print(f"历史数据期数: {len(historical_data)}")
    print(f"期号范围: {historical_data['当期期号'].min()} - {historical_data['当期期号'].max()}")
    
    return historical_data

def simulate_optimized_system_training(adjuster, historical_data):
    """模拟优化系统训练"""
    print("\n🔧 模拟优化系统训练")
    print("="*30)
    
    training_results = []
    
    for _, row in historical_data.iterrows():
        # 提取特征
        period = row['当期期号']
        original_confidence = row['预测置信度']
        
        # 预测数字和实际数字
        predicted_numbers = [row['预测数字1'], row['预测数字2']]
        actual_numbers = [
            row['实际数字1'], row['实际数字2'], row['实际数字3'],
            row['实际数字4'], row['实际数字5'], row['实际数字6']
        ]
        
        # 计算命中情况
        hit_numbers = list(set(predicted_numbers) & set(actual_numbers))
        is_hit = len(hit_numbers) >= 1
        hit_count = len(hit_numbers)
        
        # 使用优化系统调整置信度
        adjustment_result = adjuster.adjust_confidence_optimized(
            original_confidence,
            {'period': period, 'predicted_numbers': predicted_numbers}
        )
        
        # 构建训练结果
        training_result = {
            'period': period,
            'original_confidence': original_confidence,
            'adjusted_confidence': adjustment_result['adjusted_confidence'],
            'final_confidence': adjustment_result['final_confidence'],
            'is_hit': is_hit,
            'hit_count': hit_count,
            'adjustment_factors': adjustment_result['adjustment_factors'],
            'confidence_interval': adjustment_result['confidence_interval']
        }
        
        training_results.append(training_result)
        
        # 更新历史性能
        adjuster.update_historical_performance(training_result)
        
        # 显示进度
        if period % 20 == 0:
            print(f"  训练进度: 第{period}期 "
                  f"(置信度: {original_confidence:.3f} → {adjustment_result['final_confidence']:.3f})")
    
    print(f"✅ 优化系统训练完成，共训练 {len(training_results)} 期")
    return training_results

def predict_period_195_with_optimization():
    """使用优化系统预测第195期"""
    print("\n🔮 使用优化系统预测第195期")
    print("="*40)
    
    # 1. 初始化优化系统
    adjuster = OptimizedConfidenceAdjuster()
    
    # 2. 加载历史数据
    historical_data = load_historical_data_for_optimization()
    
    # 3. 训练优化系统
    training_results = simulate_optimized_system_training(adjuster, historical_data)
    
    # 4. 获取第194期数据作为预测基础
    period_194 = historical_data[historical_data['当期期号'] == 194].iloc[0]
    
    # 5. 模拟第195期预测
    # 基于第194期的实际数字进行预测
    base_numbers = [
        period_194['实际数字1'], period_194['实际数字2'], period_194['实际数字3'],
        period_194['实际数字4'], period_194['实际数字5'], period_194['实际数字6']
    ]
    
    # 简单的预测逻辑（实际应该使用完整的预测算法）
    predicted_numbers = [
        int(np.mean([base_numbers[0], base_numbers[3]])),  # 平均值策略
        int(np.mean([base_numbers[1], base_numbers[4]]))   # 平均值策略
    ]
    
    # 基础置信度（基于历史平均）
    base_confidence = np.mean([r['original_confidence'] for r in training_results[-20:]])
    
    print(f"预测基础:")
    print(f"  第194期实际数字: {base_numbers}")
    print(f"  第195期预测数字: {predicted_numbers}")
    print(f"  基础置信度: {base_confidence:.3f}")
    
    # 6. 应用优化置信度调整
    prediction_context = {
        'period': 195,
        'predicted_numbers': predicted_numbers,
        'base_numbers': base_numbers,
        'prediction_method': '优化系统预测'
    }
    
    confidence_result = adjuster.adjust_confidence_optimized(base_confidence, prediction_context)
    
    # 7. 构建完整预测结果
    prediction_195 = {
        'period': 195,
        'period_name': '2025年第195期',
        'predicted_numbers': predicted_numbers,
        'base_numbers': base_numbers,
        'confidence_analysis': confidence_result,
        'confidence_interpretation': adjuster.get_confidence_interpretation(
            confidence_result['final_confidence']
        ),
        'prediction_timestamp': datetime.now().isoformat(),
        'system_version': '优化版 v2.1',
        'optimization_features': [
            '扩大置信度范围',
            '增强调整因子',
            'Isotonic校准',
            '多维度权重',
            '区间细分',
            '自适应更新'
        ]
    }
    
    return prediction_195, adjuster, training_results

def main():
    """主函数"""
    print("🚀 优化版动态置信度调整系统")
    print("="*60)
    
    try:
        # 执行优化预测
        prediction_195, adjuster, training_results = predict_period_195_with_optimization()
        
        # 显示预测结果
        print(f"\n🎯 第195期优化预测结果:")
        print("="*40)
        print(f"预测数字: {prediction_195['predicted_numbers']}")
        print(f"基础数字: {prediction_195['base_numbers']}")
        
        confidence = prediction_195['confidence_analysis']
        print(f"\n置信度分析:")
        print(f"  原始置信度: {confidence['original_confidence']:.3f}")
        print(f"  调整置信度: {confidence['adjusted_confidence']:.3f}")
        print(f"  最终置信度: {confidence['final_confidence']:.3f}")
        print(f"  调整比例: {confidence['adjustment_ratio']:.3f}")
        print(f"  置信度区间: {confidence['confidence_interval']}")
        print(f"  校准状态: {'已校准' if confidence['calibration_applied'] else '未校准'}")
        
        print(f"\n调整因子详情:")
        factors = confidence['adjustment_factors']
        print(f"  准确率因子: {factors['accuracy_factor']:.3f}")
        print(f"  校准因子: {factors['calibration_factor']:.3f}")
        print(f"  稳定性因子: {factors['stability_factor']:.3f}")
        print(f"  趋势因子: {factors['trend_factor']:.3f}")
        print(f"  复合因子: {factors['composite_factor']:.3f}")
        
        print(f"\n置信度解释:")
        print(f"  {prediction_195['confidence_interpretation']}")
        
        # 导出结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 导出预测结果
        prediction_filename = f'period_195_optimized_prediction_{timestamp}.json'
        with open(prediction_filename, 'w', encoding='utf-8') as f:
            json.dump(prediction_195, f, indent=2, ensure_ascii=False, default=str)
        
        # 导出优化报告
        optimization_report = f'optimization_report_{timestamp}.json'
        adjuster.export_optimization_report(optimization_report)
        
        print(f"\n📁 生成文件:")
        print(f"  预测结果: {prediction_filename}")
        print(f"  优化报告: {optimization_report}")
        
        print(f"\n✅ 优化系统预测完成！")
        print(f"第195期预测数字: {prediction_195['predicted_numbers']}")
        print(f"最终置信度: {confidence['final_confidence']:.3f}")
        
        return prediction_195
        
    except Exception as e:
        print(f"\n❌ 优化预测过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
