#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态权重调整预测系统
基于一定期数的数字权重进行检测并动态调整

核心思想：
1. 检测某段时间内数字的出现权重
2. 对高权重数字进行加强
3. 动态调整预测策略
4. 在同一环境下测试验证可行性
"""

import pandas as pd
import numpy as np
from datetime import datetime
from collections import defaultdict, Counter, deque
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class DynamicWeightPredictionSystem:
    """动态权重调整预测系统"""
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        
        # 动态权重配置
        self.config = {
            # 权重检测窗口
            'weight_detection': {
                'window_size': 15,          # 检测窗口大小（期数）
                'update_frequency': 5,      # 每5期更新一次权重
                'min_appearance': 3,        # 最小出现次数才考虑加权
                'weight_boost_factor': 1.5, # 权重提升因子
                'weight_decay': 0.95        # 权重衰减因子
            },
            
            # 动态调整参数
            'dynamic_adjustment': {
                'hot_number_threshold': 0.3,    # 热门数字阈值
                'cold_number_threshold': 0.1,   # 冷门数字阈值
                'trend_sensitivity': 0.2,       # 趋势敏感度
                'adaptation_rate': 0.15         # 适应速率
            },
            
            # 预测策略
            'prediction_strategy': {
                'base_weight': 1.0,             # 基础权重
                'hot_boost': 1.8,               # 热门数字加成
                'trend_boost': 1.3,             # 趋势数字加成
                'diversity_factor': 0.7,        # 多样性因子
                'min_distance': 5               # 最小距离
            }
        }
        
        # 数据存储
        self.data = {}
        self.weight_history = deque(maxlen=50)  # 权重历史
        self.number_weights = defaultdict(float)  # 数字权重
        self.trend_weights = defaultdict(float)   # 趋势权重
        self.recent_patterns = deque(maxlen=20)   # 最近模式
        self.results = {}
        
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("📊 加载数据并准备动态权重分析...")
        
        try:
            # 加载数据
            full_data = pd.read_csv(self.data_file, encoding='utf-8')
            full_data = full_data.dropna().sort_values(['年份', '期号'])
            
            # 数据分割：训练集1-150期，预测151-204期
            train_condition = (
                (full_data['年份'] < 2025) |
                ((full_data['年份'] == 2025) & (full_data['期号'] <= 150))
            )
            
            predict_condition = (
                (full_data['年份'] == 2025) &
                (full_data['期号'] >= 151) &
                (full_data['期号'] <= 204)
            )
            
            self.data['train'] = full_data[train_condition].copy()
            self.data['predict'] = full_data[predict_condition].copy()
            
            print(f"   训练集: {len(self.data['train'])} 期")
            print(f"   预测集: {len(self.data['predict'])} 期")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def initialize_weights(self, train_data):
        """初始化权重系统"""
        print("\n🔧 初始化动态权重系统...")
        
        try:
            # 分析训练数据的基础权重
            all_numbers = []
            for _, row in train_data.iterrows():
                numbers = [int(row[f'数字{i}']) for i in range(1, 7)]
                all_numbers.extend(numbers)
            
            # 计算基础频率权重
            number_counts = Counter(all_numbers)
            total_count = len(all_numbers)
            
            for num in range(1, 50):
                base_freq = number_counts[num] / total_count
                self.number_weights[num] = base_freq * self.config['prediction_strategy']['base_weight']
                self.trend_weights[num] = 0.0
            
            print(f"   初始化完成，基础权重已设置")
            print(f"   权重范围: {min(self.number_weights.values()):.4f} - {max(self.number_weights.values()):.4f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 权重初始化失败: {e}")
            return False
    
    def detect_period_weights(self, recent_data, period_info):
        """检测某段时间内的数字权重"""
        window_size = self.config['weight_detection']['window_size']
        min_appearance = self.config['weight_detection']['min_appearance']
        
        # 获取最近window_size期的数据
        recent_numbers = []
        for _, row in recent_data.tail(window_size).iterrows():
            numbers = [int(row[f'数字{i}']) for i in range(1, 7)]
            recent_numbers.extend(numbers)
        
        # 统计最近期数的数字出现频率
        recent_counts = Counter(recent_numbers)
        total_recent = len(recent_numbers)
        
        # 计算权重变化
        weight_changes = {}
        hot_numbers = []
        cold_numbers = []
        
        for num in range(1, 50):
            recent_freq = recent_counts[num] / total_recent if total_recent > 0 else 0
            base_freq = self.number_weights[num]
            
            # 计算权重比例
            if base_freq > 0:
                weight_ratio = recent_freq / base_freq
            else:
                weight_ratio = 1.0
            
            weight_changes[num] = {
                'recent_freq': recent_freq,
                'base_freq': base_freq,
                'weight_ratio': weight_ratio,
                'recent_count': recent_counts[num]
            }
            
            # 识别热门和冷门数字
            if (recent_counts[num] >= min_appearance and 
                weight_ratio > 1 + self.config['dynamic_adjustment']['hot_number_threshold']):
                hot_numbers.append(num)
            elif weight_ratio < self.config['dynamic_adjustment']['cold_number_threshold']:
                cold_numbers.append(num)
        
        detection_result = {
            'period': f"{period_info['year']}年{period_info['period']}期",
            'window_size': window_size,
            'weight_changes': weight_changes,
            'hot_numbers': hot_numbers,
            'cold_numbers': cold_numbers,
            'total_numbers': len(recent_numbers)
        }
        
        return detection_result
    
    def update_dynamic_weights(self, detection_result):
        """更新动态权重"""
        boost_factor = self.config['weight_detection']['weight_boost_factor']
        decay_factor = self.config['weight_detection']['weight_decay']
        adaptation_rate = self.config['dynamic_adjustment']['adaptation_rate']
        
        print(f"   🔄 更新动态权重 - {detection_result['period']}")
        
        # 更新热门数字权重
        for num in detection_result['hot_numbers']:
            old_weight = self.number_weights[num]
            weight_ratio = detection_result['weight_changes'][num]['weight_ratio']
            
            # 动态调整权重
            boost = min(boost_factor, 1 + (weight_ratio - 1) * adaptation_rate)
            self.number_weights[num] = old_weight * boost
            
            # 更新趋势权重
            self.trend_weights[num] = min(1.0, self.trend_weights[num] + 0.2)
            
            print(f"     🔥 热门数字 {num}: 权重 {old_weight:.4f} → {self.number_weights[num]:.4f}")
        
        # 衰减其他数字权重
        for num in range(1, 50):
            if num not in detection_result['hot_numbers']:
                self.number_weights[num] *= decay_factor
                self.trend_weights[num] *= 0.9  # 趋势权重也衰减
        
        # 记录权重历史
        self.weight_history.append({
            'period': detection_result['period'],
            'hot_numbers': detection_result['hot_numbers'],
            'weights_snapshot': dict(self.number_weights),
            'trend_snapshot': dict(self.trend_weights)
        })
        
        print(f"     📊 权重更新完成，热门数字: {detection_result['hot_numbers']}")
    
    def enhanced_prediction_with_weights(self, target_year, target_period, recent_data):
        """基于动态权重的增强预测"""
        try:
            # 1. 检测当前权重
            period_info = {'year': target_year, 'period': target_period}
            detection_result = self.detect_period_weights(recent_data, period_info)
            
            # 2. 每5期更新一次权重
            if target_period % self.config['weight_detection']['update_frequency'] == 0:
                self.update_dynamic_weights(detection_result)
            
            # 3. 基于权重进行预测
            prediction = self.weight_based_prediction(detection_result)
            
            # 4. 应用多样性约束
            final_prediction = self.apply_diversity_constraints(prediction, detection_result)
            
            return final_prediction
            
        except Exception as e:
            print(f"⚠️ 权重预测失败: {e}")
            return self.fallback_prediction()
    
    def weight_based_prediction(self, detection_result):
        """基于权重的预测"""
        hot_boost = self.config['prediction_strategy']['hot_boost']
        trend_boost = self.config['prediction_strategy']['trend_boost']
        
        # 计算综合权重分数
        combined_scores = {}
        
        for num in range(1, 50):
            base_score = self.number_weights[num]
            
            # 热门数字加成
            if num in detection_result['hot_numbers']:
                base_score *= hot_boost
            
            # 趋势加成
            trend_score = self.trend_weights[num]
            if trend_score > 0.3:
                base_score *= (1 + trend_score * trend_boost)
            
            # 添加适度随机性
            random_factor = np.random.uniform(0.8, 1.2)
            combined_scores[num] = base_score * random_factor
        
        # 选择得分最高的候选数字
        sorted_scores = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 选择前两个数字，确保距离
        selected = [sorted_scores[0][0]]
        min_distance = self.config['prediction_strategy']['min_distance']
        
        for num, score in sorted_scores[1:]:
            if abs(num - selected[0]) >= min_distance:
                selected.append(num)
                break
        
        if len(selected) < 2:
            selected.append(sorted_scores[1][0])
        
        # 计算置信度
        confidence = np.mean([combined_scores[num] for num in selected])
        confidence = min(0.8, max(0.1, confidence * 10))  # 归一化到0.1-0.8
        
        return {
            'numbers': sorted(selected),
            'confidence': confidence,
            'method': 'Dynamic_Weight_Based',
            'hot_numbers_used': [n for n in selected if n in detection_result['hot_numbers']],
            'scores': {num: combined_scores[num] for num in selected}
        }
    
    def apply_diversity_constraints(self, prediction, detection_result):
        """应用多样性约束"""
        diversity_factor = self.config['prediction_strategy']['diversity_factor']
        
        # 检查最近预测的多样性
        if len(self.recent_patterns) >= 3:
            recent_combos = [tuple(sorted(pattern)) for pattern in self.recent_patterns[-3:]]
            current_combo = tuple(sorted(prediction['numbers']))
            
            # 如果最近重复太多，进行多样性调整
            if recent_combos.count(current_combo) >= 2:
                print(f"   🎨 应用多样性约束，避免重复组合 {current_combo}")
                
                # 随机替换一个数字
                replace_idx = np.random.randint(0, 2)
                keep_num = prediction['numbers'][1 - replace_idx]
                
                # 从非热门数字中选择替换
                non_hot_candidates = [n for n in range(1, 50) 
                                    if n not in detection_result['hot_numbers'] 
                                    and abs(n - keep_num) >= self.config['prediction_strategy']['min_distance']]
                
                if non_hot_candidates:
                    new_num = np.random.choice(non_hot_candidates)
                    prediction['numbers'][replace_idx] = new_num
                    prediction['numbers'] = sorted(prediction['numbers'])
                    prediction['confidence'] *= diversity_factor
                    prediction['method'] += '_Diversified'
        
        # 记录当前预测模式
        self.recent_patterns.append(prediction['numbers'])
        
        return prediction
    
    def fallback_prediction(self):
        """备用预测"""
        candidates = list(range(1, 50))
        selected = sorted(np.random.choice(candidates, 2, replace=False))
        
        return {
            'numbers': selected,
            'confidence': 0.15,
            'method': 'Fallback_Random',
            'hot_numbers_used': [],
            'scores': {}
        }
    
    def predict_with_dynamic_weights(self):
        """使用动态权重进行预测"""
        print("\n🎯 动态权重预测151-204期...")
        
        try:
            # 初始化权重系统
            if not self.initialize_weights(self.data['train']):
                return []
            
            # 合并训练数据用于权重检测
            all_data = pd.concat([self.data['train'], self.data['predict']], ignore_index=True)
            
            predictions = []
            
            for idx, row in self.data['predict'].iterrows():
                year = int(row['年份'])
                period = int(row['期号'])
                actual_numbers = [int(row[f'数字{i}']) for i in range(1, 7)]
                
                # 获取到当前期为止的所有数据
                current_data = all_data[(all_data['年份'] < year) |
                                      ((all_data['年份'] == year) & (all_data['期号'] <= period))]
                
                # 动态权重预测
                prediction = self.enhanced_prediction_with_weights(year, period, current_data)
                
                # 计算命中
                pred_set = set(prediction['numbers'])
                actual_set = set(actual_numbers)
                hit_count = len(pred_set & actual_set)
                
                result = {
                    'year': year,
                    'period': period,
                    'period_id': f"{year}年{period}期",
                    'predicted_numbers': prediction['numbers'],
                    'actual_numbers': actual_numbers,
                    'confidence': prediction['confidence'],
                    'hit_count': hit_count,
                    'is_hit': hit_count > 0,
                    'hit_rate': hit_count / 2.0,
                    'pred_sum': sum(prediction['numbers']),
                    'actual_sum': sum(actual_numbers),
                    'method': prediction['method'],
                    'hot_numbers_used': prediction.get('hot_numbers_used', []),
                    'prediction_scores': prediction.get('scores', {})
                }
                
                predictions.append(result)
                
                # 实时输出
                status = "✅" if result['is_hit'] else "❌"
                hot_info = f" (热门:{prediction.get('hot_numbers_used', [])})" if prediction.get('hot_numbers_used') else ""
                print(f"   {result['period_id']}: 预测{prediction['numbers']} → 实际{actual_numbers[:2]} {status}{hot_info}")
            
            return predictions
            
        except Exception as e:
            print(f"❌ 动态权重预测失败: {e}")
            return []
    
    def analyze_dynamic_performance(self, predictions):
        """分析动态权重性能"""
        print(f"\n📊 动态权重系统性能分析...")
        
        if not predictions:
            print("❌ 无预测结果")
            return
        
        # 基本统计
        total_periods = len(predictions)
        hit_periods = sum(1 for p in predictions if p['is_hit'])
        hit_rate = hit_periods / total_periods if total_periods > 0 else 0
        
        # 热门数字使用统计
        hot_number_predictions = sum(1 for p in predictions if p['hot_numbers_used'])
        hot_number_hits = sum(1 for p in predictions if p['is_hit'] and p['hot_numbers_used'])
        hot_number_hit_rate = hot_number_hits / hot_number_predictions if hot_number_predictions > 0 else 0
        
        # 多样性分析
        pred_combinations = [tuple(sorted(p['predicted_numbers'])) for p in predictions]
        unique_combinations = len(set(pred_combinations))
        diversity_rate = unique_combinations / total_periods if total_periods > 0 else 0
        
        # 权重更新统计
        weight_updates = len(self.weight_history)
        
        print(f"   总预测期数: {total_periods}")
        print(f"   命中期数: {hit_periods}")
        print(f"   总命中率: {hit_rate:.1%}")
        print(f"   热门数字预测: {hot_number_predictions}次")
        print(f"   热门数字命中: {hot_number_hits}次")
        print(f"   热门数字命中率: {hot_number_hit_rate:.1%}")
        print(f"   唯一组合数: {unique_combinations}")
        print(f"   多样性率: {diversity_rate:.1%}")
        print(f"   权重更新次数: {weight_updates}")
        
        # 权重演化分析
        if self.weight_history:
            print(f"\n📈 权重演化分析:")
            for i, history in enumerate(list(self.weight_history)[-3:]):  # 显示最近3次更新
                print(f"   {history['period']}: 热门数字 {history['hot_numbers']}")
        
        return {
            'hit_rate': hit_rate,
            'hot_number_hit_rate': hot_number_hit_rate,
            'diversity_rate': diversity_rate,
            'weight_updates': weight_updates,
            'unique_combinations': unique_combinations
        }
    
    def save_dynamic_results_to_csv(self, predictions):
        """保存动态权重结果到CSV文件"""
        print(f"\n💾 保存动态权重结果到CSV文件...")
        
        try:
            if not predictions:
                print("❌ 无预测结果可保存")
                return None
            
            # 准备CSV数据
            csv_data = []
            for result in predictions:
                csv_data.append({
                    '年份': result['year'],
                    '期号': result['period'],
                    '期号标识': result['period_id'],
                    '预测数字1': result['predicted_numbers'][0],
                    '预测数字2': result['predicted_numbers'][1],
                    '预测组合': str(result['predicted_numbers']),
                    '预测置信度': round(result['confidence'], 4),
                    '实际数字1': result['actual_numbers'][0],
                    '实际数字2': result['actual_numbers'][1],
                    '实际数字3': result['actual_numbers'][2],
                    '实际数字4': result['actual_numbers'][3],
                    '实际数字5': result['actual_numbers'][4],
                    '实际数字6': result['actual_numbers'][5],
                    '实际组合': str(result['actual_numbers']),
                    '命中数量': result['hit_count'],
                    '是否命中': '是' if result['is_hit'] else '否',
                    '命中率': round(result['hit_rate'], 4),
                    '预测数字和': result['pred_sum'],
                    '实际数字和': result['actual_sum'],
                    '预测方法': result['method'],
                    '使用热门数字': str(result['hot_numbers_used']),
                    '预测得分': str(result['prediction_scores']),
                    '生成时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
            
            # 保存CSV文件
            csv_file = f"动态权重预测结果151-204期_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df = pd.DataFrame(csv_data)
            df.to_csv(csv_file, index=False, encoding='utf-8')
            
            print(f"✅ 动态权重CSV文件已保存: {csv_file}")
            return csv_file
            
        except Exception as e:
            print(f"❌ CSV保存失败: {e}")
            return None
    
    def run_dynamic_weight_system(self):
        """运行动态权重系统"""
        print("🚀 动态权重调整预测系统")
        print("基于一定期数的数字权重进行检测并动态调整")
        print("=" * 80)
        
        print("🔧 核心特性:")
        print("   ✅ 动态权重检测：每15期检测数字权重变化")
        print("   ✅ 智能权重调整：对高权重数字进行1.5倍加强")
        print("   ✅ 趋势识别机制：识别热门和冷门数字趋势")
        print("   ✅ 自适应预测：基于权重动态调整预测策略")
        print("   ✅ 多样性保障：避免过度依赖单一模式")
        
        # 1. 数据加载
        if not self.load_and_prepare_data():
            return False
        
        # 2. 动态权重预测
        predictions = self.predict_with_dynamic_weights()
        
        if not predictions:
            print("❌ 预测失败")
            return False
        
        # 3. 性能分析
        performance = self.analyze_dynamic_performance(predictions)
        
        # 4. 保存CSV文件
        csv_file = self.save_dynamic_results_to_csv(predictions)
        
        # 5. 最终评估
        if performance:
            hit_rate = performance['hit_rate']
            hot_hit_rate = performance['hot_number_hit_rate']
            diversity_rate = performance['diversity_rate']
            
            print(f"\n🏆 动态权重系统最终评估:")
            print(f"   总命中率: {hit_rate:.1%}")
            print(f"   热门数字命中率: {hot_hit_rate:.1%}")
            print(f"   多样性率: {diversity_rate:.1%}")
            print(f"   权重更新次数: {performance['weight_updates']}")
            
            # 系统评级
            if hit_rate > 0.25 and hot_hit_rate > 0.30:
                system_grade = "A级 - 优秀"
            elif hit_rate > 0.20 and hot_hit_rate > 0.25:
                system_grade = "B级 - 良好"
            elif hit_rate > 0.15:
                system_grade = "C级 - 可接受"
            else:
                system_grade = "D级 - 需改进"
            
            print(f"   系统评级: {system_grade}")
            
            if csv_file:
                print(f"   详细结果: {csv_file}")
            
            # 动态权重效果评估
            if hot_hit_rate > hit_rate * 1.2:
                weight_effect = "权重调整有效"
            elif hot_hit_rate > hit_rate:
                weight_effect = "权重调整略有效果"
            else:
                weight_effect = "权重调整效果有限"
            
            print(f"   权重调整效果: {weight_effect}")
            
            return hit_rate > 0.15
        
        return False

def main():
    """主函数"""
    print("🔬 动态权重调整预测系统")
    print("基于一定期数的数字权重进行检测并动态调整")
    print("=" * 80)
    
    # 创建动态权重系统
    system = DynamicWeightPredictionSystem()
    
    # 运行动态权重系统
    success = system.run_dynamic_weight_system()
    
    if success:
        print(f"\n🎉 动态权重调整预测系统运行成功！")
        print(f"已完成基于权重检测的动态调整预测验证。")
    else:
        print(f"\n⚠️ 动态权重系统需要进一步优化")

if __name__ == "__main__":
    main()
