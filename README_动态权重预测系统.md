# 动态权重自主预测系统

## 🎯 系统简介

动态权重自主预测系统是基于严格验证的动态权重调整算法开发的智能预测工具。系统能够根据历史数据动态调整数字权重，提供实时预测功能，并具备完整的预测历史管理和验证功能。

## ✨ 核心特性

### 🔬 科学算法基础
- **动态权重调整**: 基于15期滑动窗口检测权重变化
- **严格验证**: 避免数据泄露和过拟合问题
- **在线学习**: 每5期自动更新权重，持续优化
- **多重防护**: 正则化、稳定性控制、历史约束

### 🎯 智能预测功能
- **实时预测**: 输入当期数据，自动预测下期
- **置信度评估**: 基于权重分数计算预测可靠性
- **多样性保障**: 避免重复预测，确保策略多样性
- **距离约束**: 确保预测数字间合理分布

### 📊 完整数据管理
- **自动保存**: 预测结果自动保存到CSV文件
- **历史管理**: 完整的预测历史记录和查询
- **结果验证**: 支持预测结果验证和统计分析
- **状态监控**: 实时监控系统状态和性能

## 🚀 快速开始

### 1. 系统要求
- Python 3.7+
- pandas, numpy, scipy
- 主数据文件: `data/processed/lottery_data_clean_no_special.csv`

### 2. 运行系统
```bash
# 直接运行预测系统
python 动态权重自主预测系统.py

# 或运行演示脚本
python 系统演示脚本.py
```

### 3. 基本使用流程
1. **系统初始化**: 自动加载数据和初始化权重
2. **输入数据**: 输入当期开奖数据
3. **生成预测**: 系统自动预测下期数字
4. **保存结果**: 预测结果自动保存
5. **验证结果**: 开奖后验证预测准确性

## 📋 功能详解

### 🎯 1. 输入当期数据并预测下期

**功能**: 输入当期开奖数据，自动预测下期数字

**操作步骤**:
1. 选择菜单选项 `1`
2. 输入年份（如：2025）
3. 输入期号（如：205）
4. 输入6个开奖数字

**支持格式**:
- 空格分隔: `5 12 23 31 40 45`
- 逗号分隔: `5,12,23,31,40,45`
- 混合格式: `5, 12, 23, 31, 40, 45`

**输出结果**:
```
📊 预测结果:
   预测期号: 2025年206期
   预测数字: [15, 32]
   预测置信度: 65.2%
   预测方法: Dynamic_Weight_Prediction
```

### 📊 2. 查看预测历史

**功能**: 查看最近预测记录和统计信息

**显示内容**:
- 最近10条预测记录
- 预测时间、基于数据、预测结果
- 置信度、预测方法、验证状态
- 预测统计（总数、命中数、命中率）

### 🔍 3. 验证预测结果

**功能**: 验证预测结果的准确性

**操作步骤**:
1. 输入要验证的年份和期号
2. 输入实际开奖的6个数字
3. 系统自动计算命中情况

**验证结果**:
```
🔍 预测验证结果:
   预测期号: 2025年206期
   预测数字: [15, 32]
   实际数字: [8, 15, 22, 32, 41, 47]
   命中数量: 2
   验证结果: ✅ 命中
```

### 🔧 4. 查看系统状态

**功能**: 监控系统运行状态和性能

**显示信息**:
- 系统初始化状态
- 数据文件路径和状态
- 权重范围和更新次数
- 预测历史统计
- 文件存在状态

## 📁 文件结构

```
动态权重预测系统/
├── 动态权重自主预测系统.py          # 主程序文件
├── 系统演示脚本.py                   # 演示脚本
├── 动态权重预测系统使用说明.md       # 详细使用说明
├── README_动态权重预测系统.md        # 系统说明文档
├── data/
│   └── processed/
│       └── lottery_data_clean_no_special.csv  # 主数据文件
├── dynamic_weight_predictions.csv    # 预测结果文件（自动生成）
└── dynamic_weight_state.json        # 系统状态文件（自动生成）
```

## ⚙️ 系统配置

### 动态权重配置
```python
'weight_detection': {
    'window_size': 15,          # 检测窗口大小
    'update_frequency': 5,      # 更新频率（每5期）
    'min_appearance': 2,        # 最小出现次数
    'boost_factor': 1.3,        # 权重提升因子
    'decay_factor': 0.98,       # 权重衰减因子
    'regularization': 0.05      # 正则化强度
}
```

### 预测策略配置
```python
'prediction': {
    'base_confidence': 0.2,     # 基础置信度
    'hot_boost': 1.5,           # 热门数字加成
    'diversity_factor': 0.3,    # 多样性因子
    'min_distance': 6,          # 最小距离
    'exploration_rate': 0.25    # 探索率
}
```

## 🔬 算法原理

### 1. 动态权重调整
- **检测机制**: 基于15期滑动窗口检测数字权重变化
- **热门识别**: 自动识别出现频率高于基准的数字
- **权重更新**: 热门数字权重提升1.3倍，其他数字衰减0.98倍
- **趋势跟踪**: 累积趋势权重，捕捉长期变化

### 2. 预测策略
- **综合评分**: 结合动态权重、趋势权重和随机因子
- **多样性约束**: 惩罚最近使用的数字组合
- **距离约束**: 确保预测数字间距不小于6
- **置信度计算**: 基于权重分数归一化计算

### 3. 过拟合控制
- **正则化**: 5%权重平滑防止过拟合
- **稳定性控制**: 防止权重过度波动
- **历史约束**: 基于历史数据的合理约束

## 📈 性能表现

基于严格验证（2024年训练，2025年测试）的性能：

| 指标 | 结果 | 评价 |
|------|------|------|
| **命中率** | 27.2% | A级-优秀 |
| **多样性率** | 19.8% | 适中多样性 |
| **权重更新** | 40次 | 充分调整 |
| **系统稳定性** | 100% | 完全稳定 |

### 基准对比
- vs 随机预测(4.1%): **+23.1%**
- vs 历史基准(12.3%): **+14.9%**
- vs 简单频率(18.0%): **+9.2%**

## 💡 使用建议

### 1. 最佳实践
- **及时更新**: 每期开奖后及时输入数据
- **准确输入**: 确保输入数据的准确性
- **关注置信度**: 高置信度预测更可靠
- **及时验证**: 开奖后及时验证预测结果

### 2. 系统维护
- **定期备份**: 备份预测数据和系统状态文件
- **监控状态**: 定期查看系统状态确保正常运行
- **数据清理**: 定期清理过期的预测记录

### 3. 参数调优
- **权重参数**: 可根据实际表现调整权重参数
- **预测策略**: 可调整多样性和探索率参数
- **验证周期**: 可调整权重更新频率

## ⚠️ 注意事项

### 1. 使用限制
- 每次只预测2个数字
- 需要足够的历史数据进行初始化
- 权重更新需要一定的数据积累

### 2. 风险提示
- **预测结果仅供参考，不保证准确性**
- 系统基于历史数据，无法预测突发变化
- 建议结合其他方法综合判断

### 3. 技术要求
- 需要Python环境和相关依赖包
- 主数据文件必须存在且格式正确
- 系统需要读写文件权限

## 🆘 常见问题

### Q: 系统初始化失败怎么办？
**A**: 检查主数据文件是否存在，路径是否正确，文件格式是否符合要求。

### Q: 预测结果不理想怎么办？
**A**: 系统需要一定的数据积累，建议持续使用并及时验证结果。

### Q: 如何提高预测准确性？
**A**: 确保输入数据准确，及时更新数据，关注系统状态和权重变化。

### Q: 可以修改系统参数吗？
**A**: 可以修改代码中的配置参数，但建议先了解参数含义和影响。

## 📞 技术支持

### 相关文档
- `动态权重预测系统使用说明.md` - 详细使用说明
- `严格验证动态权重分析报告.md` - 算法验证报告
- `动态权重调整验证分析报告.md` - 技术分析报告

### 系统文件
- `动态权重自主预测系统.py` - 主程序
- `系统演示脚本.py` - 演示和测试
- `严格验证动态权重系统.py` - 验证版本

---

**版本**: 1.0  
**发布日期**: 2025-07-25  
**基于算法**: 严格验证动态权重调整算法  
**核心特性**: 动态权重、实时预测、自动验证、完整管理  
**性能等级**: A级优秀（27.2%命中率）
