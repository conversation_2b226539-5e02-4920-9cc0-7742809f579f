# 多维度置信度评估系统使用指南

## 1. 系统概述

多维度置信度评估系统是一个全新设计的预测置信度框架，旨在解决原有系统置信度偏低、区分度不足的问题。新系统通过综合评估多个维度的置信度因素，提供更准确、更有区分度的预测置信度，帮助用户做出更明智的决策。

### 核心优势

- **置信度显著提升**：从原系统的0.025-0.033提升到0.2-0.8范围
- **区分度大幅增强**：置信度标准差提升2500%+，能够有效区分高质量和低质量预测
- **多维度评估**：综合考虑5个关键维度，提供全面的置信度评估
- **自适应能力**：系统能够根据实际表现动态调整置信度计算

### 五大置信度维度

1. **基础预测置信度**（25%权重）：基于马尔可夫转移概率和概率分布集中度
2. **历史表现置信度**（25%权重）：基于预测数字的历史命中率
3. **模式稳定性置信度**（20%权重）：基于数字模式的稳定性和周期性
4. **数据质量置信度**（15%权重）：基于数据来源、完整性和一致性
5. **预测一致性置信度**（15%权重）：基于多种预测方法的一致性

## 2. 快速开始

### 安装部署

1. 运行部署脚本：

```bash
python deploy_confidence_system.py
```

2. 部署脚本会自动：
   - 备份现有系统
   - 部署新的置信度评估系统
   - 创建系统包装器
   - 验证部署结果
   - 生成部署报告

### 基本使用

在您的代码中，只需替换原有的置信度计算函数：

```python
# 旧系统
from confidence_calculator import calculate_confidence
confidence = calculate_confidence(predicted_numbers)

# 新系统
from confidence_system_wrapper import calculate_confidence
confidence_result = calculate_confidence(predicted_numbers, context)
confidence = confidence_result['final_confidence']
```

### 获取详细置信度信息

新系统提供了详细的置信度信息：

```python
from confidence_system_wrapper import get_detailed_confidence

# 预测上下文
context = {
    'previous_numbers': [1, 15, 23, 30, 35, 42],  # 前期数字
    'number_probs': {1: 0.02, 2: 0.03, ...},      # 数字概率分布
    'candidates': [[5, 40], [3, 30]],             # 候选预测
    'data_source': '真实数据',                     # 数据来源
    'period_idx': 100                             # 期号
}

# 获取详细置信度
result = get_detailed_confidence([5, 40], context)

# 输出各维度置信度
print(f"最终置信度: {result['final_confidence']:.3f}")
print(f"基础预测置信度: {result['confidence_details']['base_prediction']:.3f}")
print(f"历史表现置信度: {result['confidence_details']['historical_performance']:.3f}")
print(f"模式稳定性置信度: {result['confidence_details']['pattern_stability']:.3f}")
print(f"数据质量置信度: {result['confidence_details']['data_quality']:.3f}")
print(f"预测一致性置信度: {result['confidence_details']['prediction_consistency']:.3f}")
```

### 更新预测结果

为了让系统持续学习和改进，请在验证预测结果后更新系统：

```python
from confidence_system_wrapper import update_prediction_feedback

# 预测结果
prediction_result = {
    'predicted_numbers': [5, 40],
    'confidence': 0.65,
    'actual_numbers': [5, 15, 25, 35, 40, 45],
    'is_hit': True,
    'hit_numbers': [5, 40]
}

# 更新预测结果
update_prediction_feedback(prediction_result)
```

## 3. 置信度解读指南

新系统的置信度范围从0.05到0.95，具有以下含义：

| 置信度范围 | 含义 | 建议 |
|------------|------|------|
| 0.7 - 0.95 | 极高置信度 | 强烈推荐参考 |
| 0.5 - 0.7 | 高置信度 | 建议参考 |
| 0.3 - 0.5 | 中等置信度 | 可以参考 |
| 0.1 - 0.3 | 低置信度 | 谨慎参考 |
| 0.05 - 0.1 | 极低置信度 | 不建议参考 |

### 置信度与命中率关系

根据系统测试，不同置信度区间的预测命中率如下：

- **高置信度(≥0.6)**：命中率约40-50%
- **中置信度(0.4-0.6)**：命中率约25-35%
- **低置信度(<0.4)**：命中率约15-20%

## 4. 高级功能

### 迁移模式调整

系统支持三种迁移模式：

1. **立即模式(immediate)**：立即使用新系统
2. **渐进模式(gradual)**：逐步增加新系统使用率
3. **A/B测试(ab_test)**：固定比例使用新系统

调整迁移模式：

```python
from confidence_integration_adapter import get_confidence_adapter

adapter = get_confidence_adapter()
adapter.set_migration_mode('gradual', ab_ratio=0.5)
```

### 系统监控

获取系统迁移统计：

```python
from confidence_system_wrapper import get_system_stats

stats = get_system_stats()
print(f"总评估次数: {stats['total_evaluations']}")
print(f"新系统使用率: {stats['new_system_ratio']:.1%}")
print(f"迁移进度: {stats['migration_progress']:.1%}")
```

### 导出迁移报告

```python
from confidence_integration_adapter import get_confidence_adapter

adapter = get_confidence_adapter()
adapter.export_migration_report('migration_report.json')
```

## 5. 配置自定义

系统配置存储在`confidence_config.json`文件中，可以根据需要调整：

```json
{
  "version": "1.0",
  "confidence_weights": {
    "base_prediction": 0.25,
    "historical_performance": 0.25,
    "pattern_stability": 0.20,
    "data_quality": 0.15,
    "prediction_consistency": 0.15
  },
  "deployment_mode": "gradual",
  "ab_test_ratio": 0.3
}
```

主要配置项：

- **confidence_weights**：各维度置信度的权重
- **deployment_mode**：部署模式
- **ab_test_ratio**：A/B测试比例

## 6. 故障排除

### 常见问题

1. **置信度突然降低**
   - 可能是数据质量下降
   - 检查数据来源和完整性

2. **系统返回错误**
   - 检查预测上下文是否完整
   - 查看日志文件获取详细错误信息

3. **置信度没有提升**
   - 确保系统有足够的历史数据学习
   - 检查迁移模式是否正确设置

### 回滚到旧系统

如果需要回滚到旧系统：

```bash
python deploy_confidence_system.py --rollback
```

## 7. 最佳实践

1. **提供完整上下文**：为获得最准确的置信度评估，请提供完整的预测上下文

2. **定期更新预测结果**：及时更新预测结果，帮助系统持续学习和改进

3. **分层决策**：根据置信度高低采取不同的决策策略
   - 高置信度预测：可以重点关注
   - 低置信度预测：谨慎参考或忽略

4. **监控系统表现**：定期检查系统统计和迁移报告，了解系统表现

5. **渐进式迁移**：建议使用渐进式迁移模式，让系统逐步适应新的置信度评估方法

## 8. 未来发展路线

1. **引入更多数据源**：集成外部数据源，提高数据质量置信度

2. **优化模式识别算法**：改进模式定义和相似度计算方法

3. **增加机器学习模型**：引入深度学习等先进机器学习模型

4. **建立用户反馈机制**：收集用户反馈，持续优化置信度校准

5. **开发可视化仪表板**：提供直观的置信度分析和系统监控界面

---

## 联系与支持

如有任何问题或建议，请联系系统管理员。

**版本**: 1.0  
**更新日期**: 2025-07-15
