
优化动态置信度调整系统全面测试验证报告
======================================================================

验证信息:
==============================
验证日期: 2025-07-15T20:30:42
验证期数: 192期
期号范围: 第2-194期
系统版本: 优化版 v2.1 vs 原始版 v2.0

系统性能对比:
==============================
命中率对比:
  原始系统命中率: 35.4%
  优化系统命中率: 35.4%
  命中率差异: +0.0%
  相对改进: +0.0%
  统计显著性: 不显著 (p=nan)

置信度对比:
  原始平均置信度: 0.029 ± 0.002
  优化平均置信度: 0.116 ± 0.008
  置信度改进: +0.087
  改进比例: 100.0%

置信度区间分析:
==============================
区间1: 0.1-0.2:
  样本数量: 192
  命中率: 35.4%
  平均置信度: 0.116
  平均改进: +0.087

校准有效性分析:
==============================
校准应用次数: 172
校准应用比例: 89.6%
校准误差: 0.2486
校准后命中率: 36.6%
校准后平均置信度: 0.118

详细统计分析:
==============================
原始置信度统计:
  范围: 0.025 - 0.034
  标准差: 0.002

优化置信度统计:
  范围: 0.100 - 0.132
  标准差: 0.008

置信度改进统计:
  最大改进: +0.106
  最小改进: +0.068

系统评估结论:
==============================
❌ 优化系统命中率未见改善
✅ 置信度调整效果显著，大幅提升了置信度水平
❌ 校准效果较差，需要进一步优化

改进建议:
==============================
1. 考虑调整预测算法本身，而不仅仅是置信度
3. 优化校准模型，考虑使用更先进的校准方法
4. 增加置信度的变化范围，提供更精细的区分度

生成文件:
==============================
详细数据: comprehensive_validation_results_20250715_203038.csv
可视化图表: comprehensive_validation_charts_20250715_203038.png

---
报告生成时间: 2025-07-15 20:30:42
验证系统版本: 全面测试验证器 v1.0
