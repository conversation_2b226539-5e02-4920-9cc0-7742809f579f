#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重复数值机制深度思辨分析
基于历史多期数据中重复出现数值的机制研究
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict, Counter
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class RepeatPatternAnalyzer:
    """
    重复数值机制分析器
    深度分析历史数据中的重复模式和机制
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_2023_2025.csv"
        self.data = None
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            print(f"✅ 数据加载成功: {len(self.data)}期")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def analyze_immediate_repeats(self, window_size=2):
        """分析连续期数的重复模式"""
        print(f"\n🔍 分析连续{window_size}期的重复模式")
        print("=" * 50)
        
        repeat_stats = {
            'total_periods': len(self.data) - window_size + 1,
            'periods_with_repeats': 0,
            'total_repeat_numbers': 0,
            'repeat_details': [],
            'repeat_distribution': Counter()
        }
        
        for i in range(len(self.data) - window_size + 1):
            current_period = self.data.iloc[i]
            next_period = self.data.iloc[i + 1] if window_size == 2 else None
            
            # 获取当前期和下一期的数字
            current_numbers = set([current_period[f'数字{j}'] for j in range(1, 7)])
            
            if window_size == 2 and next_period is not None:
                next_numbers = set([next_period[f'数字{j}'] for j in range(1, 7)])
                
                # 计算重复数字
                repeat_numbers = current_numbers & next_numbers
                repeat_count = len(repeat_numbers)
                
                if repeat_count > 0:
                    repeat_stats['periods_with_repeats'] += 1
                    repeat_stats['total_repeat_numbers'] += repeat_count
                    repeat_stats['repeat_distribution'][repeat_count] += 1
                    
                    repeat_stats['repeat_details'].append({
                        'period_1': current_period['期号'],
                        'period_2': next_period['期号'],
                        'repeat_count': repeat_count,
                        'repeat_numbers': sorted(list(repeat_numbers)),
                        'current_numbers': sorted(list(current_numbers)),
                        'next_numbers': sorted(list(next_numbers))
                    })
        
        # 计算统计指标
        repeat_rate = repeat_stats['periods_with_repeats'] / repeat_stats['total_periods']
        avg_repeats_per_period = repeat_stats['total_repeat_numbers'] / repeat_stats['total_periods']
        
        print(f"📊 连续期重复统计:")
        print(f"  总期数对: {repeat_stats['total_periods']}")
        print(f"  有重复的期数对: {repeat_stats['periods_with_repeats']}")
        print(f"  重复发生率: {repeat_rate:.3f} ({repeat_rate*100:.1f}%)")
        print(f"  平均每期重复数字: {avg_repeats_per_period:.2f}个")
        
        print(f"\n📈 重复数字分布:")
        for repeat_count in sorted(repeat_stats['repeat_distribution'].keys()):
            count = repeat_stats['repeat_distribution'][repeat_count]
            percentage = count / repeat_stats['total_periods'] * 100
            print(f"  {repeat_count}个重复: {count}次 ({percentage:.1f}%)")
        
        return repeat_stats
    
    def analyze_window_repeats(self, window_sizes=[3, 5, 7, 10]):
        """分析不同时间窗口内的重复模式"""
        print(f"\n🔍 分析不同时间窗口的重复模式")
        print("=" * 50)
        
        window_results = {}
        
        for window_size in window_sizes:
            print(f"\n📊 分析{window_size}期窗口:")
            
            window_stats = {
                'window_size': window_size,
                'total_windows': len(self.data) - window_size + 1,
                'repeat_frequencies': Counter(),
                'high_frequency_numbers': [],
                'repeat_patterns': []
            }
            
            for i in range(len(self.data) - window_size + 1):
                window_data = self.data.iloc[i:i+window_size]
                
                # 统计窗口内每个数字的出现频率
                number_frequencies = Counter()
                for _, row in window_data.iterrows():
                    for j in range(1, 7):
                        number_frequencies[row[f'数字{j}']] += 1
                
                # 找出重复出现的数字（出现次数>1）
                repeat_numbers = {num: freq for num, freq in number_frequencies.items() if freq > 1}
                
                if repeat_numbers:
                    window_stats['repeat_patterns'].append({
                        'start_period': window_data.iloc[0]['期号'],
                        'end_period': window_data.iloc[-1]['期号'],
                        'repeat_numbers': repeat_numbers,
                        'max_frequency': max(repeat_numbers.values()),
                        'total_repeats': sum(repeat_numbers.values()) - len(repeat_numbers)
                    })
                
                # 更新频率统计
                for freq in number_frequencies.values():
                    if freq > 1:
                        window_stats['repeat_frequencies'][freq] += 1
            
            # 计算统计指标
            windows_with_repeats = len(window_stats['repeat_patterns'])
            repeat_window_rate = windows_with_repeats / window_stats['total_windows']
            
            print(f"  总窗口数: {window_stats['total_windows']}")
            print(f"  有重复的窗口: {windows_with_repeats}")
            print(f"  重复窗口率: {repeat_window_rate:.3f} ({repeat_window_rate*100:.1f}%)")
            
            if window_stats['repeat_frequencies']:
                print(f"  重复频率分布:")
                for freq in sorted(window_stats['repeat_frequencies'].keys()):
                    count = window_stats['repeat_frequencies'][freq]
                    print(f"    出现{freq}次: {count}个数字")
            
            window_results[window_size] = window_stats
        
        return window_results
    
    def analyze_number_persistence(self):
        """分析数字的持续性模式"""
        print(f"\n🔍 分析数字的持续性模式")
        print("=" * 50)
        
        persistence_stats = {
            'number_streaks': defaultdict(list),  # 每个数字的连续出现序列
            'max_streaks': {},                    # 每个数字的最大连续长度
            'avg_streaks': {},                    # 每个数字的平均连续长度
            'total_appearances': Counter()        # 每个数字的总出现次数
        }
        
        # 为每个数字追踪连续出现
        for num in range(1, 50):
            current_streak = 0
            streaks = []
            
            for _, row in self.data.iterrows():
                period_numbers = [row[f'数字{j}'] for j in range(1, 7)]
                
                if num in period_numbers:
                    current_streak += 1
                    persistence_stats['total_appearances'][num] += 1
                else:
                    if current_streak > 0:
                        streaks.append(current_streak)
                        current_streak = 0
            
            # 处理最后一个连续序列
            if current_streak > 0:
                streaks.append(current_streak)
            
            if streaks:
                persistence_stats['number_streaks'][num] = streaks
                persistence_stats['max_streaks'][num] = max(streaks)
                persistence_stats['avg_streaks'][num] = np.mean(streaks)
            else:
                persistence_stats['max_streaks'][num] = 0
                persistence_stats['avg_streaks'][num] = 0
        
        # 分析结果
        print(f"📊 数字持续性统计:")
        
        # 最大连续出现
        max_streak_numbers = sorted(persistence_stats['max_streaks'].items(), 
                                  key=lambda x: x[1], reverse=True)[:10]
        print(f"\n🏆 最大连续出现次数 (Top 10):")
        for num, streak in max_streak_numbers:
            total_apps = persistence_stats['total_appearances'][num]
            print(f"  数字{num:2d}: 最大连续{streak}期, 总出现{total_apps}次")
        
        # 平均连续长度
        avg_streak_numbers = sorted([(num, avg) for num, avg in persistence_stats['avg_streaks'].items() if avg > 0], 
                                  key=lambda x: x[1], reverse=True)[:10]
        print(f"\n📈 平均连续长度 (Top 10):")
        for num, avg_streak in avg_streak_numbers:
            print(f"  数字{num:2d}: 平均连续{avg_streak:.2f}期")
        
        # 连续性分布
        all_streaks = []
        for streaks in persistence_stats['number_streaks'].values():
            all_streaks.extend(streaks)
        
        if all_streaks:
            streak_distribution = Counter(all_streaks)
            print(f"\n📊 连续长度分布:")
            for length in sorted(streak_distribution.keys())[:10]:
                count = streak_distribution[length]
                percentage = count / len(all_streaks) * 100
                print(f"  连续{length}期: {count}次 ({percentage:.1f}%)")
        
        return persistence_stats
    
    def analyze_cyclical_patterns(self, cycle_lengths=[7, 14, 21, 28]):
        """分析周期性重复模式"""
        print(f"\n🔍 分析周期性重复模式")
        print("=" * 50)
        
        cyclical_results = {}
        
        for cycle_length in cycle_lengths:
            print(f"\n📊 分析{cycle_length}期周期:")
            
            cycle_stats = {
                'cycle_length': cycle_length,
                'total_cycles': len(self.data) // cycle_length,
                'repeat_correlations': [],
                'phase_patterns': defaultdict(Counter)
            }
            
            # 分析每个周期位置的数字模式
            for i in range(len(self.data)):
                phase = i % cycle_length
                period_numbers = [self.data.iloc[i][f'数字{j}'] for j in range(1, 7)]
                
                for num in period_numbers:
                    cycle_stats['phase_patterns'][phase][num] += 1
            
            # 计算周期相关性
            correlations = []
            for phase in range(cycle_length):
                phase_numbers = cycle_stats['phase_patterns'][phase]
                if len(phase_numbers) > 0:
                    # 计算该阶段最常见的数字
                    top_numbers = phase_numbers.most_common(5)
                    correlations.append({
                        'phase': phase,
                        'top_numbers': top_numbers,
                        'total_occurrences': sum(phase_numbers.values())
                    })
            
            cycle_stats['repeat_correlations'] = correlations
            
            print(f"  总周期数: {cycle_stats['total_cycles']}")
            print(f"  周期阶段分析:")
            for corr in correlations[:5]:  # 显示前5个阶段
                phase = corr['phase']
                top_nums = corr['top_numbers'][:3]  # 显示前3个数字
                print(f"    阶段{phase}: {[f'{num}({count})' for num, count in top_nums]}")
            
            cyclical_results[cycle_length] = cycle_stats
        
        return cyclical_results
    
    def statistical_significance_test(self, repeat_stats):
        """统计显著性检验"""
        print(f"\n🔬 重复模式统计显著性检验")
        print("=" * 50)
        
        # 理论随机概率计算
        # 两期连续中有重复数字的概率
        total_numbers = 49
        numbers_per_period = 6
        
        # 使用超几何分布计算理论概率
        theoretical_probs = {}
        for k in range(1, 7):  # k个重复数字
            # P(恰好k个重复) = C(6,k) * C(43,6-k) / C(49,6)
            from math import comb
            prob = comb(6, k) * comb(43, 6-k) / comb(49, 6)
            theoretical_probs[k] = prob
        
        # 理论上至少1个重复的概率
        theoretical_at_least_one = sum(theoretical_probs.values())
        
        print(f"📊 理论概率分析:")
        print(f"  理论上至少1个重复的概率: {theoretical_at_least_one:.4f} ({theoretical_at_least_one*100:.2f}%)")
        
        # 实际观察概率
        observed_rate = repeat_stats['periods_with_repeats'] / repeat_stats['total_periods']
        print(f"  实际观察到的重复概率: {observed_rate:.4f} ({observed_rate*100:.2f}%)")
        
        # 卡方检验
        observed_distribution = []
        expected_distribution = []
        
        for k in range(0, 7):
            if k == 0:
                observed = repeat_stats['total_periods'] - repeat_stats['periods_with_repeats']
                expected = repeat_stats['total_periods'] * (1 - theoretical_at_least_one)
            else:
                observed = repeat_stats['repeat_distribution'].get(k, 0)
                expected = repeat_stats['total_periods'] * theoretical_probs.get(k, 0)
            
            observed_distribution.append(observed)
            expected_distribution.append(expected)
        
        # 执行卡方检验
        chi2_stat, p_value = stats.chisquare(observed_distribution, expected_distribution)
        
        print(f"\n🔬 卡方检验结果:")
        print(f"  卡方统计量: {chi2_stat:.4f}")
        print(f"  p值: {p_value:.6f}")
        
        if p_value < 0.05:
            print(f"  结论: ✅ 重复模式具有统计显著性 (p < 0.05)")
            print(f"  解释: 观察到的重复模式显著偏离随机期望")
        else:
            print(f"  结论: ❌ 重复模式不具有统计显著性 (p ≥ 0.05)")
            print(f"  解释: 观察到的重复模式可能是随机现象")
        
        return {
            'theoretical_prob': theoretical_at_least_one,
            'observed_prob': observed_rate,
            'chi2_stat': chi2_stat,
            'p_value': p_value,
            'is_significant': p_value < 0.05
        }
    
    def generate_repeat_mechanism_report(self):
        """生成重复机制分析报告"""
        print("\n" + "=" * 80)
        print("🧠 重复数值机制深度思辨分析报告")
        print("=" * 80)
        
        # 1. 连续期重复分析
        immediate_repeats = self.analyze_immediate_repeats()
        
        # 2. 时间窗口重复分析
        window_repeats = self.analyze_window_repeats()
        
        # 3. 数字持续性分析
        persistence_patterns = self.analyze_number_persistence()
        
        # 4. 周期性模式分析
        cyclical_patterns = self.analyze_cyclical_patterns()
        
        # 5. 统计显著性检验
        significance_test = self.statistical_significance_test(immediate_repeats)
        
        # 生成综合分析
        print(f"\n🎯 重复机制综合分析")
        print("=" * 50)
        
        print(f"📊 核心发现:")
        print(f"  1. 连续期重复率: {immediate_repeats['periods_with_repeats']/immediate_repeats['total_periods']*100:.1f}%")
        print(f"  2. 平均重复数字: {immediate_repeats['total_repeat_numbers']/immediate_repeats['total_periods']:.2f}个/期")
        print(f"  3. 统计显著性: {'显著' if significance_test['is_significant'] else '不显著'}")
        print(f"  4. 理论vs实际: {significance_test['theoretical_prob']*100:.1f}% vs {significance_test['observed_prob']*100:.1f}%")
        
        return {
            'immediate_repeats': immediate_repeats,
            'window_repeats': window_repeats,
            'persistence_patterns': persistence_patterns,
            'cyclical_patterns': cyclical_patterns,
            'significance_test': significance_test
        }

def main():
    """主函数"""
    print("🧠 重复数值机制深度思辨分析")
    print("基于历史多期数据的重复模式研究")
    print("=" * 60)
    
    analyzer = RepeatPatternAnalyzer()
    
    if not analyzer.load_data():
        return
    
    # 执行完整的重复机制分析
    results = analyzer.generate_repeat_mechanism_report()
    
    print(f"\n🎉 重复机制分析完成！")
    print(f"📊 分析了 {len(analyzer.data)} 期历史数据")
    print(f"🔍 发现了多种重复模式和机制")

if __name__ == "__main__":
    main()
