#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无偏评分算法
Unbiased scoring algorithm to avoid overfitting and data leakage
"""

import numpy as np
import pandas as pd


def calculate_unbiased_score(predicted_numbers, confidence, current_period):
    """
    计算无偏评分（避免数据泄露和过拟合）
    """
    pred_num1, pred_num2 = predicted_numbers
    
    # 1. 基础评分（只基于置信度）
    base_score = confidence * 1000
    
    # 2. 简化的数字特征（避免过度优化）
    # 使用更保守的调整系数
    
    # 数字和特征（基于数学期望，不依赖历史数据）
    num_sum = pred_num1 + pred_num2
    if 30 <= num_sum <= 80:  # 更宽泛的范围
        base_score *= 1.1  # 更保守的加成
    
    # 数字差值特征
    num_diff = abs(pred_num1 - pred_num2)
    if 5 <= num_diff <= 30:  # 更宽泛的范围
        base_score *= 1.05  # 更保守的加成
    
    # 3. 动态历史表现（只使用当前期之前的数据）
    # 这里应该基于滚动窗口计算，而不是固定的高频数字列表
    
    # 4. 限制分数范围并添加随机性（减少过拟合）
    final_score = max(15, min(85, base_score))  # 更保守的分数范围
    
    # 添加小量随机噪声，避免过度自信
    noise = np.random.normal(0, 2)  # 2分的标准差
    final_score = max(10, min(90, final_score + noise))
    
    return final_score


def get_dynamic_high_freq_numbers(historical_data, current_period, window_size=50):
    """
    动态获取高频数字（只使用历史数据）
    """
    # 只使用当前期之前的数据
    hist_data = historical_data[historical_data['期号'] < current_period]
    
    # 使用滚动窗口
    recent_data = hist_data.tail(window_size)
    
    # 统计数字频率
    all_numbers = []
    for _, row in recent_data.iterrows():
        all_numbers.extend([
            row['数字1'], row['数字2'], row['数字3'],
            row['数字4'], row['数字5'], row['数字6']
        ])
    
    from collections import Counter
    freq_counter = Counter(all_numbers)
    
    # 返回前10个高频数字
    return [num for num, _ in freq_counter.most_common(10)]

def calculate_rolling_performance_score(predicted_numbers, confidence, 
                                      historical_data, current_period):
    """
    基于滚动窗口的性能评分
    """
    # 获取动态高频数字
    high_freq_nums = get_dynamic_high_freq_numbers(historical_data, current_period)
    
    # 基础评分
    score = calculate_unbiased_score(predicted_numbers, confidence, current_period)
    
    # 动态调整（基于历史表现）
    pred_num1, pred_num2 = predicted_numbers
    
    # 如果预测数字在近期高频列表中，给予小幅加成
    if pred_num1 in high_freq_nums[:5] or pred_num2 in high_freq_nums[:5]:
        score *= 1.05  # 很小的加成，避免过拟合
    
    return min(90, max(10, score))

if __name__ == "__main__":
    print("无偏评分算法模块已创建")
