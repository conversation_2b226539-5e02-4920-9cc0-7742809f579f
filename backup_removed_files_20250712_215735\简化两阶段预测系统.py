#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化两阶段数字选择策略分析系统
基于Occam's razor原则，采用最简单的两阶段方法
阶段1：直接使用马尔可夫方法获取概率排序
阶段2：简单的概率加权选择
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class SimpleTwoStagePredictor:
    """
    简化两阶段预测系统
    遵循Occam's razor原则，保持最大简洁性
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.data = None
        self.train_data = None
        self.test_data = None
        self.transition_prob = {}
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            print(f"✅ 数据加载成功: {len(self.data)}期")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def prepare_data_split(self):
        """准备训练和测试数据"""
        print("\n📊 准备数据分割")
        print("=" * 50)
        
        self.train_data = self.data[(self.data['年份'] >= 2023) & (self.data['年份'] < 2025)].copy()
        self.test_data = self.data[(self.data['年份'] == 2025) & (self.data['期号'] <= 182)].copy()
        
        print(f"训练数据: {len(self.train_data)}期 (2023-2024年)")
        print(f"测试数据: {len(self.test_data)}期 (2025年1-182期)")
        
        return len(self.train_data) > 0 and len(self.test_data) > 0
    
    def build_markov_model(self):
        """构建马尔可夫模型"""
        print("\n🔬 构建马尔可夫模型")
        
        transition_count = defaultdict(lambda: defaultdict(int))
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        self.transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                self.transition_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        print(f"✅ 马尔可夫模型构建完成，状态数量: {len(self.transition_prob)}")
        return True
    
    def get_number_probabilities(self, previous_numbers):
        """获取所有数字的概率分布"""
        number_probs = defaultdict(float)
        total_prob = 0.0
        
        # 基于马尔可夫转移概率计算
        for prev_num in previous_numbers:
            if prev_num in self.transition_prob:
                for next_num, prob in self.transition_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        # 归一化概率
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        # 对于没有转移概率的数字，给予小的基础概率
        for num in range(1, 50):
            if num not in number_probs:
                number_probs[num] = 0.001
        
        return dict(number_probs)
    
    def stage1_get_candidates(self, previous_numbers, num_candidates=12):
        """阶段1：获取候选数字（基于概率排序）"""
        number_probs = self.get_number_probabilities(previous_numbers)
        
        # 按概率排序，选择前num_candidates个
        sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
        candidates = [num for num, prob in sorted_numbers[:num_candidates]]
        
        # 计算候选数字的平均概率作为置信度
        avg_prob = np.mean([prob for num, prob in sorted_numbers[:num_candidates]])
        
        return candidates, avg_prob
    
    def stage2_select_final(self, candidates, candidate_probs, num_final=2):
        """阶段2：从候选中选择最终数字（简单概率加权）"""
        if len(candidates) < num_final:
            return candidates + list(range(1, num_final - len(candidates) + 1)), 0.1
        
        # 获取候选数字的概率
        candidate_prob_dict = {num: candidate_probs.get(num, 0.001) for num in candidates}
        
        # 简单策略：直接选择概率最高的2个
        sorted_candidates = sorted(candidate_prob_dict.items(), key=lambda x: x[1], reverse=True)
        final_numbers = [num for num, prob in sorted_candidates[:num_final]]
        
        # 计算置信度
        final_probs = [prob for num, prob in sorted_candidates[:num_final]]
        confidence = np.mean(final_probs) if final_probs else 0.1
        
        return final_numbers, confidence
    
    def predict_single_period(self, previous_numbers):
        """预测单期的2个数字"""
        # 获取所有数字的概率分布
        all_probs = self.get_number_probabilities(previous_numbers)
        
        # 阶段1：获取12个候选数字
        candidates, stage1_confidence = self.stage1_get_candidates(previous_numbers, 12)
        
        # 阶段2：选择最终2个数字
        final_2, stage2_confidence = self.stage2_select_final(candidates, all_probs, 2)
        
        # 综合置信度
        overall_confidence = (stage1_confidence + stage2_confidence) / 2
        
        return {
            'final_prediction': final_2,
            'candidates': candidates,
            'all_probabilities': all_probs,
            'stage1_confidence': stage1_confidence,
            'stage2_confidence': stage2_confidence,
            'overall_confidence': overall_confidence
        }
    
    def validate_simple_method(self):
        """验证简化两阶段方法"""
        print("\n🔬 验证简化两阶段预测方法")
        print("=" * 50)
        
        predictions = []
        correct_predictions = 0
        stage1_hits = 0
        direct_2_hits = 0  # 直接选择前2个数字的命中数
        
        for idx, test_row in self.test_data.iterrows():
            period_num = test_row['期号']
            actual_numbers = set([test_row[f'数字{j}'] for j in range(1, 7)])
            
            # 获取前一期数字
            if idx == self.test_data.index[0]:
                prev_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                prev_idx = self.test_data.index[self.test_data.index.get_loc(idx) - 1]
                prev_numbers = set([self.test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            # 预测
            result = self.predict_single_period(prev_numbers)
            
            # 直接选择概率最高的2个数字（对比基准）
            sorted_all = sorted(result['all_probabilities'].items(), key=lambda x: x[1], reverse=True)
            direct_2 = [num for num, prob in sorted_all[:2]]
            
            # 评估
            stage1_hit_count = len(set(result['candidates']) & actual_numbers)
            stage1_hits += stage1_hit_count
            
            final_hit_count = len(set(result['final_prediction']) & actual_numbers)
            is_success = final_hit_count >= 1
            
            direct_hit_count = len(set(direct_2) & actual_numbers)
            direct_success = direct_hit_count >= 1
            
            if is_success:
                correct_predictions += 1
            if direct_success:
                direct_2_hits += 1
            
            predictions.append({
                'period': period_num,
                'predicted_2': result['final_prediction'],
                'direct_2': direct_2,
                'candidates_12': result['candidates'],
                'actual': list(actual_numbers),
                'stage1_hits': stage1_hit_count,
                'final_hits': final_hit_count,
                'direct_hits': direct_hit_count,
                'success': is_success,
                'direct_success': direct_success,
                'overall_confidence': result['overall_confidence']
            })
        
        # 计算性能指标
        total_periods = len(predictions)
        success_rate = correct_predictions / total_periods
        direct_success_rate = direct_2_hits / total_periods
        avg_stage1_hits = stage1_hits / total_periods
        
        print(f"✅ 简化两阶段方法验证完成")
        print(f"  测试期数: {total_periods}")
        print(f"  阶段1平均命中: {avg_stage1_hits:.2f}/6 ({avg_stage1_hits/6*100:.1f}%)")
        print(f"  两阶段最终成功率: {success_rate:.3f} ({success_rate*100:.1f}%)")
        print(f"  直接选择2数成功率: {direct_success_rate:.3f} ({direct_success_rate*100:.1f}%)")
        print(f"  基准性能: 29.2%")
        print(f"  两阶段vs基准: {(success_rate - 0.292)*100:+.1f}个百分点")
        print(f"  直接选择vs基准: {(direct_success_rate - 0.292)*100:+.1f}个百分点")
        
        return {
            'predictions': predictions,
            'two_stage_success_rate': success_rate,
            'direct_success_rate': direct_success_rate,
            'avg_stage1_hits': avg_stage1_hits,
            'two_stage_improvement': success_rate - 0.292,
            'direct_improvement': direct_success_rate - 0.292
        }

def main():
    """主函数"""
    print("🎯 简化两阶段数字选择策略分析系统")
    print("基于Occam's razor原则的最简化实现")
    print("=" * 60)
    
    # 初始化系统
    predictor = SimpleTwoStagePredictor()
    
    # 1. 加载数据
    if not predictor.load_data():
        return
    
    # 2. 准备数据分割
    if not predictor.prepare_data_split():
        return
    
    # 3. 构建马尔可夫模型
    if not predictor.build_markov_model():
        return
    
    # 4. 验证简化方法
    results = predictor.validate_simple_method()
    
    if results:
        # 5. 统计显著性检验
        baseline_rate = 0.292
        n = len(results['predictions'])
        
        # 检验两阶段方法
        two_stage_rate = results['two_stage_success_rate']
        try:
            result_2stage = stats.binomtest(int(two_stage_rate * n), n, baseline_rate, alternative='greater')
            p_value_2stage = result_2stage.pvalue
        except AttributeError:
            from scipy.stats import binom
            p_value_2stage = 1 - binom.cdf(int(two_stage_rate * n) - 1, n, baseline_rate)
        
        # 检验直接选择方法
        direct_rate = results['direct_success_rate']
        try:
            result_direct = stats.binomtest(int(direct_rate * n), n, baseline_rate, alternative='greater')
            p_value_direct = result_direct.pvalue
        except AttributeError:
            p_value_direct = 1 - binom.cdf(int(direct_rate * n) - 1, n, baseline_rate)
        
        print(f"\n📊 统计显著性检验")
        print(f"  基准成功率: {baseline_rate:.3f}")
        print(f"  样本数量: {n}")
        print(f"\n  两阶段方法:")
        print(f"    观察成功率: {two_stage_rate:.3f}")
        print(f"    p值: {p_value_2stage:.4f}")
        print(f"    显著性(p<0.05): {'是' if p_value_2stage < 0.05 else '否'}")
        print(f"\n  直接选择方法:")
        print(f"    观察成功率: {direct_rate:.3f}")
        print(f"    p值: {p_value_direct:.4f}")
        print(f"    显著性(p<0.05): {'是' if p_value_direct < 0.05 else '否'}")
        
        # 6. 保存结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f"简化两阶段预测验证结果_{timestamp}.csv"
        summary_file = f"简化两阶段预测摘要_{timestamp}.txt"
        
        df_results = pd.DataFrame(results['predictions'])
        df_results.to_csv(results_file, index=False, encoding='utf-8')
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(f"简化两阶段预测方法验证摘要\n")
            f.write(f"=" * 40 + "\n")
            f.write(f"两阶段成功率: {results['two_stage_success_rate']:.3f} ({results['two_stage_success_rate']*100:.1f}%)\n")
            f.write(f"直接选择成功率: {results['direct_success_rate']:.3f} ({results['direct_success_rate']*100:.1f}%)\n")
            f.write(f"阶段1平均命中: {results['avg_stage1_hits']:.2f}/6\n")
            f.write(f"两阶段相对基准: {results['two_stage_improvement']*100:+.1f}个百分点\n")
            f.write(f"直接选择相对基准: {results['direct_improvement']*100:+.1f}个百分点\n")
            f.write(f"两阶段p值: {float(p_value_2stage):.4f}\n")
            f.write(f"直接选择p值: {float(p_value_direct):.4f}\n")
        
        print(f"\n✅ 结果已保存:")
        print(f"  详细结果: {results_file}")
        print(f"  摘要结果: {summary_file}")
        
        # 7. 结论
        print(f"\n🎯 简化两阶段预测方法分析结论")
        print("=" * 50)
        
        if results['direct_improvement'] > results['two_stage_improvement']:
            print("📊 关键发现：直接选择概率最高的2个数字比两阶段方法更有效")
            if results['direct_improvement'] > 0 and p_value_direct < 0.05:
                print("✅ 直接选择方法显著优于基准")
            elif results['direct_improvement'] > 0:
                print("⚠️ 直接选择方法略优于基准，但未达到统计显著性")
            else:
                print("❌ 直接选择方法未能改善基准性能")
        else:
            if results['two_stage_improvement'] > 0 and p_value_2stage < 0.05:
                print("✅ 两阶段方法显著优于基准")
            elif results['two_stage_improvement'] > 0:
                print("⚠️ 两阶段方法略优于基准，但未达到统计显著性")
            else:
                print("❌ 两阶段方法未能改善基准性能")
        
        print(f"\n💡 核心洞察：")
        print(f"  两阶段方法的理论假设可能存在问题")
        print(f"  增加复杂性并不一定带来性能提升")
        print(f"  简单的概率排序可能已经是最优策略")

if __name__ == "__main__":
    main()
