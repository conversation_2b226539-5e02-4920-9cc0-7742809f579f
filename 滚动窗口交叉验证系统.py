#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
滚动窗口交叉验证系统
实施严格的滚动窗口验证，检验29.2%马尔可夫基线的稳健性
使用2年训练窗口预测6个月，在2023-2025年期间进行多窗口验证
"""

import pandas as pd
import numpy as np
import json
from collections import defaultdict
from datetime import datetime, timedelta
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class RollingWindowValidator:
    """
    滚动窗口交叉验证器
    检验马尔可夫预测系统的时间稳健性
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.data = None
        
        # 验证配置
        self.train_window_periods = 731  # 2年训练窗口
        self.test_window_periods = 90   # 6个月测试窗口
        self.step_size = 15             # 1个月滚动步长
        
        # 理论基准
        self.theoretical_baseline = 0.232143  # 23.214%
        
        self.validation_results = []
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            # 添加全局期号用于滚动窗口
            self.data['global_period'] = range(len(self.data))
            
            print(f"✅ 数据加载成功: {len(self.data)}期")
            print(f"  数据范围: {self.data['年份'].min()}年-{self.data['年份'].max()}年")
            print(f"  期号范围: {self.data['期号'].min()}-{self.data['期号'].max()}期")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def build_markov_model(self, train_data):
        """构建马尔可夫转移矩阵"""
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(train_data) - 1):
            current_numbers = set([train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 转换为概率
        transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            if total > 0:
                transition_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transition_count[curr_num].items()
                }
        
        return transition_prob
    
    def predict_next_period(self, previous_numbers, transition_prob):
        """预测下一期的2个数字"""
        if not transition_prob:
            return [1, 2], 0.0
        
        # 计算下一期各数字的概率
        number_probs = defaultdict(float)
        total_prob = 0.0
        
        for prev_num in previous_numbers:
            if prev_num in transition_prob:
                for next_num, prob in transition_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        # 归一化概率
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        # 选择概率最高的2个数字
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            predicted_2digits = [num for num, prob in sorted_numbers[:2]]
            confidence = np.mean([prob for num, prob in sorted_numbers[:2]])
        else:
            # 备选方案
            predicted_2digits = [1, 2]
            confidence = 0.1
        
        return predicted_2digits, confidence
    
    def validate_single_window(self, window_id, train_start, train_end, test_start, test_end):
        """验证单个滚动窗口"""
        # 获取训练和测试数据
        train_data = self.data.iloc[train_start:train_end].copy()
        test_data = self.data.iloc[test_start:test_end].copy()
        
        if len(train_data) < 100 or len(test_data) < 10:
            return None
        
        # 构建马尔可夫模型
        transition_prob = self.build_markov_model(train_data)
        
        if not transition_prob:
            return None
        
        # 预测和评估
        predictions = []
        correct_predictions = 0
        
        for idx, test_row in test_data.iterrows():
            period_num = test_row['期号']
            year = test_row['年份']
            actual_numbers = set([test_row[f'数字{j}'] for j in range(1, 7)])
            
            # 获取前一期数字
            if idx == test_data.index[0]:
                # 使用训练数据的最后一期
                prev_numbers = set([train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                # 使用测试数据中的前一期
                prev_idx = test_data.index[test_data.index.get_loc(idx) - 1]
                prev_numbers = set([test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            # 预测
            predicted_numbers, confidence = self.predict_next_period(prev_numbers, transition_prob)
            
            # 评估
            hit_count = len(set(predicted_numbers) & actual_numbers)
            is_success = hit_count >= 1
            
            if is_success:
                correct_predictions += 1
            
            predictions.append({
                'year': year,
                'period': period_num,
                'predicted': predicted_numbers,
                'actual': list(actual_numbers),
                'hits': hit_count,
                'success': is_success,
                'confidence': confidence
            })
        
        # 计算性能指标
        success_rate = correct_predictions / len(predictions) if len(predictions) > 0 else 0
        
        return {
            'window_id': window_id,
            'train_start_period': train_data.iloc[0]['期号'],
            'train_end_period': train_data.iloc[-1]['期号'],
            'train_start_year': train_data.iloc[0]['年份'],
            'train_end_year': train_data.iloc[-1]['年份'],
            'test_start_period': test_data.iloc[0]['期号'],
            'test_end_period': test_data.iloc[-1]['期号'],
            'test_start_year': test_data.iloc[0]['年份'],
            'test_end_year': test_data.iloc[-1]['年份'],
            'train_periods': len(train_data),
            'test_periods': len(test_data),
            'markov_states': len(transition_prob),
            'success_rate': success_rate,
            'correct_predictions': correct_predictions,
            'total_predictions': len(predictions),
            'predictions': predictions[:5]  # 保存前5个预测作为样本
        }
    
    def run_rolling_window_validation(self):
        """运行滚动窗口验证"""
        print(f"\n🔄 滚动窗口交叉验证")
        print("=" * 60)
        print(f"  训练窗口: {self.train_window_periods}期 (约2年)")
        print(f"  测试窗口: {self.test_window_periods}期 (约6个月)")
        print(f"  滚动步长: {self.step_size}期 (约1个月)")
        
        # 确定验证范围
        min_start = self.train_window_periods
        max_start = len(self.data) - self.train_window_periods - self.test_window_periods
        
        window_id = 0
        for start_idx in range(min_start, max_start, self.step_size):
            train_start = start_idx - self.train_window_periods
            train_end = start_idx
            test_start = start_idx
            test_end = min(start_idx + self.test_window_periods, len(self.data))
            
            if test_end - test_start < 30:  # 确保测试窗口足够大
                break
            
            result = self.validate_single_window(window_id, train_start, train_end, test_start, test_end)
            
            if result:
                self.validation_results.append(result)
                print(f"  窗口{window_id}: {result['train_start_year']}-{result['train_end_year']}训练 → "
                      f"{result['test_start_year']}年{result['test_start_period']}-{result['test_end_period']}期测试 "
                      f"({result['success_rate']:.3f})")
                window_id += 1
        
        print(f"\n✅ 滚动窗口验证完成，共{len(self.validation_results)}个窗口")
        
    def analyze_stability(self):
        """分析性能稳定性"""
        print(f"\n📊 性能稳定性分析")
        print("=" * 60)
        
        if not self.validation_results:
            print("❌ 无验证结果")
            return None
        
        success_rates = [result['success_rate'] for result in self.validation_results]
        
        # 基本统计
        mean_rate = np.mean(success_rates)
        std_rate = np.std(success_rates)
        min_rate = np.min(success_rates)
        max_rate = np.max(success_rates)
        
        print(f"  验证窗口数: {len(success_rates)}")
        print(f"  平均成功率: {mean_rate:.3f} ({mean_rate*100:.1f}%)")
        print(f"  标准差: {std_rate:.3f}")
        print(f"  最小值: {min_rate:.3f} ({min_rate*100:.1f}%)")
        print(f"  最大值: {max_rate:.3f} ({max_rate*100:.1f}%)")
        print(f"  变异系数: {std_rate/mean_rate:.3f}")
        
        # 与单一测试集对比
        single_test_performance = 0.292  # 原始2025年测试结果
        print(f"\n与单一测试集对比:")
        print(f"  单一测试集性能: {single_test_performance:.3f}")
        print(f"  滚动验证平均: {mean_rate:.3f}")
        print(f"  差异: {(mean_rate - single_test_performance)*100:+.1f}个百分点")
        
        # 与理论基准对比
        print(f"\n与理论基准对比:")
        print(f"  理论基准: {self.theoretical_baseline:.3f}")
        print(f"  滚动验证平均: {mean_rate:.3f}")
        print(f"  优势: {(mean_rate - self.theoretical_baseline)*100:+.1f}个百分点")
        
        # 统计显著性检验
        t_stat, p_value = stats.ttest_1samp(success_rates, self.theoretical_baseline)
        
        print(f"\n统计显著性检验 (vs理论基准):")
        print(f"  t统计量: {t_stat:.3f}")
        print(f"  p值: {p_value:.6f}")
        print(f"  显著性(p<0.05): {'是' if p_value < 0.05 else '否'}")
        
        # 稳定性评估
        if std_rate / mean_rate < 0.1:
            stability = "高稳定性"
        elif std_rate / mean_rate < 0.2:
            stability = "中等稳定性"
        else:
            stability = "低稳定性"
        
        print(f"\n稳定性评估: {stability}")
        
        return {
            'window_count': len(success_rates),
            'mean_success_rate': mean_rate,
            'std_success_rate': std_rate,
            'min_success_rate': min_rate,
            'max_success_rate': max_rate,
            'coefficient_of_variation': std_rate / mean_rate,
            'single_test_comparison': mean_rate - single_test_performance,
            'theoretical_advantage': mean_rate - self.theoretical_baseline,
            'statistical_test': {
                't_statistic': t_stat,
                'p_value': p_value,
                'significant': p_value < 0.05
            },
            'stability_assessment': stability,
            'all_success_rates': success_rates
        }
    
    def generate_comprehensive_report(self):
        """生成综合报告"""
        print(f"\n📋 生成综合验证报告")
        print("=" * 60)
        
        stability_analysis = self.analyze_stability()
        
        if not stability_analysis:
            return None
        
        # 保存详细结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f"滚动窗口交叉验证结果_{timestamp}.json"
        
        # 处理numpy类型
        def convert_numpy_types(obj):
            if isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(v) for v in obj]
            elif hasattr(obj, 'item'):
                return obj.item()
            elif isinstance(obj, (np.bool_, bool)):
                return bool(obj)
            elif isinstance(obj, (np.integer, int)):
                return int(obj)
            elif isinstance(obj, (np.floating, float)):
                return float(obj)
            else:
                return obj
        
        comprehensive_results = {
            'validation_config': {
                'train_window_periods': self.train_window_periods,
                'test_window_periods': self.test_window_periods,
                'step_size': self.step_size,
                'theoretical_baseline': self.theoretical_baseline
            },
            'stability_analysis': convert_numpy_types(stability_analysis),
            'detailed_results': convert_numpy_types(self.validation_results),
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(comprehensive_results, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 综合验证结果已保存: {results_file}")
        
        return comprehensive_results

def main():
    """主函数"""
    print("🎯 滚动窗口交叉验证系统")
    print("检验马尔可夫预测系统的时间稳健性")
    print("=" * 70)
    
    # 初始化验证器
    validator = RollingWindowValidator()
    
    # 1. 加载数据
    if not validator.load_data():
        return
    
    # 2. 运行滚动窗口验证
    validator.run_rolling_window_validation()
    
    # 3. 生成综合报告
    results = validator.generate_comprehensive_report()
    
    if results:
        # 4. 关键结论
        stability = results['stability_analysis']
        
        print(f"\n🎉 滚动窗口验证关键结论")
        print("=" * 50)
        print(f"✅ 验证窗口: {stability['window_count']}个")
        print(f"✅ 平均性能: {stability['mean_success_rate']:.3f} ({stability['mean_success_rate']*100:.1f}%)")
        print(f"✅ 性能稳定性: {stability['stability_assessment']}")
        print(f"✅ 理论优势: +{stability['theoretical_advantage']*100:.1f}个百分点")
        print(f"✅ 统计显著性: {'是' if stability['statistical_test']['significant'] else '否'}")
        
        print(f"\n💡 核心洞察:")
        if stability['mean_success_rate'] > 0.29:
            print(f"  1. 滚动验证确认了马尔可夫基线的优秀性能")
        elif stability['mean_success_rate'] > 0.25:
            print(f"  1. 滚动验证显示性能略低于单一测试集")
        else:
            print(f"  1. 滚动验证揭示了性能的不稳定性")
        
        print(f"  2. 变异系数{stability['coefficient_of_variation']:.3f}反映了{'高' if stability['coefficient_of_variation'] < 0.1 else '中等' if stability['coefficient_of_variation'] < 0.2 else '低'}稳定性")
        print(f"  3. 相对于理论基准的优势得到{'验证' if stability['statistical_test']['significant'] else '部分验证'}")
        print(f"  4. 为预测系统的实际部署提供了{'可靠' if stability['coefficient_of_variation'] < 0.15 else '需谨慎考虑'}的依据")

if __name__ == "__main__":
    main()
