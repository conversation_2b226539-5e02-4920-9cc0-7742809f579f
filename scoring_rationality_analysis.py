#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评分系统与命中率相互合理性思辨分析
Critical analysis of scoring system and hit rate rationality
"""

import pandas as pd
import numpy as np
from scipy import stats
import matplotlib.pyplot as plt

def load_and_analyze_current_data():
    """加载并分析当前数据"""
    print("📊 当前数据深度分析")
    print("="*60)
    
    try:
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        
        # 过滤有效数据
        valid_data = df[
            df['是否命中'].notna() & 
            (df['是否命中'] != '') &
            pd.to_numeric(df['预测评分'], errors='coerce').notna()
        ].copy()
        
        valid_data['评分数值'] = pd.to_numeric(valid_data['预测评分'], errors='coerce')
        
        print(f"有效分析数据: {len(valid_data)}条")
        
        # 基础统计
        scores = valid_data['评分数值']
        print(f"评分统计:")
        print(f"   范围: {scores.min():.1f} - {scores.max():.1f}")
        print(f"   均值: {scores.mean():.1f}")
        print(f"   标准差: {scores.std():.1f}")
        
        # 命中率统计
        hit_count = len(valid_data[valid_data['是否命中'] == '是'])
        overall_hit_rate = hit_count / len(valid_data) * 100
        print(f"整体命中率: {overall_hit_rate:.1f}% ({hit_count}/{len(valid_data)})")
        
        return valid_data
        
    except Exception as e:
        print(f"❌ 数据分析失败: {e}")
        return None

def analyze_score_hit_correlation(data):
    """分析评分与命中率的相关性"""
    print(f"\n🔍 评分与命中率相关性分析")
    print("="*50)
    
    try:
        # 转换命中状态为数值
        data['命中数值'] = data['是否命中'].map({'是': 1, '否': 0})
        
        # 计算相关系数
        correlation = data['评分数值'].corr(data['命中数值'])
        print(f"皮尔逊相关系数: {correlation:.4f}")
        
        # 相关性解读
        if abs(correlation) < 0.1:
            correlation_strength = "几乎无相关"
        elif abs(correlation) < 0.3:
            correlation_strength = "弱相关"
        elif abs(correlation) < 0.5:
            correlation_strength = "中等相关"
        elif abs(correlation) < 0.7:
            correlation_strength = "强相关"
        else:
            correlation_strength = "很强相关"
        
        print(f"相关性强度: {correlation_strength}")
        
        # 统计显著性检验
        correlation_stat, p_value = stats.pearsonr(data['评分数值'], data['命中数值'])
        print(f"统计显著性: p-value = {p_value:.4f}")
        
        if p_value < 0.05:
            print("✅ 相关性统计显著 (p < 0.05)")
        else:
            print("❌ 相关性不显著 (p ≥ 0.05)")
        
        return correlation, p_value
        
    except Exception as e:
        print(f"❌ 相关性分析失败: {e}")
        return None, None

def analyze_score_distribution_rationality(data):
    """分析评分分布的合理性"""
    print(f"\n📈 评分分布合理性分析")
    print("="*40)
    
    try:
        scores = data['评分数值']
        
        # 分布特征
        print(f"分布特征:")
        print(f"   偏度 (Skewness): {stats.skew(scores):.3f}")
        print(f"   峰度 (Kurtosis): {stats.kurtosis(scores):.3f}")
        
        # 正态性检验
        shapiro_stat, shapiro_p = stats.shapiro(scores)
        print(f"   正态性检验: p-value = {shapiro_p:.4f}")
        
        if shapiro_p > 0.05:
            print("   ✅ 评分分布接近正态分布")
        else:
            print("   ⚠️ 评分分布偏离正态分布")
        
        # 分析评分集中度
        q25, q50, q75 = np.percentile(scores, [25, 50, 75])
        iqr = q75 - q25
        
        print(f"   四分位数: Q1={q25:.1f}, Q2={q50:.1f}, Q3={q75:.1f}")
        print(f"   四分位距: {iqr:.1f}")
        
        # 评估评分区分度
        unique_scores = len(scores.unique())
        score_range = scores.max() - scores.min()
        
        print(f"   唯一评分数: {unique_scores}")
        print(f"   评分范围: {score_range:.1f}")
        print(f"   平均区分度: {score_range/unique_scores:.2f}分/级")
        
        # 合理性评估
        print(f"\n合理性评估:")
        
        # 1. 区分度是否足够
        if score_range > 15:
            print("   ✅ 评分范围充足，有良好区分度")
        else:
            print("   ⚠️ 评分范围较窄，区分度有限")
        
        # 2. 分布是否过于集中
        concentration = (scores >= q25) & (scores <= q75)
        concentration_pct = concentration.sum() / len(scores) * 100
        
        if concentration_pct < 60:
            print("   ✅ 评分分布较为分散")
        elif concentration_pct < 80:
            print("   ⚠️ 评分分布适中集中")
        else:
            print("   ❌ 评分分布过于集中")
        
        return True
        
    except Exception as e:
        print(f"❌ 分布分析失败: {e}")
        return False

def analyze_hit_rate_by_score_ranges(data):
    """按评分区间分析命中率的合理性"""
    print(f"\n🎯 按评分区间分析命中率合理性")
    print("="*50)
    
    try:
        scores = data['评分数值']
        
        # 动态创建评分区间
        min_score = scores.min()
        max_score = scores.max()
        
        # 创建5个等间距区间
        intervals = np.linspace(min_score, max_score, 6)
        
        print(f"评分区间分析:")
        print(f"{'区间':<15} {'预测数':<8} {'命中数':<8} {'命中率':<8} {'期望命中率':<12}")
        print("-" * 65)
        
        hit_rates = []
        expected_rates = []
        
        for i in range(len(intervals)-1):
            start = intervals[i]
            end = intervals[i+1]
            
            # 获取区间数据
            if i == len(intervals)-2:  # 最后一个区间包含最大值
                interval_data = data[(data['评分数值'] >= start) & (data['评分数值'] <= end)]
            else:
                interval_data = data[(data['评分数值'] >= start) & (data['评分数值'] < end)]
            
            if len(interval_data) > 0:
                hit_count = len(interval_data[interval_data['是否命中'] == '是'])
                hit_rate = hit_count / len(interval_data) * 100
                
                # 基于评分计算期望命中率（假设评分反映真实概率）
                avg_score = interval_data['评分数值'].mean()
                expected_rate = avg_score  # 假设评分直接对应命中概率
                
                hit_rates.append(hit_rate)
                expected_rates.append(expected_rate)
                
                interval_name = f"{start:.1f}-{end:.1f}"
                print(f"{interval_name:<15} {len(interval_data):<8} {hit_count:<8} {hit_rate:<7.1f}% {expected_rate:<11.1f}%")
        
        # 分析期望与实际的差异
        if len(hit_rates) > 0 and len(expected_rates) > 0:
            print(f"\n期望vs实际分析:")
            
            # 计算平均偏差
            deviations = [abs(actual - expected) for actual, expected in zip(hit_rates, expected_rates)]
            avg_deviation = np.mean(deviations)
            
            print(f"   平均偏差: {avg_deviation:.1f}%")
            
            if avg_deviation < 5:
                print("   ✅ 评分与实际命中率高度一致")
            elif avg_deviation < 10:
                print("   ⚠️ 评分与实际命中率基本一致")
            else:
                print("   ❌ 评分与实际命中率存在较大偏差")
            
            # 检查是否存在单调性
            hit_rate_trend = np.corrcoef(range(len(hit_rates)), hit_rates)[0,1]
            print(f"   命中率趋势相关性: {hit_rate_trend:.3f}")
            
            if hit_rate_trend > 0.5:
                print("   ✅ 高分预测确实有更高命中率")
            elif hit_rate_trend > 0:
                print("   ⚠️ 高分预测命中率略高")
            else:
                print("   ❌ 高分预测命中率未必更高")
        
        return hit_rates, expected_rates
        
    except Exception as e:
        print(f"❌ 区间分析失败: {e}")
        return [], []

def philosophical_analysis():
    """哲学层面的思辨分析"""
    print(f"\n🤔 哲学层面思辨分析")
    print("="*50)
    
    print("1. 评分系统的本质问题:")
    print("   🎯 评分是对未来不确定性的量化尝试")
    print("   🎯 但彩票本质上是随机事件")
    print("   🎯 评分能否真正预测随机性？")
    
    print(f"\n2. 合理性的多重标准:")
    print("   📊 统计合理性: 评分与命中率是否相关")
    print("   🧠 认知合理性: 评分是否符合人类直觉")
    print("   💰 实用合理性: 评分是否有实际价值")
    print("   🔬 科学合理性: 评分是否基于可验证的方法")
    
    print(f"\n3. 悖论与矛盾:")
    print("   ⚖️ 保守悖论: 过于保守失去价值，过于乐观不可信")
    print("   🔄 循环论证: 用历史数据验证基于历史数据的模型")
    print("   🎲 随机性悖论: 试图预测本质随机的事件")
    print("   📈 过拟合陷阱: 模型在训练数据上表现好，实际应用差")
    
    print(f"\n4. 深层思考:")
    print("   🌟 评分系统的价值可能不在于绝对准确性")
    print("   🌟 而在于提供一种结构化的思考框架")
    print("   🌟 帮助用户理性分析，避免完全随意选择")
    print("   🌟 即使命中率不高，也可能有心理安慰价值")

def practical_rationality_assessment(data, correlation, p_value):
    """实用合理性评估"""
    print(f"\n💡 实用合理性综合评估")
    print("="*50)
    
    try:
        # 评估维度
        dimensions = {
            '统计相关性': 0,
            '预测区分度': 0,
            '系统稳定性': 0,
            '用户体验': 0,
            '风险控制': 0
        }
        
        # 1. 统计相关性评估
        if correlation is not None:
            if abs(correlation) > 0.2 and p_value < 0.05:
                dimensions['统计相关性'] = 85
            elif abs(correlation) > 0.1:
                dimensions['统计相关性'] = 60
            else:
                dimensions['统计相关性'] = 30
        
        # 2. 预测区分度评估
        scores = data['评分数值']
        score_range = scores.max() - scores.min()
        unique_scores = len(scores.unique())
        
        if score_range > 15 and unique_scores > 50:
            dimensions['预测区分度'] = 90
        elif score_range > 10:
            dimensions['预测区分度'] = 70
        else:
            dimensions['预测区分度'] = 40
        
        # 3. 系统稳定性评估
        score_std = scores.std()
        if 2 < score_std < 8:  # 适中的标准差
            dimensions['系统稳定性'] = 85
        elif score_std < 2:
            dimensions['系统稳定性'] = 50  # 过于稳定可能缺乏区分度
        else:
            dimensions['系统稳定性'] = 60
        
        # 4. 用户体验评估
        grade_dist = data['评分等级'].value_counts()
        if len(grade_dist) >= 4:  # 至少4个等级
            # 检查分布是否合理
            max_grade_pct = grade_dist.max() / len(data) * 100
            if max_grade_pct < 80:  # 没有过度集中
                dimensions['用户体验'] = 80
            else:
                dimensions['用户体验'] = 50
        else:
            dimensions['用户体验'] = 30
        
        # 5. 风险控制评估
        high_score_data = data[data['评分数值'] >= data['评分数值'].quantile(0.8)]
        if len(high_score_data) > 0:
            high_score_hit_rate = len(high_score_data[high_score_data['是否命中'] == '是']) / len(high_score_data) * 100
            if high_score_hit_rate > 35:
                dimensions['风险控制'] = 85
            elif high_score_hit_rate > 25:
                dimensions['风险控制'] = 70
            else:
                dimensions['风险控制'] = 50
        
        # 输出评估结果
        print("各维度评估结果:")
        total_score = 0
        for dimension, score in dimensions.items():
            print(f"   {dimension}: {score}/100")
            total_score += score
        
        overall_score = total_score / len(dimensions)
        print(f"\n综合评估: {overall_score:.1f}/100")
        
        # 给出评估结论
        if overall_score >= 80:
            conclusion = "✅ 评分系统合理性很高"
        elif overall_score >= 65:
            conclusion = "⚠️ 评分系统合理性中等"
        elif overall_score >= 50:
            conclusion = "⚠️ 评分系统合理性偏低"
        else:
            conclusion = "❌ 评分系统合理性不足"
        
        print(f"结论: {conclusion}")
        
        return overall_score, dimensions
        
    except Exception as e:
        print(f"❌ 评估失败: {e}")
        return 0, {}

def generate_improvement_suggestions(overall_score, dimensions):
    """生成改进建议"""
    print(f"\n🚀 改进建议")
    print("="*30)
    
    print("基于评估结果的改进建议:")
    
    # 针对性建议
    if dimensions.get('统计相关性', 0) < 60:
        print("   📊 统计相关性不足:")
        print("      - 重新审视特征工程")
        print("      - 考虑引入更多预测因子")
        print("      - 优化评分算法权重")
    
    if dimensions.get('预测区分度', 0) < 70:
        print("   📈 预测区分度有限:")
        print("      - 扩大评分范围")
        print("      - 增加评分精度")
        print("      - 优化等级划分")
    
    if dimensions.get('系统稳定性', 0) < 70:
        print("   ⚖️ 系统稳定性问题:")
        print("      - 调整随机性参数")
        print("      - 平衡保守性与区分度")
        print("      - 建立动态调整机制")
    
    if dimensions.get('用户体验', 0) < 70:
        print("   👤 用户体验待改善:")
        print("      - 优化等级分布")
        print("      - 提供更清晰的解释")
        print("      - 增加置信区间显示")
    
    if dimensions.get('风险控制', 0) < 70:
        print("   🛡️ 风险控制需加强:")
        print("      - 提高高分预测准确性")
        print("      - 建立风险警示机制")
        print("      - 定期重新校准模型")
    
    # 通用建议
    print(f"\n通用改进方向:")
    print("   1. 建立长期跟踪机制，持续验证评分有效性")
    print("   2. 引入多模型集成，提高预测稳定性")
    print("   3. 增加不确定性量化，提供置信区间")
    print("   4. 建立用户反馈机制，持续优化体验")
    print("   5. 定期进行A/B测试，验证改进效果")

def main():
    """主函数"""
    print("🤔 评分系统与命中率相互合理性思辨分析")
    print("="*80)
    
    # 1. 加载和分析数据
    data = load_and_analyze_current_data()
    if data is None:
        return
    
    # 2. 相关性分析
    correlation, p_value = analyze_score_hit_correlation(data)
    
    # 3. 评分分布合理性
    analyze_score_distribution_rationality(data)
    
    # 4. 按区间分析命中率
    hit_rates, expected_rates = analyze_hit_rate_by_score_ranges(data)
    
    # 5. 哲学思辨
    philosophical_analysis()
    
    # 6. 实用合理性评估
    overall_score, dimensions = practical_rationality_assessment(data, correlation, p_value)
    
    # 7. 改进建议
    generate_improvement_suggestions(overall_score, dimensions)
    
    print(f"\n🎯 核心结论")
    print("="*50)
    print("评分系统与命中率的合理性是一个多维度问题:")
    print("   📊 从统计角度: 需要验证相关性和显著性")
    print("   🧠 从认知角度: 需要符合用户直觉和期望")
    print("   💰 从实用角度: 需要提供实际价值和指导")
    print("   🔬 从科学角度: 需要基于可验证的方法论")
    print("   🤔 从哲学角度: 需要承认预测随机性的局限")
    
    print(f"\n当前系统评估: {overall_score:.1f}/100")
    if overall_score >= 70:
        print("✅ 系统整体合理性较好，可继续使用并持续优化")
    else:
        print("⚠️ 系统合理性有待提升，建议重点改进薄弱环节")

if __name__ == "__main__":
    main()
