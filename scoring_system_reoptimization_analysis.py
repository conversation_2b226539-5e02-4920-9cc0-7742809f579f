#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
思辨分析：优化预测系统后是否需要重新优化评分系统
Analysis: Whether scoring system needs reoptimization after prediction system optimization

核心问题：
1. 原评分系统是基于旧预测算法训练的
2. 新预测算法的特征分布可能已经改变
3. 评分系统的有效性可能受到影响
"""

import pandas as pd
import numpy as np
from collections import Counter, defaultdict
import pickle
import os
from datetime import datetime

def analyze_scoring_system_compatibility():
    """分析评分系统与优化后预测系统的兼容性"""
    print("🤔 思辨分析：评分系统兼容性")
    print("="*50)
    
    print("核心思辨问题:")
    print("1. 原评分系统基于旧算法的预测特征训练")
    print("2. 新算法改变了预测模式和特征分布")
    print("3. 评分系统是否还能准确评估新预测？")
    
    # 分析原评分系统的训练基础
    print(f"\n📊 原评分系统分析:")
    print("训练数据基础:")
    print("  - 基于34.3%增强马尔可夫算法（未优化版本）")
    print("  - 数字30频率70.9%，数字40频率20.4%")
    print("  - 预测模式高度集中，缺乏多样性")
    print("  - 评分模型学习了这种集中化的模式")
    
    print(f"\n🔧 优化后预测系统特点:")
    print("算法变化:")
    print("  - 权重参数显著调整")
    print("  - 数字分类完全重构")
    print("  - 新增多样性约束机制")
    print("  - 预测模式从集中转向多样化")
    
    print(f"\n⚠️ 潜在兼容性问题:")
    print("1. 特征分布偏移:")
    print("   - 原评分系统期望高频预测30和40")
    print("   - 新系统很少预测30和40")
    print("   - 可能导致评分系统'不认识'新预测模式")
    
    print("2. 评分偏差:")
    print("   - 对多样化预测可能给出不准确的低分")
    print("   - 对新的高频数字(如2,15,16)缺乏准确评估")
    print("   - 评分与实际命中率的关联性可能下降")
    
    print("3. 模型过时:")
    print("   - 训练数据不再代表新的预测模式")
    print("   - 评分阈值可能需要重新校准")
    print("   - 评分等级划分可能不再适用")

def generate_new_prediction_data():
    """使用优化后的系统生成新的预测数据"""
    print(f"\n🔄 生成优化系统的新预测数据")
    print("="*50)
    
    try:
        # 导入优化后的系统
        import sys
        sys.path.append('.')
        from 集成评分系统的预测系统 import IntegratedPredictionSystem
        
        # 加载真实数据
        lottery_df = pd.read_csv('data/processed/lottery_data_clean_no_special.csv')
        df_2025 = lottery_df[lottery_df['年份'] == 2025].copy()
        df_2025 = df_2025.sort_values('期号').reset_index(drop=True)
        
        print(f"✅ 加载2025年数据: {len(df_2025)}期")
        
        # 初始化优化系统
        system = IntegratedPredictionSystem()
        if not system.initialize_system():
            print("❌ 系统初始化失败")
            return None
        
        print("✅ 优化系统初始化完成")
        
        # 生成新预测数据
        new_predictions = []
        
        for i in range(len(df_2025) - 1):
            current_period = df_2025.iloc[i]
            next_period = df_2025.iloc[i + 1]
            
            # 当期数字
            current_numbers = [current_period[f'数字{j}'] for j in range(1, 7)]
            
            # 使用优化系统预测
            prediction, confidence = system.predict_next_period(current_numbers)
            
            # 实际下期数字
            actual_numbers = [next_period[f'数字{j}'] for j in range(1, 7)]
            
            # 计算命中情况
            hit_numbers = list(set(prediction) & set(actual_numbers))
            hit_count = len(hit_numbers)
            is_hit = hit_count >= 1
            
            # 构建预测记录
            pred_record = {
                '预测日期': datetime.now().strftime('%Y-%m-%d'),
                '预测时间': datetime.now().strftime('%H:%M:%S'),
                '当期年份': current_period['年份'],
                '当期期号': current_period['期号'],
                '预测期号': f"{next_period['年份']}年{next_period['期号']}期",
                '当期数字1': current_numbers[0],
                '当期数字2': current_numbers[1],
                '当期数字3': current_numbers[2],
                '当期数字4': current_numbers[3],
                '当期数字5': current_numbers[4],
                '当期数字6': current_numbers[5],
                '预测数字1': prediction[0],
                '预测数字2': prediction[1],
                '预测置信度': f"{confidence:.6f}",
                '预测方法': '优化34.3%增强马尔可夫',
                '实际数字1': actual_numbers[0],
                '实际数字2': actual_numbers[1],
                '实际数字3': actual_numbers[2],
                '实际数字4': actual_numbers[3],
                '实际数字5': actual_numbers[4],
                '实际数字6': actual_numbers[5],
                '命中数量': hit_count,
                '是否命中': '是' if is_hit else '否',
                '命中数字': ','.join(map(str, sorted(hit_numbers))) if hit_numbers else '',
                '备注': '优化系统预测-待评分'
            }
            
            new_predictions.append(pred_record)
            
            # 显示进度
            if (i + 1) % 20 == 0:
                print(f"  生成进度: {i+1}/{len(df_2025)-1}")
        
        print(f"✅ 新预测数据生成完成: {len(new_predictions)}期")
        
        return new_predictions
        
    except Exception as e:
        print(f"❌ 生成新预测数据失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_prediction_pattern_changes(new_predictions):
    """分析预测模式变化"""
    print(f"\n📊 分析预测模式变化")
    print("="*40)
    
    if not new_predictions:
        print("❌ 无新预测数据")
        return
    
    # 加载原始预测数据
    original_df = pd.read_csv('prediction_data.csv')
    
    # 分析预测频率变化
    def get_prediction_stats(data, name):
        if isinstance(data, pd.DataFrame):
            all_preds = []
            for _, row in data.iterrows():
                all_preds.extend([row['预测数字1'], row['预测数字2']])
        else:
            all_preds = []
            for pred in data:
                all_preds.extend([pred['预测数字1'], pred['预测数字2']])
        
        freq = Counter(all_preds)
        total = len(data)
        
        print(f"{name}预测频率 (Top 10):")
        for num, count in freq.most_common(10):
            percentage = count / total * 100
            print(f"  数字{num:2d}: {count:3d}次 ({percentage:5.1f}%)")
        
        return freq
    
    print("原始系统 vs 优化系统:")
    original_freq = get_prediction_stats(original_df, "原始系统")
    new_freq = get_prediction_stats(new_predictions, "优化系统")
    
    # 重点对比30和40
    print(f"\n🎯 关键数字对比:")
    orig_30 = original_freq.get(30, 0) / len(original_df) * 100
    orig_40 = original_freq.get(40, 0) / len(original_df) * 100
    new_30 = new_freq.get(30, 0) / len(new_predictions) * 100
    new_40 = new_freq.get(40, 0) / len(new_predictions) * 100
    
    print(f"数字30: {orig_30:.1f}% → {new_30:.1f}% ({new_30-orig_30:+.1f}%)")
    print(f"数字40: {orig_40:.1f}% → {new_40:.1f}% ({new_40-orig_40:+.1f}%)")
    
    # 分析命中率变化
    orig_hits = len(original_df[original_df['是否命中'] == '是'])
    orig_hit_rate = orig_hits / len(original_df)
    
    new_hits = sum(1 for pred in new_predictions if pred['是否命中'] == '是')
    new_hit_rate = new_hits / len(new_predictions)
    
    print(f"\n📈 命中率对比:")
    print(f"原始系统: {orig_hit_rate:.1%} ({orig_hits}/{len(original_df)})")
    print(f"优化系统: {new_hit_rate:.1%} ({new_hits}/{len(new_predictions)})")
    print(f"变化: {new_hit_rate - orig_hit_rate:+.1%}")
    
    return {
        'original_freq': original_freq,
        'new_freq': new_freq,
        'original_hit_rate': orig_hit_rate,
        'new_hit_rate': new_hit_rate
    }

def evaluate_scoring_system_effectiveness(new_predictions):
    """评估现有评分系统对新预测的有效性"""
    print(f"\n🎯 评估评分系统有效性")
    print("="*40)
    
    if not new_predictions:
        print("❌ 无新预测数据")
        return
    
    try:
        # 加载评分系统
        from hit_rate_scoring_system import HitRateScoringSystem
        
        if os.path.exists("scoring_model.pkl"):
            with open("scoring_model.pkl", 'rb') as f:
                scoring_data = pickle.load(f)
            
            scoring_system = HitRateScoringSystem()
            scoring_system.model = scoring_data['model']
            scoring_system.features = scoring_data['features']
            scoring_system.score_thresholds = scoring_data['score_thresholds']
            scoring_system.validation_results = scoring_data['validation_results']
            
            print("✅ 评分系统加载完成")
        else:
            print("❌ 评分模型文件不存在")
            return
        
        # 为新预测计算评分
        scored_predictions = []
        
        for pred in new_predictions:
            # 准备评分数据
            scoring_data = {
                'pred_num1': pred['预测数字1'],
                'pred_num2': pred['预测数字2'],
                'period': int(pred['当期期号']),
                'original_confidence': float(pred['预测置信度']),
                'optimized_confidence': float(pred['预测置信度']) * 1.2,  # 模拟优化置信度
                'accuracy_factor': 0.8,
                'calibration_factor': 0.9,
                'stability_factor': 0.85,
                'trend_factor': 0.75,
                'composite_factor': 0.82
            }
            
            # 计算评分
            try:
                score_result = scoring_system.calculate_prediction_score(scoring_data)
                pred['预测评分'] = f"{score_result['score']:.1f}"
                pred['评分等级'] = score_result['grade']
                pred['评分建议'] = score_result['recommendation']
                pred['评分概率'] = f"{score_result['probability']:.3f}"
            except Exception as e:
                print(f"⚠️ 评分计算失败: {e}")
                pred['预测评分'] = "35.0"
                pred['评分等级'] = "C (评分失败)"
                pred['评分建议'] = "无法评分"
                pred['评分概率'] = "0.350"
            
            scored_predictions.append(pred)
        
        print(f"✅ 评分计算完成: {len(scored_predictions)}期")
        
        # 分析评分有效性
        scores = [float(pred['预测评分']) for pred in scored_predictions]
        hits = [pred['是否命中'] == '是' for pred in scored_predictions]
        
        print(f"\n📊 评分有效性分析:")
        print(f"平均评分: {np.mean(scores):.1f}分")
        print(f"评分范围: {np.min(scores):.1f} - {np.max(scores):.1f}分")
        
        # 按评分区间分析命中率
        score_ranges = [
            (50, 100, "≥50分"),
            (40, 50, "40-49分"),
            (30, 40, "30-39分"),
            (20, 30, "20-29分"),
            (0, 20, "<20分")
        ]
        
        print(f"\n评分区间命中率:")
        for min_score, max_score, label in score_ranges:
            range_indices = [i for i, score in enumerate(scores) 
                           if min_score <= score < max_score]
            
            if range_indices:
                range_hits = sum(hits[i] for i in range_indices)
                range_hit_rate = range_hits / len(range_indices)
                print(f"  {label}: {range_hit_rate:.1%} ({range_hits}/{len(range_indices)})")
        
        return scored_predictions
        
    except Exception as e:
        print(f"❌ 评分系统评估失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_new_prediction_data_file(scored_predictions):
    """创建新的prediction_data.csv文件"""
    print(f"\n💾 创建新的prediction_data.csv文件")
    print("="*50)
    
    if not scored_predictions:
        print("❌ 无评分预测数据")
        return
    
    # 备份原文件
    if os.path.exists('prediction_data.csv'):
        backup_filename = f'prediction_data_backup_before_optimization_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        os.rename('prediction_data.csv', backup_filename)
        print(f"📁 原文件已备份: {backup_filename}")
    
    # 创建新的DataFrame
    df_new = pd.DataFrame(scored_predictions)
    
    # 确保列顺序正确
    columns_order = [
        '预测日期', '预测时间', '当期年份', '当期期号', '预测期号',
        '当期数字1', '当期数字2', '当期数字3', '当期数字4', '当期数字5', '当期数字6',
        '预测数字1', '预测数字2', '预测置信度', '预测方法',
        '预测评分', '评分等级', '评分建议', '评分概率',
        '实际数字1', '实际数字2', '实际数字3', '实际数字4', '实际数字5', '实际数字6',
        '命中数量', '是否命中', '命中数字', '备注'
    ]
    
    # 重新排列列顺序
    df_new = df_new[columns_order]
    
    # 保存新文件
    df_new.to_csv('prediction_data.csv', index=False, encoding='utf-8-sig')
    
    print(f"✅ 新prediction_data.csv文件创建完成")
    print(f"   总记录数: {len(df_new)}")
    print(f"   数据来源: 优化后的预测系统")
    print(f"   评分状态: 使用原评分系统计算")
    
    # 生成统计摘要
    hit_count = len(df_new[df_new['是否命中'] == '是'])
    hit_rate = hit_count / len(df_new)
    avg_score = pd.to_numeric(df_new['预测评分']).mean()
    
    print(f"\n📊 新数据统计:")
    print(f"   命中率: {hit_rate:.1%} ({hit_count}/{len(df_new)})")
    print(f"   平均评分: {avg_score:.1f}分")
    
    return df_new

def generate_reoptimization_recommendations():
    """生成重新优化建议"""
    print(f"\n💡 评分系统重新优化建议")
    print("="*50)
    
    print("基于分析结果的建议:")
    
    print(f"\n1. 立即需要做的:")
    print("   ✅ 使用新预测数据更新prediction_data.csv")
    print("   ✅ 评估现有评分系统的有效性")
    print("   ⚠️ 监控评分与命中率的关联性")
    
    print(f"\n2. 短期优化 (1-2周):")
    print("   🔄 基于新预测模式重新训练评分系统")
    print("   🔄 调整评分阈值和等级划分")
    print("   🔄 验证新评分系统的有效性")
    
    print(f"\n3. 中期改进 (1个月):")
    print("   🔄 收集更多优化系统的预测数据")
    print("   🔄 建立动态评分模型")
    print("   🔄 实现评分系统的自适应更新")
    
    print(f"\n4. 长期发展 (3个月+):")
    print("   🔄 开发多模型集成评分系统")
    print("   🔄 实现实时学习和模型更新")
    print("   🔄 建立完整的预测-评分生态系统")
    
    print(f"\n🎯 核心思辨结论:")
    print("预测系统优化后，评分系统确实需要相应调整！")
    print("原因:")
    print("1. 预测模式发生根本性变化")
    print("2. 特征分布不再匹配训练数据")
    print("3. 评分准确性可能受到影响")
    print("4. 需要基于新模式重新校准")

def main():
    """主函数"""
    print("🤔 思辨分析：优化预测系统后评分系统的重新优化需求")
    print("="*80)
    
    try:
        # 1. 分析兼容性问题
        analyze_scoring_system_compatibility()
        
        # 2. 生成新预测数据
        new_predictions = generate_new_prediction_data()
        
        if new_predictions:
            # 3. 分析预测模式变化
            pattern_changes = analyze_prediction_pattern_changes(new_predictions)
            
            # 4. 评估评分系统有效性
            scored_predictions = evaluate_scoring_system_effectiveness(new_predictions)
            
            if scored_predictions:
                # 5. 创建新的prediction_data.csv
                new_df = create_new_prediction_data_file(scored_predictions)
                
                # 6. 生成重新优化建议
                generate_reoptimization_recommendations()
                
                print(f"\n🎉 分析完成！")
                print(f"✅ 新prediction_data.csv文件已创建")
                print(f"✅ 基于优化后的预测系统")
                print(f"⚠️ 建议重新优化评分系统以获得更好效果")
            else:
                print(f"\n❌ 评分计算失败，无法创建完整的新文件")
        else:
            print(f"\n❌ 新预测数据生成失败")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
