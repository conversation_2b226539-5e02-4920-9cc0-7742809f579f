#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统修复验证 - 验证JSON序列化问题修复
"""

import sys
import json
import numpy as np
from datetime import datetime

def test_json_serialization():
    """测试JSON序列化修复"""
    print("🔧 测试JSON序列化修复")
    print("=" * 50)
    
    # 模拟预测报告数据
    test_data = {
        'prediction': [np.int64(40), np.int64(3)],
        'confidence': np.float64(0.027),
        'timestamp': datetime.now().isoformat(),
        'input_numbers': [np.int64(22), np.int64(40), np.int64(29), np.int64(45), np.int64(11), np.int64(1)],
        'method': 'enhanced_markov_34.3%',
        'version': '34.3%_verified'
    }
    
    print(f"原始数据类型:")
    for key, value in test_data.items():
        print(f"  {key}: {type(value)} = {value}")
    
    # 转换函数
    def convert_types(obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {k: convert_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_types(v) for v in obj]
        elif isinstance(obj, set):
            return list(obj)
        else:
            return obj
    
    # 转换数据
    converted_data = convert_types(test_data)
    
    print(f"\n转换后数据类型:")
    for key, value in converted_data.items():
        print(f"  {key}: {type(value)} = {value}")
    
    # 测试JSON序列化
    try:
        json_str = json.dumps(converted_data, ensure_ascii=False, indent=2)
        print(f"\n✅ JSON序列化成功")
        print(f"JSON长度: {len(json_str)}字符")
        
        # 测试保存到文件
        test_file = "test_prediction.json"
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(converted_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 文件保存成功: {test_file}")
        
        # 测试读取文件
        with open(test_file, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        
        print(f"✅ 文件读取成功")
        print(f"读取的预测结果: {loaded_data['prediction']}")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON序列化失败: {e}")
        return False

def test_production_system():
    """测试生产系统"""
    print(f"\n🚀 测试生产系统")
    print("=" * 50)
    
    try:
        # 导入生产系统
        from 生产级预测系统 import ProductionPredictionSystem
        
        # 初始化系统
        system = ProductionPredictionSystem()
        
        print("✅ 系统导入成功")
        
        # 测试初始化
        if system.initialize_system():
            print("✅ 系统初始化成功")
            
            # 测试预测
            current_numbers = [22, 40, 29, 45, 11, 1]
            prediction_report = system.predict_next_period(current_numbers)
            
            print("✅ 预测执行成功")
            print(f"预测结果: {prediction_report['prediction']}")
            print(f"置信度: {prediction_report['confidence']:.3f}")
            
            return True
        else:
            print("❌ 系统初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 生产系统测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 34.3%预测系统修复验证")
    print("=" * 60)
    
    # 1. 测试JSON序列化修复
    json_test_passed = test_json_serialization()
    
    # 2. 测试生产系统
    system_test_passed = test_production_system()
    
    # 总结
    print(f"\n📊 修复验证总结")
    print("=" * 40)
    print(f"JSON序列化测试: {'✅ 通过' if json_test_passed else '❌ 失败'}")
    print(f"生产系统测试: {'✅ 通过' if system_test_passed else '❌ 失败'}")
    
    if json_test_passed and system_test_passed:
        print(f"\n🎉 所有测试通过，系统修复成功！")
        print(f"✅ JSON序列化问题已解决")
        print(f"✅ 预测记录可以正常保存")
        print(f"✅ 系统可以正常使用")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步检查")
    
    return json_test_passed and system_test_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
