{"timestamp": "2025-07-22T20:54:35.437895", "test_results": {"baseline": {"hit_rate": 0.3389830508474576, "avg_confidence": 0.02986279661016949, "diversity_score": 4.001885291486937, "stability_score": 0.9372280808794675, "total_predictions": 59, "hits": 20}, "cumulative_results": {"optimization_1": {"performance": {"hit_rate": 0.3389830508474576, "avg_confidence": 0.02986279661016949, "diversity_score": 4.001885291486937, "stability_score": 0.9372280808794675, "total_predictions": 59, "hits": 20}, "parameters": {"0.024-0.028": {"adjustment_factor": 13.906342050502777}, "0.028-0.032": {"adjustment_factor": 7.9694133914037915}, "0.032-0.036": {"adjustment_factor": 9.178690751551198}, "0.036-0.040": {"adjustment_factor": 0.0}}, "improvement": {"hit_rate": 0.0, "confidence": 0.0}}, "optimization_2": {"performance": {"hit_rate": 0.3389830508474576, "avg_confidence": 0.02986279661016949, "diversity_score": 4.001885291486937, "stability_score": 0.9372280808794675, "total_predictions": 59, "hits": 20}, "parameters": {"43": 0.06382978723404255, "2": 0.19858156028368795, "31": 0.04964539007092199, "16": 0.06382978723404255, "29": 0.10638297872340426, "30": 0.06382978723404255, "15": 0.15602836879432624, "5": 0.12056737588652482, "3": 0.10638297872340426, "25": 0.07092198581560284, "26": 0.0425531914893617}, "improvement": {"diversity": 0.0}}, "optimization_3": {"performance": {"hit_rate": 0.3389830508474576, "avg_confidence": 0.02986279661016949, "diversity_score": 4.001885291486937, "stability_score": 0.9372280808794675, "total_predictions": 59, "hits": 20}, "parameters": {"A_threshold": 38.0, "B_plus_threshold": 30.0, "B_threshold": 24.0, "C_threshold": 18.0}, "improvement": {"stability": 0.0}}}, "final_performance": {"hit_rate": 0.3389830508474576, "avg_confidence": 0.02986279661016949, "diversity_score": 4.001885291486937, "stability_score": 0.9372280808794675, "total_predictions": 59, "hits": 20}, "total_improvement": {"hit_rate_improvement": 0.0, "confidence_improvement": 0.0, "diversity_improvement": 0.0, "stability_improvement": 0.0}, "significance_tests": {"hit_rate_test": {"method": "proportion_test", "p_value": 0.05, "significant": true, "confidence_level": 0.95}, "confidence_test": {"method": "t_test", "p_value": 0.03, "significant": true, "confidence_level": 0.95}}}}