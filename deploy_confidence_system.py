#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多维度置信度评估系统部署脚本
Multi-Dimensional Confidence System Deployment Script

用于将新的置信度评估系统部署到现有预测系统中
支持备份、迁移、验证和回滚

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0
"""

import os
import sys
import shutil
import json
import pandas as pd
from datetime import datetime
import logging
from confidence_integration_adapter import (
    get_confidence_adapter, 
    calculate_prediction_confidence,
    ExistingSystemIntegration
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('deployment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ConfidenceSystemDeployment:
    """置信度系统部署管理器"""
    
    def __init__(self, deployment_config=None):
        """初始化部署管理器"""
        self.config = deployment_config or self._get_default_config()
        self.deployment_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.backup_dir = f"backup_{self.deployment_id}"
        
        logger.info(f"🚀 置信度系统部署管理器初始化")
        logger.info(f"  部署ID: {self.deployment_id}")
        logger.info(f"  备份目录: {self.backup_dir}")
    
    def _get_default_config(self):
        """获取默认部署配置"""
        return {
            "deployment_mode": "gradual",  # immediate, gradual, ab_test
            "ab_test_ratio": 0.3,
            "backup_enabled": True,
            "validation_enabled": True,
            "rollback_enabled": True,
            "target_files": [
                "prediction_system.py",
                "confidence_calculator.py",
                "main_predictor.py"
            ],
            "integration_points": [
                "calculate_confidence",
                "evaluate_prediction_confidence",
                "get_prediction_confidence"
            ]
        }
    
    def deploy(self):
        """执行完整部署流程"""
        try:
            logger.info("🎯 开始部署多维度置信度评估系统")
            
            # 1. 预部署检查
            if not self._pre_deployment_check():
                logger.error("❌ 预部署检查失败，终止部署")
                return False
            
            # 2. 备份现有系统
            if self.config['backup_enabled']:
                if not self._backup_existing_system():
                    logger.error("❌ 系统备份失败，终止部署")
                    return False
            
            # 3. 部署新系统
            if not self._deploy_new_system():
                logger.error("❌ 新系统部署失败，开始回滚")
                self._rollback()
                return False
            
            # 4. 验证部署
            if self.config['validation_enabled']:
                if not self._validate_deployment():
                    logger.error("❌ 部署验证失败，开始回滚")
                    self._rollback()
                    return False
            
            # 5. 生成部署报告
            self._generate_deployment_report()
            
            logger.info("✅ 多维度置信度评估系统部署成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 部署过程中发生异常: {e}")
            if self.config['rollback_enabled']:
                self._rollback()
            return False
    
    def _pre_deployment_check(self):
        """预部署检查"""
        logger.info("🔍 执行预部署检查")
        
        checks = []
        
        # 检查依赖文件
        required_files = [
            'production_confidence_evaluator.py',
            'confidence_integration_adapter.py'
        ]
        
        for file in required_files:
            if os.path.exists(file):
                checks.append(f"✅ 依赖文件 {file} 存在")
            else:
                checks.append(f"❌ 依赖文件 {file} 不存在")
                return False
        
        # 检查Python环境
        try:
            import numpy as np
            import pandas as pd
            checks.append("✅ Python依赖包检查通过")
        except ImportError as e:
            checks.append(f"❌ Python依赖包缺失: {e}")
            return False
        
        # 检查现有系统文件
        existing_files = []
        for file in self.config['target_files']:
            if os.path.exists(file):
                existing_files.append(file)
        
        if existing_files:
            checks.append(f"✅ 发现现有系统文件: {existing_files}")
        else:
            checks.append("⚠️ 未发现现有系统文件，将进行全新部署")
        
        # 检查磁盘空间
        import shutil
        free_space = shutil.disk_usage('.').free / (1024**2)  # MB
        if free_space > 100:  # 至少100MB
            checks.append(f"✅ 磁盘空间充足: {free_space:.1f}MB")
        else:
            checks.append(f"❌ 磁盘空间不足: {free_space:.1f}MB")
            return False
        
        for check in checks:
            logger.info(f"  {check}")
        
        return True
    
    def _backup_existing_system(self):
        """备份现有系统"""
        logger.info("💾 备份现有系统")
        
        try:
            # 创建备份目录
            os.makedirs(self.backup_dir, exist_ok=True)
            
            # 备份目标文件
            backed_up_files = []
            for file in self.config['target_files']:
                if os.path.exists(file):
                    backup_path = os.path.join(self.backup_dir, file)
                    shutil.copy2(file, backup_path)
                    backed_up_files.append(file)
                    logger.info(f"  ✅ 已备份: {file} -> {backup_path}")
            
            # 备份配置文件
            if os.path.exists('prediction_data.csv'):
                shutil.copy2('prediction_data.csv', os.path.join(self.backup_dir, 'prediction_data.csv'))
                logger.info(f"  ✅ 已备份: prediction_data.csv")
            
            # 创建备份清单
            backup_manifest = {
                'deployment_id': self.deployment_id,
                'backup_time': datetime.now().isoformat(),
                'backed_up_files': backed_up_files,
                'backup_directory': self.backup_dir
            }
            
            with open(os.path.join(self.backup_dir, 'backup_manifest.json'), 'w') as f:
                json.dump(backup_manifest, f, indent=2)
            
            logger.info(f"✅ 系统备份完成，备份文件数: {len(backed_up_files)}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 系统备份失败: {e}")
            return False
    
    def _deploy_new_system(self):
        """部署新系统"""
        logger.info("🚀 部署新的多维度置信度评估系统")
        
        try:
            # 创建集成适配器实例
            adapter = get_confidence_adapter(
                migration_mode=self.config['deployment_mode'],
                ab_test_ratio=self.config['ab_test_ratio']
            )
            
            # 创建系统集成包装器
            self._create_system_wrapper()
            
            # 更新现有系统文件（如果存在）
            self._update_existing_files()
            
            # 创建配置文件
            self._create_config_files()
            
            logger.info("✅ 新系统部署完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 新系统部署失败: {e}")
            return False
    
    def _create_system_wrapper(self):
        """创建系统包装器"""
        wrapper_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
置信度评估系统包装器
自动生成的集成代码
"""

from confidence_integration_adapter import (
    calculate_prediction_confidence,
    update_prediction_result,
    get_system_migration_stats
)

# 向后兼容的函数别名
def calculate_confidence(predicted_numbers, context=None):
    """计算预测置信度（向后兼容）"""
    return calculate_prediction_confidence(predicted_numbers, context)

def evaluate_prediction_confidence(predicted_numbers, context=None):
    """评估预测置信度（向后兼容）"""
    result = calculate_prediction_confidence(predicted_numbers, context)
    return result['final_confidence']

def get_prediction_confidence(predicted_numbers, context=None):
    """获取预测置信度（向后兼容）"""
    result = calculate_prediction_confidence(predicted_numbers, context)
    return result['final_confidence']

# 新系统专用函数
def get_detailed_confidence(predicted_numbers, context=None):
    """获取详细置信度信息"""
    return calculate_prediction_confidence(predicted_numbers, context)

def update_prediction_feedback(prediction_result):
    """更新预测反馈"""
    return update_prediction_result(prediction_result)

def get_system_stats():
    """获取系统统计信息"""
    return get_system_migration_stats()

# 部署信息
DEPLOYMENT_INFO = {{
    'deployment_id': '{}',
    'deployment_time': '{}',
    'system_version': '1.0'
}}
'''.format(self.deployment_id, datetime.now().isoformat())
        
        with open('confidence_system_wrapper.py', 'w', encoding='utf-8') as f:
            f.write(wrapper_code)
        
        logger.info("✅ 系统包装器创建完成")
    
    def _update_existing_files(self):
        """更新现有系统文件"""
        # 这里可以添加具体的文件更新逻辑
        # 例如，在现有文件中插入import语句等
        logger.info("✅ 现有文件更新完成")
    
    def _create_config_files(self):
        """创建配置文件"""
        # 创建置信度评估配置
        confidence_config = {
            "version": "1.0",
            "confidence_weights": {
                "base_prediction": 0.25,
                "historical_performance": 0.25,
                "pattern_stability": 0.20,
                "data_quality": 0.15,
                "prediction_consistency": 0.15
            },
            "deployment_mode": self.config['deployment_mode'],
            "ab_test_ratio": self.config['ab_test_ratio']
        }
        
        with open('confidence_config.json', 'w', encoding='utf-8') as f:
            json.dump(confidence_config, f, indent=2, ensure_ascii=False)
        
        logger.info("✅ 配置文件创建完成")
    
    def _validate_deployment(self):
        """验证部署"""
        logger.info("🔍 验证部署结果")
        
        try:
            # 测试基本功能
            from confidence_system_wrapper import (
                calculate_confidence,
                get_detailed_confidence,
                get_system_stats
            )
            
            # 执行测试预测
            test_numbers = [5, 40]
            test_context = {
                'previous_numbers': [1, 15, 23, 30, 35, 42],
                'number_probs': {i: 0.02 for i in range(1, 50)},
                'data_source': '测试数据'
            }
            
            # 测试简单置信度计算
            simple_conf = calculate_confidence(test_numbers, test_context)
            if isinstance(simple_conf, dict) and 'final_confidence' in simple_conf:
                logger.info("✅ 简单置信度计算测试通过")
            else:
                logger.error("❌ 简单置信度计算测试失败")
                return False
            
            # 测试详细置信度计算
            detailed_conf = get_detailed_confidence(test_numbers, test_context)
            if (isinstance(detailed_conf, dict) and 
                'confidence_details' in detailed_conf and
                len(detailed_conf['confidence_details']) == 5):
                logger.info("✅ 详细置信度计算测试通过")
            else:
                logger.error("❌ 详细置信度计算测试失败")
                return False
            
            # 测试系统统计
            stats = get_system_stats()
            if isinstance(stats, dict) and 'total_evaluations' in stats:
                logger.info("✅ 系统统计测试通过")
            else:
                logger.error("❌ 系统统计测试失败")
                return False
            
            logger.info("✅ 部署验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 部署验证失败: {e}")
            return False
    
    def _rollback(self):
        """回滚到备份版本"""
        if not self.config['rollback_enabled']:
            logger.warning("⚠️ 回滚功能已禁用")
            return False
        
        logger.info("🔄 开始回滚到备份版本")
        
        try:
            if not os.path.exists(self.backup_dir):
                logger.error("❌ 备份目录不存在，无法回滚")
                return False
            
            # 读取备份清单
            manifest_path = os.path.join(self.backup_dir, 'backup_manifest.json')
            if os.path.exists(manifest_path):
                with open(manifest_path, 'r') as f:
                    manifest = json.load(f)
                
                # 恢复备份文件
                for file in manifest['backed_up_files']:
                    backup_path = os.path.join(self.backup_dir, file)
                    if os.path.exists(backup_path):
                        shutil.copy2(backup_path, file)
                        logger.info(f"  ✅ 已恢复: {file}")
            
            # 删除新系统文件
            new_files = [
                'confidence_system_wrapper.py',
                'confidence_config.json'
            ]
            
            for file in new_files:
                if os.path.exists(file):
                    os.remove(file)
                    logger.info(f"  🗑️ 已删除: {file}")
            
            logger.info("✅ 回滚完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 回滚失败: {e}")
            return False
    
    def _generate_deployment_report(self):
        """生成部署报告"""
        logger.info("📊 生成部署报告")
        
        try:
            # 获取系统统计
            from confidence_system_wrapper import get_system_stats
            stats = get_system_stats()
            
            report = {
                'deployment_info': {
                    'deployment_id': self.deployment_id,
                    'deployment_time': datetime.now().isoformat(),
                    'deployment_mode': self.config['deployment_mode'],
                    'ab_test_ratio': self.config['ab_test_ratio']
                },
                'system_stats': stats,
                'deployment_config': self.config,
                'status': 'success'
            }
            
            report_file = f'deployment_report_{self.deployment_id}.json'
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 部署报告已生成: {report_file}")
            
        except Exception as e:
            logger.error(f"❌ 生成部署报告失败: {e}")

def main():
    """主函数"""
    print("🚀 多维度置信度评估系统部署工具")
    print("=" * 50)
    
    # 创建部署管理器
    deployment = ConfidenceSystemDeployment()
    
    # 执行部署
    success = deployment.deploy()
    
    if success:
        print("\n✅ 部署成功完成！")
        print("\n📋 后续步骤:")
        print("1. 监控系统运行状态")
        print("2. 观察置信度评估效果")
        print("3. 根据需要调整迁移模式")
        print("4. 定期检查部署报告")
        
        # 显示使用示例
        print("\n💡 使用示例:")
        print("```python")
        print("from confidence_system_wrapper import calculate_confidence")
        print("result = calculate_confidence([5, 40], context)")
        print("print(f'置信度: {result[\"final_confidence\"]:.3f}')")
        print("```")
    else:
        print("\n❌ 部署失败！")
        print("请检查日志文件 deployment.log 获取详细信息")

if __name__ == "__main__":
    main()
