#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复命中判断错误
Fix hit detection errors in prediction data
"""

import pandas as pd
import numpy as np

def fix_hit_detection():
    """修复命中检测错误"""
    print("🔧 修复命中检测错误")
    print("="*40)
    
    # 加载数据
    df = pd.read_csv('prediction_data.csv', encoding='utf-8')
    
    # 备份原文件
    df.to_csv('prediction_data_backup_before_fix.csv', index=False, encoding='utf-8')
    print("✅ 原文件已备份")
    
    fixes_made = 0
    
    for idx, row in df.iterrows():
        # 检查是否有完整的预测和实际数据
        if (pd.notna(row['预测数字1']) and pd.notna(row['预测数字2']) and 
            pd.notna(row['实际数字1']) and row['实际数字1'] != ''):
            
            try:
                # 获取预测数字
                pred1 = int(float(row['预测数字1']))
                pred2 = int(float(row['预测数字2']))
                
                # 获取实际数字
                actual = [
                    int(float(row['实际数字1'])),
                    int(float(row['实际数字2'])),
                    int(float(row['实际数字3'])),
                    int(float(row['实际数字4'])),
                    int(float(row['实际数字5'])),
                    int(float(row['实际数字6']))
                ]
                
                # 重新计算命中情况
                hit_numbers = []
                if pred1 in actual:
                    hit_numbers.append(pred1)
                if pred2 in actual:
                    hit_numbers.append(pred2)
                
                hit_count = len(hit_numbers)
                is_hit = hit_count > 0
                
                # 更新记录
                df.loc[idx, '命中数量'] = hit_count
                df.loc[idx, '是否命中'] = '是' if is_hit else '否'
                df.loc[idx, '命中数字'] = ','.join(map(str, hit_numbers)) if hit_numbers else ''
                
                fixes_made += 1
                
            except (ValueError, TypeError):
                continue
    
    # 保存修复后的文件
    df.to_csv('prediction_data.csv', index=False, encoding='utf-8')
    print(f"✅ 修复完成，共处理 {fixes_made} 条记录")
    
    return fixes_made

if __name__ == "__main__":
    fix_hit_detection()
