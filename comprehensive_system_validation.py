#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化动态置信度调整系统全面测试验证
Comprehensive System Validation for Optimized Dynamic Confidence Adjustment

对优化后的系统进行全面测试验证，包括命中率对比、置信度分析等

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0
"""

import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from optimized_confidence_system import OptimizedConfidenceAdjuster
import json
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ComprehensiveSystemValidator:
    """全面系统验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.original_data = None
        self.validation_results = []
        self.comparison_stats = {}
        
        print("🔍 优化动态置信度调整系统全面测试验证")
        print("="*60)
    
    def load_historical_data(self):
        """加载历史数据"""
        print("📊 加载历史数据")
        print("="*30)
        
        df = pd.read_csv('prediction_data.csv')
        
        # 数据清洗和准备
        df_clean = df[df['实际数字1'].notna()].copy()
        df_clean = df_clean.sort_values('当期期号').reset_index(drop=True)
        
        # 计算原始命中情况
        hit_results = []
        for _, row in df_clean.iterrows():
            predicted_numbers = [row['预测数字1'], row['预测数字2']]
            actual_numbers = [
                row['实际数字1'], row['实际数字2'], row['实际数字3'],
                row['实际数字4'], row['实际数字5'], row['实际数字6']
            ]
            
            hit_numbers = list(set(predicted_numbers) & set(actual_numbers))
            is_hit = len(hit_numbers) >= 1
            hit_count = len(hit_numbers)
            
            hit_results.append({
                'period': row['当期期号'],
                'predicted_numbers': predicted_numbers,
                'actual_numbers': actual_numbers,
                'hit_numbers': hit_numbers,
                'is_hit': is_hit,
                'hit_count': hit_count,
                'original_confidence': row['预测置信度'],
                'prediction_method': row['预测方法']
            })
        
        self.original_data = hit_results
        
        print(f"历史数据加载完成:")
        print(f"  总期数: {len(hit_results)}")
        print(f"  期号范围: {hit_results[0]['period']} - {hit_results[-1]['period']}")
        print(f"  原始命中率: {np.mean([r['is_hit'] for r in hit_results]):.1%}")
        
        return hit_results
    
    def simulate_optimized_system_performance(self, historical_data):
        """模拟优化系统性能（严格时间序列）"""
        print("\n🔧 模拟优化系统性能")
        print("="*40)
        
        # 初始化优化调整器
        adjuster = OptimizedConfidenceAdjuster()
        
        validation_results = []
        
        for i, data_point in enumerate(historical_data):
            period = data_point['period']
            
            # 使用优化系统调整置信度（只使用历史数据）
            prediction_context = {
                'period': period,
                'predicted_numbers': data_point['predicted_numbers'],
                'prediction_method': data_point['prediction_method']
            }
            
            confidence_result = adjuster.adjust_confidence_optimized(
                data_point['original_confidence'],
                prediction_context
            )
            
            # 构建验证结果
            validation_result = {
                'period': period,
                'predicted_numbers': data_point['predicted_numbers'],
                'actual_numbers': data_point['actual_numbers'],
                'hit_numbers': data_point['hit_numbers'],
                'is_hit': data_point['is_hit'],
                'hit_count': data_point['hit_count'],
                'original_confidence': data_point['original_confidence'],
                'optimized_confidence': confidence_result['final_confidence'],
                'adjustment_ratio': confidence_result['adjustment_ratio'],
                'adjustment_factors': confidence_result['adjustment_factors'],
                'confidence_interval': confidence_result['confidence_interval'],
                'calibration_applied': confidence_result['calibration_applied'],
                'confidence_interpretation': adjuster.get_confidence_interpretation(
                    confidence_result['final_confidence']
                ),
                'prediction_method': data_point['prediction_method']
            }
            
            validation_results.append(validation_result)
            
            # 更新历史性能（用于下一期调整）
            performance_update = {
                'period': period,
                'original_confidence': data_point['original_confidence'],
                'adjusted_confidence': confidence_result['final_confidence'],
                'is_hit': data_point['is_hit'],
                'hit_count': data_point['hit_count']
            }
            
            adjuster.update_historical_performance(performance_update)
            
            # 显示进度
            if period % 20 == 0:
                print(f"  验证进度: 第{period}期 "
                      f"(置信度: {data_point['original_confidence']:.3f} → {confidence_result['final_confidence']:.3f})")
        
        self.validation_results = validation_results
        
        print(f"✅ 优化系统性能模拟完成，共验证 {len(validation_results)} 期")
        return validation_results
    
    def compare_system_performance(self):
        """对比系统性能"""
        print("\n⚖️ 对比系统性能")
        print("="*30)
        
        # 基本统计
        original_hits = [r['is_hit'] for r in self.original_data]
        optimized_hits = [r['is_hit'] for r in self.validation_results]
        
        original_hit_rate = np.mean(original_hits)
        optimized_hit_rate = np.mean(optimized_hits)
        
        # 置信度统计
        original_confidences = [r['original_confidence'] for r in self.validation_results]
        optimized_confidences = [r['optimized_confidence'] for r in self.validation_results]
        
        # 统计显著性检验
        # 使用配对t检验作为替代
        from scipy.stats import ttest_rel

        # 构建混淆矩阵
        both_hit = sum(1 for i in range(len(original_hits)) if original_hits[i] and optimized_hits[i])
        original_only = sum(1 for i in range(len(original_hits)) if original_hits[i] and not optimized_hits[i])
        optimized_only = sum(1 for i in range(len(original_hits)) if not original_hits[i] and optimized_hits[i])
        both_miss = sum(1 for i in range(len(original_hits)) if not original_hits[i] and not optimized_hits[i])

        # 使用配对t检验
        try:
            # 将布尔值转换为数值进行t检验
            original_numeric = [1 if hit else 0 for hit in original_hits]
            optimized_numeric = [1 if hit else 0 for hit in optimized_hits]

            t_stat, p_value = ttest_rel(optimized_numeric, original_numeric)
            p_value = abs(p_value)  # 取绝对值，因为我们关心是否有差异
        except:
            p_value = 1.0  # 如果检验失败，设为不显著
        
        # 置信度改进统计
        confidence_improvements = [
            opt - orig for opt, orig in zip(optimized_confidences, original_confidences)
        ]
        
        improvement_rate = np.mean([1 if imp > 0 else 0 for imp in confidence_improvements])
        
        self.comparison_stats = {
            'hit_rate_comparison': {
                'original_hit_rate': original_hit_rate,
                'optimized_hit_rate': optimized_hit_rate,
                'hit_rate_difference': optimized_hit_rate - original_hit_rate,
                'hit_rate_improvement_pct': (optimized_hit_rate - original_hit_rate) / original_hit_rate * 100,
                'statistical_significance': {
                    'p_value': p_value,
                    'is_significant': p_value < 0.05
                }
            },
            'confidence_comparison': {
                'original_mean_confidence': np.mean(original_confidences),
                'optimized_mean_confidence': np.mean(optimized_confidences),
                'confidence_improvement': np.mean(confidence_improvements),
                'confidence_improvement_rate': improvement_rate,
                'confidence_std_original': np.std(original_confidences),
                'confidence_std_optimized': np.std(optimized_confidences)
            },
            'detailed_stats': {
                'both_hit': both_hit,
                'original_only': original_only,
                'optimized_only': optimized_only,
                'both_miss': both_miss,
                'total_periods': len(original_hits)
            }
        }
        
        print(f"命中率对比:")
        print(f"  原始系统命中率: {original_hit_rate:.1%}")
        print(f"  优化系统命中率: {optimized_hit_rate:.1%}")
        print(f"  命中率差异: {optimized_hit_rate - original_hit_rate:+.1%}")
        print(f"  相对改进: {(optimized_hit_rate - original_hit_rate) / original_hit_rate * 100:+.1f}%")
        print(f"  统计显著性: {'显著' if p_value < 0.05 else '不显著'} (p={p_value:.4f})")
        
        print(f"\n置信度对比:")
        print(f"  原始平均置信度: {np.mean(original_confidences):.3f}")
        print(f"  优化平均置信度: {np.mean(optimized_confidences):.3f}")
        print(f"  置信度改进: {np.mean(confidence_improvements):+.3f}")
        print(f"  改进比例: {improvement_rate:.1%}")
        
        return self.comparison_stats
    
    def generate_detailed_csv_output(self):
        """生成详细的CSV输出"""
        print("\n📊 生成详细CSV数据")
        print("="*30)
        
        # 准备CSV数据
        csv_data = []
        
        for result in self.validation_results:
            # 计算置信度提升比例
            confidence_improvement_ratio = (
                result['optimized_confidence'] / result['original_confidence'] 
                if result['original_confidence'] > 0 else 1.0
            )
            
            csv_row = {
                '期号': result['period'],
                '预测数字1': result['predicted_numbers'][0],
                '预测数字2': result['predicted_numbers'][1],
                '实际数字1': result['actual_numbers'][0],
                '实际数字2': result['actual_numbers'][1],
                '实际数字3': result['actual_numbers'][2],
                '实际数字4': result['actual_numbers'][3],
                '实际数字5': result['actual_numbers'][4],
                '实际数字6': result['actual_numbers'][5],
                '命中数字': str(result['hit_numbers']),
                '命中数量': result['hit_count'],
                '是否命中': '是' if result['is_hit'] else '否',
                '原始置信度': result['original_confidence'],
                '优化置信度': result['optimized_confidence'],
                '置信度提升比例': confidence_improvement_ratio,
                '置信度提升百分比': f"{(confidence_improvement_ratio - 1) * 100:.1f}%",
                '调整比例': result['adjustment_ratio'],
                '准确率因子': result['adjustment_factors']['accuracy_factor'],
                '校准因子': result['adjustment_factors']['calibration_factor'],
                '稳定性因子': result['adjustment_factors']['stability_factor'],
                '趋势因子': result['adjustment_factors']['trend_factor'],
                '复合因子': result['adjustment_factors']['composite_factor'],
                '置信度区间': result['confidence_interval'],
                '校准状态': '已应用' if result['calibration_applied'] else '未应用',
                '可靠性评估': result['confidence_interpretation'],
                '预测方法': result['prediction_method']
            }
            
            csv_data.append(csv_row)
        
        # 创建DataFrame并保存
        df_output = pd.DataFrame(csv_data)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f'comprehensive_validation_results_{timestamp}.csv'
        
        df_output.to_csv(csv_filename, index=False, encoding='utf-8-sig')
        
        print(f"详细CSV数据已保存: {csv_filename}")
        print(f"  数据行数: {len(csv_data)}")
        print(f"  数据列数: {len(csv_data[0])}")
        
        return csv_filename, df_output
    
    def create_comprehensive_visualizations(self):
        """创建综合可视化图表"""
        print("\n📊 创建综合可视化图表")
        print("="*30)
        
        fig, axes = plt.subplots(3, 2, figsize=(16, 18))
        fig.suptitle('优化动态置信度调整系统全面验证结果', fontsize=16, fontweight='bold')
        
        # 1. 命中率对比
        ax1 = axes[0, 0]
        systems = ['原始系统', '优化系统']
        hit_rates = [
            self.comparison_stats['hit_rate_comparison']['original_hit_rate'],
            self.comparison_stats['hit_rate_comparison']['optimized_hit_rate']
        ]
        
        bars = ax1.bar(systems, hit_rates, color=['#FF6B6B', '#4ECDC4'], alpha=0.7)
        ax1.set_ylabel('命中率')
        ax1.set_title('系统命中率对比')
        ax1.set_ylim(0, max(hit_rates) * 1.2)
        
        # 添加数值标注
        for bar, rate in zip(bars, hit_rates):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{rate:.1%}', ha='center', va='bottom', fontweight='bold')
        
        # 添加显著性标注
        p_value = self.comparison_stats['hit_rate_comparison']['statistical_significance']['p_value']
        significance = '***' if p_value < 0.001 else '**' if p_value < 0.01 else '*' if p_value < 0.05 else 'ns'
        ax1.text(0.5, max(hit_rates) * 1.1, f'p={p_value:.4f} ({significance})', 
                ha='center', transform=ax1.transData)
        
        ax1.grid(True, alpha=0.3)
        
        # 2. 置信度分布对比
        ax2 = axes[0, 1]
        original_confidences = [r['original_confidence'] for r in self.validation_results]
        optimized_confidences = [r['optimized_confidence'] for r in self.validation_results]
        
        ax2.hist(original_confidences, bins=20, alpha=0.6, label='原始置信度', color='#FF6B6B')
        ax2.hist(optimized_confidences, bins=20, alpha=0.6, label='优化置信度', color='#4ECDC4')
        ax2.set_xlabel('置信度')
        ax2.set_ylabel('频次')
        ax2.set_title('置信度分布对比')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 时间序列命中率
        ax3 = axes[1, 0]
        periods = [r['period'] for r in self.validation_results]
        
        # 计算滚动命中率
        window_size = 20
        rolling_original = []
        rolling_optimized = []
        
        for i in range(window_size, len(self.validation_results)):
            original_window = [self.original_data[j]['is_hit'] for j in range(i-window_size, i)]
            optimized_window = [self.validation_results[j]['is_hit'] for j in range(i-window_size, i)]
            
            rolling_original.append(np.mean(original_window))
            rolling_optimized.append(np.mean(optimized_window))
        
        rolling_periods = periods[window_size:]
        
        ax3.plot(rolling_periods, rolling_original, label='原始系统', color='#FF6B6B', alpha=0.8)
        ax3.plot(rolling_periods, rolling_optimized, label='优化系统', color='#4ECDC4', alpha=0.8)
        ax3.set_xlabel('期号')
        ax3.set_ylabel('滚动命中率')
        ax3.set_title(f'滚动命中率趋势 (窗口大小: {window_size})')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 置信度改进散点图
        ax4 = axes[1, 1]
        confidence_improvements = [
            opt - orig for opt, orig in zip(optimized_confidences, original_confidences)
        ]
        
        colors = ['green' if imp > 0 else 'red' for imp in confidence_improvements]
        ax4.scatter(original_confidences, confidence_improvements, c=colors, alpha=0.6)
        ax4.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax4.set_xlabel('原始置信度')
        ax4.set_ylabel('置信度改进')
        ax4.set_title('置信度改进散点图')
        ax4.grid(True, alpha=0.3)
        
        # 5. 调整因子分析
        ax5 = axes[2, 0]
        factor_names = ['准确率', '校准', '稳定性', '趋势']
        factor_means = [
            np.mean([r['adjustment_factors']['accuracy_factor'] for r in self.validation_results]),
            np.mean([r['adjustment_factors']['calibration_factor'] for r in self.validation_results]),
            np.mean([r['adjustment_factors']['stability_factor'] for r in self.validation_results]),
            np.mean([r['adjustment_factors']['trend_factor'] for r in self.validation_results])
        ]
        
        bars = ax5.bar(factor_names, factor_means, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'], alpha=0.7)
        ax5.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='基准线')
        ax5.set_ylabel('平均调整因子')
        ax5.set_title('调整因子分析')
        ax5.legend()
        ax5.grid(True, alpha=0.3)
        
        # 添加数值标注
        for bar, value in zip(bars, factor_means):
            height = bar.get_height()
            ax5.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=9)
        
        # 6. 性能总结
        ax6 = axes[2, 1]
        ax6.axis('off')
        
        # 创建性能总结文本
        summary_text = f"""
系统性能对比总结

命中率对比:
  原始系统: {self.comparison_stats['hit_rate_comparison']['original_hit_rate']:.1%}
  优化系统: {self.comparison_stats['hit_rate_comparison']['optimized_hit_rate']:.1%}
  改进幅度: {self.comparison_stats['hit_rate_comparison']['hit_rate_difference']:+.1%}
  相对改进: {self.comparison_stats['hit_rate_comparison']['hit_rate_improvement_pct']:+.1f}%

置信度对比:
  原始平均: {self.comparison_stats['confidence_comparison']['original_mean_confidence']:.3f}
  优化平均: {self.comparison_stats['confidence_comparison']['optimized_mean_confidence']:.3f}
  改进幅度: {self.comparison_stats['confidence_comparison']['confidence_improvement']:+.3f}
  改进比例: {self.comparison_stats['confidence_comparison']['confidence_improvement_rate']:.1%}

统计显著性:
  p值: {self.comparison_stats['hit_rate_comparison']['statistical_significance']['p_value']:.4f}
  显著性: {'显著' if self.comparison_stats['hit_rate_comparison']['statistical_significance']['is_significant'] else '不显著'}

验证期数: {self.comparison_stats['detailed_stats']['total_periods']}
        """
        
        ax6.text(0.05, 0.95, summary_text, transform=ax6.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.5))
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chart_filename = f'comprehensive_validation_charts_{timestamp}.png'
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        
        print(f"综合可视化图表已保存: {chart_filename}")
        
        return chart_filename

    def generate_comprehensive_report(self, csv_filename, chart_filename):
        """生成综合验证报告"""
        print("\n📋 生成综合验证报告")
        print("="*30)

        # 计算详细统计
        confidence_improvements = [
            r['optimized_confidence'] - r['original_confidence']
            for r in self.validation_results
        ]

        # 按置信度区间分析
        interval_analysis = {}
        for result in self.validation_results:
            interval = result['confidence_interval']
            if interval not in interval_analysis:
                interval_analysis[interval] = {
                    'count': 0,
                    'hit_count': 0,
                    'confidences': [],
                    'improvements': []
                }

            interval_analysis[interval]['count'] += 1
            if result['is_hit']:
                interval_analysis[interval]['hit_count'] += 1
            interval_analysis[interval]['confidences'].append(result['optimized_confidence'])
            interval_analysis[interval]['improvements'].append(
                result['optimized_confidence'] - result['original_confidence']
            )

        # 计算每个区间的统计
        for interval in interval_analysis:
            data = interval_analysis[interval]
            data['hit_rate'] = data['hit_count'] / data['count'] if data['count'] > 0 else 0
            data['mean_confidence'] = np.mean(data['confidences'])
            data['mean_improvement'] = np.mean(data['improvements'])

        # 创建详细报告
        report = {
            'validation_info': {
                'validation_date': datetime.now().isoformat(),
                'total_periods': len(self.validation_results),
                'period_range': f"{self.validation_results[0]['period']}-{self.validation_results[-1]['period']}",
                'system_version': '优化版 v2.1 vs 原始版 v2.0'
            },
            'system_comparison': self.comparison_stats,
            'confidence_analysis': {
                'original_confidence_stats': {
                    'mean': np.mean([r['original_confidence'] for r in self.validation_results]),
                    'std': np.std([r['original_confidence'] for r in self.validation_results]),
                    'min': np.min([r['original_confidence'] for r in self.validation_results]),
                    'max': np.max([r['original_confidence'] for r in self.validation_results])
                },
                'optimized_confidence_stats': {
                    'mean': np.mean([r['optimized_confidence'] for r in self.validation_results]),
                    'std': np.std([r['optimized_confidence'] for r in self.validation_results]),
                    'min': np.min([r['optimized_confidence'] for r in self.validation_results]),
                    'max': np.max([r['optimized_confidence'] for r in self.validation_results])
                },
                'improvement_stats': {
                    'mean_improvement': np.mean(confidence_improvements),
                    'improvement_rate': np.mean([1 if imp > 0 else 0 for imp in confidence_improvements]),
                    'max_improvement': np.max(confidence_improvements),
                    'min_improvement': np.min(confidence_improvements)
                }
            },
            'interval_analysis': interval_analysis,
            'calibration_effectiveness': self._analyze_calibration_effectiveness(),
            'generated_files': {
                'csv_data': csv_filename,
                'visualization': chart_filename
            }
        }

        # 保存JSON报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_filename = f'comprehensive_validation_report_{timestamp}.json'

        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)

        # 生成文本报告
        text_report = self._generate_text_report(report)
        text_filename = f'comprehensive_validation_summary_{timestamp}.txt'

        with open(text_filename, 'w', encoding='utf-8') as f:
            f.write(text_report)

        print(f"详细报告已保存: {json_filename}")
        print(f"摘要报告已保存: {text_filename}")

        return json_filename, text_filename

    def _analyze_calibration_effectiveness(self):
        """分析校准有效性"""
        calibrated_results = [r for r in self.validation_results if r['calibration_applied']]

        if len(calibrated_results) == 0:
            return {'calibration_applied_count': 0, 'effectiveness': 'N/A'}

        # 计算校准后的性能
        calibrated_confidences = [r['optimized_confidence'] for r in calibrated_results]
        calibrated_hits = [r['is_hit'] for r in calibrated_results]

        # 简单的校准误差计算
        if len(set(calibrated_confidences)) > 1:
            from sklearn.calibration import calibration_curve
            try:
                fraction_of_positives, mean_predicted_value = calibration_curve(
                    calibrated_hits, calibrated_confidences, n_bins=min(10, len(calibrated_results)//5)
                )
                calibration_error = np.mean(np.abs(fraction_of_positives - mean_predicted_value))
            except:
                calibration_error = abs(np.mean(calibrated_hits) - np.mean(calibrated_confidences))
        else:
            calibration_error = abs(np.mean(calibrated_hits) - np.mean(calibrated_confidences))

        return {
            'calibration_applied_count': len(calibrated_results),
            'calibration_applied_rate': len(calibrated_results) / len(self.validation_results),
            'calibration_error': calibration_error,
            'calibrated_hit_rate': np.mean(calibrated_hits),
            'calibrated_mean_confidence': np.mean(calibrated_confidences)
        }

    def _generate_text_report(self, report):
        """生成文本报告"""
        text = f"""
优化动态置信度调整系统全面测试验证报告
{'='*70}

验证信息:
{'='*30}
验证日期: {report['validation_info']['validation_date'][:19]}
验证期数: {report['validation_info']['total_periods']}期
期号范围: 第{report['validation_info']['period_range']}期
系统版本: {report['validation_info']['system_version']}

系统性能对比:
{'='*30}
命中率对比:
  原始系统命中率: {report['system_comparison']['hit_rate_comparison']['original_hit_rate']:.1%}
  优化系统命中率: {report['system_comparison']['hit_rate_comparison']['optimized_hit_rate']:.1%}
  命中率差异: {report['system_comparison']['hit_rate_comparison']['hit_rate_difference']:+.1%}
  相对改进: {report['system_comparison']['hit_rate_comparison']['hit_rate_improvement_pct']:+.1f}%
  统计显著性: {'显著' if report['system_comparison']['hit_rate_comparison']['statistical_significance']['is_significant'] else '不显著'} (p={report['system_comparison']['hit_rate_comparison']['statistical_significance']['p_value']:.4f})

置信度对比:
  原始平均置信度: {report['confidence_analysis']['original_confidence_stats']['mean']:.3f} ± {report['confidence_analysis']['original_confidence_stats']['std']:.3f}
  优化平均置信度: {report['confidence_analysis']['optimized_confidence_stats']['mean']:.3f} ± {report['confidence_analysis']['optimized_confidence_stats']['std']:.3f}
  置信度改进: {report['confidence_analysis']['improvement_stats']['mean_improvement']:+.3f}
  改进比例: {report['confidence_analysis']['improvement_stats']['improvement_rate']:.1%}

置信度区间分析:
{'='*30}"""

        for interval, data in report['interval_analysis'].items():
            text += f"""
{interval}:
  样本数量: {data['count']}
  命中率: {data['hit_rate']:.1%}
  平均置信度: {data['mean_confidence']:.3f}
  平均改进: {data['mean_improvement']:+.3f}"""

        text += f"""

校准有效性分析:
{'='*30}
校准应用次数: {report['calibration_effectiveness']['calibration_applied_count']}
校准应用比例: {report['calibration_effectiveness']['calibration_applied_rate']:.1%}
校准误差: {report['calibration_effectiveness']['calibration_error']:.4f}
校准后命中率: {report['calibration_effectiveness']['calibrated_hit_rate']:.1%}
校准后平均置信度: {report['calibration_effectiveness']['calibrated_mean_confidence']:.3f}

详细统计分析:
{'='*30}
原始置信度统计:
  范围: {report['confidence_analysis']['original_confidence_stats']['min']:.3f} - {report['confidence_analysis']['original_confidence_stats']['max']:.3f}
  标准差: {report['confidence_analysis']['original_confidence_stats']['std']:.3f}

优化置信度统计:
  范围: {report['confidence_analysis']['optimized_confidence_stats']['min']:.3f} - {report['confidence_analysis']['optimized_confidence_stats']['max']:.3f}
  标准差: {report['confidence_analysis']['optimized_confidence_stats']['std']:.3f}

置信度改进统计:
  最大改进: {report['confidence_analysis']['improvement_stats']['max_improvement']:+.3f}
  最小改进: {report['confidence_analysis']['improvement_stats']['min_improvement']:+.3f}

系统评估结论:
{'='*30}"""

        # 添加评估结论
        hit_rate_diff = report['system_comparison']['hit_rate_comparison']['hit_rate_difference']
        is_significant = report['system_comparison']['hit_rate_comparison']['statistical_significance']['is_significant']
        confidence_improvement = report['confidence_analysis']['improvement_stats']['mean_improvement']

        if hit_rate_diff > 0 and is_significant:
            text += "\n✅ 优化系统在命中率方面有显著改善"
        elif hit_rate_diff > 0:
            text += "\n⚠️ 优化系统命中率有所改善，但统计上不显著"
        else:
            text += "\n❌ 优化系统命中率未见改善"

        if confidence_improvement > 0.01:
            text += "\n✅ 置信度调整效果显著，大幅提升了置信度水平"
        elif confidence_improvement > 0:
            text += "\n⚠️ 置信度有所改善，但提升幅度有限"
        else:
            text += "\n❌ 置信度调整效果不明显"

        calibration_error = report['calibration_effectiveness']['calibration_error']
        if calibration_error < 0.1:
            text += "\n✅ 校准效果良好，置信度与实际命中率匹配度高"
        elif calibration_error < 0.2:
            text += "\n⚠️ 校准效果一般，存在一定偏差"
        else:
            text += "\n❌ 校准效果较差，需要进一步优化"

        text += f"""

改进建议:
{'='*30}"""

        if hit_rate_diff <= 0:
            text += "\n1. 考虑调整预测算法本身，而不仅仅是置信度"

        if confidence_improvement < 0.05:
            text += "\n2. 增加调整因子的权重，提高置信度调整幅度"

        if calibration_error > 0.1:
            text += "\n3. 优化校准模型，考虑使用更先进的校准方法"

        if report['confidence_analysis']['optimized_confidence_stats']['std'] < 0.05:
            text += "\n4. 增加置信度的变化范围，提供更精细的区分度"

        text += f"""

生成文件:
{'='*30}
详细数据: {report['generated_files']['csv_data']}
可视化图表: {report['generated_files']['visualization']}

---
报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
验证系统版本: 全面测试验证器 v1.0
"""

        return text

    def run_comprehensive_validation(self):
        """运行全面验证"""
        print("🚀 启动优化动态置信度调整系统全面验证")
        print("="*70)

        try:
            # 1. 加载历史数据
            historical_data = self.load_historical_data()

            # 2. 模拟优化系统性能
            validation_results = self.simulate_optimized_system_performance(historical_data)

            # 3. 对比系统性能
            comparison_stats = self.compare_system_performance()

            # 4. 生成详细CSV输出
            csv_filename, df_output = self.generate_detailed_csv_output()

            # 5. 创建综合可视化
            chart_filename = self.create_comprehensive_visualizations()

            # 6. 生成综合报告
            json_report, text_report = self.generate_comprehensive_report(csv_filename, chart_filename)

            print("\n" + "="*70)
            print("✅ 优化动态置信度调整系统全面验证完成！")
            print("="*70)

            print(f"\n🎯 验证结果摘要:")
            print(f"  验证期数: {len(validation_results)}")
            print(f"  原始命中率: {comparison_stats['hit_rate_comparison']['original_hit_rate']:.1%}")
            print(f"  优化命中率: {comparison_stats['hit_rate_comparison']['optimized_hit_rate']:.1%}")
            print(f"  命中率改进: {comparison_stats['hit_rate_comparison']['hit_rate_difference']:+.1%}")
            print(f"  置信度改进: {comparison_stats['confidence_comparison']['confidence_improvement']:+.3f}")
            print(f"  统计显著性: {'显著' if comparison_stats['hit_rate_comparison']['statistical_significance']['is_significant'] else '不显著'}")

            print(f"\n📁 生成文件:")
            print(f"  详细数据: {csv_filename}")
            print(f"  可视化图表: {chart_filename}")
            print(f"  详细报告: {json_report}")
            print(f"  摘要报告: {text_report}")

            return {
                'validation_results': validation_results,
                'comparison_stats': comparison_stats,
                'csv_filename': csv_filename,
                'chart_filename': chart_filename,
                'json_report': json_report,
                'text_report': text_report
            }

        except Exception as e:
            print(f"\n❌ 验证过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    validator = ComprehensiveSystemValidator()
    results = validator.run_comprehensive_validation()

    if results:
        print(f"\n🎉 全面验证成功完成！")
        print(f"优化动态置信度调整系统已通过全面测试验证。")
    else:
        print(f"\n❌ 全面验证失败！")
        print(f"请检查错误信息并修复问题。")

if __name__ == "__main__":
    main()
