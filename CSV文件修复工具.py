#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV文件修复工具 - 修复prediction_data.csv中的显示问题
专门处理188-192期的命中状态显示问题
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class CSVRepairTool:
    """CSV文件修复工具"""
    
    def __init__(self):
        self.prediction_data_file = "prediction_data.csv"
        self.main_data_file = "data/processed/lottery_data_clean_no_special.csv"
        
    def load_files(self):
        """加载文件"""
        try:
            # 加载预测数据
            if not os.path.exists(self.prediction_data_file):
                print(f"❌ 预测数据文件不存在: {self.prediction_data_file}")
                return False
            
            self.prediction_df = pd.read_csv(self.prediction_data_file, encoding='utf-8-sig')
            print(f"✅ 预测数据加载完成: {len(self.prediction_df)}条记录")
            
            # 加载主数据
            if not os.path.exists(self.main_data_file):
                print(f"❌ 主数据文件不存在: {self.main_data_file}")
                return False
            
            self.main_df = pd.read_csv(self.main_data_file)
            print(f"✅ 主数据加载完成: {len(self.main_df)}期")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载文件失败: {e}")
            return False
    
    def analyze_csv_issues(self):
        """分析CSV文件问题"""
        print(f"\n🔍 分析prediction_data.csv文件问题")
        print("=" * 60)
        
        # 检查未验证的预测
        unverified = self.prediction_df[
            (self.prediction_df['实际数字1'].isna()) | 
            (self.prediction_df['实际数字1'] == '') |
            (self.prediction_df['实际数字1'] == 'nan')
        ].copy()
        
        print(f"📊 文件状态分析:")
        print(f"总记录数: {len(self.prediction_df)}")
        print(f"已验证记录: {len(self.prediction_df) - len(unverified)}")
        print(f"未验证记录: {len(unverified)}")
        
        if len(unverified) > 0:
            print(f"\n📋 未验证的预测记录:")
            for idx, row in unverified.iterrows():
                print(f"  行{idx+2}: {row['预测期号']} - 预测[{row['预测数字1']},{row['预测数字2']}]")
        
        # 检查主数据中是否有对应的实际数据
        print(f"\n🔍 检查主数据中的对应期数:")
        for idx, row in unverified.iterrows():
            period_name = row['预测期号']
            if '年' in period_name and '期' in period_name:
                try:
                    # 解析期号
                    year_str = period_name.split('年')[0]
                    period_str = period_name.split('年')[1].replace('期', '')
                    year = int(year_str)
                    period = int(period_str)
                    
                    # 在主数据中查找
                    matching_main = self.main_df[
                        (self.main_df['年份'] == year) & 
                        (self.main_df['期号'] == period)
                    ]
                    
                    if len(matching_main) > 0:
                        actual_numbers = [matching_main.iloc[0][f'数字{i}'] for i in range(1, 7)]
                        print(f"  ✅ {period_name}: 主数据中存在 {actual_numbers}")
                    else:
                        print(f"  ❌ {period_name}: 主数据中不存在")
                        
                except Exception as e:
                    print(f"  ⚠️ {period_name}: 解析失败 - {e}")
    
    def auto_verify_from_main_data(self):
        """从主数据自动验证预测"""
        print(f"\n🔧 从主数据自动验证预测")
        print("=" * 50)
        
        # 获取未验证的预测
        unverified_mask = (
            (self.prediction_df['实际数字1'].isna()) | 
            (self.prediction_df['实际数字1'] == '') |
            (self.prediction_df['实际数字1'] == 'nan')
        )
        
        unverified_indices = self.prediction_df[unverified_mask].index.tolist()
        
        if len(unverified_indices) == 0:
            print("✅ 所有预测都已验证")
            return True
        
        verified_count = 0
        
        for idx in unverified_indices:
            row = self.prediction_df.iloc[idx]
            period_name = row['预测期号']
            
            try:
                # 解析期号
                if '年' in period_name and '期' in period_name:
                    year_str = period_name.split('年')[0]
                    period_str = period_name.split('年')[1].replace('期', '')
                    year = int(year_str)
                    period = int(period_str)
                    
                    # 在主数据中查找对应的实际数据
                    matching_main = self.main_df[
                        (self.main_df['年份'] == year) & 
                        (self.main_df['期号'] == period)
                    ]
                    
                    if len(matching_main) > 0:
                        # 获取实际开奖数字
                        actual_numbers = [matching_main.iloc[0][f'数字{i}'] for i in range(1, 7)]
                        
                        # 获取预测数字
                        predicted_numbers = [row['预测数字1'], row['预测数字2']]
                        
                        # 计算命中情况
                        predicted_set = set(predicted_numbers)
                        actual_set = set(actual_numbers)
                        hit_numbers = predicted_set & actual_set
                        hit_count = len(hit_numbers)
                        is_hit = hit_count >= 1
                        
                        # 更新预测记录
                        for i, num in enumerate(actual_numbers, 1):
                            self.prediction_df.loc[idx, f'实际数字{i}'] = num
                        
                        self.prediction_df.loc[idx, '命中数量'] = hit_count
                        self.prediction_df.loc[idx, '是否命中'] = '是' if is_hit else '否'
                        self.prediction_df.loc[idx, '命中数字'] = ','.join(map(str, sorted(hit_numbers))) if hit_numbers else ''
                        
                        # 更新备注
                        current_note = self.prediction_df.loc[idx, '备注']
                        if pd.isna(current_note) or current_note == '':
                            self.prediction_df.loc[idx, '备注'] = '自动验证'
                        else:
                            self.prediction_df.loc[idx, '备注'] = f"{current_note},自动验证"
                        
                        verified_count += 1
                        status = "✅ 命中" if is_hit else "❌ 未命中"
                        hit_info = f"命中{sorted(hit_numbers)}" if hit_numbers else "未命中"
                        print(f"  {period_name}: 预测{predicted_numbers} vs 实际{actual_numbers} {status} {hit_info}")
                    
                    else:
                        print(f"  ⚠️ {period_name}: 主数据中无对应记录")
                
            except Exception as e:
                print(f"  ❌ {period_name}: 验证失败 - {e}")
        
        print(f"\n✅ 自动验证完成: {verified_count}条记录已更新")
        return verified_count > 0
    
    def save_repaired_csv(self):
        """保存修复后的CSV文件"""
        try:
            # 备份原文件
            backup_file = f"{self.prediction_data_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.prediction_df_original = pd.read_csv(self.prediction_data_file, encoding='utf-8-sig')
            self.prediction_df_original.to_csv(backup_file, index=False, encoding='utf-8-sig')
            print(f"✅ 原文件已备份: {backup_file}")
            
            # 保存修复后的文件
            self.prediction_df.to_csv(self.prediction_data_file, index=False, encoding='utf-8-sig')
            print(f"✅ 修复后的文件已保存: {self.prediction_data_file}")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return False
    
    def show_verification_summary(self):
        """显示验证总结"""
        print(f"\n📊 验证总结")
        print("=" * 50)
        
        total_predictions = len(self.prediction_df)
        
        # 已验证的预测
        verified_predictions = self.prediction_df[
            (~self.prediction_df['实际数字1'].isna()) & 
            (self.prediction_df['实际数字1'] != '') &
            (self.prediction_df['实际数字1'] != 'nan')
        ].copy()
        
        unverified_count = total_predictions - len(verified_predictions)
        
        print(f"总预测期数: {total_predictions}")
        print(f"已验证期数: {len(verified_predictions)}")
        print(f"待验证期数: {unverified_count}")
        
        if len(verified_predictions) > 0:
            hits = len(verified_predictions[verified_predictions['是否命中'] == '是'])
            hit_rate = hits / len(verified_predictions)
            
            print(f"命中期数: {hits}")
            print(f"命中率: {hit_rate:.3f} ({hit_rate:.1%})")
            
            # 最近验证的记录
            recent = verified_predictions.tail(10)
            print(f"\n最近{len(recent)}期验证结果:")
            for _, row in recent.iterrows():
                status = "✅" if row['是否命中'] == '是' else "❌"
                hit_info = f"命中{row['命中数字']}" if row['命中数字'] else "未命中"
                print(f"  {row['预测期号']}: 预测[{row['预测数字1']},{row['预测数字2']}] {status} {hit_info}")
    
    def show_specific_periods(self, start_period=188, end_period=192):
        """显示特定期数的详细信息"""
        print(f"\n📋 {start_period}-{end_period}期详细信息")
        print("=" * 80)
        
        for period in range(start_period, end_period + 1):
            period_name = f"2025年{period}期"
            
            # 在预测数据中查找
            matching_predictions = self.prediction_df[
                self.prediction_df['预测期号'] == period_name
            ]
            
            if len(matching_predictions) > 0:
                row = matching_predictions.iloc[0]
                predicted = [row['预测数字1'], row['预测数字2']]
                
                # 检查是否已验证
                if pd.isna(row['实际数字1']) or row['实际数字1'] == '':
                    print(f"{period_name}: 预测{predicted} - ⏳ 待验证")
                else:
                    actual = [row[f'实际数字{i}'] for i in range(1, 7)]
                    status = "✅" if row['是否命中'] == '是' else "❌"
                    hit_info = f"命中{row['命中数字']}" if row['命中数字'] else "未命中"
                    print(f"{period_name}: 预测{predicted} vs 实际{actual} {status} {hit_info}")
            else:
                print(f"{period_name}: ❌ 无预测记录")
    
    def main_menu(self):
        """主菜单"""
        while True:
            print(f"\n🔧 CSV文件修复工具")
            print("=" * 40)
            print("1. 分析CSV文件问题")
            print("2. 从主数据自动验证预测")
            print("3. 保存修复后的文件")
            print("4. 显示验证总结")
            print("5. 显示188-192期详细信息")
            print("6. 退出工具")
            
            choice = input("\n请选择操作 (1-6): ").strip()
            
            if choice == '1':
                self.analyze_csv_issues()
            
            elif choice == '2':
                if self.auto_verify_from_main_data():
                    print("✅ 自动验证完成")
                else:
                    print("⚠️ 没有可验证的记录")
            
            elif choice == '3':
                self.save_repaired_csv()
            
            elif choice == '4':
                self.show_verification_summary()
            
            elif choice == '5':
                self.show_specific_periods(188, 192)
            
            elif choice == '6':
                print("👋 退出修复工具")
                break
            
            else:
                print("❌ 无效选择，请重新输入")

def main():
    """主函数"""
    print("🔧 CSV文件修复工具")
    print("专门修复prediction_data.csv中188-192期的命中状态显示问题")
    print("=" * 70)
    
    tool = CSVRepairTool()
    
    # 初始化
    if not tool.load_files():
        return
    
    print("✅ 工具初始化完成")
    print("\n💡 功能说明:")
    print("1. 分析CSV文件中的问题")
    print("2. 从主数据自动验证未验证的预测")
    print("3. 修复命中状态显示问题")
    print("4. 保存修复后的文件")
    
    # 启动主菜单
    tool.main_menu()

if __name__ == "__main__":
    main()
