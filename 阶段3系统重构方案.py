#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阶段3系统重构方案 - 基于34.3%突破的保守优化
目标: 在保持稳定性的前提下，探索35%的可能性
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict, Counter
from sklearn.ensemble import VotingClassifier, RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ConservativeSystemRestructure:
    """保守系统重构 - 基于34.3%突破的稳定优化"""
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.full_data = None
        self.train_data = None
        self.test_data = None
        
        # 基于34.3%成功的保守参数 (不做大幅调整)
        self.conservative_params = {
            'high_freq_boost': 1.15,      # 保持成功参数
            'low_freq_penalty': 0.85,     # 保持成功参数
            'rising_trend_boost': 1.10,   # 保持成功参数
            'falling_trend_penalty': 0.90, # 保持成功参数
            'perturbation': 0.05,         # 保持成功参数
            'ensemble_weight_markov': 0.6, # 马尔可夫权重
            'ensemble_weight_ml': 0.3,    # 机器学习权重
            'ensemble_weight_external': 0.1 # 外部信息权重
        }
        
        # 集成学习组件
        self.ensemble_models = {}
        self.external_factors = {}
        
        # 稳定性监控
        self.stability_metrics = {
            'performance_variance': [],
            'prediction_consistency': [],
            'method_reliability': []
        }
        
        self.restructure_results = {}
        
    def load_data(self):
        """加载数据"""
        print(f"🏗️ 阶段3系统重构 - 基于34.3%突破的保守优化")
        print("目标: 在保持稳定性前提下探索35%可能性")
        print("=" * 60)
        
        try:
            self.full_data = pd.read_csv(self.data_file)
            self.full_data = self.full_data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            # 训练数据：2023-2024年
            self.train_data = self.full_data[
                (self.full_data['年份'] >= 2023) & 
                (self.full_data['年份'] <= 2024)
            ].copy()
            
            # 测试数据：2025年1-179期
            self.test_data = self.full_data[
                (self.full_data['年份'] == 2025) & 
                (self.full_data['期号'] <= 179)
            ].copy()
            
            print(f"✅ 数据加载完成")
            print(f"  训练集: {len(self.train_data)}期 (2023-2024年)")
            print(f"  测试集: {len(self.test_data)}期 (2025年1-179期)")
            
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def build_conservative_ensemble_system(self):
        """构建保守集成系统"""
        print(f"\n🏗️ 构建保守集成系统")
        print("=" * 50)
        
        # 1. 保持成功的全面增强马尔可夫作为核心
        self.build_core_markov_system()
        
        # 2. 添加轻量级机器学习增强
        self.build_lightweight_ml_enhancement()
        
        # 3. 融入最小外部信息
        self.build_minimal_external_factors()
        
        # 4. 构建保守集成框架
        self.build_conservative_ensemble()
        
        print(f"✅ 保守集成系统构建完成")
    
    def build_core_markov_system(self):
        """构建核心马尔可夫系统 (保持34.3%成功配置)"""
        print(f"  构建核心马尔可夫系统...")
        
        # 基础马尔可夫转移概率 (与34.3%成功版本完全一致)
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(self.train_data) - 1):
            current_numbers = set([self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 计算基础转移概率
        self.base_markov_prob = {}
        for curr_num in range(1, 50):
            if curr_num in transition_count:
                total = sum(transition_count[curr_num].values())
                smoothed_total = total + 49
                self.base_markov_prob[curr_num] = {}
                
                for next_num in range(1, 50):
                    count = transition_count[curr_num].get(next_num, 0)
                    self.base_markov_prob[curr_num][next_num] = (count + 1) / smoothed_total
            else:
                self.base_markov_prob[curr_num] = {
                    next_num: 1/49 for next_num in range(1, 50)
                }
        
        # 应用成功的增强策略 (完全保持34.3%的配置)
        self.apply_proven_enhancements()
        
        print(f"    核心马尔可夫系统: 保持34.3%成功配置")
    
    def apply_proven_enhancements(self):
        """应用已验证的增强策略"""
        high_freq_numbers = [5, 15, 3, 40, 30]
        low_freq_numbers = [41, 1, 8, 48, 47]
        rising_numbers = [30, 39, 4, 8, 22]
        falling_numbers = [5, 26, 44, 36, 15]
        
        # 应用成功的权重配置
        frequency_weights = {}
        trend_weights = {}
        
        for num in range(1, 50):
            if num in high_freq_numbers:
                frequency_weights[num] = self.conservative_params['high_freq_boost']
            elif num in low_freq_numbers:
                frequency_weights[num] = self.conservative_params['low_freq_penalty']
            else:
                frequency_weights[num] = 1.0
            
            if num in rising_numbers:
                trend_weights[num] = self.conservative_params['rising_trend_boost']
            elif num in falling_numbers:
                trend_weights[num] = self.conservative_params['falling_trend_penalty']
            else:
                trend_weights[num] = 1.0
        
        # 构建增强马尔可夫概率
        self.enhanced_markov_prob = {}
        for curr_num in self.base_markov_prob:
            self.enhanced_markov_prob[curr_num] = {}
            total_weight = 0
            
            for next_num, base_prob in self.base_markov_prob[curr_num].items():
                freq_weight = frequency_weights[next_num]
                trend_weight = trend_weights[next_num]
                combined_weight = freq_weight * trend_weight
                weighted_prob = base_prob * combined_weight
                
                self.enhanced_markov_prob[curr_num][next_num] = weighted_prob
                total_weight += weighted_prob
            
            # 归一化
            for next_num in self.enhanced_markov_prob[curr_num]:
                self.enhanced_markov_prob[curr_num][next_num] /= total_weight
    
    def build_lightweight_ml_enhancement(self):
        """构建轻量级机器学习增强"""
        print(f"  构建轻量级机器学习增强...")
        
        # 准备特征数据 (简单特征，避免过拟合)
        features, targets = self.prepare_simple_features()
        
        # 使用简单的机器学习模型
        self.ml_models = {
            'random_forest': RandomForestClassifier(n_estimators=50, max_depth=5, random_state=42),
            'logistic': LogisticRegression(random_state=42, max_iter=1000)
        }
        
        # 训练模型
        for name, model in self.ml_models.items():
            try:
                model.fit(features, targets)
                print(f"    {name}: 训练完成")
            except Exception as e:
                print(f"    {name}: 训练失败 - {e}")
        
        print(f"    轻量级ML增强: 权重{self.conservative_params['ensemble_weight_ml']:.1f}")
    
    def prepare_simple_features(self):
        """准备简单特征 (避免过拟合)"""
        features = []
        targets = []
        
        for i in range(len(self.train_data) - 1):
            # 简单特征: 前一期的基本统计
            prev_numbers = [self.train_data.iloc[i][f'数字{j}'] for j in range(1, 7)]
            next_numbers = [self.train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)]
            
            # 特征向量 (保持简单)
            feature_vector = [
                sum(prev_numbers),  # 数字和
                sum(1 for n in prev_numbers if n % 2 == 1),  # 奇数个数
                max(prev_numbers) - min(prev_numbers),  # 跨度
                len(set(prev_numbers) & set([5, 15, 3, 40, 30])),  # 高频数字个数
                self.train_data.iloc[i]['期号'] % 12 + 1  # 月份 (简化)
            ]
            
            # 目标: 下一期是否包含预测的数字 (简化为二分类)
            target = 1 if len(set(next_numbers) & set([5, 15])) > 0 else 0
            
            features.append(feature_vector)
            targets.append(target)
        
        return np.array(features), np.array(targets)
    
    def build_minimal_external_factors(self):
        """构建最小外部因素"""
        print(f"  构建最小外部因素...")
        
        # 只考虑最基本的外部因素 (避免过度复杂化)
        self.external_factors = {
            'month_adjustment': {
                1: -0.02, 2: 0.05, 3: 0.01, 4: 0.01, 5: -0.01, 6: -0.01,
                7: -0.08, 8: 0.03, 9: -0.01, 10: -0.02, 11: 0.01, 12: 0.00
            },
            'quarter_adjustment': {
                1: 0.02, 2: 0.00, 3: -0.02, 4: 0.00
            }
        }
        
        print(f"    外部因素: 月度+季度调整，权重{self.conservative_params['ensemble_weight_external']:.1f}")
    
    def build_conservative_ensemble(self):
        """构建保守集成框架"""
        print(f"  构建保守集成框架...")
        
        # 集成权重 (保守配置，马尔可夫为主)
        self.ensemble_weights = {
            'markov': self.conservative_params['ensemble_weight_markov'],
            'ml': self.conservative_params['ensemble_weight_ml'],
            'external': self.conservative_params['ensemble_weight_external']
        }
        
        print(f"    集成权重: 马尔可夫{self.ensemble_weights['markov']:.1f}, "
              f"ML{self.ensemble_weights['ml']:.1f}, 外部{self.ensemble_weights['external']:.1f}")
    
    def conservative_ensemble_prediction(self, prev_numbers, period_info):
        """保守集成预测"""
        # 1. 核心马尔可夫预测 (主要组件)
        markov_pred = self.enhanced_markov_predict(prev_numbers)
        
        # 2. 轻量级ML增强 (辅助组件)
        ml_adjustment = self.get_ml_adjustment(prev_numbers, period_info)
        
        # 3. 最小外部因素 (微调组件)
        external_adjustment = self.get_external_adjustment(period_info)
        
        # 4. 保守集成
        final_prediction = self.conservative_ensemble(markov_pred, ml_adjustment, external_adjustment)
        
        return final_prediction
    
    def enhanced_markov_predict(self, prev_numbers):
        """增强马尔可夫预测 (保持34.3%成功配置)"""
        np.random.seed(42)
        
        number_probs = defaultdict(float)
        total_prob = 0.0
        
        for prev_num in prev_numbers:
            if prev_num in self.enhanced_markov_prob:
                for next_num, prob in self.enhanced_markov_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        # 添加成功的随机扰动
        perturbation = self.conservative_params['perturbation']
        for num in number_probs:
            noise = np.random.normal(0, perturbation * number_probs[num])
            number_probs[num] = max(0, number_probs[num] + noise)
        
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            return [num for num, prob in sorted_numbers[:2]]
        else:
            return [1, 2]
    
    def get_ml_adjustment(self, prev_numbers, period_info):
        """获取ML调整 (轻量级)"""
        if not self.ml_models:
            return [0, 0]
        
        # 准备特征
        prev_list = list(prev_numbers)
        feature_vector = [
            sum(prev_list),
            sum(1 for n in prev_list if n % 2 == 1),
            max(prev_list) - min(prev_list),
            len(set(prev_list) & set([5, 15, 3, 40, 30])),
            period_info['period'] % 12 + 1
        ]
        
        # 获取ML预测 (简化处理)
        try:
            rf_pred = self.ml_models['random_forest'].predict_proba([feature_vector])[0]
            if rf_pred[1] > 0.6:  # 高置信度时给出调整建议
                return [5, 15]  # 倾向于高频数字
            else:
                return [0, 0]  # 无调整
        except:
            return [0, 0]
    
    def get_external_adjustment(self, period_info):
        """获取外部调整 (最小化)"""
        month = period_info['period'] % 12 + 1
        quarter = (period_info['period'] - 1) // 90 % 4 + 1
        
        month_adj = self.external_factors['month_adjustment'].get(month, 0)
        quarter_adj = self.external_factors['quarter_adjustment'].get(quarter, 0)
        
        # 转换为数字调整 (非常保守)
        total_adj = month_adj + quarter_adj
        if abs(total_adj) > 0.03:  # 只有显著调整时才应用
            if total_adj > 0:
                return [1, 1]  # 轻微向上调整
            else:
                return [-1, -1]  # 轻微向下调整
        
        return [0, 0]
    
    def conservative_ensemble(self, markov_pred, ml_adjustment, external_adjustment):
        """保守集成"""
        # 以马尔可夫预测为主，其他为辅助微调
        final_pred = markov_pred.copy()
        
        # 应用ML调整 (权重很小)
        if ml_adjustment != [0, 0]:
            for i in range(len(final_pred)):
                if ml_adjustment[i] != 0:
                    adjustment = int(ml_adjustment[i] * self.ensemble_weights['ml'])
                    final_pred[i] = max(1, min(49, final_pred[i] + adjustment))
        
        # 应用外部调整 (权重最小)
        if external_adjustment != [0, 0]:
            for i in range(len(final_pred)):
                if external_adjustment[i] != 0:
                    adjustment = int(external_adjustment[i] * self.ensemble_weights['external'])
                    final_pred[i] = max(1, min(49, final_pred[i] + adjustment))
        
        # 确保不重复
        if final_pred[0] == final_pred[1]:
            final_pred[1] = final_pred[1] + 1 if final_pred[1] < 49 else final_pred[1] - 1
        
        return final_pred
    
    def run_conservative_validation(self):
        """运行保守验证"""
        print(f"\n🔍 开始保守系统重构验证")
        print("=" * 60)
        
        # 对比方法
        methods = {
            '全面增强马尔可夫 (34.3%基线)': self.baseline_enhanced_markov,
            '保守集成系统': self.conservative_ensemble_prediction,
            '纯马尔可夫基线': self.pure_markov_baseline
        }
        
        results = {}
        stability_data = {}
        
        for method_name, method_func in methods.items():
            print(f"\n验证方法: {method_name}")
            result, stability = self.validate_with_stability_monitoring(method_func)
            results[method_name] = result
            stability_data[method_name] = stability
            print(f"  命中率: {result['hit_rate']:.3f} ({result['hits']}/{result['total']})")
            print(f"  稳定性: {stability['consistency']:.3f}")
        
        self.restructure_results = results
        self.stability_data = stability_data
        
        # 分析重构效果
        self.analyze_restructure_effects()
        
        return results
    
    def baseline_enhanced_markov(self, prev_numbers, period_info):
        """基线增强马尔可夫 (34.3%配置)"""
        return self.enhanced_markov_predict(prev_numbers)
    
    def pure_markov_baseline(self, prev_numbers, period_info):
        """纯马尔可夫基线"""
        return self.markov_predict_basic(prev_numbers)
    
    def markov_predict_basic(self, prev_numbers):
        """基础马尔可夫预测"""
        np.random.seed(42)
        
        number_probs = defaultdict(float)
        total_prob = 0.0
        
        for prev_num in prev_numbers:
            if prev_num in self.base_markov_prob:
                for next_num, prob in self.base_markov_prob[prev_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            return [num for num, prob in sorted_numbers[:2]]
        else:
            return [1, 2]
    
    def validate_with_stability_monitoring(self, method_func):
        """带稳定性监控的验证"""
        hits = 0
        total = 0
        hit_rates_by_segment = []
        predictions = []
        
        # 分段验证以监控稳定性
        segment_size = 30
        segments = len(self.test_data) // segment_size
        
        for segment in range(segments):
            segment_hits = 0
            segment_total = 0
            
            start_idx = segment * segment_size
            end_idx = min((segment + 1) * segment_size, len(self.test_data))
            
            for i in range(start_idx, end_idx):
                test_row = self.test_data.iloc[i]
                period = test_row['期号']
                actual_numbers = [test_row[f'数字{j}'] for j in range(1, 7)]
                
                # 获取前一期数字
                if i == 0:
                    prev_numbers = set([self.train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
                else:
                    prev_row = self.test_data.iloc[i-1]
                    prev_numbers = set([prev_row[f'数字{j}'] for j in range(1, 7)])
                
                period_info = {'year': test_row['年份'], 'period': period}
                
                try:
                    predicted = method_func(prev_numbers, period_info)
                    predicted_set = set(predicted)
                    actual_set = set(actual_numbers)
                    
                    hit_count = len(predicted_set & actual_set)
                    is_hit = hit_count >= 1
                    
                    total += 1
                    segment_total += 1
                    if is_hit:
                        hits += 1
                        segment_hits += 1
                    
                    predictions.append(predicted)
                    
                except Exception as e:
                    total += 1
                    segment_total += 1
                    predictions.append(None)
            
            if segment_total > 0:
                segment_rate = segment_hits / segment_total
                hit_rates_by_segment.append(segment_rate)
        
        # 计算稳定性指标
        consistency = 1 - np.std(hit_rates_by_segment) if hit_rates_by_segment else 0
        
        result = {
            'hits': hits,
            'total': total,
            'hit_rate': hits / total if total > 0 else 0
        }
        
        stability = {
            'consistency': consistency,
            'segment_rates': hit_rates_by_segment,
            'predictions': predictions
        }
        
        return result, stability

    def analyze_restructure_effects(self):
        """分析重构效果"""
        print(f"\n📊 保守系统重构效果分析")
        print("=" * 60)

        # 获取基线结果
        baseline_rate = self.restructure_results['全面增强马尔可夫 (34.3%基线)']['hit_rate']
        restructure_rate = self.restructure_results['保守集成系统']['hit_rate']

        print(f"基线方法 (全面增强马尔可夫): {baseline_rate:.3f}")
        print(f"保守集成系统: {restructure_rate:.3f}")

        improvement = restructure_rate - baseline_rate
        improvement_pct = (improvement / baseline_rate) * 100 if baseline_rate > 0 else 0

        print(f"\n保守重构效果:")
        print(f"  绝对提升: {improvement:+.3f}")
        print(f"  相对提升: {improvement_pct:+.1f}%")

        # 稳定性分析
        print(f"\n稳定性分析:")
        for method_name, stability in self.stability_data.items():
            print(f"  {method_name}: 一致性{stability['consistency']:.3f}")

        # 与理论极限对比
        theoretical_limit = 0.348
        print(f"\n与理论极限对比:")
        for method_name, result in self.restructure_results.items():
            distance_to_limit = theoretical_limit - result['hit_rate']
            completion = result['hit_rate'] / theoretical_limit
            print(f"  {method_name}: 距离极限{distance_to_limit:.3f}, 完成度{completion:.1%}")

    def generate_final_recommendation(self):
        """生成最终建议"""
        print(f"\n🎯 最终建议")
        print("=" * 60)

        # 找出最佳方法
        best_method = max(self.restructure_results.items(), key=lambda x: x[1]['hit_rate'])
        best_stability = self.stability_data[best_method[0]]['consistency']

        print(f"🏆 最佳方法: {best_method[0]}")
        print(f"   命中率: {best_method[1]['hit_rate']:.3f}")
        print(f"   稳定性: {best_stability:.3f}")

        # 基于结果给出建议
        if best_method[1]['hit_rate'] > 0.343:  # 超过34.3%
            print(f"\n🎉 系统重构成功！")
            print(f"   建议: 部署{best_method[0]}")
            print(f"   预期: 长期稳定运行在{best_method[1]['hit_rate']:.1%}水平")

            if best_method[1]['hit_rate'] > 0.348:  # 超过理论极限
                print(f"   🔥 突破理论极限！这是历史性成就！")

        elif best_method[1]['hit_rate'] >= 0.340:  # 接近34.3%
            print(f"\n✅ 系统重构基本成功")
            print(f"   建议: 可以考虑部署，但需要持续监控")
            print(f"   预期: 与34.3%基线相当的性能")

        else:  # 低于34.0%
            print(f"\n⚠️ 系统重构效果有限")
            print(f"   建议: 保持34.3%基线方法")
            print(f"   原因: 重构未能带来显著提升")

        # 技术总结
        print(f"\n🔧 技术总结:")
        print(f"   核心发现: 34.3%全面增强马尔可夫已接近最优")
        print(f"   重构价值: 验证了系统稳定性和可扩展性")
        print(f"   未来方向: 保持稳定运行，谨慎创新")

        # 风险评估
        print(f"\n🛡️ 风险评估:")
        if best_stability > 0.8:
            print(f"   稳定性: 优秀 ({best_stability:.3f})")
        elif best_stability > 0.6:
            print(f"   稳定性: 良好 ({best_stability:.3f})")
        else:
            print(f"   稳定性: 需要关注 ({best_stability:.3f})")

    def save_restructure_results(self):
        """保存重构结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存详细结果
        results_file = f"阶段3系统重构结果_{timestamp}.json"

        def convert_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_types(v) for v in obj]
            elif isinstance(obj, set):
                return list(obj)
            else:
                return obj

        save_data = {
            'restructure_results': convert_types(self.restructure_results),
            'stability_data': convert_types(self.stability_data),
            'conservative_params': convert_types(self.conservative_params),
            'ensemble_weights': convert_types(self.ensemble_weights)
        }

        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)

        print(f"\n✅ 重构结果已保存: {results_file}")

def main():
    """主函数"""
    print("🏗️ 阶段3系统重构 - 基于34.3%突破的保守优化")
    print("目标: 在保持稳定性前提下探索35%可能性")
    print("=" * 80)

    system = ConservativeSystemRestructure()

    # 1. 加载数据
    if not system.load_data():
        return

    # 2. 构建保守集成系统
    system.build_conservative_ensemble_system()

    # 3. 运行保守验证
    results = system.run_conservative_validation()

    # 4. 生成最终建议
    system.generate_final_recommendation()

    # 5. 保存重构结果
    system.save_restructure_results()

    # 6. 最终总结
    best_rate = max(result['hit_rate'] for result in results.values())
    print(f"\n🎉 阶段3系统重构完成")
    print(f"最佳命中率: {best_rate:.3f}")

    if best_rate > 0.348:
        print(f"🔥 突破理论极限！历史性成就！")
    elif best_rate > 0.343:
        print(f"🎯 成功提升！超越34.3%基线")
    else:
        print(f"🛡️ 保持稳定，34.3%基线依然是最佳选择")

if __name__ == "__main__":
    main()
