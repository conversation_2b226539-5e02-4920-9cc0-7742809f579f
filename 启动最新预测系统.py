#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最新预测系统启动器
简化的启动脚本，自动检测和运行最新的预测系统
"""

import os
import sys
from datetime import datetime

def check_system_files():
    """检查系统文件"""
    print("🔍 检查系统文件...")
    
    required_files = [
        "prediction_data_final_production.csv",
        "最新集成预测系统.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        
        # 尝试使用备用文件
        if "prediction_data_final_production.csv" in missing_files:
            if os.path.exists("prediction_data.csv"):
                print("✅ 找到备用数据文件: prediction_data.csv")
            else:
                print("❌ 未找到任何数据文件")
                return False
    
    print("✅ 系统文件检查完成")
    return True

def show_system_info():
    """显示系统信息"""
    print("\n" + "="*60)
    print("🚀 最新集成预测系统 v4.2")
    print("="*60)
    print("📅 启动时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("🔧 系统特性:")
    print("   ✅ Best_Ensemble_Method_v3.0 集成算法")
    print("   ✅ 118种评分多样化系统")
    print("   ✅ 7个等级 (A+到D级)")
    print("   ✅ 动态调整机制")
    print("   ✅ 时间泄露保护")
    print("   ✅ 预测稳定性控制")
    print("   ✅ 完整监控验证功能")
    print("\n📊 数据状态:")
    
    # 检查数据文件状态
    if os.path.exists("prediction_data_final_production.csv"):
        import pandas as pd
        try:
            data = pd.read_csv("prediction_data_final_production.csv")
            print(f"   📁 生产数据: {len(data)} 条记录")
            
            # 检查数据质量
            valid_predictions = len(data.dropna(subset=['预测数字1', '预测数字2']))
            completeness = valid_predictions / len(data) * 100
            print(f"   📈 数据完整性: {completeness:.1f}%")
            
            # 检查多样性
            unique_scores = len(data['预测评分'].dropna().unique())
            unique_grades = len(data['评分等级'].dropna().unique())
            print(f"   🌈 评分多样性: {unique_scores} 种评分, {unique_grades} 个等级")
            
        except Exception as e:
            print(f"   ⚠️ 数据文件读取异常: {e}")
    
    print("\n💡 使用提示:")
    print("   1. 选择选项1输入当期开奖数据进行预测")
    print("   2. 选择选项2直接进行预测")
    print("   3. 查看选项3-5了解系统状态和性能")
    print("   4. 系统会自动保存所有预测结果")
    print("="*60)

def run_latest_system():
    """运行最新系统"""
    try:
        # 导入并运行最新系统
        from 最新集成预测系统 import main
        main()
    except ImportError as e:
        print(f"❌ 导入系统模块失败: {e}")
        print("请确保 最新集成预测系统.py 文件存在")
        return False
    except Exception as e:
        print(f"❌ 系统运行失败: {e}")
        return False
    
    return True

def main():
    """主启动函数"""
    print("🎯 最新预测系统启动器")
    print("正在启动最新集成预测系统...")
    
    # 检查系统文件
    if not check_system_files():
        print("\n❌ 系统文件检查失败，无法启动")
        input("按回车键退出...")
        return
    
    # 显示系统信息
    show_system_info()
    
    # 等待用户确认
    print("\n🚀 准备启动系统...")
    input("按回车键继续...")
    
    # 运行最新系统
    if not run_latest_system():
        print("\n❌ 系统启动失败")
        input("按回车键退出...")
        return
    
    print("\n👋 系统已退出")

if __name__ == "__main__":
    main()
