#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平衡的评分系统
Balanced scoring system with reasonable thresholds
"""

import numpy as np

class BalancedScoringSystem:
    """平衡的评分系统"""
    
    def __init__(self):
        self.name = "平衡评分系统"
        self.version = "2.1"
    
    def calculate_balanced_score(self, predicted_numbers, confidence, current_period=None):
        """计算平衡评分"""
        pred_num1, pred_num2 = predicted_numbers
        
        # 1. 基础评分（适中的倍数）
        base_score = confidence * 800  # 从600调整到800
        
        # 2. 数字特征调整
        num_sum = pred_num1 + pred_num2
        num_diff = abs(pred_num1 - pred_num2)
        
        # 适中的调整系数
        if 20 <= num_sum <= 80:
            base_score *= 1.15  # 从1.08调整到1.15
            
        if 5 <= num_diff <= 40:
            base_score *= 1.10  # 从1.05调整到1.10
        
        # 3. 小数字特征
        small_numbers = list(range(1, 11))
        if pred_num1 in small_numbers or pred_num2 in small_numbers:
            base_score *= 1.08  # 从1.03调整到1.08
        
        # 4. 扩大分数范围
        final_score = max(18, min(85, base_score))  # 从15-65调整到18-85
        
        # 5. 添加适量随机性
        if current_period:
            np.random.seed(int(current_period) % 1000)
            noise = np.random.normal(0, 2.0)  # 增加噪声到2.0
            final_score = max(15, min(90, final_score + noise))
        
        return final_score
    
    def get_balanced_grade(self, score):
        """获取平衡的评分等级"""
        if score >= 35:
            return "A (较高概率)", "重点关注"
        elif score >= 28:
            return "B+ (中高概率)", "值得关注"
        elif score >= 22:
            return "B (中等概率)", "可以考虑"
        elif score >= 18:
            return "C (较低概率)", "谨慎考虑"
        else:
            return "D (低概率)", "不建议"
    
    def calculate_prediction_score(self, prediction_data):
        """计算预测评分（主接口）"""
        try:
            if isinstance(prediction_data.get('predicted_numbers'), list):
                predicted_numbers = prediction_data['predicted_numbers']
            else:
                predicted_numbers = [
                    prediction_data.get('pred_num1', 30),
                    prediction_data.get('pred_num2', 3)
                ]
            
            confidence = prediction_data.get('confidence', 
                        prediction_data.get('original_confidence', 0.025))
            current_period = prediction_data.get('period', 
                           prediction_data.get('current_period', 100))
            
            # 计算平衡评分
            score = self.calculate_balanced_score(
                predicted_numbers, confidence, current_period
            )
            
            # 获取等级
            grade, recommendation = self.get_balanced_grade(score)
            
            return {
                'score': round(score, 1),
                'grade': grade,
                'recommendation': recommendation,
                'probability': min(0.85, score / 100),  # 提高概率上限到85%
                'system_version': self.version
            }
            
        except Exception as e:
            return {
                'score': 25.0,
                'grade': "C (较低概率)",
                'recommendation': "谨慎考虑",
                'probability': 0.25,
                'system_version': self.version,
                'error': str(e)
            }

if __name__ == "__main__":
    # 测试平衡评分系统
    system = BalancedScoringSystem()
    
    test_cases = [
        {'predicted_numbers': [2, 15], 'confidence': 0.032, 'period': 100},
        {'predicted_numbers': [30, 40], 'confidence': 0.025, 'period': 150},
        {'predicted_numbers': [43, 47], 'confidence': 0.020, 'period': 200}
    ]
    
    print("平衡评分系统测试:")
    for case in test_cases:
        result = system.calculate_prediction_score(case)
        print(f"预测{case['predicted_numbers']}: {result['score']:.1f}分 ({result['grade']}) - {result['recommendation']}")
