#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据管理系统 - 管理真实数据和预测数据
1. 将用户输入的真实数据添加到主数据文件
2. 创建和管理预测数据CSV文件
3. 生成历史预测数据(2025年1-185期)
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class DataManagementSystem:
    """数据管理系统"""
    
    def __init__(self):
        self.main_data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.prediction_data_file = "prediction_data.csv"
        self.prediction_history_file = "prediction_history.json"
        
        # 34.3%方法的最佳参数
        self.optimal_params = {
            'high_freq_boost': 1.15,
            'low_freq_penalty': 0.85,
            'rising_trend_boost': 1.10,
            'falling_trend_penalty': 0.90,
            'perturbation': 0.05
        }
        
        # 数字分类
        self.high_freq_numbers = [5, 15, 3, 40, 30]
        self.low_freq_numbers = [41, 1, 8, 48, 47]
        self.rising_numbers = [30, 39, 4, 8, 22]
        self.falling_numbers = [5, 26, 44, 36, 15]
        
        # 模型组件
        self.enhanced_markov_prob = None
        
    def load_main_data(self):
        """加载主数据文件"""
        try:
            self.main_data = pd.read_csv(self.main_data_file)
            self.main_data = self.main_data.sort_values(['年份', '期号']).reset_index(drop=True)
            print(f"✅ 主数据加载完成: {len(self.main_data)}期")
            return True
        except Exception as e:
            print(f"❌ 主数据加载失败: {e}")
            return False
    
    def add_real_data_to_main_file(self, year, period, numbers):
        """将真实数据添加到主数据文件"""
        try:
            # 检查数据是否已存在
            existing = self.main_data[
                (self.main_data['年份'] == year) & 
                (self.main_data['期号'] == period)
            ]
            
            if len(existing) > 0:
                print(f"⚠️ {year}年{period}期数据已存在，是否更新？")
                choice = input("输入 y 更新，n 跳过: ").strip().lower()
                if choice != 'y':
                    return False
                
                # 更新现有数据
                idx = existing.index[0]
                for i, num in enumerate(sorted(numbers), 1):
                    self.main_data.loc[idx, f'数字{i}'] = num
                print(f"✅ 更新 {year}年{period}期数据")
            else:
                # 添加新数据
                new_row = {
                    '年份': year,
                    '期号': period
                }
                for i, num in enumerate(sorted(numbers), 1):
                    new_row[f'数字{i}'] = num
                
                # 使用pd.concat替代append
                new_df = pd.DataFrame([new_row])
                self.main_data = pd.concat([self.main_data, new_df], ignore_index=True)
                self.main_data = self.main_data.sort_values(['年份', '期号']).reset_index(drop=True)
                print(f"✅ 添加 {year}年{period}期数据")
            
            # 保存到文件
            self.main_data.to_csv(self.main_data_file, index=False, encoding='utf-8')
            print(f"✅ 主数据文件已更新")
            
            return True
            
        except Exception as e:
            print(f"❌ 添加数据失败: {e}")
            return False
    
    def create_prediction_data_csv(self):
        """创建预测数据CSV文件"""
        try:
            # 检查文件是否已存在
            if os.path.exists(self.prediction_data_file):
                print(f"📄 预测数据文件已存在: {self.prediction_data_file}")
                choice = input("是否重新创建？(y/n): ").strip().lower()
                if choice != 'y':
                    return True
            
            # 创建CSV文件结构
            columns = [
                '预测日期', '预测时间', '当期年份', '当期期号', '预测期号',
                '当期数字1', '当期数字2', '当期数字3', '当期数字4', '当期数字5', '当期数字6',
                '预测数字1', '预测数字2', '预测置信度', '预测方法',
                '实际数字1', '实际数字2', '实际数字3', '实际数字4', '实际数字5', '实际数字6',
                '命中数量', '是否命中', '命中数字', '备注'
            ]
            
            # 创建空的DataFrame
            prediction_df = pd.DataFrame(columns=columns)
            prediction_df.to_csv(self.prediction_data_file, index=False, encoding='utf-8-sig')
            
            print(f"✅ 预测数据CSV文件已创建: {self.prediction_data_file}")
            return True
            
        except Exception as e:
            print(f"❌ 创建预测数据文件失败: {e}")
            return False
    
    def build_enhanced_markov_model(self):
        """构建增强马尔可夫模型"""
        print("🔧 构建34.3%增强马尔可夫模型...")
        
        # 使用2023-2024年作为训练数据
        train_data = self.main_data[
            (self.main_data['年份'] >= 2023) & 
            (self.main_data['年份'] <= 2024)
        ].copy()
        
        # 基础马尔可夫转移概率
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(train_data) - 1):
            current_numbers = set([train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 计算基础转移概率
        base_markov_prob = {}
        for curr_num in range(1, 50):
            if curr_num in transition_count:
                total = sum(transition_count[curr_num].values())
                smoothed_total = total + 49
                base_markov_prob[curr_num] = {}
                
                for next_num in range(1, 50):
                    count = transition_count[curr_num].get(next_num, 0)
                    base_markov_prob[curr_num][next_num] = (count + 1) / smoothed_total
            else:
                base_markov_prob[curr_num] = {
                    next_num: 1/49 for next_num in range(1, 50)
                }
        
        # 应用增强策略
        self.apply_enhancements(base_markov_prob)
        
        print("✅ 增强马尔可夫模型构建完成")
    
    def apply_enhancements(self, base_markov_prob):
        """应用增强策略"""
        # 计算权重
        frequency_weights = {}
        trend_weights = {}
        
        for num in range(1, 50):
            if num in self.high_freq_numbers:
                frequency_weights[num] = self.optimal_params['high_freq_boost']
            elif num in self.low_freq_numbers:
                frequency_weights[num] = self.optimal_params['low_freq_penalty']
            else:
                frequency_weights[num] = 1.0
            
            if num in self.rising_numbers:
                trend_weights[num] = self.optimal_params['rising_trend_boost']
            elif num in self.falling_numbers:
                trend_weights[num] = self.optimal_params['falling_trend_penalty']
            else:
                trend_weights[num] = 1.0
        
        # 构建增强马尔可夫概率
        self.enhanced_markov_prob = {}
        for curr_num in base_markov_prob:
            self.enhanced_markov_prob[curr_num] = {}
            total_weight = 0
            
            for next_num, base_prob in base_markov_prob[curr_num].items():
                freq_weight = frequency_weights[next_num]
                trend_weight = trend_weights[next_num]
                combined_weight = freq_weight * trend_weight
                weighted_prob = base_prob * combined_weight
                
                self.enhanced_markov_prob[curr_num][next_num] = weighted_prob
                total_weight += weighted_prob
            
            # 归一化
            for next_num in self.enhanced_markov_prob[curr_num]:
                self.enhanced_markov_prob[curr_num][next_num] /= total_weight
    
    def predict_next_period(self, current_numbers, period_num):
        """预测下期数字"""
        if self.enhanced_markov_prob is None:
            raise RuntimeError("模型未初始化")
        
        # 使用期号作为随机种子确保可重复性
        np.random.seed(42 + period_num)
        
        number_probs = defaultdict(float)
        total_prob = 0.0
        
        current_set = set(current_numbers)
        
        for curr_num in current_set:
            if curr_num in self.enhanced_markov_prob:
                for next_num, prob in self.enhanced_markov_prob[curr_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        # 添加随机扰动
        perturbation = self.optimal_params['perturbation']
        for num in number_probs:
            noise = np.random.normal(0, perturbation * number_probs[num])
            number_probs[num] = max(0, number_probs[num] + noise)
        
        # 选择前2个数字
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            prediction = [num for num, prob in sorted_numbers[:2]]
            confidence = (sorted_numbers[0][1] + sorted_numbers[1][1]) / 2
        else:
            prediction = [1, 2]
            confidence = 0.02
        
        return prediction, confidence
    
    def generate_historical_predictions(self):
        """生成2025年1-185期的历史预测数据"""
        print("🔮 生成2025年1-185期历史预测数据...")
        
        # 获取2025年数据
        data_2025 = self.main_data[self.main_data['年份'] == 2025].copy()
        
        if len(data_2025) == 0:
            print("❌ 没有2025年数据")
            return False
        
        # 准备预测数据列表
        prediction_records = []
        
        # 从第1期开始预测
        for i in range(len(data_2025)):
            current_row = data_2025.iloc[i]
            current_period = current_row['期号']
            current_numbers = [current_row[f'数字{j}'] for j in range(1, 7)]
            
            # 获取前一期数字用于预测当前期
            if i == 0:
                # 第一期使用2024年最后一期数据
                data_2024 = self.main_data[self.main_data['年份'] == 2024]
                if len(data_2024) > 0:
                    prev_numbers = [data_2024.iloc[-1][f'数字{j}'] for j in range(1, 7)]
                else:
                    continue
            else:
                # 使用前一期数据
                prev_row = data_2025.iloc[i-1]
                prev_numbers = [prev_row[f'数字{j}'] for j in range(1, 7)]
            
            # 执行预测
            try:
                predicted_numbers, confidence = self.predict_next_period(prev_numbers, current_period)
                
                # 计算命中情况
                predicted_set = set(predicted_numbers)
                actual_set = set(current_numbers)
                hit_numbers = predicted_set & actual_set
                hit_count = len(hit_numbers)
                is_hit = hit_count >= 1
                
                # 创建预测记录
                record = {
                    '预测日期': '2025-07-13',
                    '预测时间': '18:40:00',
                    '当期年份': 2025,
                    '当期期号': current_period,
                    '预测期号': f"2025年{current_period}期",
                    '当期数字1': prev_numbers[0] if len(prev_numbers) > 0 else '',
                    '当期数字2': prev_numbers[1] if len(prev_numbers) > 1 else '',
                    '当期数字3': prev_numbers[2] if len(prev_numbers) > 2 else '',
                    '当期数字4': prev_numbers[3] if len(prev_numbers) > 3 else '',
                    '当期数字5': prev_numbers[4] if len(prev_numbers) > 4 else '',
                    '当期数字6': prev_numbers[5] if len(prev_numbers) > 5 else '',
                    '预测数字1': predicted_numbers[0] if len(predicted_numbers) > 0 else '',
                    '预测数字2': predicted_numbers[1] if len(predicted_numbers) > 1 else '',
                    '预测置信度': f"{confidence:.6f}",
                    '预测方法': '34.3%增强马尔可夫',
                    '实际数字1': current_numbers[0],
                    '实际数字2': current_numbers[1],
                    '实际数字3': current_numbers[2],
                    '实际数字4': current_numbers[3],
                    '实际数字5': current_numbers[4],
                    '实际数字6': current_numbers[5],
                    '命中数量': hit_count,
                    '是否命中': '是' if is_hit else '否',
                    '命中数字': ','.join(map(str, sorted(hit_numbers))) if hit_numbers else '',
                    '备注': '历史回测数据'
                }
                
                prediction_records.append(record)
                
                if current_period % 20 == 0:
                    print(f"  已处理到{current_period}期...")
                
            except Exception as e:
                print(f"  预测{current_period}期失败: {e}")
                continue
        
        # 保存到CSV文件
        if prediction_records:
            prediction_df = pd.DataFrame(prediction_records)
            prediction_df.to_csv(self.prediction_data_file, index=False, encoding='utf-8-sig')
            
            # 计算统计信息
            total_predictions = len(prediction_records)
            hits = sum(1 for r in prediction_records if r['是否命中'] == '是')
            hit_rate = hits / total_predictions if total_predictions > 0 else 0
            
            print(f"✅ 历史预测数据生成完成")
            print(f"  总预测期数: {total_predictions}")
            print(f"  命中期数: {hits}")
            print(f"  命中率: {hit_rate:.3f} ({hit_rate:.1%})")
            print(f"  保存文件: {self.prediction_data_file}")
            
            return True
        else:
            print("❌ 没有生成任何预测数据")
            return False
    
    def add_new_prediction_to_csv(self, prediction_data):
        """添加新预测到CSV文件"""
        try:
            # 读取现有数据
            if os.path.exists(self.prediction_data_file):
                existing_df = pd.read_csv(self.prediction_data_file, encoding='utf-8-sig')
            else:
                # 如果文件不存在，创建新文件
                self.create_prediction_data_csv()
                existing_df = pd.DataFrame()
            
            # 准备新记录
            new_record = {
                '预测日期': prediction_data.get('prediction_date', datetime.now().strftime('%Y-%m-%d')),
                '预测时间': prediction_data.get('prediction_time', datetime.now().strftime('%H:%M:%S')),
                '当期年份': prediction_data.get('current_year', ''),
                '当期期号': prediction_data.get('current_period', ''),
                '预测期号': prediction_data.get('predicted_period', ''),
                '当期数字1': prediction_data.get('current_numbers', [None]*6)[0],
                '当期数字2': prediction_data.get('current_numbers', [None]*6)[1],
                '当期数字3': prediction_data.get('current_numbers', [None]*6)[2],
                '当期数字4': prediction_data.get('current_numbers', [None]*6)[3],
                '当期数字5': prediction_data.get('current_numbers', [None]*6)[4],
                '当期数字6': prediction_data.get('current_numbers', [None]*6)[5],
                '预测数字1': prediction_data.get('predicted_numbers', [None]*2)[0],
                '预测数字2': prediction_data.get('predicted_numbers', [None]*2)[1],
                '预测置信度': f"{prediction_data.get('confidence', 0):.6f}",
                '预测方法': prediction_data.get('method', '34.3%增强马尔可夫'),
                '实际数字1': prediction_data.get('actual_numbers', [None]*6)[0] if prediction_data.get('actual_numbers') else None,
                '实际数字2': prediction_data.get('actual_numbers', [None]*6)[1] if prediction_data.get('actual_numbers') else None,
                '实际数字3': prediction_data.get('actual_numbers', [None]*6)[2] if prediction_data.get('actual_numbers') else None,
                '实际数字4': prediction_data.get('actual_numbers', [None]*6)[3] if prediction_data.get('actual_numbers') else None,
                '实际数字5': prediction_data.get('actual_numbers', [None]*6)[4] if prediction_data.get('actual_numbers') else None,
                '实际数字6': prediction_data.get('actual_numbers', [None]*6)[5] if prediction_data.get('actual_numbers') else None,
                '命中数量': prediction_data.get('hit_count', ''),
                '是否命中': '是' if prediction_data.get('is_hit') else '否' if prediction_data.get('is_hit') is not None else '',
                '命中数字': ','.join(map(str, prediction_data.get('hit_numbers', []))) if prediction_data.get('hit_numbers') else '',
                '备注': prediction_data.get('notes', '')
            }
            
            # 添加新记录
            new_df = pd.DataFrame([new_record])
            updated_df = pd.concat([existing_df, new_df], ignore_index=True)
            
            # 保存到文件
            updated_df.to_csv(self.prediction_data_file, index=False, encoding='utf-8-sig')
            
            print(f"✅ 新预测已添加到CSV文件")
            return True
            
        except Exception as e:
            print(f"❌ 添加预测到CSV失败: {e}")
            return False
    
    def show_prediction_statistics(self):
        """显示预测统计"""
        try:
            if not os.path.exists(self.prediction_data_file):
                print("📊 预测数据文件不存在")
                return
            
            df = pd.read_csv(self.prediction_data_file, encoding='utf-8-sig')
            
            if len(df) == 0:
                print("📊 暂无预测数据")
                return
            
            print(f"\n📊 预测数据统计")
            print("=" * 50)
            
            total_predictions = len(df)
            verified_predictions = df[df['是否命中'].notna() & (df['是否命中'] != '')].copy()
            hits = len(verified_predictions[verified_predictions['是否命中'] == '是'])
            
            print(f"总预测期数: {total_predictions}")
            print(f"已验证期数: {len(verified_predictions)}")
            print(f"待验证期数: {total_predictions - len(verified_predictions)}")
            
            if len(verified_predictions) > 0:
                hit_rate = hits / len(verified_predictions)
                print(f"命中期数: {hits}")
                print(f"命中率: {hit_rate:.3f} ({hit_rate:.1%})")
                
                # 最近5期预测
                recent = verified_predictions.tail(5)
                print(f"\n最近{len(recent)}期预测:")
                for _, row in recent.iterrows():
                    status = "✅" if row['是否命中'] == '是' else "❌"
                    print(f"  {row['预测期号']}: 预测[{row['预测数字1']},{row['预测数字2']}] "
                          f"实际[{row['实际数字1']},{row['实际数字2']},{row['实际数字3']},{row['实际数字4']},{row['实际数字5']},{row['实际数字6']}] {status}")
            
        except Exception as e:
            print(f"❌ 显示统计失败: {e}")

def main():
    """主函数"""
    print("📊 数据管理系统")
    print("=" * 60)
    
    system = DataManagementSystem()
    
    # 1. 加载主数据
    if not system.load_main_data():
        return
    
    # 2. 构建模型
    system.build_enhanced_markov_model()
    
    while True:
        print(f"\n📊 数据管理系统")
        print("=" * 40)
        print("1. 添加真实开奖数据到主文件")
        print("2. 创建预测数据CSV文件")
        print("3. 生成2025年1-185期历史预测数据")
        print("4. 添加186期数据到主文件")
        print("5. 查看预测统计")
        print("6. 退出系统")
        
        choice = input("\n请选择操作 (1-6): ").strip()
        
        if choice == '1':
            try:
                year = int(input("请输入年份: "))
                period = int(input("请输入期号: "))
                numbers_str = input("请输入6个数字 (空格或逗号分隔): ")
                
                if ',' in numbers_str:
                    numbers = [int(x.strip()) for x in numbers_str.split(',') if x.strip()]
                else:
                    numbers = [int(x) for x in numbers_str.split() if x]
                
                if len(numbers) != 6:
                    print("❌ 必须输入6个数字")
                    continue
                
                system.add_real_data_to_main_file(year, period, numbers)
                
            except ValueError:
                print("❌ 输入格式错误")
        
        elif choice == '2':
            system.create_prediction_data_csv()
        
        elif choice == '3':
            system.generate_historical_predictions()
        
        elif choice == '4':
            # 添加186期数据
            numbers_186 = [13, 40, 22, 20, 17, 9]
            system.add_real_data_to_main_file(2025, 186, numbers_186)
        
        elif choice == '5':
            system.show_prediction_statistics()
        
        elif choice == '6':
            print("👋 退出系统")
            break
        
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    main()
