# 彩票预测系统 - 重构版

这是一个完整的彩票数据分析和预测项目，采用现代数据科学方法，专注于6个主要数字的预测（不包含特码），使用严格的训练/测试分割和先进的机器学习算法。

## 项目结构

```
CP_vs/
├── core/                         # 核心模块
│   ├── data_processor.py         # 数据处理和特征工程
│   ├── model_trainer.py          # 模型训练和验证
│   ├── evaluator.py              # 模型评估和性能分析
│   └── predictor.py              # 预测生成器
├── models/                       # 训练好的模型
├── results/                      # 结果和报告
├── utils/                        # 工具函数
│   └── visualization.py          # 可视化工具
├── 数据集.txt                    # 原始彩票数据 (180期)
├── main.py                       # 主程序入口
└── README.md                     # 项目说明文档
```

## 数据集说明

- **总数据量**: 180期 (第001期到第180期)
- **训练集**: 第001-160期 (160期数据用于模型训练)
- **测试集**: 第161-180期 (20期数据用于模型验证)
- **预测目标**: 6个主要数字 (范围1-49，不包含特码)

## 项目特点

### ✅ 严格的数据分割
- 使用时间序列分割，避免数据泄漏
- 训练集和测试集完全分离
- 保证模型评估的真实性

### ✅ 先进的机器学习算法
- XGBoost、LightGBM、CatBoost等梯度提升算法
- 深度学习模型 (LSTM、Transformer)
- 集成学习方法
- 贝叶斯优化超参数调优

### 步骤3：特征工程 ✅
创建了107个特征，包括：
- **滞后特征**：前1、2、3、5、10期的历史数据
- **滑动窗口统计特征**：5、10、20期的和值、奇偶比、大小比统计
- **间隔特征**：每个数字距离上次出现的时间间隔
- **组合特征**：连号个数、重复号个数等

### 步骤4：数据集划分 ✅
- **训练集**：第1-140期（移除NaN后121期）
- **测试集**：第141-160期（20期）
- 严格按时间顺序划分，避免数据泄露

### 步骤5：模型训练与评估 ✅
实现了随机森林模型：
- 为每个数字位置训练独立的回归模型
- 使用100棵决策树，最大深度10
- 特征重要性分析显示"和值"是最重要的特征

### 步骤6：模型性能评估 ✅
**结果分析**：
- 随机森林：平均每期预测对 0.20 个数字
- 随机基准：平均每期预测对 0.40 个数字
- **模型表现不如随机基准**

## 关键发现

### 1. 数据特征
- 数字分布相对均匀，符合彩票的随机性设计
- 卡方统计量44.81，说明存在一定的不均匀性
- 平均连号个数0.60，重复号个数0.81

### 2. 预测难度
- 理论随机命中概率：单个数字12.2%
- 彩票具有很强的随机性，历史模式难以预测未来
- 160期数据量相对较少，不足以学习复杂模式

### 3. 模型局限性
- 传统机器学习模型在彩票预测上效果有限
- 特征工程虽然全面，但可能无法捕捉真正的预测信号
- 模型可能过拟合了训练数据中的噪声

## 运行方法

1. **数据清洗和EDA**：
```bash
python lottery_analysis.py
```

2. **模型训练和评估**：
```bash
python lottery_models.py
```

3. **生成综合报告**：
```bash
python lottery_analysis_report.py
```

## 改进建议

### A. 特征工程改进
1. 尝试更复杂的组合特征
2. 考虑季节性、周期性特征
3. 引入外部因素（日期、节假日等）
4. 使用更长的历史窗口

### B. 模型改进
1. 尝试集成学习方法
2. 使用深度学习模型（LSTM、Transformer）
3. 考虑概率预测而非点预测
4. 使用贝叶斯方法处理不确定性

### C. 评估方法改进
1. 使用更多样的评估指标
2. 考虑部分匹配的奖励机制
3. 分析预测的置信度
4. 进行更长期的回测

## 重要声明

⚠️ **风险提示**：
1. 彩票具有很强的随机性，AI预测效果有限
2. 任何预测模型都不能保证盈利
3. 理性对待彩票，不要过度依赖预测
4. 本项目仅作为数据科学学习和研究用途

## 技术栈

- **Python 3.x**
- **数据处理**：pandas, numpy
- **可视化**：matplotlib, seaborn
- **机器学习**：scikit-learn
- **深度学习**：tensorflow/keras

## 结论

通过完整的数据科学流程分析，我们发现：

1. **数据质量良好**：160期数据结构完整，分布相对均匀
2. **特征工程全面**：创建了多维度的107个特征
3. **模型实现正确**：随机森林模型训练和评估流程规范
4. **结果符合预期**：彩票的随机性使得预测极其困难

这个项目很好地展示了数据科学的完整流程，虽然在彩票预测上效果有限，但作为学习项目具有很高的价值。它证明了即使使用先进的机器学习技术，面对真正随机的数据时，预测能力仍然有限。

## 许可证

本项目仅供学习和研究使用。
