#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新prediction_data.csv文件
Update prediction_data.csv with latest fixed predictions
"""

import pandas as pd
import sys
import os
from datetime import datetime

def update_prediction_csv():
    """更新预测数据CSV文件"""
    print("📝 更新prediction_data.csv文件")
    print("="*50)
    
    try:
        # 导入修复后的系统
        sys.path.append('.')
        from 集成评分系统的预测系统 import IntegratedPredictionSystem
        
        # 创建系统实例
        system = IntegratedPredictionSystem()
        
        # 初始化系统
        if not system.initialize_system():
            print("❌ 系统初始化失败")
            return False
        
        print("✅ 系统初始化成功")
        
        # 读取现有CSV文件
        if os.path.exists('prediction_data.csv'):
            df = pd.read_csv('prediction_data.csv', encoding='utf-8')
            print(f"📂 读取现有数据: {len(df)}条记录")
        else:
            print("❌ prediction_data.csv文件不存在")
            return False
        
        # 测试数据：2025年197期
        test_input = [10, 12, 15, 24, 25, 43]
        current_time = datetime.now()
        
        print(f"🧪 测试输入: {test_input}")
        
        # 进行预测
        prediction, confidence = system.predict_next_period(test_input)
        print(f"预测结果: {prediction} (置信度: {confidence:.6f})")
        
        # 计算评分
        score_data = {
            'predicted_numbers': prediction,
            'confidence': confidence,
            'current_period': 197
        }
        
        score_result = system.calculate_prediction_score(score_data)
        print(f"评分结果: {score_result['score']:.1f}分 ({score_result['grade']})")
        
        # 创建新的预测记录
        new_record = {
            '预测日期': current_time.strftime('%Y-%m-%d'),
            '预测时间': current_time.strftime('%H:%M:%S'),
            '当期年份': 2025,
            '当期期号': 197,
            '预测期号': '2025年198期',
            '当期数字1': test_input[0],
            '当期数字2': test_input[1],
            '当期数字3': test_input[2],
            '当期数字4': test_input[3],
            '当期数字5': test_input[4],
            '当期数字6': test_input[5],
            '预测数字1': prediction[0],
            '预测数字2': prediction[1],
            '预测置信度': f"{confidence:.6f}",
            '预测方法': '修复后34.3%增强马尔可夫',
            '预测评分': f"{score_result['score']:.1f}",
            '评分等级': score_result['grade'],
            '评分建议': score_result['recommendation'],
            '评分概率': f"{score_result['probability']:.3f}",
            '实际数字1': '',
            '实际数字2': '',
            '实际数字3': '',
            '实际数字4': '',
            '实际数字5': '',
            '实际数字6': '',
            '命中数量': '',
            '是否命中': '',
            '命中数字': '',
            '备注': '修复后预测-一致性和评分已修复'
        }
        
        # 添加新记录到DataFrame
        new_df = pd.DataFrame([new_record])
        updated_df = pd.concat([df, new_df], ignore_index=True)
        
        # 保存更新后的CSV文件
        updated_df.to_csv('prediction_data.csv', index=False, encoding='utf-8')
        
        print(f"✅ 已添加新预测记录到CSV文件")
        print(f"📊 更新后总记录数: {len(updated_df)}")
        
        # 显示最新记录
        print(f"\n📋 最新预测记录:")
        print(f"   预测: {prediction}")
        print(f"   置信度: {confidence:.6f}")
        print(f"   评分: {score_result['score']:.1f}分")
        print(f"   等级: {score_result['grade']}")
        print(f"   建议: {score_result['recommendation']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新CSV文件失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_csv_consistency():
    """验证CSV文件中的预测一致性"""
    print(f"\n🔍 验证CSV文件中的预测一致性")
    print("="*40)
    
    try:
        # 读取CSV文件
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        
        # 查找2025年197期的所有预测记录
        period_197_records = df[
            (df['当期年份'] == 2025) & 
            (df['当期期号'] == 197) &
            (df['当期数字1'] == 10) &
            (df['当期数字2'] == 12) &
            (df['当期数字3'] == 15) &
            (df['当期数字4'] == 24) &
            (df['当期数字5'] == 25) &
            (df['当期数字6'] == 43)
        ]
        
        print(f"找到 {len(period_197_records)} 条2025年197期的预测记录")
        
        if len(period_197_records) > 0:
            print(f"\n预测记录详情:")
            for idx, record in period_197_records.iterrows():
                print(f"  记录{idx+1}: [{record['预测数字1']}, {record['预测数字2']}] "
                      f"置信度:{record['预测置信度']} 评分:{record['预测评分']} "
                      f"备注:{record['备注']}")
            
            # 检查预测一致性
            unique_predictions = period_197_records[['预测数字1', '预测数字2']].drop_duplicates()
            
            if len(unique_predictions) == 1:
                print(f"\n✅ 预测结果一致!")
                pred = unique_predictions.iloc[0]
                print(f"   一致预测: [{pred['预测数字1']}, {pred['预测数字2']}]")
            else:
                print(f"\n⚠️ 发现 {len(unique_predictions)} 种不同的预测结果:")
                for idx, pred in unique_predictions.iterrows():
                    print(f"   预测{idx+1}: [{pred['预测数字1']}, {pred['预测数字2']}]")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证CSV文件失败: {e}")
        return False

def generate_csv_summary():
    """生成CSV文件摘要"""
    print(f"\n📊 生成CSV文件摘要")
    print("="*30)
    
    try:
        # 读取CSV文件
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        
        print(f"总记录数: {len(df)}")
        print(f"预测期间: 2025年{df['当期期号'].min()}期 - 2025年{df['当期期号'].max()}期")
        
        # 评分等级分布
        grade_counts = df['评分等级'].value_counts()
        print(f"\n评分等级分布:")
        for grade, count in grade_counts.items():
            percentage = (count / len(df)) * 100
            print(f"  {grade}: {count}次 ({percentage:.1f}%)")
        
        # 预测方法分布
        method_counts = df['预测方法'].value_counts()
        print(f"\n预测方法分布:")
        for method, count in method_counts.items():
            percentage = (count / len(df)) * 100
            print(f"  {method}: {count}次 ({percentage:.1f}%)")
        
        # 最新记录
        latest_record = df.iloc[-1]
        print(f"\n最新预测记录:")
        print(f"  日期: {latest_record['预测日期']} {latest_record['预测时间']}")
        print(f"  期号: {latest_record['预测期号']}")
        print(f"  预测: [{latest_record['预测数字1']}, {latest_record['预测数字2']}]")
        print(f"  评分: {latest_record['预测评分']}分 ({latest_record['评分等级']})")
        print(f"  备注: {latest_record['备注']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成摘要失败: {e}")
        return False

def main():
    """主函数"""
    print("📝 更新prediction_data.csv文件")
    print("="*60)
    
    # 1. 更新CSV文件
    update_success = update_prediction_csv()
    
    # 2. 验证一致性
    if update_success:
        verify_csv_consistency()
    
    # 3. 生成摘要
    generate_csv_summary()
    
    if update_success:
        print(f"\n✅ CSV文件更新完成!")
        print(f"现在prediction_data.csv包含最新的修复后预测数据")
        print(f"预测一致性和评分计算问题已修复")
    else:
        print(f"\n❌ CSV文件更新失败")

if __name__ == "__main__":
    main()
