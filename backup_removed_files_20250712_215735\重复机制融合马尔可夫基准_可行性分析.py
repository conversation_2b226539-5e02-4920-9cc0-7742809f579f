#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重复机制融合马尔可夫基准可行性分析
评估重复数值机制作为特征加入29.2%马尔可夫基准的可行性
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class RepeatEnhancedMarkovAnalyzer:
    """
    重复机制增强马尔可夫分析器
    评估重复特征融合的可行性和风险
    """
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_2021_2025_integrated.csv"
        self.data = None
        self.train_data = None
        self.test_data = None
        
        # 基准性能
        self.baseline_performance = 0.292  # 29.2%马尔可夫基准
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_file)
            self.data = self.data.sort_values(['年份', '期号']).reset_index(drop=True)
            print(f"✅ 数据加载成功: {len(self.data)}期")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def prepare_data(self):
        """准备训练和测试数据"""
        self.train_data = self.data[(self.data['年份'] >= 2023) & (self.data['年份'] < 2025)].copy()
        self.test_data = self.data[(self.data['年份'] == 2025) & (self.data['期号'] <= 182)].copy()
        
        print(f"训练数据: {len(self.train_data)}期 (2023-2024年)")
        print(f"测试数据: {len(self.test_data)}期 (2025年)")
        
        return len(self.train_data) > 0 and len(self.test_data) > 0
    
    def calculate_repeat_features(self, current_numbers, history_data, window_sizes=[2, 3, 5]):
        """计算重复特征"""
        repeat_features = {}
        
        for window_size in window_sizes:
            if len(history_data) >= window_size:
                # 获取最近window_size期的数据
                recent_data = history_data.tail(window_size)
                
                # 统计重复频率
                number_frequencies = Counter()
                for _, row in recent_data.iterrows():
                    for j in range(1, 7):
                        number_frequencies[row[f'数字{j}']] += 1
                
                # 计算当前数字在历史窗口中的重复频率
                repeat_scores = {}
                for num in current_numbers:
                    repeat_scores[num] = number_frequencies.get(num, 0) / window_size
                
                # 特征值：平均重复频率
                avg_repeat_freq = np.mean(list(repeat_scores.values())) if repeat_scores else 0
                max_repeat_freq = max(repeat_scores.values()) if repeat_scores else 0
                
                repeat_features[f'avg_repeat_{window_size}'] = avg_repeat_freq
                repeat_features[f'max_repeat_{window_size}'] = max_repeat_freq
        
        return repeat_features
    
    def baseline_markov_method(self, train_data, test_data):
        """原始29.2%马尔可夫基准方法"""
        print("\n🔬 原始马尔可夫基准方法")
        
        predictions = []
        
        # 构建转移矩阵
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(train_data) - 1):
            current_numbers = set([train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 转换为概率
        transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            transition_prob[curr_num] = {
                next_num: count / total 
                for next_num, count in transition_count[curr_num].items()
            }
        
        # 逐期预测
        for idx, test_row in test_data.iterrows():
            # 获取前一期数字
            if idx == test_data.index[0]:
                prev_numbers = set([train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
            else:
                prev_idx = test_data.index[test_data.index.get_loc(idx) - 1]
                prev_numbers = set([test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
            
            # 计算概率
            number_probs = defaultdict(float)
            for prev_num in prev_numbers:
                if prev_num in transition_prob:
                    for next_num, prob in transition_prob[prev_num].items():
                        number_probs[next_num] += prob
            
            # 选择概率最高的2个数字
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            predicted_2digits = [num for num, prob in sorted_numbers[:2]]
            
            # 验证
            actual_numbers = set([test_row[f'数字{j}'] for j in range(1, 7)])
            hit_count = len(set(predicted_2digits) & actual_numbers)
            
            predictions.append({
                'period': test_row['期号'],
                'predicted': predicted_2digits,
                'actual': list(actual_numbers),
                'hits': hit_count,
                'success': hit_count >= 1
            })
        
        success_rate = sum(1 for p in predictions if p['success']) / len(predictions)
        print(f"基准马尔可夫成功率: {success_rate:.3f} ({success_rate*100:.1f}%)")
        
        return predictions, success_rate
    
    def repeat_enhanced_markov_method(self, train_data, test_data, repeat_weight=0.1):
        """重复机制增强的马尔可夫方法"""
        print(f"\n🔬 重复机制增强马尔可夫方法 (权重: {repeat_weight})")
        
        predictions = []
        
        # 构建基础转移矩阵
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(train_data) - 1):
            current_numbers = set([train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 转换为概率
        transition_prob = {}
        for curr_num in transition_count:
            total = sum(transition_count[curr_num].values())
            transition_prob[curr_num] = {
                next_num: count / total 
                for next_num, count in transition_count[curr_num].items()
            }
        
        # 逐期预测
        for idx, test_row in test_data.iterrows():
            # 获取前一期数字
            if idx == test_data.index[0]:
                prev_numbers = set([train_data.iloc[-1][f'数字{j}'] for j in range(1, 7)])
                history_for_repeat = train_data
            else:
                prev_idx = test_data.index[test_data.index.get_loc(idx) - 1]
                prev_numbers = set([test_data.loc[prev_idx][f'数字{j}'] for j in range(1, 7)])
                # 历史数据包括训练数据和已预测的测试数据
                history_for_repeat = pd.concat([train_data, test_data.loc[:prev_idx]])
            
            # 1. 基础马尔可夫概率
            markov_probs = defaultdict(float)
            for prev_num in prev_numbers:
                if prev_num in transition_prob:
                    for next_num, prob in transition_prob[prev_num].items():
                        markov_probs[next_num] += prob
            
            # 归一化马尔可夫概率
            total_markov_prob = sum(markov_probs.values())
            if total_markov_prob > 0:
                for num in markov_probs:
                    markov_probs[num] /= total_markov_prob
            
            # 2. 重复特征计算
            repeat_features = self.calculate_repeat_features(
                prev_numbers, history_for_repeat, [2, 3, 5]
            )
            
            # 3. 重复增强概率
            repeat_enhanced_probs = {}
            
            # 计算所有可能数字的重复增强分数
            for num in range(1, 50):
                # 基础马尔可夫概率
                base_prob = markov_probs.get(num, 0.001)  # 最小概率避免0
                
                # 重复增强分数
                repeat_score = 0
                for window_size in [2, 3, 5]:
                    if len(history_for_repeat) >= window_size:
                        recent_data = history_for_repeat.tail(window_size)
                        num_freq = 0
                        for _, row in recent_data.iterrows():
                            if num in [row[f'数字{j}'] for j in range(1, 7)]:
                                num_freq += 1
                        repeat_score += num_freq / window_size
                
                repeat_score /= 3  # 平均重复分数
                
                # 融合概率：(1-weight) * markov + weight * repeat
                enhanced_prob = (1 - repeat_weight) * base_prob + repeat_weight * repeat_score
                repeat_enhanced_probs[num] = enhanced_prob
            
            # 选择概率最高的2个数字
            sorted_numbers = sorted(repeat_enhanced_probs.items(), key=lambda x: x[1], reverse=True)
            predicted_2digits = [num for num, prob in sorted_numbers[:2]]
            
            # 验证
            actual_numbers = set([test_row[f'数字{j}'] for j in range(1, 7)])
            hit_count = len(set(predicted_2digits) & actual_numbers)
            
            predictions.append({
                'period': test_row['期号'],
                'predicted': predicted_2digits,
                'actual': list(actual_numbers),
                'hits': hit_count,
                'success': hit_count >= 1,
                'repeat_features': repeat_features,
                'markov_top2': [num for num, prob in sorted(markov_probs.items(), key=lambda x: x[1], reverse=True)[:2]]
            })
        
        success_rate = sum(1 for p in predictions if p['success']) / len(predictions)
        print(f"重复增强马尔可夫成功率: {success_rate:.3f} ({success_rate*100:.1f}%)")
        
        return predictions, success_rate
    
    def analyze_weight_sensitivity(self, train_data, test_data, weights=[0.05, 0.1, 0.15, 0.2, 0.25]):
        """分析重复权重的敏感性"""
        print(f"\n🔍 重复权重敏感性分析")
        print("=" * 50)
        
        weight_results = {}
        
        for weight in weights:
            print(f"\n测试权重: {weight}")
            _, success_rate = self.repeat_enhanced_markov_method(train_data, test_data, weight)
            weight_results[weight] = success_rate
            
            # 与基准比较
            diff = success_rate - self.baseline_performance
            print(f"  性能差异: {diff:+.3f} ({diff*100:+.1f}个百分点)")
        
        return weight_results
    
    def risk_assessment(self, baseline_results, enhanced_results):
        """风险评估"""
        print(f"\n⚠️ 重复机制融合风险评估")
        print("=" * 50)
        
        baseline_rate = baseline_results[1]
        enhanced_rate = enhanced_results[1]
        
        # 性能变化
        performance_change = enhanced_rate - baseline_rate
        relative_change = (performance_change / baseline_rate) * 100
        
        print(f"📊 性能变化分析:")
        print(f"  基准性能: {baseline_rate:.3f} ({baseline_rate*100:.1f}%)")
        print(f"  增强性能: {enhanced_rate:.3f} ({enhanced_rate*100:.1f}%)")
        print(f"  绝对变化: {performance_change:+.3f} ({performance_change*100:+.1f}个百分点)")
        print(f"  相对变化: {relative_change:+.1f}%")
        
        # 风险评估
        risk_level = "低风险"
        if abs(performance_change) > 0.02:
            risk_level = "中等风险"
        if performance_change < -0.03:
            risk_level = "高风险"
        
        print(f"\n🎯 风险等级: {risk_level}")
        
        # 详细分析
        baseline_preds = baseline_results[0]
        enhanced_preds = enhanced_results[0]
        
        # 预测差异分析
        different_predictions = 0
        for i in range(len(baseline_preds)):
            base_pred = set(baseline_preds[i]['predicted'])
            enh_pred = set(enhanced_preds[i]['predicted'])
            if base_pred != enh_pred:
                different_predictions += 1
        
        diff_rate = different_predictions / len(baseline_preds)
        print(f"\n📈 预测差异分析:")
        print(f"  预测不同的期数: {different_predictions}/{len(baseline_preds)}")
        print(f"  预测差异率: {diff_rate:.3f} ({diff_rate*100:.1f}%)")
        
        return {
            'performance_change': performance_change,
            'relative_change': relative_change,
            'risk_level': risk_level,
            'prediction_difference_rate': diff_rate
        }
    
    def feasibility_conclusion(self, weight_results, risk_assessment):
        """可行性结论"""
        print(f"\n🎯 重复机制融合可行性结论")
        print("=" * 60)
        
        # 找到最佳权重
        best_weight = max(weight_results.keys(), key=lambda w: weight_results[w])
        best_performance = weight_results[best_weight]
        best_improvement = best_performance - self.baseline_performance
        
        print(f"📊 最佳配置:")
        print(f"  最佳权重: {best_weight}")
        print(f"  最佳性能: {best_performance:.3f} ({best_performance*100:.1f}%)")
        print(f"  性能提升: {best_improvement:+.3f} ({best_improvement*100:+.1f}个百分点)")
        
        # 可行性评估
        is_feasible = best_improvement > 0.005 and risk_assessment['risk_level'] != "高风险"
        
        print(f"\n🔬 可行性评估:")
        print(f"  技术可行性: {'✅ 可行' if best_improvement > 0 else '❌ 不可行'}")
        print(f"  风险可控性: {'✅ 可控' if risk_assessment['risk_level'] != '高风险' else '❌ 风险过高'}")
        print(f"  实用价值: {'✅ 有价值' if best_improvement > 0.005 else '❌ 价值有限'}")
        print(f"  综合结论: {'✅ 推荐融合' if is_feasible else '❌ 不推荐融合'}")
        
        # 具体建议
        print(f"\n💡 具体建议:")
        if is_feasible:
            print(f"  ✅ 推荐使用重复机制增强马尔可夫基准")
            print(f"  ✅ 建议权重设置: {best_weight}")
            print(f"  ✅ 预期性能提升: {best_improvement*100:.1f}个百分点")
            print(f"  ⚠️ 需要持续监控性能变化")
        else:
            print(f"  ❌ 不推荐融合重复机制")
            print(f"  ❌ 性能提升不明显或风险过高")
            print(f"  ✅ 建议继续使用29.2%马尔可夫基准")
            print(f"  💡 可考虑其他特征或方法")
        
        return is_feasible, best_weight, best_improvement

def main():
    """主函数"""
    print("🔬 重复机制融合马尔可夫基准可行性分析")
    print("评估重复数值机制作为特征加入29.2%马尔可夫基准的可行性")
    print("=" * 80)
    
    analyzer = RepeatEnhancedMarkovAnalyzer()
    
    if not analyzer.load_data():
        return
    
    if not analyzer.prepare_data():
        return
    
    # 1. 基准方法测试
    baseline_results = analyzer.baseline_markov_method(analyzer.train_data, analyzer.test_data)
    
    # 2. 重复增强方法测试
    enhanced_results = analyzer.repeat_enhanced_markov_method(analyzer.train_data, analyzer.test_data, 0.1)
    
    # 3. 权重敏感性分析
    weight_results = analyzer.analyze_weight_sensitivity(analyzer.train_data, analyzer.test_data)
    
    # 4. 风险评估
    risk_assessment = analyzer.risk_assessment(baseline_results, enhanced_results)
    
    # 5. 可行性结论
    is_feasible, best_weight, best_improvement = analyzer.feasibility_conclusion(weight_results, risk_assessment)
    
    print(f"\n🎉 分析完成！")
    print(f"📊 可行性: {'推荐' if is_feasible else '不推荐'}")
    print(f"🎯 最佳权重: {best_weight if is_feasible else 'N/A'}")
    print(f"📈 预期提升: {best_improvement*100:+.1f}个百分点" if is_feasible else "无显著提升")

if __name__ == "__main__":
    main()
