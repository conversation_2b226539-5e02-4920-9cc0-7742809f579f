#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成预测系统
Integrated Prediction System

将增强置信度预测系统与动态置信度调整器集成，
实现高精度预测和自适应置信度调整

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0
"""

import pandas as pd
import numpy as np
from enhanced_confidence_prediction_system import EnhancedConfidencePredictionSystem
from dynamic_confidence_adjuster import DynamicConfidenceAdjuster
from datetime import datetime
import json
import os
import warnings
warnings.filterwarnings('ignore')

class IntegratedPredictionSystem:
    """集成预测系统"""
    
    def __init__(self, config=None):
        """初始化系统"""
        self.config = config or self._get_default_config()
        
        # 初始化增强预测系统
        self.prediction_system = EnhancedConfidencePredictionSystem(
            self.config.get('prediction_config')
        )
        
        # 初始化动态置信度调整器
        self.confidence_adjuster = DynamicConfidenceAdjuster(
            self.config.get('adjuster_config')
        )
        
        # 系统状态
        self.is_trained = False
        self.prediction_history = []
        self.system_metrics = {
            'start_time': datetime.now(),
            'predictions_count': 0,
            'success_count': 0,
            'success_rate': 0.0,
            'avg_confidence': 0.0,
            'avg_adjusted_confidence': 0.0
        }
        
        print(f"🚀 集成预测系统初始化完成")
        print(f"  预测系统: 增强置信度预测系统")
        print(f"  调整器: 动态置信度调整器")
    
    def _get_default_config(self):
        """获取默认配置"""
        return {
            'prediction_config': None,  # 使用预测系统默认配置
            'adjuster_config': None,    # 使用调整器默认配置
            'integration_params': {
                'adjuster_weight': 0.3,  # 调整器权重
                'prediction_weight': 0.7,  # 预测系统权重
                'min_confidence': 0.05,
                'max_confidence': 0.95,
                'auto_calibration': True,
                'calibration_interval': 10  # 每10次预测校准一次
            },
            'output_params': {
                'save_predictions': True,
                'save_metrics': True,
                'output_dir': 'results'
            }
        }
    
    def load_data(self, train_file, test_file=None):
        """加载数据"""
        success = self.prediction_system.load_data(train_file, test_file)
        self.is_trained = success
        return success
    
    def predict_single_period(self, previous_numbers, period_idx, data_source="预测数据"):
        """预测单期"""
        print(f"\n🎯 集成系统预测第{period_idx}期")
        
        # 使用增强预测系统进行预测
        prediction_result = self.prediction_system.predict_single_period(
            previous_numbers, period_idx, data_source
        )
        
        # 获取原始置信度
        original_confidence = prediction_result['confidence']
        
        # 使用动态调整器调整置信度
        prediction_context = {
            'previous_numbers': previous_numbers,
            'data_source': data_source,
            'period_idx': period_idx
        }
        
        adjusted_confidence = self.confidence_adjuster.adjust_confidence(
            original_confidence, prediction_context
        )
        
        # 计算最终置信度
        weights = self.config['integration_params']
        final_confidence = (
            weights['prediction_weight'] * original_confidence +
            weights['adjuster_weight'] * adjusted_confidence
        )
        
        # 确保在合理范围内
        min_conf = self.config['integration_params']['min_confidence']
        max_conf = self.config['integration_params']['max_confidence']
        final_confidence = max(min_conf, min(max_conf, final_confidence))
        
        # 更新预测结果
        prediction_result['original_confidence'] = original_confidence
        prediction_result['adjusted_confidence'] = adjusted_confidence
        prediction_result['final_confidence'] = final_confidence
        prediction_result['confidence'] = final_confidence  # 覆盖原始置信度
        
        print(f"  预测数字: {prediction_result['predicted_numbers']}")
        print(f"  原始置信度: {original_confidence:.3f}")
        print(f"  调整后置信度: {adjusted_confidence:.3f}")
        print(f"  最终置信度: {final_confidence:.3f}")
        
        # 更新系统指标
        self.system_metrics['predictions_count'] += 1
        self.system_metrics['avg_confidence'] = (
            (self.system_metrics['avg_confidence'] * (self.system_metrics['predictions_count'] - 1) +
             original_confidence) / self.system_metrics['predictions_count']
        )
        self.system_metrics['avg_adjusted_confidence'] = (
            (self.system_metrics['avg_adjusted_confidence'] * (self.system_metrics['predictions_count'] - 1) +
             final_confidence) / self.system_metrics['predictions_count']
        )
        
        return prediction_result
    
    def validate_prediction(self, prediction_result, actual_numbers):
        """验证预测结果"""
        # 使用预测系统验证
        validated_result = self.prediction_system.validate_prediction(
            prediction_result, actual_numbers
        )
        
        # 更新动态调整器
        self.confidence_adjuster.update_prediction_result(validated_result)
        
        # 更新系统指标
        if validated_result['is_hit']:
            self.system_metrics['success_count'] += 1
        
        self.system_metrics['success_rate'] = (
            self.system_metrics['success_count'] / self.system_metrics['predictions_count']
            if self.system_metrics['predictions_count'] > 0 else 0
        )
        
        # 添加到历史记录
        self.prediction_history.append(validated_result)
        
        # 自动校准
        if (self.config['integration_params']['auto_calibration'] and
            len(self.prediction_history) % self.config['integration_params']['calibration_interval'] == 0):
            self._auto_calibrate()
        
        print(f"  实际数字: {sorted(list(actual_numbers))}")
        print(f"  命中数字: {validated_result['hit_numbers']}")
        print(f"  命中情况: {'✅ 成功' if validated_result['is_hit'] else '❌ 失败'}")
        
        return validated_result
    
    def _auto_calibrate(self):
        """自动校准系统"""
        print(f"\n🔧 执行自动校准")
        
        # 获取调整报告
        adjustment_report = self.confidence_adjuster.get_adjustment_report()
        
        # 更新集成权重
        if len(self.prediction_history) >= 10:
            # 计算原始置信度与命中率的相关性
            original_confidences = [p['original_confidence'] for p in self.prediction_history[-10:]]
            adjusted_confidences = [p['adjusted_confidence'] for p in self.prediction_history[-10:]]
            hits = [1 if p['is_hit'] else 0 for p in self.prediction_history[-10:]]
            
            if len(original_confidences) > 1:
                original_correlation = np.corrcoef(original_confidences, hits)[0, 1]
                adjusted_correlation = np.corrcoef(adjusted_confidences, hits)[0, 1]
                
                # 根据相关性调整权重
                if adjusted_correlation > original_correlation:
                    # 调整后的置信度更准确，增加调整器权重
                    self.config['integration_params']['adjuster_weight'] = min(
                        0.5, self.config['integration_params']['adjuster_weight'] + 0.05
                    )
                    self.config['integration_params']['prediction_weight'] = (
                        1 - self.config['integration_params']['adjuster_weight']
                    )
                else:
                    # 原始置信度更准确，增加预测系统权重
                    self.config['integration_params']['prediction_weight'] = min(
                        0.9, self.config['integration_params']['prediction_weight'] + 0.05
                    )
                    self.config['integration_params']['adjuster_weight'] = (
                        1 - self.config['integration_params']['prediction_weight']
                    )
                
                print(f"  调整权重: 预测系统={self.config['integration_params']['prediction_weight']:.2f}, "
                      f"调整器={self.config['integration_params']['adjuster_weight']:.2f}")
        
        # 显示调整建议
        if 'recommendations' in adjustment_report:
            print(f"  调整建议:")
            for rec in adjustment_report['recommendations']:
                print(f"    - {rec}")
    
    def batch_predict(self, start_period, end_period, base_data=None):
        """批量预测"""
        print(f"\n🚀 集成系统批量预测 {start_period}-{end_period}期")
        
        predictions = []
        
        # 获取起始数据
        if base_data is not None:
            previous_numbers = set(base_data)
            data_source = "用户输入"
        else:
            # 使用训练数据的最后一期
            last_row = self.prediction_system.train_data.iloc[-1]
            previous_numbers = set([last_row[f'数字{j}'] for j in range(1, 7)])
            data_source = "历史数据"
        
        # 逐期预测
        for period in range(start_period, end_period + 1):
            prediction_result = self.predict_single_period(
                previous_numbers, period, data_source
            )
            predictions.append(prediction_result)
            
            # 更新前期数据为当前预测结果
            previous_numbers = set(prediction_result['predicted_numbers'])
            data_source = "预测数据"
        
        return predictions
    
    def evaluate_system_performance(self, test_data=None):
        """评估系统性能"""
        print(f"\n📊 集成系统性能评估")
        
        if test_data is None:
            test_data = self.prediction_system.test_data
        
        if test_data is None:
            print("  ❌ 没有测试数据")
            return None
        
        predictions = []
        
        # 逐期预测和验证
        for idx, row in test_data.iterrows():
            period_num = row['期号']
            actual_numbers = set([row[f'数字{j}'] for j in range(1, 7)])
            
            # 获取前期数字
            if idx == test_data.index[0]:
                # 使用训练数据的最后一期
                prev_row = self.prediction_system.train_data.iloc[-1]
                previous_numbers = set([prev_row[f'数字{j}'] for j in range(1, 7)])
                data_source = "历史数据"
            else:
                # 使用测试数据的前一期
                prev_idx = test_data.index[test_data.index.get_loc(idx) - 1]
                prev_row = test_data.loc[prev_idx]
                previous_numbers = set([prev_row[f'数字{j}'] for j in range(1, 7)])
                data_source = "真实数据"
            
            # 预测
            prediction_result = self.predict_single_period(
                previous_numbers, period_num, data_source
            )
            
            # 验证
            prediction_result = self.validate_prediction(prediction_result, actual_numbers)
            predictions.append(prediction_result)
        
        # 计算性能指标
        total_predictions = len(predictions)
        success_count = sum(1 for p in predictions if p['is_hit'])
        success_rate = success_count / total_predictions if total_predictions > 0 else 0
        
        avg_original_confidence = np.mean([p['original_confidence'] for p in predictions])
        avg_adjusted_confidence = np.mean([p['adjusted_confidence'] for p in predictions])
        avg_final_confidence = np.mean([p['final_confidence'] for p in predictions])
        
        # 计算置信度校准
        final_confidences = [p['final_confidence'] for p in predictions]
        hits = [1 if p['is_hit'] else 0 for p in predictions]
        
        confidence_calibration = np.corrcoef(final_confidences, hits)[0, 1] if len(final_confidences) > 1 else 0
        
        # 性能报告
        performance_report = {
            'total_predictions': total_predictions,
            'success_count': success_count,
            'success_rate': success_rate,
            'avg_original_confidence': avg_original_confidence,
            'avg_adjusted_confidence': avg_adjusted_confidence,
            'avg_final_confidence': avg_final_confidence,
            'confidence_calibration': confidence_calibration,
            'predictions': predictions
        }
        
        print(f"  总预测期数: {total_predictions}")
        print(f"  成功预测: {success_count}")
        print(f"  成功率: {success_rate:.3f} ({success_rate*100:.1f}%)")
        print(f"  原始平均置信度: {avg_original_confidence:.3f}")
        print(f"  调整后平均置信度: {avg_adjusted_confidence:.3f}")
        print(f"  最终平均置信度: {avg_final_confidence:.3f}")
        print(f"  置信度校准: {confidence_calibration:.3f}")
        
        # 保存性能报告
        if self.config['output_params']['save_metrics']:
            self._save_performance_report(performance_report)
        
        return performance_report
    
    def _save_performance_report(self, performance_report):
        """保存性能报告"""
        # 确保输出目录存在
        output_dir = self.config['output_params']['output_dir']
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{output_dir}/integrated_system_performance_{timestamp}.json"
        
        # 准备报告数据
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'total_predictions': performance_report['total_predictions'],
            'success_count': performance_report['success_count'],
            'success_rate': performance_report['success_rate'],
            'avg_original_confidence': performance_report['avg_original_confidence'],
            'avg_adjusted_confidence': performance_report['avg_adjusted_confidence'],
            'avg_final_confidence': performance_report['avg_final_confidence'],
            'confidence_calibration': performance_report['confidence_calibration'],
            'system_config': self.config,
            'adjuster_report': self.confidence_adjuster.get_adjustment_report()
        }
        
        # 保存报告
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"  ✅ 性能报告已保存到 {filename}")
        
        return filename
