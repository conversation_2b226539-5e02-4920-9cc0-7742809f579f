#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Model Evaluator for Lottery Prediction System
Evaluates model performance on test set with comprehensive metrics
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Any
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Set Chinese font for matplotlib
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class LotteryModelEvaluator:
    """
    Comprehensive model evaluator for lottery prediction system
    Focuses on practical lottery prediction metrics
    """
    
    def __init__(self, results_dir: str = 'results'):
        self.results_dir = results_dir
        self.evaluation_results = {}
        self.predictions = {}
        
        import os
        os.makedirs(results_dir, exist_ok=True)
    
    def predict_single_model(self, model, X_test: pd.DataFrame, 
                           model_name: str, target_col: str,
                           scaler=None) -> np.ndarray:
        """
        Generate predictions for a single model
        """
        # Apply scaling if needed
        if scaler is not None:
            X_test_scaled = scaler.transform(X_test)
            predictions = model.predict(X_test_scaled)
        else:
            predictions = model.predict(X_test)
        
        # Clip predictions to valid range (1-49)
        predictions = np.clip(np.round(predictions), 1, 49).astype(int)
        
        return predictions
    
    def evaluate_single_model(self, model, X_test: pd.DataFrame, y_test: pd.Series,
                            model_name: str, target_col: str, scaler=None) -> Dict:
        """
        Evaluate a single model's performance
        """
        # Generate predictions
        predictions = self.predict_single_model(model, X_test, model_name, target_col, scaler)
        
        # Calculate regression metrics
        mse = mean_squared_error(y_test, predictions)
        mae = mean_absolute_error(y_test, predictions)
        rmse = np.sqrt(mse)
        
        # Calculate accuracy (exact matches)
        accuracy = np.mean(predictions == y_test.values)
        
        # Calculate hit rate within tolerance
        tolerance_1 = np.mean(np.abs(predictions - y_test.values) <= 1)
        tolerance_2 = np.mean(np.abs(predictions - y_test.values) <= 2)
        tolerance_3 = np.mean(np.abs(predictions - y_test.values) <= 3)
        
        return {
            'predictions': predictions,
            'mse': mse,
            'mae': mae,
            'rmse': rmse,
            'accuracy': accuracy,
            'tolerance_1': tolerance_1,
            'tolerance_2': tolerance_2,
            'tolerance_3': tolerance_3
        }
    
    def evaluate_all_models(self, models: Dict, scalers: Dict,
                          test_data: pd.DataFrame, feature_columns: List[str],
                          target_columns: List[str]) -> Dict:
        """
        Evaluate all trained models
        """
        print("=== 开始模型评估 ===")
        
        all_results = {}
        all_predictions = {}
        
        for target_col in target_columns:
            print(f"\n评估目标: {target_col}")
            print("-" * 40)
            
            # Prepare test data
            X_test = test_data[feature_columns].fillna(0)
            y_test = test_data[target_col]
            
            target_results = {}
            target_predictions = {}
            
            for model_key, model in models.items():
                if target_col in model_key:
                    model_name = model_key.replace(f"_{target_col}", "")
                    
                    # Get scaler if exists
                    scaler = scalers.get(model_key, None)
                    
                    # Evaluate model
                    result = self.evaluate_single_model(
                        model, X_test, y_test, model_name, target_col, scaler
                    )
                    
                    target_results[model_name] = result
                    target_predictions[model_name] = result['predictions']
                    
                    print(f"  {model_name}: 准确率={result['accuracy']:.3f}, "
                          f"RMSE={result['rmse']:.2f}, "
                          f"容差±1={result['tolerance_1']:.3f}")
            
            all_results[target_col] = target_results
            all_predictions[target_col] = target_predictions
        
        self.evaluation_results = all_results
        self.predictions = all_predictions
        
        print("\n=== 模型评估完成 ===")
        return all_results
    
    def calculate_lottery_hit_rates(self, test_data: pd.DataFrame) -> Dict:
        """
        Calculate lottery-specific hit rates (how many numbers predicted correctly per period)
        """
        print("\n=== 计算彩票命中率 ===")
        
        target_columns = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        actual_numbers = test_data[target_columns].values
        
        hit_rate_results = {}
        
        # Get all model names
        model_names = set()
        for target_col in target_columns:
            if target_col in self.predictions:
                model_names.update(self.predictions[target_col].keys())
        
        for model_name in model_names:
            print(f"\n分析模型: {model_name}")
            
            # Collect predictions for all 6 numbers
            predicted_numbers = []
            for target_col in target_columns:
                if target_col in self.predictions and model_name in self.predictions[target_col]:
                    predicted_numbers.append(self.predictions[target_col][model_name])
            
            if len(predicted_numbers) == 6:
                predicted_numbers = np.array(predicted_numbers).T  # Shape: (n_periods, 6)
                
                # Calculate hits per period
                hits_per_period = []
                for i in range(len(actual_numbers)):
                    actual_set = set(actual_numbers[i])
                    predicted_set = set(predicted_numbers[i])
                    hits = len(actual_set.intersection(predicted_set))
                    hits_per_period.append(hits)
                
                # Calculate statistics
                avg_hits = np.mean(hits_per_period)
                max_hits = np.max(hits_per_period)
                hit_distribution = np.bincount(hits_per_period, minlength=7)
                
                hit_rate_results[model_name] = {
                    'hits_per_period': hits_per_period,
                    'avg_hits': avg_hits,
                    'max_hits': max_hits,
                    'hit_distribution': hit_distribution,
                    'periods_with_0_hits': hit_distribution[0],
                    'periods_with_1_hits': hit_distribution[1],
                    'periods_with_2_hits': hit_distribution[2],
                    'periods_with_3_hits': hit_distribution[3],
                    'periods_with_4_hits': hit_distribution[4],
                    'periods_with_5_hits': hit_distribution[5],
                    'periods_with_6_hits': hit_distribution[6]
                }
                
                print(f"  平均命中数/期: {avg_hits:.3f}")
                print(f"  最高单期命中: {max_hits}")
                print(f"  命中分布: {hit_distribution}")
        
        return hit_rate_results
    
    def compare_with_random_baseline(self, test_data: pd.DataFrame, 
                                   num_simulations: int = 1000) -> Dict:
        """
        Compare model performance with random baseline
        """
        print("\n=== 随机基准对比 ===")
        
        target_columns = ['数字1', '数字2', '数字3', '数字4', '数字5', '数字6']
        actual_numbers = test_data[target_columns].values
        n_periods = len(actual_numbers)
        
        # Random baseline simulation
        random_hits = []
        for _ in range(num_simulations):
            period_hits = []
            for i in range(n_periods):
                # Generate 6 random numbers (1-49)
                random_numbers = np.random.choice(range(1, 50), size=6, replace=False)
                actual_set = set(actual_numbers[i])
                random_set = set(random_numbers)
                hits = len(actual_set.intersection(random_set))
                period_hits.append(hits)
            random_hits.append(np.mean(period_hits))
        
        random_avg = np.mean(random_hits)
        random_std = np.std(random_hits)
        
        print(f"随机基准平均命中率: {random_avg:.3f} ± {random_std:.3f}")
        
        # Compare with model results
        hit_rates = self.calculate_lottery_hit_rates(test_data)
        
        comparison_results = {
            'random_baseline': {
                'avg_hits': random_avg,
                'std_hits': random_std
            },
            'model_comparison': {}
        }
        
        for model_name, results in hit_rates.items():
            improvement = results['avg_hits'] - random_avg
            comparison_results['model_comparison'][model_name] = {
                'avg_hits': results['avg_hits'],
                'improvement_over_random': improvement,
                'improvement_percentage': (improvement / random_avg) * 100 if random_avg > 0 else 0
            }
            
            print(f"{model_name}: {results['avg_hits']:.3f} "
                  f"(比随机高 {improvement:+.3f}, {(improvement/random_avg)*100:+.1f}%)")
        
        return comparison_results
    
    def create_evaluation_visualizations(self, test_data: pd.DataFrame):
        """
        Create comprehensive evaluation visualizations
        """
        print("\n=== 创建评估可视化 ===")
        
        # Calculate hit rates
        hit_rates = self.calculate_lottery_hit_rates(test_data)
        
        if not hit_rates:
            print("没有可用的命中率数据")
            return
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('彩票预测模型评估结果', fontsize=16, fontweight='bold')
        
        # 1. Average hits per model
        model_names = list(hit_rates.keys())
        avg_hits = [hit_rates[model]['avg_hits'] for model in model_names]
        
        axes[0, 0].bar(model_names, avg_hits, color='skyblue', alpha=0.7)
        axes[0, 0].set_title('各模型平均命中数/期')
        axes[0, 0].set_ylabel('平均命中数')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # Add value labels on bars
        for i, v in enumerate(avg_hits):
            axes[0, 0].text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
        
        # 2. Hit distribution for best model
        best_model = max(hit_rates.keys(), key=lambda x: hit_rates[x]['avg_hits'])
        hit_dist = hit_rates[best_model]['hit_distribution']
        
        axes[0, 1].bar(range(7), hit_dist, color='lightgreen', alpha=0.7)
        axes[0, 1].set_title(f'命中分布 - {best_model}')
        axes[0, 1].set_xlabel('每期命中数')
        axes[0, 1].set_ylabel('期数')
        axes[0, 1].set_xticks(range(7))
        
        # 3. Hits per period over time
        hits_per_period = hit_rates[best_model]['hits_per_period']
        periods = range(161, 161 + len(hits_per_period))
        
        axes[1, 0].plot(periods, hits_per_period, marker='o', linewidth=2, markersize=6)
        axes[1, 0].set_title(f'各期命中数变化 - {best_model}')
        axes[1, 0].set_xlabel('期号')
        axes[1, 0].set_ylabel('命中数')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. Model comparison heatmap
        if len(model_names) > 1:
            # Create comparison matrix
            metrics = ['avg_hits', 'max_hits']
            comparison_data = []
            
            for model in model_names:
                row = [hit_rates[model]['avg_hits'], hit_rates[model]['max_hits']]
                comparison_data.append(row)
            
            comparison_df = pd.DataFrame(comparison_data, 
                                       index=model_names, 
                                       columns=['平均命中', '最高命中'])
            
            sns.heatmap(comparison_df, annot=True, fmt='.3f', 
                       cmap='YlOrRd', ax=axes[1, 1])
            axes[1, 1].set_title('模型性能对比')
        else:
            axes[1, 1].text(0.5, 0.5, '需要多个模型进行对比', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('模型对比')
        
        plt.tight_layout()
        
        # Save visualization
        viz_path = f"{self.results_dir}/evaluation_results.png"
        plt.savefig(viz_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"评估可视化已保存到: {viz_path}")
    
    def generate_evaluation_report(self, test_data: pd.DataFrame) -> str:
        """
        Generate comprehensive evaluation report
        """
        print("\n=== 生成评估报告 ===")
        
        # Calculate all metrics
        hit_rates = self.calculate_lottery_hit_rates(test_data)
        baseline_comparison = self.compare_with_random_baseline(test_data)
        
        # Generate report
        report = []
        report.append("# 彩票预测模型评估报告")
        report.append(f"\n## 测试数据概况")
        report.append(f"- 测试期数: 第161-180期 (共{len(test_data)}期)")
        report.append(f"- 预测目标: 6个主要数字 (1-49范围)")
        
        report.append(f"\n## 模型性能总结")
        
        if hit_rates:
            # Sort models by performance
            sorted_models = sorted(hit_rates.items(), 
                                 key=lambda x: x[1]['avg_hits'], reverse=True)
            
            for i, (model_name, results) in enumerate(sorted_models, 1):
                report.append(f"\n### {i}. {model_name}")
                report.append(f"- 平均命中数/期: {results['avg_hits']:.3f}")
                report.append(f"- 最高单期命中: {results['max_hits']}")
                report.append(f"- 命中0个的期数: {results['periods_with_0_hits']}")
                report.append(f"- 命中1个的期数: {results['periods_with_1_hits']}")
                report.append(f"- 命中2个的期数: {results['periods_with_2_hits']}")
                report.append(f"- 命中3+个的期数: {sum(results['hit_distribution'][3:])}")
        
        report.append(f"\n## 与随机基准对比")
        if 'random_baseline' in baseline_comparison:
            random_avg = baseline_comparison['random_baseline']['avg_hits']
            report.append(f"- 随机基准平均命中: {random_avg:.3f}")
            
            for model_name, comp in baseline_comparison['model_comparison'].items():
                improvement = comp['improvement_over_random']
                improvement_pct = comp['improvement_percentage']
                report.append(f"- {model_name}: {comp['avg_hits']:.3f} "
                             f"(比随机高 {improvement:+.3f}, {improvement_pct:+.1f}%)")
        
        report.append(f"\n## 结论与建议")
        if hit_rates:
            best_model = max(hit_rates.keys(), key=lambda x: hit_rates[x]['avg_hits'])
            best_performance = hit_rates[best_model]['avg_hits']
            
            report.append(f"- 最佳模型: {best_model} (平均命中 {best_performance:.3f}/期)")
            
            if best_performance > 1.0:
                report.append("- 模型表现超过随机水平，具有一定预测能力")
            else:
                report.append("- 模型表现接近随机水平，预测能力有限")
            
            report.append("- 建议继续优化特征工程和模型参数")
            report.append("- 考虑集成多个模型以提高稳定性")
        
        # Save report
        report_content = "\n".join(report)
        report_path = f"{self.results_dir}/evaluation_report.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"评估报告已保存到: {report_path}")
        return report_path


if __name__ == "__main__":
    # Test the evaluator
    print("模型评估器测试完成")
