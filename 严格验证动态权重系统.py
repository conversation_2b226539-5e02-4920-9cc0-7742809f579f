#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
严格验证动态权重系统
训练集：2024年全年数据
测试集：2025年1-204期数据
严格避免数据泄露和过拟合问题

核心改进：
1. 严格的时间分割：训练集2024年，测试集2025年
2. 防止数据泄露：测试时只使用历史数据
3. 动态权重更新：基于滑动窗口实时更新
4. 过拟合控制：正则化和早停机制
5. 在线学习：逐期更新权重，模拟真实预测环境
"""

import pandas as pd
import numpy as np
from datetime import datetime
from collections import defaultdict, Counter, deque
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class StrictDynamicWeightSystem:
    """严格验证的动态权重系统"""
    
    def __init__(self):
        self.data_file = "data/processed/lottery_data_clean_no_special.csv"
        
        # 严格验证配置
        self.config = {
            # 数据分割配置
            'data_split': {
                'train_year': 2024,           # 训练集：2024年
                'test_year': 2025,            # 测试集：2025年
                'test_start_period': 1,       # 从第1期开始
                'test_end_period': 204        # 到第204期结束
            },
            
            # 动态权重配置
            'dynamic_weight': {
                'initial_window': 30,         # 初始权重窗口（2024年最后30期）
                'update_window': 15,          # 更新窗口大小
                'update_frequency': 5,        # 每5期更新一次
                'min_appearance': 2,          # 最小出现次数
                'boost_factor': 1.3,          # 权重提升因子（降低以防过拟合）
                'decay_factor': 0.98,         # 权重衰减因子（减缓衰减）
                'regularization': 0.05        # 正则化强度
            },
            
            # 过拟合控制
            'overfitting_control': {
                'weight_smoothing': 0.1,      # 权重平滑
                'trend_damping': 0.8,         # 趋势阻尼
                'noise_reduction': 0.05,      # 噪声减少
                'stability_factor': 0.9       # 稳定性因子
            },
            
            # 预测策略
            'prediction': {
                'base_confidence': 0.2,       # 基础置信度
                'hot_boost': 1.5,             # 热门数字加成（降低）
                'diversity_factor': 0.3,      # 多样性因子
                'min_distance': 6,            # 最小距离
                'exploration_rate': 0.25      # 探索率
            }
        }
        
        # 系统状态
        self.train_data = None
        self.test_data = None
        self.base_weights = defaultdict(float)      # 基础权重（从训练集学习）
        self.dynamic_weights = defaultdict(float)   # 动态权重
        self.trend_weights = defaultdict(float)     # 趋势权重
        self.weight_history = deque(maxlen=50)      # 权重历史
        self.prediction_history = deque(maxlen=20)  # 预测历史
        self.performance_tracker = []               # 性能追踪
        
    def load_and_split_data_strict(self):
        """严格的数据加载和分割"""
        print("📊 严格数据分割 - 避免数据泄露...")
        
        try:
            # 加载数据
            full_data = pd.read_csv(self.data_file, encoding='utf-8')
            full_data = full_data.dropna().sort_values(['年份', '期号'])
            
            # 严格时间分割
            train_condition = (full_data['年份'] == self.config['data_split']['train_year'])
            test_condition = (
                (full_data['年份'] == self.config['data_split']['test_year']) &
                (full_data['期号'] >= self.config['data_split']['test_start_period']) &
                (full_data['期号'] <= self.config['data_split']['test_end_period'])
            )
            
            self.train_data = full_data[train_condition].copy()
            self.test_data = full_data[test_condition].copy()
            
            print(f"   训练集: {len(self.train_data)} 期 (2024年全年)")
            print(f"   测试集: {len(self.test_data)} 期 (2025年1-204期)")
            print(f"   时间间隔: 严格分离，无数据泄露")
            
            # 验证数据完整性
            if len(self.train_data) == 0:
                print("❌ 训练集为空")
                return False
            if len(self.test_data) == 0:
                print("❌ 测试集为空")
                return False
                
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def initialize_base_weights(self):
        """从训练集初始化基础权重"""
        print("\n🔧 从2024年训练集初始化基础权重...")
        
        try:
            # 提取训练集中的所有数字
            all_train_numbers = []
            for _, row in self.train_data.iterrows():
                numbers = [int(row[f'数字{i}']) for i in range(1, 7)]
                all_train_numbers.extend(numbers)
            
            # 计算基础频率
            number_counts = Counter(all_train_numbers)
            total_count = len(all_train_numbers)
            
            # 初始化基础权重
            for num in range(1, 50):
                base_freq = number_counts[num] / total_count if total_count > 0 else 0
                # 应用正则化，避免过拟合
                regularization = self.config['overfitting_control']['weight_smoothing']
                self.base_weights[num] = base_freq * (1 - regularization) + regularization / 49
                self.dynamic_weights[num] = self.base_weights[num]
                self.trend_weights[num] = 0.0
            
            print(f"   基础权重范围: {min(self.base_weights.values()):.4f} - {max(self.base_weights.values()):.4f}")
            print(f"   权重总和: {sum(self.base_weights.values()):.4f}")
            print(f"   正则化强度: {regularization:.3f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 基础权重初始化失败: {e}")
            return False
    
    def update_dynamic_weights_online(self, current_period, historical_data):
        """在线更新动态权重（避免数据泄露）"""
        update_freq = self.config['dynamic_weight']['update_frequency']
        
        # 只在指定频率更新权重
        if current_period % update_freq != 0:
            return False
        
        print(f"   🔄 在线更新权重 - 第{current_period}期")
        
        try:
            window_size = self.config['dynamic_weight']['update_window']
            min_appearance = self.config['dynamic_weight']['min_appearance']
            boost_factor = self.config['dynamic_weight']['boost_factor']
            decay_factor = self.config['dynamic_weight']['decay_factor']
            
            # 获取最近window_size期的历史数据（不包含当前期）
            recent_data = historical_data.tail(window_size)
            
            # 提取最近期数的数字
            recent_numbers = []
            for _, row in recent_data.iterrows():
                numbers = [int(row[f'数字{i}']) for i in range(1, 7)]
                recent_numbers.extend(numbers)
            
            if len(recent_numbers) == 0:
                return False
            
            # 统计最近频率
            recent_counts = Counter(recent_numbers)
            total_recent = len(recent_numbers)
            
            # 识别热门数字
            hot_numbers = []
            for num in range(1, 50):
                if recent_counts[num] >= min_appearance:
                    recent_freq = recent_counts[num] / total_recent
                    base_freq = self.base_weights[num]
                    
                    if base_freq > 0:
                        weight_ratio = recent_freq / base_freq
                        if weight_ratio > 1.2:  # 降低阈值，更敏感
                            hot_numbers.append(num)
            
            # 更新动态权重
            for num in range(1, 50):
                if num in hot_numbers:
                    # 热门数字权重提升
                    old_weight = self.dynamic_weights[num]
                    self.dynamic_weights[num] = old_weight * boost_factor
                    
                    # 更新趋势权重
                    self.trend_weights[num] = min(1.0, self.trend_weights[num] + 0.15)
                else:
                    # 其他数字权重衰减
                    self.dynamic_weights[num] *= decay_factor
                    self.trend_weights[num] *= 0.95
            
            # 应用稳定性控制
            stability_factor = self.config['overfitting_control']['stability_factor']
            for num in range(1, 50):
                # 权重平滑，防止过度波动
                self.dynamic_weights[num] = (
                    self.dynamic_weights[num] * stability_factor + 
                    self.base_weights[num] * (1 - stability_factor)
                )
            
            # 记录权重更新历史
            self.weight_history.append({
                'period': current_period,
                'hot_numbers': hot_numbers,
                'weight_range': (min(self.dynamic_weights.values()), max(self.dynamic_weights.values())),
                'hot_count': len(hot_numbers)
            })
            
            print(f"     🔥 识别热门数字: {hot_numbers[:5]}{'...' if len(hot_numbers) > 5 else ''} (共{len(hot_numbers)}个)")
            
            return True
            
        except Exception as e:
            print(f"❌ 权重更新失败: {e}")
            return False
    
    def predict_with_dynamic_weights(self, target_period, historical_data):
        """基于动态权重进行预测"""
        try:
            # 计算综合权重分数
            combined_scores = {}
            hot_boost = self.config['prediction']['hot_boost']
            
            for num in range(1, 50):
                # 基础动态权重
                base_score = self.dynamic_weights[num]
                
                # 趋势加成
                trend_score = self.trend_weights[num]
                if trend_score > 0.1:
                    base_score *= (1 + trend_score * 0.5)
                
                # 添加适度随机性（探索）
                exploration_rate = self.config['prediction']['exploration_rate']
                if np.random.random() < exploration_rate:
                    random_factor = np.random.uniform(0.8, 1.3)
                    base_score *= random_factor
                
                combined_scores[num] = base_score
            
            # 应用多样性约束
            combined_scores = self.apply_diversity_constraint(combined_scores)
            
            # 选择得分最高的两个数字
            sorted_scores = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
            
            selected = [sorted_scores[0][0]]
            min_distance = self.config['prediction']['min_distance']
            
            # 选择第二个数字，确保距离
            for num, score in sorted_scores[1:]:
                if abs(num - selected[0]) >= min_distance:
                    selected.append(num)
                    break
            
            if len(selected) < 2:
                selected.append(sorted_scores[1][0])
            
            # 计算置信度
            confidence = np.mean([combined_scores[num] for num in selected])
            confidence = min(0.6, max(0.1, confidence * 20))  # 归一化
            
            # 记录预测历史
            prediction_combo = tuple(sorted(selected))
            self.prediction_history.append(prediction_combo)
            
            return {
                'numbers': sorted(selected),
                'confidence': confidence,
                'method': 'Strict_Dynamic_Weight',
                'scores': {num: combined_scores[num] for num in selected}
            }
            
        except Exception as e:
            print(f"⚠️ 预测失败: {e}")
            return self.fallback_prediction()
    
    def apply_diversity_constraint(self, scores):
        """应用多样性约束"""
        diversity_factor = self.config['prediction']['diversity_factor']
        
        # 对最近使用过的数字进行惩罚
        if len(self.prediction_history) >= 3:
            recent_numbers = set()
            for combo in list(self.prediction_history)[-3:]:
                recent_numbers.update(combo)
            
            for num in recent_numbers:
                if num in scores:
                    scores[num] *= (1 - diversity_factor)
        
        return scores
    
    def fallback_prediction(self):
        """备用预测方法"""
        # 基于基础权重的简单预测
        sorted_weights = sorted(self.base_weights.items(), key=lambda x: x[1], reverse=True)
        
        # 从前20个高权重数字中随机选择
        candidates = [num for num, weight in sorted_weights[:20]]
        selected = sorted(np.random.choice(candidates, 2, replace=False))
        
        return {
            'numbers': selected,
            'confidence': 0.15,
            'method': 'Fallback_BaseWeight',
            'scores': {}
        }
    
    def predict_2025_periods_strict(self):
        """严格预测2025年1-204期"""
        print("\n🎯 严格预测2025年1-204期（避免数据泄露）...")
        
        try:
            predictions = []
            
            # 初始化历史数据（只包含2024年训练数据）
            historical_data = self.train_data.copy()
            
            for _, row in self.test_data.iterrows():
                year = int(row['年份'])
                period = int(row['期号'])
                actual_numbers = [int(row[f'数字{i}']) for i in range(1, 7)]
                
                # 在线更新权重（基于历史数据，不包含当前期）
                self.update_dynamic_weights_online(period, historical_data)
                
                # 基于动态权重预测
                prediction = self.predict_with_dynamic_weights(period, historical_data)
                
                # 计算命中情况
                pred_set = set(prediction['numbers'])
                actual_set = set(actual_numbers)
                hit_count = len(pred_set & actual_set)
                
                result = {
                    'year': year,
                    'period': period,
                    'period_id': f"{year}年{period}期",
                    'predicted_numbers': prediction['numbers'],
                    'actual_numbers': actual_numbers,
                    'confidence': prediction['confidence'],
                    'hit_count': hit_count,
                    'is_hit': hit_count > 0,
                    'hit_rate': hit_count / 2.0,
                    'pred_sum': sum(prediction['numbers']),
                    'actual_sum': sum(actual_numbers),
                    'method': prediction['method'],
                    'prediction_scores': prediction.get('scores', {})
                }
                
                predictions.append(result)
                
                # 将当前期数据添加到历史数据中（用于下一期的权重更新）
                historical_data = pd.concat([historical_data, row.to_frame().T], ignore_index=True)
                
                # 实时输出
                status = "✅" if result['is_hit'] else "❌"
                if period % 20 == 0 or result['is_hit']:  # 每20期或命中时输出
                    print(f"   {result['period_id']}: 预测{prediction['numbers']} → 实际{actual_numbers[:2]} {status}")
            
            return predictions
            
        except Exception as e:
            print(f"❌ 严格预测失败: {e}")
            return []
    
    def analyze_strict_performance(self, predictions):
        """分析严格验证性能"""
        print(f"\n📊 严格验证性能分析...")
        
        if not predictions:
            print("❌ 无预测结果")
            return None
        
        # 基本统计
        total_periods = len(predictions)
        hit_periods = sum(1 for p in predictions if p['is_hit'])
        hit_rate = hit_periods / total_periods if total_periods > 0 else 0
        
        total_hit_count = sum(p['hit_count'] for p in predictions)
        avg_confidence = np.mean([p['confidence'] for p in predictions])
        
        # 多样性分析
        pred_combinations = [tuple(sorted(p['predicted_numbers'])) for p in predictions]
        unique_combinations = len(set(pred_combinations))
        diversity_rate = unique_combinations / total_periods if total_periods > 0 else 0
        
        # 方法分布
        method_counts = Counter([p['method'] for p in predictions])
        
        # 权重更新统计
        weight_updates = len(self.weight_history)
        
        # 时间段性能分析
        period_performance = {}
        for i in range(0, total_periods, 50):
            end_idx = min(i + 50, total_periods)
            period_preds = predictions[i:end_idx]
            period_hits = sum(1 for p in period_preds if p['is_hit'])
            period_rate = period_hits / len(period_preds) if period_preds else 0
            period_performance[f"{i+1}-{end_idx}期"] = period_rate
        
        print(f"   总预测期数: {total_periods}")
        print(f"   命中期数: {hit_periods}")
        print(f"   总命中率: {hit_rate:.1%}")
        print(f"   总命中数: {total_hit_count}")
        print(f"   平均置信度: {avg_confidence:.3f}")
        print(f"   唯一组合数: {unique_combinations}")
        print(f"   多样性率: {diversity_rate:.1%}")
        print(f"   权重更新次数: {weight_updates}")
        
        print(f"\n📈 预测方法分布:")
        for method, count in method_counts.items():
            print(f"   {method}: {count}次 ({count/total_periods:.1%})")
        
        print(f"\n📊 时间段性能:")
        for period_range, rate in period_performance.items():
            print(f"   {period_range}: {rate:.1%}")
        
        # 基准对比
        baselines = {
            '随机预测': 0.041,
            '历史基准': 0.123,
            '简单频率': 0.180,
            '之前最佳': 0.264
        }
        
        print(f"\n📈 严格验证基准对比:")
        for method, baseline in baselines.items():
            improvement = hit_rate - baseline
            status = "✅" if improvement > 0 else "❌"
            print(f"   vs {method}({baseline:.1%}): {improvement:+.1%} {status}")
        
        return {
            'hit_rate': hit_rate,
            'hit_count': total_hit_count,
            'total_periods': total_periods,
            'diversity_rate': diversity_rate,
            'avg_confidence': avg_confidence,
            'weight_updates': weight_updates,
            'method_distribution': dict(method_counts),
            'period_performance': period_performance
        }
    
    def save_strict_results_to_csv(self, predictions):
        """保存严格验证结果到CSV"""
        print(f"\n💾 保存严格验证结果到CSV文件...")
        
        try:
            if not predictions:
                print("❌ 无预测结果可保存")
                return None
            
            # 准备CSV数据
            csv_data = []
            for result in predictions:
                csv_data.append({
                    '年份': result['year'],
                    '期号': result['period'],
                    '期号标识': result['period_id'],
                    '预测数字1': result['predicted_numbers'][0],
                    '预测数字2': result['predicted_numbers'][1],
                    '预测组合': str(result['predicted_numbers']),
                    '预测置信度': round(result['confidence'], 4),
                    '实际数字1': result['actual_numbers'][0],
                    '实际数字2': result['actual_numbers'][1],
                    '实际数字3': result['actual_numbers'][2],
                    '实际数字4': result['actual_numbers'][3],
                    '实际数字5': result['actual_numbers'][4],
                    '实际数字6': result['actual_numbers'][5],
                    '实际组合': str(result['actual_numbers']),
                    '命中数量': result['hit_count'],
                    '是否命中': '是' if result['is_hit'] else '否',
                    '命中率': round(result['hit_rate'], 4),
                    '预测数字和': result['pred_sum'],
                    '实际数字和': result['actual_sum'],
                    '预测方法': result['method'],
                    '预测得分': str(result['prediction_scores']),
                    '训练集': '2024年全年',
                    '验证方式': '严格时间分割',
                    '数据泄露': '无',
                    '生成时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
            
            # 保存CSV文件
            csv_file = f"严格验证动态权重预测2025年1-204期_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df = pd.DataFrame(csv_data)
            df.to_csv(csv_file, index=False, encoding='utf-8')
            
            print(f"✅ 严格验证CSV文件已保存: {csv_file}")
            return csv_file
            
        except Exception as e:
            print(f"❌ CSV保存失败: {e}")
            return None
    
    def run_strict_validation_system(self):
        """运行严格验证系统"""
        print("🚀 严格验证动态权重系统")
        print("训练集：2024年全年 | 测试集：2025年1-204期")
        print("严格避免数据泄露和过拟合问题")
        print("=" * 80)
        
        print("🔧 严格验证特性:")
        print("   ✅ 时间严格分割：2024年训练，2025年测试")
        print("   ✅ 无数据泄露：测试时只使用历史数据")
        print("   ✅ 在线学习：逐期更新权重模拟真实环境")
        print("   ✅ 过拟合控制：正则化和权重平滑")
        print("   ✅ 动态权重：每5期更新一次权重")
        
        # 1. 严格数据分割
        if not self.load_and_split_data_strict():
            return False
        
        # 2. 初始化基础权重
        if not self.initialize_base_weights():
            return False
        
        # 3. 严格预测2025年1-204期
        predictions = self.predict_2025_periods_strict()
        
        if not predictions:
            print("❌ 预测失败")
            return False
        
        # 4. 性能分析
        performance = self.analyze_strict_performance(predictions)
        
        # 5. 保存CSV文件
        csv_file = self.save_strict_results_to_csv(predictions)
        
        # 6. 最终评估
        if performance:
            hit_rate = performance['hit_rate']
            diversity_rate = performance['diversity_rate']
            weight_updates = performance['weight_updates']
            
            print(f"\n🏆 严格验证最终评估:")
            print(f"   预测期数: 2025年1-204期 ({performance['total_periods']}期)")
            print(f"   命中率: {hit_rate:.1%}")
            print(f"   多样性率: {diversity_rate:.1%}")
            print(f"   权重更新: {weight_updates}次")
            
            # 系统评级
            if hit_rate > 0.25:
                system_grade = "A级 - 优秀"
            elif hit_rate > 0.20:
                system_grade = "B级 - 良好"
            elif hit_rate > 0.15:
                system_grade = "C级 - 可接受"
            else:
                system_grade = "D级 - 需改进"
            
            print(f"   系统评级: {system_grade}")
            
            # 验证严格性评估
            if hit_rate > 0.15 and diversity_rate > 0.3:
                validation_quality = "严格验证通过"
            elif hit_rate > 0.12:
                validation_quality = "基本验证通过"
            else:
                validation_quality = "验证需要改进"
            
            print(f"   验证质量: {validation_quality}")
            
            if csv_file:
                print(f"   详细结果: {csv_file}")
            
            return hit_rate > 0.12
        
        return False

def main():
    """主函数"""
    print("🔬 严格验证动态权重系统")
    print("训练集：2024年 | 测试集：2025年1-204期")
    print("严格避免数据泄露和过拟合问题")
    print("=" * 80)
    
    # 创建严格验证系统
    system = StrictDynamicWeightSystem()
    
    # 运行严格验证系统
    success = system.run_strict_validation_system()
    
    if success:
        print(f"\n🎉 严格验证动态权重系统运行成功！")
        print(f"已完成2025年1-204期的严格验证预测。")
    else:
        print(f"\n⚠️ 严格验证系统需要进一步优化")

if __name__ == "__main__":
    main()
