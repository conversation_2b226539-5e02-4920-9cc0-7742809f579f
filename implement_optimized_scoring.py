#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实施优化评分系统并更新数据
Implement optimized scoring system and update prediction_data.csv
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

class OptimizedScoringSystem:
    """优化的评分系统 - 避免过拟合和数据泄露"""
    
    def __init__(self):
        self.name = "优化无偏评分系统"
        self.version = "2.0"
        
    def calculate_unbiased_score(self, predicted_numbers, confidence, current_period=None):
        """
        计算无偏评分
        严格避免数据泄露和过拟合
        """
        pred_num1, pred_num2 = predicted_numbers
        
        # 1. 基础评分（降低倍数，更保守）
        base_score = confidence * 600  # 从1000降到600
        
        # 2. 基于数学期望的特征（不依赖历史数据优化）
        num_sum = pred_num1 + pred_num2
        num_diff = abs(pred_num1 - pred_num2)
        
        # 更保守的调整系数
        if 20 <= num_sum <= 80:  # 更宽泛的范围
            base_score *= 1.08  # 从1.2降到1.08
            
        if 5 <= num_diff <= 40:  # 更宽泛的范围
            base_score *= 1.05  # 从1.1降到1.05
        
        # 3. 基于数学概率的小数字特征（不是历史优化）
        # 1-10的数字在数学上出现概率相对均匀
        small_numbers = list(range(1, 11))
        if pred_num1 in small_numbers or pred_num2 in small_numbers:
            base_score *= 1.03  # 从1.3降到1.03，避免过度加成
        
        # 4. 限制分数范围，更加现实
        final_score = max(15, min(65, base_score))  # 从10-100改为15-65
        
        # 5. 添加基于期号的可重复随机性
        if current_period:
            np.random.seed(int(current_period) % 1000)
            noise = np.random.normal(0, 1.0)  # 减小噪声
            final_score = max(12, min(70, final_score + noise))
        
        return final_score
    
    def get_conservative_grade(self, score):
        """获取保守的评分等级"""
        if score >= 55:
            return "A (较高概率)", "重点关注"
        elif score >= 45:
            return "B+ (中高概率)", "值得关注"
        elif score >= 35:
            return "B (中等概率)", "可以考虑"
        elif score >= 25:
            return "C (较低概率)", "谨慎考虑"
        else:
            return "D (低概率)", "不建议"
    
    def calculate_prediction_score(self, prediction_data):
        """计算预测评分（主接口）"""
        try:
            # 提取数据
            if isinstance(prediction_data.get('predicted_numbers'), list):
                predicted_numbers = prediction_data['predicted_numbers']
            else:
                predicted_numbers = [
                    prediction_data.get('pred_num1', 30),
                    prediction_data.get('pred_num2', 3)
                ]
            
            confidence = prediction_data.get('confidence', 
                        prediction_data.get('original_confidence', 0.025))
            current_period = prediction_data.get('period', 
                           prediction_data.get('current_period', 100))
            
            # 计算无偏评分
            score = self.calculate_unbiased_score(
                predicted_numbers, confidence, current_period
            )
            
            # 获取保守等级
            grade, recommendation = self.get_conservative_grade(score)
            
            return {
                'score': round(score, 1),
                'grade': grade,
                'recommendation': recommendation,
                'probability': min(0.65, score / 100),  # 限制最高概率为65%
                'system_version': self.version
            }
            
        except Exception as e:
            # 失败时返回保守默认值
            return {
                'score': 20.0,
                'grade': "D (低概率)",
                'recommendation': "不建议",
                'probability': 0.20,
                'system_version': self.version,
                'error': str(e)
            }

def backup_current_data():
    """备份当前数据"""
    print("📁 备份当前数据")
    print("="*30)
    
    try:
        # 创建备份
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f'prediction_data_backup_before_optimization_{timestamp}.csv'
        
        if os.path.exists('prediction_data.csv'):
            df = pd.read_csv('prediction_data.csv', encoding='utf-8')
            df.to_csv(backup_filename, index=False, encoding='utf-8')
            print(f"✅ 数据已备份: {backup_filename}")
            print(f"   备份记录数: {len(df)}")
            return True
        else:
            print("❌ 原数据文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

def update_prediction_scores():
    """使用优化评分系统更新所有预测评分"""
    print(f"\n🔄 更新预测评分")
    print("="*30)
    
    try:
        # 加载数据
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        print(f"加载数据: {len(df)}条记录")
        
        # 创建优化评分系统
        scoring_system = OptimizedScoringSystem()
        
        # 统计更新情况
        updated_count = 0
        error_count = 0
        
        # 逐行更新评分
        for idx, row in df.iterrows():
            try:
                # 检查是否有预测数据
                if pd.notna(row['预测数字1']) and pd.notna(row['预测数字2']):
                    
                    # 准备评分数据
                    prediction_data = {
                        'predicted_numbers': [
                            int(float(row['预测数字1'])),
                            int(float(row['预测数字2']))
                        ],
                        'confidence': float(row['预测置信度']),
                        'current_period': int(row['当期期号'])
                    }
                    
                    # 计算新评分
                    score_result = scoring_system.calculate_prediction_score(prediction_data)
                    
                    # 更新数据
                    df.loc[idx, '预测评分'] = score_result['score']
                    df.loc[idx, '评分等级'] = score_result['grade']
                    df.loc[idx, '评分建议'] = score_result['recommendation']
                    df.loc[idx, '评分概率'] = f"{score_result['probability']:.3f}"
                    
                    # 更新备注，标记为优化后评分
                    current_note = str(row['备注']) if pd.notna(row['备注']) else ""
                    if "优化评分v2.0" not in current_note:
                        new_note = f"{current_note},优化评分v2.0" if current_note else "优化评分v2.0"
                        df.loc[idx, '备注'] = new_note
                    
                    updated_count += 1
                    
            except (ValueError, TypeError) as e:
                error_count += 1
                continue
        
        print(f"更新统计:")
        print(f"   成功更新: {updated_count}条")
        print(f"   更新失败: {error_count}条")
        
        return df, updated_count
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        import traceback
        traceback.print_exc()
        return None, 0

def analyze_score_changes(original_df, updated_df):
    """分析评分变化"""
    print(f"\n📊 分析评分变化")
    print("="*30)
    
    try:
        # 过滤有效数据
        valid_data = updated_df[
            updated_df['预测评分'].notna() & 
            (updated_df['预测评分'] != '')
        ].copy()
        
        # 转换评分为数值
        valid_data['评分数值'] = pd.to_numeric(valid_data['预测评分'], errors='coerce')
        
        print(f"有效评分记录: {len(valid_data)}")
        
        # 评分分布统计
        print(f"\n新评分分布:")
        print(f"   平均分: {valid_data['评分数值'].mean():.1f}")
        print(f"   中位数: {valid_data['评分数值'].median():.1f}")
        print(f"   分数范围: {valid_data['评分数值'].min():.1f} - {valid_data['评分数值'].max():.1f}")
        print(f"   标准差: {valid_data['评分数值'].std():.1f}")
        
        # 等级分布
        grade_dist = valid_data['评分等级'].value_counts()
        print(f"\n等级分布:")
        for grade, count in grade_dist.items():
            percentage = (count / len(valid_data)) * 100
            print(f"   {grade}: {count}个 ({percentage:.1f}%)")
        
        # 分析命中率与新评分的关系
        hit_data = valid_data[valid_data['是否命中'].notna() & (valid_data['是否命中'] != '')]
        
        if len(hit_data) > 0:
            print(f"\n新评分与命中率关系:")
            
            # 按新评分分组
            score_ranges = [
                (50, 70, "高分(50-70)"),
                (35, 50, "中分(35-50)"),
                (0, 35, "低分(<35)")
            ]
            
            for min_score, max_score, range_name in score_ranges:
                range_data = hit_data[
                    (hit_data['评分数值'] >= min_score) & 
                    (hit_data['评分数值'] < max_score)
                ]
                
                if len(range_data) > 0:
                    hit_count = len(range_data[range_data['是否命中'] == '是'])
                    hit_rate = (hit_count / len(range_data)) * 100
                    print(f"   {range_name}: {hit_rate:.1f}% ({hit_count}/{len(range_data)})")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def save_updated_data(updated_df):
    """保存更新后的数据"""
    print(f"\n💾 保存更新后的数据")
    print("="*30)
    
    try:
        # 保存到原文件
        updated_df.to_csv('prediction_data.csv', index=False, encoding='utf-8')
        print(f"✅ 数据已保存到 prediction_data.csv")
        print(f"   总记录数: {len(updated_df)}")
        
        # 创建优化后的副本
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        optimized_filename = f'prediction_data_optimized_{timestamp}.csv'
        updated_df.to_csv(optimized_filename, index=False, encoding='utf-8')
        print(f"✅ 优化版本已保存: {optimized_filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return False

def validate_optimization():
    """验证优化效果"""
    print(f"\n✅ 验证优化效果")
    print("="*30)
    
    try:
        df = pd.read_csv('prediction_data.csv', encoding='utf-8')
        
        # 检查优化标记
        optimized_records = df[df['备注'].str.contains('优化评分v2.0', na=False)]
        print(f"优化记录数: {len(optimized_records)}")
        
        # 检查评分范围
        valid_scores = pd.to_numeric(df['预测评分'], errors='coerce').dropna()
        print(f"评分范围: {valid_scores.min():.1f} - {valid_scores.max():.1f}")
        
        # 检查是否还有极端高分
        high_scores = valid_scores[valid_scores > 80]
        print(f"高分(>80)记录: {len(high_scores)}个")
        
        if len(high_scores) == 0:
            print("✅ 成功消除极端高分")
        
        # 检查评分分布合理性
        if valid_scores.std() < 10:  # 标准差小于10说明分布更合理
            print("✅ 评分分布更加合理")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def generate_optimization_report():
    """生成优化报告"""
    print(f"\n📋 优化报告")
    print("="*50)
    
    print("🎯 优化目标:")
    print("   1. 消除过拟合和数据泄露风险")
    print("   2. 提供更保守和现实的评分")
    print("   3. 建立更可靠的预测评估体系")
    
    print(f"\n🔧 优化措施:")
    print("   1. 降低基础评分倍数: 1000 → 600")
    print("   2. 减少加成系数: 最高1.716倍 → 1.166倍")
    print("   3. 限制评分范围: 10-100 → 15-65")
    print("   4. 简化特征工程，避免历史数据依赖")
    print("   5. 添加适量随机性，减少过度自信")
    
    print(f"\n📊 预期效果:")
    print("   1. 评分更加保守和现实")
    print("   2. 消除90+的极端高分")
    print("   3. 提高评分系统的泛化能力")
    print("   4. 降低用户的不合理期望")
    
    print(f"\n✅ 实施状态:")
    print("   1. ✅ 新评分系统已创建")
    print("   2. ✅ 历史数据已重新评分")
    print("   3. ✅ 数据文件已更新")
    print("   4. ✅ 备份文件已创建")

def main():
    """主函数"""
    print("🚀 实施优化评分系统")
    print("="*60)
    
    # 1. 备份当前数据
    if not backup_current_data():
        print("❌ 备份失败，停止执行")
        return
    
    # 2. 更新预测评分
    updated_df, update_count = update_prediction_scores()
    if updated_df is None:
        print("❌ 更新失败，停止执行")
        return
    
    # 3. 分析评分变化
    analyze_score_changes(None, updated_df)
    
    # 4. 保存更新后的数据
    if not save_updated_data(updated_df):
        print("❌ 保存失败")
        return
    
    # 5. 验证优化效果
    validate_optimization()
    
    # 6. 生成优化报告
    generate_optimization_report()
    
    print(f"\n🎉 优化完成!")
    print(f"✅ 成功更新 {update_count} 条预测记录")
    print(f"✅ 评分系统已优化，避免过拟合风险")
    print(f"✅ prediction_data.csv 已更新")
    print(f"📁 备份文件已创建，可随时恢复")

if __name__ == "__main__":
    main()
