#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Model Trainer for Lottery Prediction System
Implements advanced ML algorithms with proper validation
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import joblib
import os
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, ElasticNet
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.feature_selection import SelectKBest, mutual_info_regression
import warnings
warnings.filterwarnings('ignore')

# Try to import advanced algorithms
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False


class LotteryModelTrainer:
    """
    Advanced model trainer for lottery prediction
    Focuses on 6 main numbers with proper validation
    """
    
    def __init__(self, models_dir: str = 'models'):
        self.models_dir = models_dir
        self.models = {}
        self.scalers = {}
        self.training_results = {}
        
        # Create models directory
        os.makedirs(models_dir, exist_ok=True)
        
        # Initialize available models
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize available ML models with anti-overfitting parameters"""
        self.available_models = {
            'RandomForest': RandomForestRegressor(
                n_estimators=100,  # Reduced from 200
                max_depth=8,       # Reduced from 10
                min_samples_split=10,  # Increased from 5
                min_samples_leaf=5,    # Increased from 2
                max_features='sqrt',   # Added feature sampling
                random_state=42,
                n_jobs=-1
            ),
            'GradientBoosting': GradientBoostingRegressor(
                n_estimators=100,  # Reduced from 200
                max_depth=4,       # Reduced from 6
                learning_rate=0.05, # Reduced from 0.1
                subsample=0.8,     # Added subsampling
                random_state=42
            ),
            'Ridge': Ridge(alpha=10.0, random_state=42),  # Increased regularization
            'ElasticNet': ElasticNet(alpha=1.0, l1_ratio=0.7, random_state=42, max_iter=2000)
        }

        # Add XGBoost if available with regularization
        if XGBOOST_AVAILABLE:
            self.available_models['XGBoost'] = xgb.XGBRegressor(
                n_estimators=100,
                max_depth=4,
                learning_rate=0.05,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=1.0,
                random_state=42,
                n_jobs=-1
            )

        # Add LightGBM if available with regularization
        if LIGHTGBM_AVAILABLE:
            self.available_models['LightGBM'] = lgb.LGBMRegressor(
                n_estimators=100,
                max_depth=4,
                learning_rate=0.05,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=1.0,
                random_state=42,
                n_jobs=-1,
                verbose=-1
            )
    
    def select_features_for_target(self, X_train: pd.DataFrame, y_train: pd.Series,
                                  max_features: int = 15) -> List[str]:
        """
        Select the most important features for a specific target using mutual information
        """
        if len(X_train.columns) <= max_features:
            return X_train.columns.tolist()

        try:
            # Use mutual information for feature selection
            selector = SelectKBest(score_func=mutual_info_regression, k=max_features)
            selector.fit(X_train, y_train)
            selected_features = X_train.columns[selector.get_support()].tolist()
            return selected_features
        except:
            # Fallback to correlation-based selection
            correlations = X_train.corrwith(y_train).abs().sort_values(ascending=False)
            return correlations.head(max_features).index.tolist()

    def prepare_data(self, train_data: pd.DataFrame, test_data: pd.DataFrame,
                    feature_columns: List[str], target_col: str,
                    use_feature_selection: bool = True) -> Tuple:
        """
        Prepare training and testing data for a specific target with feature selection
        """
        # Extract features and target
        X_train = train_data[feature_columns].fillna(0)
        y_train = train_data[target_col]
        X_test = test_data[feature_columns].fillna(0)
        y_test = test_data[target_col]

        # Apply feature selection if requested
        selected_features = feature_columns
        if use_feature_selection and len(feature_columns) > 15:
            selected_features = self.select_features_for_target(X_train, y_train, max_features=15)
            X_train = X_train[selected_features]
            X_test = X_test[selected_features]
            print(f"  特征选择: {len(feature_columns)} -> {len(selected_features)}")

        return X_train, X_test, y_train, y_test, selected_features
    
    def train_single_model(self, model_name: str, X_train: pd.DataFrame,
                          y_train: pd.Series, target_col: str,
                          selected_features: List[str] = None,
                          use_cv: bool = True) -> Dict:
        """
        Train a single model for a specific target column with cross-validation
        """
        print(f"训练 {model_name} 模型 - {target_col}")

        model = self.available_models[model_name]

        # Convert DataFrame to numpy array for tree-based models
        if model_name in ['RandomForest', 'GradientBoosting', 'XGBoost', 'LightGBM']:
            X_train_final = X_train.values
        elif model_name in ['Ridge', 'ElasticNet']:
            # Scale data for linear models
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            self.scalers[f"{model_name}_{target_col}"] = scaler
            X_train_final = X_train_scaled
        else:
            X_train_final = X_train.values

        # Cross-validation for model evaluation
        cv_scores = []
        if use_cv:
            tscv = TimeSeriesSplit(n_splits=3)

            for train_idx, val_idx in tscv.split(X_train_final):
                X_tr, X_val = X_train_final[train_idx], X_train_final[val_idx]
                y_tr, y_val = y_train.iloc[train_idx], y_train.iloc[val_idx]

                # Clone and train model
                model_cv = model.__class__(**model.get_params())
                model_cv.fit(X_tr, y_tr)

                # Predict and evaluate
                y_pred = model_cv.predict(X_val)
                mse = mean_squared_error(y_val, y_pred)
                cv_scores.append(mse)

            cv_mean = np.mean(cv_scores)
            cv_std = np.std(cv_scores)
            print(f"  CV MSE: {cv_mean:.4f} ± {cv_std:.4f}")

        # Train final model on full training data
        model.fit(X_train_final, y_train)
        
        # Store model
        model_key = f"{model_name}_{target_col}"
        self.models[model_key] = model
        
        # Save model to disk
        model_path = os.path.join(self.models_dir, f"{model_key}.joblib")
        joblib.dump(model, model_path)
        
        # Save scaler if used
        if model_name in ['Ridge', 'ElasticNet']:
            scaler_path = os.path.join(self.models_dir, f"scaler_{model_key}.joblib")
            joblib.dump(self.scalers[model_key], scaler_path)

        # Save feature information
        if selected_features:
            feature_path = os.path.join(self.models_dir, f"{model_key}_features.txt")
            with open(feature_path, 'w', encoding='utf-8') as f:
                for feature in selected_features:
                    f.write(f"{feature}\n")
        
        training_result = {
            'model': model,
            'model_path': model_path,
            'cv_scores': cv_scores if use_cv else None,
            'cv_mean': cv_mean if use_cv else None,
            'cv_std': cv_std if use_cv else None
        }
        
        return training_result
    
    def train_all_models(self, train_data: pd.DataFrame, test_data: pd.DataFrame,
                        feature_columns: List[str], target_columns: List[str],
                        selected_models = None) -> Dict:
        """
        Train all models for all target columns
        """
        print("=== 开始模型训练 ===")
        
        if selected_models is None:
            selected_models = list(self.available_models.keys())
        
        all_results = {}
        
        for target_col in target_columns:
            print(f"\n训练目标: {target_col}")
            print("-" * 40)
            
            # Prepare data for this target
            X_train, X_test, y_train, y_test, selected_features = self.prepare_data(
                train_data, test_data, feature_columns, target_col
            )
            
            target_results = {}
            
            for model_name in selected_models:
                if model_name in self.available_models:
                    try:
                        result = self.train_single_model(
                            model_name, X_train, y_train, target_col, selected_features
                        )
                        target_results[model_name] = result
                    except Exception as e:
                        print(f"  错误训练 {model_name}: {e}")
                        continue
            
            all_results[target_col] = target_results
        
        self.training_results = all_results
        print("\n=== 模型训练完成 ===")
        
        return all_results
    
    def optimize_hyperparameters(self, train_data: pd.DataFrame, 
                                feature_columns: List[str], target_col: str,
                                model_name: str) -> Dict:
        """
        Optimize hyperparameters for a specific model and target
        """
        print(f"优化超参数: {model_name} - {target_col}")
        
        X_train = train_data[feature_columns].fillna(0)
        y_train = train_data[target_col]
        
        # Define parameter grids
        param_grids = {
            'RandomForest': {
                'n_estimators': [100, 200, 300],
                'max_depth': [8, 10, 12],
                'min_samples_split': [2, 5, 10]
            },
            'XGBoost': {
                'n_estimators': [100, 200],
                'max_depth': [4, 6, 8],
                'learning_rate': [0.05, 0.1, 0.15]
            } if XGBOOST_AVAILABLE else {},
            'Ridge': {
                'alpha': [0.1, 1.0, 10.0, 100.0]
            }
        }
        
        if model_name not in param_grids:
            print(f"  没有为 {model_name} 定义参数网格")
            return {}
        
        # Prepare data
        if model_name in ['Ridge', 'ElasticNet']:
            scaler = StandardScaler()
            X_train = scaler.fit_transform(X_train)
        
        # Grid search with time series cross-validation
        tscv = TimeSeriesSplit(n_splits=3)
        model = self.available_models[model_name]
        
        grid_search = GridSearchCV(
            model, param_grids[model_name],
            cv=tscv, scoring='neg_mean_squared_error',
            n_jobs=-1, verbose=0
        )
        
        grid_search.fit(X_train, y_train)
        
        print(f"  最佳参数: {grid_search.best_params_}")
        print(f"  最佳分数: {-grid_search.best_score_:.4f}")
        
        # Update model with best parameters
        self.available_models[model_name] = grid_search.best_estimator_
        
        return {
            'best_params': grid_search.best_params_,
            'best_score': -grid_search.best_score_,
            'best_model': grid_search.best_estimator_
        }
    
    def load_models(self) -> Dict:
        """
        Load previously trained models from disk
        """
        loaded_models = {}
        
        if not os.path.exists(self.models_dir):
            print("模型目录不存在")
            return loaded_models
        
        model_files = [f for f in os.listdir(self.models_dir) if f.endswith('.joblib') and not f.startswith('scaler_')]
        
        for model_file in model_files:
            model_path = os.path.join(self.models_dir, model_file)
            model_key = model_file.replace('.joblib', '')
            
            try:
                model = joblib.load(model_path)
                loaded_models[model_key] = model
                
                # Load corresponding scaler if exists
                scaler_file = f"scaler_{model_file}"
                scaler_path = os.path.join(self.models_dir, scaler_file)
                if os.path.exists(scaler_path):
                    scaler = joblib.load(scaler_path)
                    self.scalers[model_key] = scaler
                
            except Exception as e:
                print(f"加载模型失败 {model_key}: {e}")
        
        self.models = loaded_models
        print(f"成功加载 {len(loaded_models)} 个模型")
        
        return loaded_models
    
    def get_model_summary(self) -> pd.DataFrame:
        """
        Get summary of training results
        """
        if not self.training_results:
            return pd.DataFrame()
        
        summary_data = []
        
        for target_col, models in self.training_results.items():
            for model_name, result in models.items():
                summary_data.append({
                    'Target': target_col,
                    'Model': model_name,
                    'CV_Mean': result.get('cv_mean', 0),
                    'CV_Std': result.get('cv_std', 0),
                    'Model_Path': result.get('model_path', '')
                })
        
        return pd.DataFrame(summary_data)


if __name__ == "__main__":
    # Test the model trainer
    from data_processor import LotteryDataProcessor
    
    # Load data
    processor = LotteryDataProcessor()
    data = processor.get_processed_data()
    
    # Initialize trainer
    trainer = LotteryModelTrainer()
    
    # Train models
    results = trainer.train_all_models(
        data['train_data'], data['test_data'],
        data['feature_columns'], data['target_columns']
    )
    
    # Print summary
    summary = trainer.get_model_summary()
    print("\n=== 训练结果摘要 ===")
    print(summary)
