{"audit_metadata": {"audit_time": "2025-07-13T04:11:45.841919", "workspace_root": ".", "total_files_scanned": 113}, "file_inventory": {"prediction_results": ["2025年180期正确预测.py", "2025年181-200期预测系统.py", "优化后的最佳预测系统.py", "全面预测方式验证实验.py", "平衡优化的预测系统.py", "最终优化预测系统.py", "正确的单期预测系统.py", "生产级马尔可夫预测系统.py", "连续预测的可能性与局限性分析.py", "预测重复问题深度思辨分析.py", "两阶段预测摘要_20250712_213120.txt", "两阶段预测系统.py", "改进两阶段预测摘要_20250712_213319.txt", "改进两阶段预测系统.py", "简化两阶段预测摘要_20250712_213517.txt", "简化两阶段预测系统.py", "动态预测组合风险评估模块.cpython-312.pyc"], "validation_reports": ["生产系统优化与随机基准验证最终报告.md", "训练数据区间调节实验分析报告.md", "训练数据区间调节实验最终报告.md"], "csv_data": ["2025年181-200期预测结果.csv", "2025年严格验证结果.csv", "专家集成系统验证结果.csv", "优化数据策略2025年181-200期预测结果.csv", "修正验证_真正反循环优化结果.csv", "改进方法181-200期预测结果.csv", "最佳实践20%目标2025年181-200期预测结果.csv", "最佳方法181-200期预测结果.csv", "生产优化2025年181-200期预测结果.csv", "生产级马尔可夫2025年181-200期预测结果.csv", "进一步优化2025年181-200期预测结果.csv", "退化修复2025年181-200期预测结果.csv", "验证结果_anti_cycle_optimization.csv", "验证结果_basic_prediction_priority.csv", "验证结果_best_practice_20_percent.csv", "验证结果_degradation_fix.csv", "两阶段预测验证结果_20250712_213120.csv", "改进两阶段预测验证结果_20250712_213319.csv", "简化两阶段预测验证结果_20250712_213517.csv", "lottery_data_2021_2025_integrated.csv", "lottery_data_2023_2025.csv", "lottery_data_clean.csv", "lottery_data_clean_no_special.csv", "lottery_features.csv", "最佳方法2数预测_181-200期_20250712_005754.csv", "最佳马尔可夫预测_1181-1200期_20250712_001632.csv", "best_method_final_predictions_20250711_194852.csv", "best_method_final_predictions_20250711_195351.csv", "best_method_final_predictions_20250712_000259.csv", "best_method_predictions_20250711_033929.csv", "comprehensive_predictions_with_3digits_20250711_183012.csv", "comprehensive_predictions_with_3digits_20250711_183926.csv", "core_predictions_20250711_060627.csv", "enhanced_predictions_20250711_061014.csv", "final_optimal_predictions_20250711_175550.csv", "intelligent_expansion_predictions_20250711_043515.csv", "latest_data_best_predictions_20250711_040024.csv", "optimal_method_predictions_20250711_045909.csv", "scored_predictions_20250711_051608.csv"], "json_reports": ["2025年181-200期预测结果.json", "2025年181-200期预测结果_20250713_034532.json", "ROI商业价值分析结果_20250713_024956.json", "优化数据策略2025年181-200期预测结果.json", "优化预测系统结果_20250713_032629.json", "全面预测验证实验结果_20250713_040649.json", "历史表现回溯结果_20250713_030952.json", "完整状态评估器结果_20250713_031510.json", "平衡优化系统结果_20250713_032748.json", "最佳方法影响分析结果_20250713_032502.json", "最终优化系统结果_20250713_034003.json", "最终优化系统结果_20250713_034013.json", "模式稳定性分析结果_20250713_031139.json", "正确单期预测结果_20250713_035244.json", "波动性指标分析结果_20250713_031320.json", "滚动窗口交叉验证结果_20250713_024641.json", "理论基准计算与验证结果_20250713_024433.json", "理论基准计算与验证结果_20250713_024510.json", "生产优化2025年181-200期预测结果.json", "生产级马尔可夫2025年181-200期预测结果.json", "训练数据区间调节实验结果_20250712_214212.json", "训练数据区间调节实验结果_20250712_214342.json", "错误归因分析结果_20250713_024820.json", "随机基准方法对比验证结果_20250712_215947.json", "两阶段预测验证结果_20250712_213026.json", "两阶段预测验证结果_20250712_213049.json"], "markdown_docs": ["1181-1200期最佳方法预测结果.md", "1181-1200期预测快速查看表.md", "1181-1200期预测结果总结.md", "181-200期预测结果总结.md", "2025年180期最终预测报告.md", "2025年181-200期2数预测结果表.md", "2025年181-200期预测分析报告.md", "2025年181-200期预测结果报告.md", "2025年第181-200期预测结果表.md", "2数预测优化最终报告.md", "全面预测方式验证实验报告.md", "历史预测文件深度思辨分析报告.md", "改进方法181-200期预测结果.md", "最佳方法181-200期预测结果.md", "特码验证与两阶段预测分析完整报告.md", "预测数字范围差异_分析报告.md"], "python_scripts": ["最佳方法影响分析与优化建议.py", "模式稳定性分析器.py", "滚动窗口交叉验证系统.py", "理论基准精确计算与验证系统.py", "系统性文件审查与一致性分析.py", "错误归因分析系统.py", "随机基准方法对比验证系统.py", "精英特征工程方法深度分析.py", "训练数据区间调节可视化分析.py", "训练数据区间调节实验系统.py", "重复数值机制深度思辨分析.py", "重复机制融合马尔可夫基准_可行性分析.py"]}, "performance_analysis": {"total_performance_records": 43, "files_with_performance_data": 31}, "baseline_analysis": {"baseline_sources_found": 16}, "data_source_analysis": {"csv_files_analyzed": 39, "data_sources": {".\\2025年181-200期预测结果.csv": {"file": "2025年181-200期预测结果.csv", "file_path": ".\\2025年181-200期预测结果.csv", "rows": 20, "columns": ["年份", "期号", "预测数字1", "预测数字2", "置信度", "基准数字"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\2025年严格验证结果.csv": {"file": "2025年严格验证结果.csv", "file_path": ".\\2025年严格验证结果.csv", "rows": 178, "columns": ["期号", "预测数字1", "预测数字2", "实际数字", "命中数", "命中率", "置信度", "预测方法"], "date_range": null, "data_type": "prediction_results"}, ".\\专家集成系统验证结果.csv": {"file": "专家集成系统验证结果.csv", "file_path": ".\\专家集成系统验证结果.csv", "rows": 179, "columns": ["期号", "集成预测1", "集成预测2", "实际开奖", "命中数", "命中率", "置信度", "决策方法", "专家预测", "专家权重", "可用数据量"], "date_range": null, "data_type": "prediction_results"}, ".\\优化数据策略2025年181-200期预测结果.csv": {"file": "优化数据策略2025年181-200期预测结果.csv", "file_path": ".\\优化数据策略2025年181-200期预测结果.csv", "rows": 20, "columns": ["period", "predicted_number_1", "predicted_number_2", "confidence", "method", "timestamp"], "date_range": null, "data_type": "prediction_results"}, ".\\修正验证_真正反循环优化结果.csv": {"file": "修正验证_真正反循环优化结果.csv", "file_path": ".\\修正验证_真正反循环优化结果.csv", "rows": 179, "columns": ["期号", "预测数字1", "预测数字2", "实际开奖", "命中数", "命中率", "置信度", "方法描述", "可用数据量"], "date_range": null, "data_type": "prediction_results"}, ".\\改进方法181-200期预测结果.csv": {"file": "改进方法181-200期预测结果.csv", "file_path": ".\\改进方法181-200期预测结果.csv", "rows": 20, "columns": ["期号", "年份", "使用方法", "预测2数字_1", "预测2数字_2", "预测3数字_1", "预测3数字_2", "预测3数字_3", "预测4数字_1", "预测4数字_2", "预测4数字_3", "预测4数字_4"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\最佳实践20%目标2025年181-200期预测结果.csv": {"file": "最佳实践20%目标2025年181-200期预测结果.csv", "file_path": ".\\最佳实践20%目标2025年181-200期预测结果.csv", "rows": 20, "columns": ["年份", "期号", "预测数字1", "预测数字2", "置信度", "预测方法"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\最佳方法181-200期预测结果.csv": {"file": "最佳方法181-200期预测结果.csv", "file_path": ".\\最佳方法181-200期预测结果.csv", "rows": 20, "columns": ["期号", "年份", "预测2数字_1", "预测2数字_2", "预测3数字_1", "预测3数字_2", "预测3数字_3", "预测4数字_1", "预测4数字_2", "预测4数字_3", "预测4数字_4"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\生产优化2025年181-200期预测结果.csv": {"file": "生产优化2025年181-200期预测结果.csv", "file_path": ".\\生产优化2025年181-200期预测结果.csv", "rows": 20, "columns": ["年份", "期号", "预测数字1", "预测数字2", "基础预测1", "基础预测2", "最终置信度", "基础置信度", "质量评分", "策略索引", "策略配置", "应用增强", "增强数量"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\生产级马尔可夫2025年181-200期预测结果.csv": {"file": "生产级马尔可夫2025年181-200期预测结果.csv", "file_path": ".\\生产级马尔可夫2025年181-200期预测结果.csv", "rows": 20, "columns": ["period", "predicted_number_1", "predicted_number_2", "confidence", "method", "timestamp"], "date_range": null, "data_type": "prediction_results"}, ".\\进一步优化2025年181-200期预测结果.csv": {"file": "进一步优化2025年181-200期预测结果.csv", "file_path": ".\\进一步优化2025年181-200期预测结果.csv", "rows": 20, "columns": ["年份", "期号", "预测数字1", "预测数字2", "基础预测1", "基础预测2", "最终置信度", "基础置信度", "策略索引", "策略类型", "策略配置", "平衡评分", "应用增强", "增强数量"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\退化修复2025年181-200期预测结果.csv": {"file": "退化修复2025年181-200期预测结果.csv", "file_path": ".\\退化修复2025年181-200期预测结果.csv", "rows": 20, "columns": ["年份", "期号", "预测数字1", "预测数字2", "置信度", "预测方法", "数据质量"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\验证结果_anti_cycle_optimization.csv": {"file": "验证结果_anti_cycle_optimization.csv", "file_path": ".\\验证结果_anti_cycle_optimization.csv", "rows": 179, "columns": ["期号", "预测数字1", "预测数字2", "实际开奖", "命中数", "命中率", "置信度", "方法描述", "可用数据量"], "date_range": null, "data_type": "prediction_results"}, ".\\验证结果_basic_prediction_priority.csv": {"file": "验证结果_basic_prediction_priority.csv", "file_path": ".\\验证结果_basic_prediction_priority.csv", "rows": 179, "columns": ["期号", "预测数字1", "预测数字2", "实际开奖", "命中数", "命中率", "置信度", "方法描述", "可用数据量"], "date_range": null, "data_type": "prediction_results"}, ".\\验证结果_best_practice_20_percent.csv": {"file": "验证结果_best_practice_20_percent.csv", "file_path": ".\\验证结果_best_practice_20_percent.csv", "rows": 179, "columns": ["期号", "预测数字1", "预测数字2", "实际开奖", "命中数", "命中率", "置信度", "方法描述", "可用数据量"], "date_range": null, "data_type": "prediction_results"}, ".\\验证结果_degradation_fix.csv": {"file": "验证结果_degradation_fix.csv", "file_path": ".\\验证结果_degradation_fix.csv", "rows": 179, "columns": ["期号", "预测数字1", "预测数字2", "实际开奖", "命中数", "命中率", "置信度", "方法描述", "可用数据量"], "date_range": null, "data_type": "prediction_results"}, ".\\backup_removed_files_20250712_215735\\两阶段预测验证结果_20250712_213120.csv": {"file": "两阶段预测验证结果_20250712_213120.csv", "file_path": ".\\backup_removed_files_20250712_215735\\两阶段预测验证结果_20250712_213120.csv", "rows": 178, "columns": ["period", "predicted_2", "candidates_12", "actual", "stage1_hits", "final_hits", "success", "overall_confidence"], "date_range": null, "data_type": "prediction_results"}, ".\\backup_removed_files_20250712_215735\\改进两阶段预测验证结果_20250712_213319.csv": {"file": "改进两阶段预测验证结果_20250712_213319.csv", "file_path": ".\\backup_removed_files_20250712_215735\\改进两阶段预测验证结果_20250712_213319.csv", "rows": 178, "columns": ["period", "predicted_2", "candidates_12", "actual", "stage1_hits", "final_hits", "success", "overall_confidence"], "date_range": null, "data_type": "prediction_results"}, ".\\backup_removed_files_20250712_215735\\简化两阶段预测验证结果_20250712_213517.csv": {"file": "简化两阶段预测验证结果_20250712_213517.csv", "file_path": ".\\backup_removed_files_20250712_215735\\简化两阶段预测验证结果_20250712_213517.csv", "rows": 178, "columns": ["period", "predicted_2", "direct_2", "candidates_12", "actual", "stage1_hits", "final_hits", "direct_hits", "success", "direct_success", "overall_confidence"], "date_range": null, "data_type": "prediction_results"}, ".\\data\\processed\\lottery_data_2021_2025_integrated.csv": {"file": "lottery_data_2021_2025_integrated.csv", "file_path": ".\\data\\processed\\lottery_data_2021_2025_integrated.csv", "rows": 1638, "columns": ["年份", "期号", "数字1", "数字2", "数字3", "数字4", "数字5", "数字6", "特码", "原始行"], "date_range": "2021年1期 - 2025年179期", "data_type": "lottery_data"}, ".\\data\\processed\\lottery_data_2023_2025.csv": {"file": "lottery_data_2023_2025.csv", "file_path": ".\\data\\processed\\lottery_data_2023_2025.csv", "rows": 909, "columns": ["年份", "期号", "数字1", "数字2", "数字3", "数字4", "数字5", "数字6", "特码", "原始行"], "date_range": "2023年1期 - 2025年179期", "data_type": "lottery_data"}, ".\\data\\processed\\lottery_data_clean.csv": {"file": "lottery_data_clean.csv", "file_path": ".\\data\\processed\\lottery_data_clean.csv", "rows": 180, "columns": ["期号", "数字1", "数字2", "数字3", "数字4", "数字5", "数字6", "特码"], "date_range": null, "data_type": "unknown"}, ".\\data\\processed\\lottery_data_clean_no_special.csv": {"file": "lottery_data_clean_no_special.csv", "file_path": ".\\data\\processed\\lottery_data_clean_no_special.csv", "rows": 1638, "columns": ["年份", "期号", "数字1", "数字2", "数字3", "数字4", "数字5", "数字6"], "date_range": "2021年1期 - 2025年179期", "data_type": "lottery_data"}, ".\\data\\processed\\lottery_features.csv": {"file": "lottery_features.csv", "file_path": ".\\data\\processed\\lottery_features.csv", "rows": 174, "columns": ["期号", "数字1", "数字2", "数字3", "数字4", "数字5", "数字6", "特码", "和值", "平均值", "极差", "方差", "标准差", "奇数个数", "偶数个数", "大数个数", "小数个数", "和值_mean_3", "和值_std_3", "和值_mean_5", "和值_std_5", "和值_mean_7", "和值_std_7", "奇数个数_mean_3", "大数个数_mean_3", "极差_mean_3", "奇数个数_mean_5", "大数个数_mean_5", "极差_mean_5", "和值_trend_3", "奇数个数_trend_3", "数字1_mode_5", "数字2_mode_5", "数字3_mode_5", "数字4_mode_5", "数字5_mode_5", "数字6_mode_5", "数字1_lag_1", "数字2_lag_1", "数字3_lag_1", "数字4_lag_1", "数字5_lag_1", "数字6_lag_1", "数字1_lag_2", "数字2_lag_2", "数字3_lag_2", "数字4_lag_2", "数字5_lag_2", "数字6_lag_2", "和值_lag_1", "奇数个数_lag_1", "大数个数_lag_1", "和值_lag_2", "奇数个数_lag_2", "大数个数_lag_2", "数字1_diff_1", "数字2_diff_1", "数字3_diff_1", "数字4_diff_1", "数字5_diff_1", "数字6_diff_1"], "date_range": null, "data_type": "unknown"}, ".\\predictions\\最佳方法2数预测_181-200期_20250712_005754.csv": {"file": "最佳方法2数预测_181-200期_20250712_005754.csv", "file_path": ".\\predictions\\最佳方法2数预测_181-200期_20250712_005754.csv", "rows": 20, "columns": ["年份", "期号", "最佳2数字", "数字1", "数字2", "数字1概率", "数字2概率", "置信度", "预测方法", "验证成功率", "候选数字池", "技术说明"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\predictions\\最佳马尔可夫预测_1181-1200期_20250712_001632.csv": {"file": "最佳马尔可夫预测_1181-1200期_20250712_001632.csv", "file_path": ".\\predictions\\最佳马尔可夫预测_1181-1200期_20250712_001632.csv", "rows": 20, "columns": ["期号", "年份", "预测2数字", "预测3数字", "预测4数字", "使用方法", "数据源"], "date_range": "2025年1181期 - 2025年1200期", "data_type": "prediction_results"}, ".\\results\\best_method_final_predictions_20250711_194852.csv": {"file": "best_method_final_predictions_20250711_194852.csv", "file_path": ".\\results\\best_method_final_predictions_20250711_194852.csv", "rows": 20, "columns": ["年份", "期号", "预测和值", "最佳2数字", "最佳3数字", "最佳4数字", "2数字置信度", "3数字置信度", "2数字方法", "3数字方法", "4数字方法", "2数字验证成功率", "3数字验证成功率", "4数字验证成功率", "验证基础"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\results\\best_method_final_predictions_20250711_195351.csv": {"file": "best_method_final_predictions_20250711_195351.csv", "file_path": ".\\results\\best_method_final_predictions_20250711_195351.csv", "rows": 20, "columns": ["年份", "期号", "预测和值", "最佳2数字", "最佳3数字", "最佳4数字", "2数字置信度", "3数字置信度", "2数字方法", "3数字方法", "4数字方法", "2数字验证成功率", "3数字验证成功率", "4数字验证成功率", "验证基础"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\results\\best_method_final_predictions_20250712_000259.csv": {"file": "best_method_final_predictions_20250712_000259.csv", "file_path": ".\\results\\best_method_final_predictions_20250712_000259.csv", "rows": 20, "columns": ["年份", "期号", "预测和值", "最佳2数字", "最佳3数字", "最佳4数字", "2数字置信度", "3数字置信度", "2数字方法", "3数字方法", "4数字方法", "2数字验证成功率", "3数字验证成功率", "4数字验证成功率", "验证基础"], "date_range": "2025年1181期 - 2025年1200期", "data_type": "prediction_results"}, ".\\results\\best_method_predictions_20250711_033929.csv": {"file": "best_method_predictions_20250711_033929.csv", "file_path": ".\\results\\best_method_predictions_20250711_033929.csv", "rows": 20, "columns": ["期号", "预测和值", "4数字预测", "最佳前2数字", "5反向预测", "置信度", "预测方法"], "date_range": null, "data_type": "prediction_results"}, ".\\results\\comprehensive_predictions_with_3digits_20250711_183012.csv": {"file": "comprehensive_predictions_with_3digits_20250711_183012.csv", "file_path": ".\\results\\comprehensive_predictions_with_3digits_20250711_183012.csv", "rows": 20, "columns": ["年份", "期号", "预测和值", "最佳2数字", "最佳3数字", "最佳4数字", "2数字置信度", "3数字置信度", "2数字方法", "3数字方法", "4数字方法", "2数字验证成功率", "3数字验证成功率", "4数字验证成功率", "验证基础"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\results\\comprehensive_predictions_with_3digits_20250711_183926.csv": {"file": "comprehensive_predictions_with_3digits_20250711_183926.csv", "file_path": ".\\results\\comprehensive_predictions_with_3digits_20250711_183926.csv", "rows": 20, "columns": ["年份", "期号", "预测和值", "最佳2数字", "最佳3数字", "最佳4数字", "2数字置信度", "3数字置信度", "2数字方法", "3数字方法", "4数字方法", "2数字验证成功率", "3数字验证成功率", "4数字验证成功率", "验证基础"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\results\\core_predictions_20250711_060627.csv": {"file": "core_predictions_20250711_060627.csv", "file_path": ".\\results\\core_predictions_20250711_060627.csv", "rows": 20, "columns": ["年份", "期号", "预测和值", "最佳2数字", "最佳4数字", "置信度", "2数字方法", "4数字方法", "验证依据", "2数字验证成功率", "4数字验证成功率"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\results\\enhanced_predictions_20250711_061014.csv": {"file": "enhanced_predictions_20250711_061014.csv", "file_path": ".\\results\\enhanced_predictions_20250711_061014.csv", "rows": 20, "columns": ["年份", "期号", "预测和值", "最佳2数字", "最佳4数字", "置信度", "动态范围", "方法", "增强特性", "预期提升"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\results\\final_optimal_predictions_20250711_175550.csv": {"file": "final_optimal_predictions_20250711_175550.csv", "file_path": ".\\results\\final_optimal_predictions_20250711_175550.csv", "rows": 20, "columns": ["年份", "期号", "预测和值", "最佳2数字", "最佳4数字", "置信度", "2数字方法", "4数字方法", "2数字验证成功率", "4数字验证成功率", "技术基础"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\results\\intelligent_expansion_predictions_20250711_043515.csv": {"file": "intelligent_expansion_predictions_20250711_043515.csv", "file_path": ".\\results\\intelligent_expansion_predictions_20250711_043515.csv", "rows": 20, "columns": ["年份", "期号", "预测和值", "基础4数字", "扩展6数字", "最佳2数字", "最佳4数字", "最佳6数字", "2数字置信度", "预测方法"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\results\\latest_data_best_predictions_20250711_040024.csv": {"file": "latest_data_best_predictions_20250711_040024.csv", "file_path": ".\\results\\latest_data_best_predictions_20250711_040024.csv", "rows": 20, "columns": ["年份", "期号", "预测和值", "4数字预测", "最佳前2数字", "5反向预测", "置信度", "预测方法", "数据来源"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\results\\optimal_method_predictions_20250711_045909.csv": {"file": "optimal_method_predictions_20250711_045909.csv", "file_path": ".\\results\\optimal_method_predictions_20250711_045909.csv", "rows": 20, "columns": ["年份", "期号", "预测和值", "最佳2数字", "最佳4数字", "置信度", "2数字方法", "4数字方法", "验证依据", "2数字验证成功率", "4数字验证成功率"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}, ".\\results\\scored_predictions_20250711_051608.csv": {"file": "scored_predictions_20250711_051608.csv", "file_path": ".\\results\\scored_predictions_20250711_051608.csv", "rows": 20, "columns": ["年份", "期号", "最佳2数字", "最佳4数字", "综合评分", "质量等级", "预估命中概率", "推荐建议", "置信度", "预测和值"], "date_range": "2025年181期 - 2025年200期", "data_type": "prediction_results"}}}, "consistency_issues": [{"type": "inconsistent_baseline", "description": "发现不一致的基线值: {0.292, 0.262, 0.32}", "files": ["优化预测系统结果_20250713_032629.json", "完整状态评估器结果_20250713_031510.json", "平衡优化系统结果_20250713_032748.json", "平衡优化系统结果_20250713_032748.json", "最佳方法影响分析结果_20250713_032502.json", "训练数据区间调节实验结果_20250712_214342.json", "训练数据区间调节实验结果_20250712_214342.json"]}, {"type": "inconsistent_date_ranges", "description": "发现不一致的数据时间范围: {'2025年1181期 - 2025年1200期', '2023年1期 - 2025年179期', '2025年181期 - 2025年200期', '2021年1期 - 2025年179期'}", "files": ["2025年181-200期预测结果.csv", "改进方法181-200期预测结果.csv", "最佳实践20%目标2025年181-200期预测结果.csv", "最佳方法181-200期预测结果.csv", "生产优化2025年181-200期预测结果.csv", "进一步优化2025年181-200期预测结果.csv", "退化修复2025年181-200期预测结果.csv", "lottery_data_2021_2025_integrated.csv", "lottery_data_2023_2025.csv", "lottery_data_clean_no_special.csv", "最佳方法2数预测_181-200期_20250712_005754.csv", "最佳马尔可夫预测_1181-1200期_20250712_001632.csv", "best_method_final_predictions_20250711_194852.csv", "best_method_final_predictions_20250711_195351.csv", "best_method_final_predictions_20250712_000259.csv", "comprehensive_predictions_with_3digits_20250711_183012.csv", "comprehensive_predictions_with_3digits_20250711_183926.csv", "core_predictions_20250711_060627.csv", "enhanced_predictions_20250711_061014.csv", "final_optimal_predictions_20250711_175550.csv", "intelligent_expansion_predictions_20250711_043515.csv", "latest_data_best_predictions_20250711_040024.csv", "optimal_method_predictions_20250711_045909.csv", "scored_predictions_20250711_051608.csv"]}], "recommendations": ["需要解决发现的一致性问题", "建议重新验证有问题的数据源"]}