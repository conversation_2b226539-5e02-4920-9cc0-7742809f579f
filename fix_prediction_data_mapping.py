#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正prediction_data.csv的数据映射问题
Fix data mapping issues in prediction_data.csv

发现的问题：
1. 预测数据中有些期号的实际开奖数据与真实数据不匹配
2. 特别是第187-197期存在数据错位问题
3. 需要重新映射和验证数据一致性
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

def load_and_analyze_data():
    """加载并分析数据"""
    print("📊 加载并分析数据")
    print("="*40)
    
    # 加载真实开奖数据
    lottery_df = pd.read_csv('data/processed/lottery_data_clean_no_special.csv')
    print(f"✅ 真实开奖数据: {len(lottery_df)}期")
    
    # 加载预测数据
    prediction_df = pd.read_csv('prediction_data.csv')
    print(f"✅ 预测数据: {len(prediction_df)}期")
    
    # 筛选2025年的真实数据
    lottery_2025 = lottery_df[lottery_df['年份'] == 2025].copy()
    lottery_2025 = lottery_2025.sort_values('期号').reset_index(drop=True)
    print(f"✅ 2025年真实数据: {len(lottery_2025)}期 (第{lottery_2025['期号'].min()}-{lottery_2025['期号'].max()}期)")
    
    return lottery_df, prediction_df, lottery_2025

def identify_mapping_issues():
    """识别映射问题"""
    print(f"\n🔍 识别映射问题")
    print("="*40)
    
    lottery_df, prediction_df, lottery_2025 = load_and_analyze_data()
    
    issues = []
    correct_mappings = []
    
    for i, pred_row in prediction_df.iterrows():
        # 解析预测期号
        period_str = pred_row['预测期号']  # 格式: "2025年X期"
        
        try:
            period_num = int(period_str.replace('2025年', '').replace('期', ''))
            
            # 在2025年真实数据中查找
            lottery_match = lottery_2025[lottery_2025['期号'] == period_num]
            
            if len(lottery_match) > 0:
                # 获取真实开奖数字
                real_numbers = sorted([lottery_match.iloc[0][f'数字{j}'] for j in range(1, 7)])
                
                # 获取预测数据中的实际数字
                pred_actual_numbers = []
                for j in range(1, 7):
                    val = pred_row[f'实际数字{j}']
                    if pd.notna(val) and val != '':
                        pred_actual_numbers.append(int(float(val)))
                
                pred_actual_numbers_sorted = sorted(pred_actual_numbers)
                
                # 比较数据
                if len(pred_actual_numbers) > 0:
                    if real_numbers == pred_actual_numbers_sorted:
                        correct_mappings.append({
                            'index': i,
                            'period': period_str,
                            'period_num': period_num,
                            'status': '✅ 正确'
                        })
                    else:
                        issues.append({
                            'index': i,
                            'period': period_str,
                            'period_num': period_num,
                            'real_numbers': real_numbers,
                            'pred_numbers': pred_actual_numbers_sorted,
                            'status': '❌ 数据不匹配'
                        })
                else:
                    issues.append({
                        'index': i,
                        'period': period_str,
                        'period_num': period_num,
                        'real_numbers': real_numbers,
                        'pred_numbers': [],
                        'status': '⚠️ 预测数据中无实际数字'
                    })
            else:
                issues.append({
                    'index': i,
                    'period': period_str,
                    'period_num': period_num,
                    'real_numbers': None,
                    'pred_numbers': [],
                    'status': '❌ 真实数据中无此期号'
                })
                
        except Exception as e:
            print(f"⚠️ 解析期号失败: {period_str}, 错误: {e}")
    
    print(f"映射问题分析:")
    print(f"  正确映射: {len(correct_mappings)}期")
    print(f"  问题映射: {len(issues)}期")
    
    if len(issues) > 0:
        print(f"\n❌ 问题详情:")
        for issue in issues:
            print(f"  {issue['period']}: {issue['status']}")
            if issue['real_numbers'] and issue['pred_numbers']:
                print(f"    真实: {issue['real_numbers']}")
                print(f"    预测: {issue['pred_numbers']}")
    
    return issues, correct_mappings

def fix_data_mapping():
    """修正数据映射"""
    print(f"\n🔧 修正数据映射")
    print("="*40)
    
    lottery_df, prediction_df, lottery_2025 = load_and_analyze_data()
    
    # 备份原文件
    backup_filename = f'prediction_data_backup_before_fix_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
    prediction_df.to_csv(backup_filename, index=False, encoding='utf-8-sig')
    print(f"📁 原文件已备份: {backup_filename}")
    
    # 创建修正后的数据
    fixed_df = prediction_df.copy()
    fix_count = 0
    
    for i, pred_row in fixed_df.iterrows():
        period_str = pred_row['预测期号']
        
        try:
            period_num = int(period_str.replace('2025年', '').replace('期', ''))
            
            # 在2025年真实数据中查找正确的开奖数据
            lottery_match = lottery_2025[lottery_2025['期号'] == period_num]
            
            if len(lottery_match) > 0:
                # 获取正确的真实开奖数字
                real_numbers = [lottery_match.iloc[0][f'数字{j}'] for j in range(1, 7)]
                
                # 更新预测数据中的实际数字
                for j, num in enumerate(real_numbers, 1):
                    fixed_df.loc[i, f'实际数字{j}'] = float(num)
                
                # 重新计算命中情况
                predicted_numbers = [pred_row['预测数字1'], pred_row['预测数字2']]
                hit_numbers = list(set(predicted_numbers) & set(real_numbers))
                hit_count = len(hit_numbers)
                is_hit = hit_count >= 1
                
                # 更新命中信息
                fixed_df.loc[i, '命中数量'] = hit_count
                fixed_df.loc[i, '是否命中'] = '是' if is_hit else '否'
                fixed_df.loc[i, '命中数字'] = ','.join(map(str, sorted(hit_numbers))) if hit_numbers else ''
                
                # 更新备注
                fixed_df.loc[i, '备注'] = '数据映射修正+真实评分'
                
                fix_count += 1
                
        except Exception as e:
            print(f"⚠️ 修正第{i}行失败: {e}")
    
    print(f"✅ 数据修正完成，共修正 {fix_count} 期")
    
    # 保存修正后的文件
    fixed_df.to_csv('prediction_data.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 修正后的数据已保存到 prediction_data.csv")
    
    return fixed_df

def validate_fixed_data():
    """验证修正后的数据"""
    print(f"\n✅ 验证修正后的数据")
    print("="*40)
    
    lottery_df, prediction_df, lottery_2025 = load_and_analyze_data()
    
    # 重新验证数据一致性
    matches = 0
    mismatches = 0
    
    for i, pred_row in prediction_df.iterrows():
        period_str = pred_row['预测期号']
        
        try:
            period_num = int(period_str.replace('2025年', '').replace('期', ''))
            
            # 在2025年真实数据中查找
            lottery_match = lottery_2025[lottery_2025['期号'] == period_num]
            
            if len(lottery_match) > 0:
                # 获取真实开奖数字
                real_numbers = sorted([lottery_match.iloc[0][f'数字{j}'] for j in range(1, 7)])
                
                # 获取预测数据中的实际数字
                pred_actual_numbers = []
                for j in range(1, 7):
                    val = pred_row[f'实际数字{j}']
                    if pd.notna(val) and val != '':
                        pred_actual_numbers.append(int(float(val)))
                
                pred_actual_numbers_sorted = sorted(pred_actual_numbers)
                
                # 比较数据
                if real_numbers == pred_actual_numbers_sorted:
                    matches += 1
                else:
                    mismatches += 1
                    if mismatches <= 5:  # 只显示前5个不匹配
                        print(f"❌ {period_str}: 真实{real_numbers} vs 预测{pred_actual_numbers_sorted}")
            
        except Exception as e:
            mismatches += 1
    
    print(f"验证结果:")
    print(f"  匹配: {matches}期")
    print(f"  不匹配: {mismatches}期")
    print(f"  匹配率: {matches/(matches+mismatches):.1%}")
    
    # 重新统计命中情况
    hit_count = len(prediction_df[prediction_df['是否命中'] == '是'])
    total_count = len(prediction_df)
    hit_rate = hit_count / total_count
    
    print(f"\n修正后的命中统计:")
    print(f"  总预测期数: {total_count}")
    print(f"  命中期数: {hit_count}")
    print(f"  命中率: {hit_rate:.1%}")

def generate_fix_report():
    """生成修正报告"""
    print(f"\n📋 生成修正报告")
    print("="*40)
    
    report = f"""
prediction_data.csv数据映射修正报告
{'='*50}

修正时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

发现的问题:
1. 预测数据中部分期号的实际开奖数据与真实数据不匹配
2. 主要集中在第187-197期，存在数据错位问题
3. 影响了命中率统计的准确性

修正措施:
1. 重新从lottery_data_clean_no_special.csv中获取正确的开奖数据
2. 按期号精确匹配2025年的真实开奖数据
3. 重新计算命中情况和统计信息
4. 更新备注信息标记为"数据映射修正+真实评分"

修正结果:
- 数据一致性得到改善
- 命中率统计更加准确
- 评分信息保持不变（基于原始预测特征计算）

文件变更:
- 原文件已备份
- prediction_data.csv已更新为修正版本
- 所有实际开奖数据已重新验证

建议:
1. 使用修正后的数据进行分析
2. 定期验证数据一致性
3. 在添加新数据时确保映射正确
"""
    
    report_filename = f'data_mapping_fix_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"✅ 修正报告已保存: {report_filename}")

def main():
    """主函数"""
    print("🔧 修正prediction_data.csv的数据映射问题")
    print("="*60)
    
    try:
        # 1. 识别映射问题
        issues, correct_mappings = identify_mapping_issues()
        
        if len(issues) > 0:
            print(f"\n发现 {len(issues)} 个映射问题，开始修正...")
            
            # 2. 修正数据映射
            fixed_df = fix_data_mapping()
            
            # 3. 验证修正后的数据
            validate_fixed_data()
            
            # 4. 生成修正报告
            generate_fix_report()
            
            print(f"\n✅ 数据映射修正完成！")
            print(f"prediction_data.csv文件已更新，数据一致性得到改善。")
        else:
            print(f"\n✅ 数据映射正确，无需修正。")
        
    except Exception as e:
        print(f"❌ 修正过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
